package com.yami.shop.bean.vo.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
@Data
public class MemberDealRespVO {

    @Schema(description = "全部成交会员" )
    private MemberDealVO allMember;

    @Schema(description = "新成交会员" )
    private MemberDealVO newMember;

    @Schema(description = "老成交会员" )
    private MemberDealVO oldMember;

    @Schema(description = "图标参数" )
    private List<MemberDealTreadVO> trendParam;
}
