package com.yami.shop.distribution.common.listener;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yami.shop.bean.event.CategoryWordEvent;
import com.yami.shop.distribution.common.constants.DistributionProdStateEnum;
import com.yami.shop.distribution.common.model.DistributionProd;
import com.yami.shop.distribution.common.service.DistributionProdService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component("CategoryDistributionListener")
@Slf4j
@AllArgsConstructor
public class CategoryDistributionListener {
    private final DistributionProdService distributionProdService;

    @EventListener(CategoryWordEvent.class)
    public void categoryListener(CategoryWordEvent event){
        List<Long> prodIds = event.getProdIdList();
        // 失效分销活动
        if(CollUtil.isNotEmpty(prodIds)){
            distributionProdService.update(Wrappers.lambdaUpdate(DistributionProd.class)
                    .set(DistributionProd::getState, DistributionProdStateEnum.PUT_OFF.getValue())
                    .in(DistributionProd::getProdId, prodIds)
                    .eq(DistributionProd::getState, DistributionProdStateEnum.PUT_ON.getValue())
            );
        }
    }
}
