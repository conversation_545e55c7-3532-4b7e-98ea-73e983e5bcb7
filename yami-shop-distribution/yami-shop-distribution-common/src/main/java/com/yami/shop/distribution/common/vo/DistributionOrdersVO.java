package com.yami.shop.distribution.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
@Data
@Schema(description = "我的分销订单项信息")
public class DistributionOrdersVO implements Serializable {
    private static final long serialVersionUID = 6222259729061826852L;

    @Schema(description = "订单流水号" )
    private String orderNumber;

    @Schema(description = "总金额" )
    private Double total;

    @Schema(description = "订单商品总数" )
    private Integer prodCount;

    @Schema(description = "图片" )
    private String pic;

    @Schema(description = "售价" )
    private Double price;

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "sku名称" )
    private String skuName;

    @Schema(description = "分销佣金" )
    private Double distributionAmount;

    @Schema(description = "佣金比例" )
    private Double commissionRate;

    @Schema(description = "订单是否有效 1 未结算，2 已结算" )
    private Integer state;

    @Schema(description = "更新时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Long orderItemId;

    @Schema(description = "失效原因(0正常，1.分销佣金大于或者等于订单项实付金额，2.订单项售后成功)" )
    private Integer reson;


}
