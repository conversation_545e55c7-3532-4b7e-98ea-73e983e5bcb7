package com.yami.shop.common.allinpay.member.req;

import com.yami.shop.common.allinpay.member.resp.CompanyBasicInfo;

import java.io.Serial;
import java.io.Serializable;

/**
 * 设置企业信息
 *
 * <AUTHOR>
 */
public class SetCompanyInfo extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    public static final String METHOD_NAME = "setCompanyInfo";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    /**
     * 设置企业信息 后回调url
     */
    private String backUrl;

    private CompanyBasicInfo companyBasicInfo;

    public SetCompanyInfo(String bizUserId, String backUrl, CompanyBasicInfo companyBasicInfo) {
        this.backUrl = backUrl;
        this.companyBasicInfo = companyBasicInfo;
        this.bizUserId = bizUserId;
    }


    public SetCompanyInfo() {
    }


    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public CompanyBasicInfo getCompanyBasicInfo() {
        return companyBasicInfo;
    }

    public void setCompanyBasicInfo(CompanyBasicInfo companyBasicInfo) {
        this.companyBasicInfo = companyBasicInfo;
    }


}
