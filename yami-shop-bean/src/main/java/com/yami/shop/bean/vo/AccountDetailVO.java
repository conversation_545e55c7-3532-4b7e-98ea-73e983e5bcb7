package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/8/10 16:00
 */
@Data
public class AccountDetailVO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "微信金额" )
    private Double wechatAmount;

    @Schema(description = "支付宝金额" )
    private Double alipayAmount;

    @Schema(description = "余额金额" )
    private Double balanceAmount;

    @Schema(description = "paypal金额" )
    private Double paypalAmount;

    @Schema(description = "积分数目" )
    private Long scoreCount;

    @Schema(description = "微信占比" )
    private Double wechatPercent;

    @Schema(description = "支付宝占比" )
    private Double alipayPercent;

    @Schema(description = "余额占比" )
    private Double balancePercent;

    @Schema(description = "paypal占比" )
    private Double paypalPercent;

    @Schema(description = "合计" )
    private Double total;
    private Long shopId;

}
