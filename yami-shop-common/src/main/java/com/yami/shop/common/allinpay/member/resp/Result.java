package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;/**
 * <AUTHOR>
 */
public class Result extends BizUserId implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 结果：成功(OK)，失败(error)
     */
    private String result;

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "Result{" +
                "result='" + result + '\'' +
                ", bizUserId='" + bizUserId + '\'' +
                '}';
    }
}
