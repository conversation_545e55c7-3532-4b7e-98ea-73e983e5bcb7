package com.yami.shop.multishop.controller;

import com.yami.shop.bean.model.AttachFileGroup;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.AttachFileGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/7/7 17:18
 */
@RestController
@RequestMapping("/admin/fileGroup")
@Tag(name = "文件分组接口")
@AllArgsConstructor
public class AttachFileGroupController {

    private final AttachFileGroupService attachFileGroupService;

    @GetMapping("/list")
    @Operation(summary = "根据分组类型获取分组列表" , description = "根据分组类型获取分组列表")
    @Parameter(name = "type", description = "分组类型 1:图片 2:视频 3:文件" , required = true)
    @PreAuthorize("@pms.hasPermission('admin:fileGroup:list')")
    public ServerResponseEntity<List<AttachFileGroup>> list(@RequestParam(value = "type", defaultValue = "1") Integer type) {
        List<AttachFileGroup> attachFileGroupPage = attachFileGroupService.list(SecurityUtils.getShopUser().getShopId(),type);
        return ServerResponseEntity.success(attachFileGroupPage);
    }

    @GetMapping
    @Operation(summary = "根据分组id获取分组" , description = "根据分组id获取分组")
    @Parameter(name = "attachFileGroupId", description = "分组Id" , required = true)
    @PreAuthorize("@pms.hasPermission('admin:fileGroup:info')")
    public ServerResponseEntity<AttachFileGroup> getByAttachFileGroupId(@RequestParam Long attachFileGroupId) {
        AttachFileGroup attachFileGroup = attachFileGroupService.getByAttachFileGroupId(attachFileGroupId);
        if (!Objects.equals(SecurityUtils.getShopUser().getShopId(), attachFileGroup.getShopId())) {
            throw new YamiShopBindException("yami.no.auth");
        }
        return ServerResponseEntity.success(attachFileGroup);
    }

    @PostMapping
    @Operation(summary = "保存文件分组" , description = "保存文件分组")
    @PreAuthorize("@pms.hasPermission('admin:fileGroup:save')")
    public ServerResponseEntity<Void> save(@Valid @RequestBody AttachFileGroup attachFileGroup) {
        attachFileGroup.setAttachFileGroupId(null);
        attachFileGroup.setShopId(SecurityUtils.getShopUser().getShopId());
        attachFileGroupService.saveGroup(attachFileGroup);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @Operation(summary = "更新文件分组" , description = "更新文件分组")
    @PreAuthorize("@pms.hasPermission('admin:fileGroup:update')")
    public ServerResponseEntity<Void> update(@Valid @RequestBody AttachFileGroup attachFileGroup) {
        attachFileGroup.setShopId(SecurityUtils.getShopUser().getShopId());
        attachFileGroupService.update(attachFileGroup);
        return ServerResponseEntity.success();
    }

    @DeleteMapping
    @Operation(summary = "根据分组id删除文件分组" , description = "根据分组id删除文件分组")
    @PreAuthorize("@pms.hasPermission('admin:fileGroup:delete')")
    public ServerResponseEntity<Void> delete(@RequestParam Long attachFileGroupId) {
        AttachFileGroup dbAttachFileGroup = attachFileGroupService.getByAttachFileGroupId(attachFileGroupId);
        if (!Objects.equals(dbAttachFileGroup.getShopId(), SecurityUtils.getShopUser().getShopId())) {
            throw new YamiShopBindException("yami.no.auth");
        }
        attachFileGroupService.deleteById(attachFileGroupId);
        return ServerResponseEntity.success();
    }
}
