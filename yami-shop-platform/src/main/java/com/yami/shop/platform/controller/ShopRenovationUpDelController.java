package com.yami.shop.platform.controller;

import cn.hutool.core.util.BooleanUtil;
import com.yami.shop.bean.model.ShopRenovation;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.ShopRenovationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;


/**
 * 店铺装修信息
 *
 * <AUTHOR>
 * @date 2021-01-05 11:03:38
 */
@RestController
@RequestMapping("/platform/shopRenovation/operate" )
@Tag(name = "店铺页面-修改、删除、设为主页")
@RequiredArgsConstructor
public class ShopRenovationUpDelController {
    @Value("${yami.expose.operation.auth:}")
    private Boolean permission;

    private final ShopRenovationService shopRenovationService;


    @SysLog("修改店铺装修信息" )
    @PutMapping("/updatePC")
    @PreAuthorize("@pms.hasPermission('platform:shopRenovation:updatePC')")
    @Operation(summary = "PC端修改店铺装修页面信息" , description = "PC端修改店铺装修页面信息")
    public ServerResponseEntity<Boolean> updatePcById(@RequestBody @Valid ShopRenovation shopRenovation) {
        if (BooleanUtil.isFalse(permission)) {
            return ServerResponseEntity.showFailMsg(I18nMessage.getMessage("yami.operate.auth"));
        }
        ShopRenovation shopRenovationDb = shopRenovationService.getById(shopRenovation.getRenovationId());
        if(!Objects.equals(shopRenovationDb.getShopId() ,Constant.PLATFORM_SHOP_ID)){
            // 没有权限进行操作
            throw new YamiShopBindException("yami.no.auth");
        }
        shopRenovationService.checkShopRenovation(shopRenovation);
        shopRenovationService.removeCache(shopRenovationDb.getShopId(), shopRenovationDb.getRenovationType(), shopRenovationDb.getRenovationId());
        return ServerResponseEntity.success(shopRenovationService.updateById(shopRenovation));
    }

    @SysLog("修改店铺装修信息" )
    @PutMapping("/updateMove")
    @PreAuthorize("@pms.hasPermission('platform:shopRenovation:updateMove')")
    @Operation(summary = "移动端修改店铺装修页面信息" , description = "移动端修改店铺装修页面信息")
    public ServerResponseEntity<Boolean> updateMoveById(@RequestBody @Valid ShopRenovation shopRenovation) {
        if (BooleanUtil.isFalse(permission)) {
            return ServerResponseEntity.showFailMsg(I18nMessage.getMessage("yami.operate.auth"));
        }
        ShopRenovation shopRenovationDb = shopRenovationService.getById(shopRenovation.getRenovationId());
        if(!Objects.equals(shopRenovationDb.getShopId() ,Constant.PLATFORM_SHOP_ID)){
            // 没有权限进行操作
            throw new YamiShopBindException("yami.no.auth");
        }
        shopRenovationService.checkShopRenovation(shopRenovation);
        shopRenovationService.removeCache(shopRenovationDb.getShopId(), shopRenovationDb.getRenovationType(), shopRenovationDb.getRenovationId());
        return ServerResponseEntity.success(shopRenovationService.updateById(shopRenovation));
    }

    @SysLog("删除店铺装修信息" )
    @DeleteMapping("/deletePC/{renovationId}" )
    @PreAuthorize("@pms.hasPermission('platform:shopRenovation:deletePC')")
    @Operation(summary = "PC端删除店铺装修页面信息" , description = "PC端删除店铺装修页面信息")
    @Parameter(name = "renovationId", description = "店铺装修id" )
    public ServerResponseEntity<Boolean> removePcById(@PathVariable("renovationId") Long renovationId) {
        if (BooleanUtil.isFalse(permission)) {
            return ServerResponseEntity.showFailMsg(I18nMessage.getMessage("yami.operate.auth"));
        }
        ShopRenovation shopRenovation = shopRenovationService.getById(renovationId);
        if(!Objects.equals(shopRenovation.getShopId() ,Constant.PLATFORM_SHOP_ID)){
            // 没有权限进行操作
            throw new YamiShopBindException("yami.no.auth");
        }
        shopRenovationService.removeCache(shopRenovation.getShopId(), shopRenovation.getRenovationType(), shopRenovation.getRenovationId());
        return ServerResponseEntity.success(shopRenovationService.removeById(renovationId));
    }

    @SysLog("删除店铺装修信息" )
    @DeleteMapping("/deleteMove/{renovationId}" )
    @PreAuthorize("@pms.hasPermission('platform:shopRenovation:deleteMove')")
    @Operation(summary = "移动端删除店铺装修页面信息" , description = "移动端删除店铺装修页面信息")
    @Parameter(name = "renovationId", description = "店铺装修id" )
    public ServerResponseEntity<Boolean> removeMoveById(@PathVariable("renovationId") Long renovationId) {
        if (BooleanUtil.isFalse(permission)) {
            return ServerResponseEntity.showFailMsg(I18nMessage.getMessage("yami.operate.auth"));
        }
        ShopRenovation shopRenovation = shopRenovationService.getById(renovationId);
        if(!Objects.equals(shopRenovation.getShopId() ,Constant.PLATFORM_SHOP_ID)){
            // 没有权限进行操作
            throw new YamiShopBindException("yami.no.auth");
        }
        shopRenovationService.delete(shopRenovation);
        shopRenovationService.removeCache(shopRenovation.getShopId(), shopRenovation.getRenovationType(), shopRenovation.getRenovationId());
        return ServerResponseEntity.success();
    }

    @PutMapping("/updateHomePagePC/{id}" )
    @PreAuthorize("@pms.hasPermission('platform:shopRenovation:updateHomePagePC')")
    @Operation(summary = "PC端修改主页标识" , description = "PC端修改主页标识")
    @Parameter(name = "id", description = "店铺装修id" )
    public ServerResponseEntity<Void> updateHomePagePc(@PathVariable("id") Long id) {
        if (BooleanUtil.isFalse(permission)) {
            return ServerResponseEntity.showFailMsg(I18nMessage.getMessage("yami.operate.auth"));
        }
        ShopRenovation shopRenovation = shopRenovationService.getById(id);
        if(!Objects.equals(shopRenovation.getShopId() ,Constant.PLATFORM_SHOP_ID)){
            // 没有权限进行操作
            throw new YamiShopBindException("yami.no.auth");
        }

        shopRenovationService.updateToHomePage(shopRenovation);
        shopRenovationService.removeCache(shopRenovation.getShopId(), shopRenovation.getRenovationType(), shopRenovation.getRenovationId());
        return ServerResponseEntity.success();
    }

    @PutMapping("/updateHomePageMove/{id}" )
    @PreAuthorize("@pms.hasPermission('platform:shopRenovation:updateHomePageMove')")
    @Operation(summary = "移动端修改主页标识" , description = "移动端修改主页标识")
    @Parameter(name = "id", description = "店铺装修id" )
    public ServerResponseEntity<Void> updateHomePageMove(@PathVariable("id") Long id) {
        if (BooleanUtil.isFalse(permission)) {
            return ServerResponseEntity.showFailMsg(I18nMessage.getMessage("yami.operate.auth"));
        }
        ShopRenovation shopRenovation = shopRenovationService.getById(id);
        if(!Objects.equals(shopRenovation.getShopId() ,Constant.PLATFORM_SHOP_ID)){
            // 没有权限进行操作
            throw new YamiShopBindException("yami.no.auth");
        }

        shopRenovationService.updateToHomePage(shopRenovation);
        shopRenovationService.removeCache(shopRenovation.getShopId(), shopRenovation.getRenovationType(), shopRenovation.getRenovationId());
        return ServerResponseEntity.success();
    }
}
