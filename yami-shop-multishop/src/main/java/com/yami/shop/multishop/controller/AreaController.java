package com.yami.shop.multishop.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yami.shop.bean.dto.AreaDto;
import com.yami.shop.bean.model.Area;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.AreaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> on 2018/10/26.
 */
@RestController
@RequestMapping("/admin/area")
@Tag(name = "地区信息接口")
@AllArgsConstructor
public class AreaController {

    private final AreaService areaService;

    @GetMapping("/list")
    @Operation(summary = "获取省市区地区信息列表（可传入地区名称搜索）" , description = "分页获取省市区地区信息列表（可传入地区名称搜索）")
    @PreAuthorize("@pms.hasPermission('admin:area:list')")
    public ServerResponseEntity<List<Area>> list(Area area) {
        List<Area> areas = areaService.list(new LambdaQueryWrapper<Area>()
                .like(area.getAreaName() != null, Area::getAreaName, area.getAreaName())
                .ne(Area::getLevel, 0));
        return ServerResponseEntity.success(areas);
    }

    @GetMapping("/listByPid")
    @Operation(summary = "通过父级id获取区域列表" , description = "通过父级id获取区域列表")
    @PreAuthorize("@pms.hasPermission('admin:area:list')")
    public ServerResponseEntity<List<Area>> listByPid(Long pid, Integer level) {
        List<Area> list = areaService.listByPid(pid, level);
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/areaListInfo")
    @Operation(summary = "获取省市区信息" , description = "获取省市区信息")
    public ServerResponseEntity<List<AreaDto>> getAreaListInfo() {
        return ServerResponseEntity.success(areaService.getAreaListInfo());
    }

    @GetMapping("/listAreaOfEnable")
    @Operation(summary = "获取可用的省市区列表(完整路径）" , description = "获取可用的省市区列表（完整路径）")
    public ServerResponseEntity<List<AreaDto>> listAreaOfEnable() {
        List<AreaDto> list = areaService.listAreaOfEnable();
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/areaList")
    @PreAuthorize("@pms.hasPermission('admin:area:list')")
    @Operation(summary = "获取可用的区域省市区信息（四级地址列表）" , description = "获取可用的区域省市区信息")
    public ServerResponseEntity<List<AreaDto>> getAllAreaList() {
        return ServerResponseEntity.success(areaService.getAllAreaList());
    }
}
