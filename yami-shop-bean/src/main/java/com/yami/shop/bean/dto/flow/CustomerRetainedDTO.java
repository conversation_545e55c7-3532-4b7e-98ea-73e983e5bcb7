package com.yami.shop.bean.dto.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-05-22 14:28:43
 */
@Data
public class CustomerRetainedDTO {
    /**
     * 时间类型 1最近1个月 2最近3个月 3最近6个月 4最近1年
     * RetainedDateType
     */
    @Schema(description = "时间类型 1最近三个月 2最近六个月 3最近一年" )
    private Integer dateType;

    @Schema(description = "留存类型 1访问留存  2成交留存" )
    private Integer retainType;
    /**
     *  1月留存 2周留存
     */
    @Schema(description = "1月留存 2周留存,暂时不统计周留存" )
    private Integer dateRetainType;

    @Schema(description = "开始时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "结束时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 时间
     */
    @Schema(description = "不传字段" )
    private Date dateTime;
    /**
     * 店铺id
     */
    @Schema(description = "不传字段" )
    private Long shopId;

    /**
     * 第三方系统id 1：微信小程序
     */
    @Schema(description = "不传字段" )
    private Integer appId;
}
