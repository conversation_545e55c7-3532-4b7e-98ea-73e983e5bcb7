package com.yami.shop.multishop.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.RefundAddr;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.response.ResponseEnum;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.common.util.PrincipalUtil;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.RefundAddrService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/shop/refundAddr")
@Tag(name = "退货地址接口")
public class RefundAddrController {

    private final RefundAddrService refundAddrService;

    /**
     * 分页查询
     *
     * @param page       分页对象
     * @param refundAddr
     * @return 分页数据
     */
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('shop:refundAddr:page')")
    @Operation(summary = "分页查询退货地址" , description = "分页查询退货地址")
    public ServerResponseEntity<IPage<RefundAddr>> getRefundAddrPage(PageParam<RefundAddr> page, RefundAddr refundAddr) {
        PageParam<RefundAddr> refundAddrPage = refundAddrService.page(page, new LambdaQueryWrapper<RefundAddr>()
                .eq(RefundAddr::getShopId, SecurityUtils.getShopUser().getShopId())
                .ne(RefundAddr::getStatus, -1).orderByDesc(RefundAddr::getRefundAddrId));
        if (CollUtil.isNotEmpty(refundAddrPage.getRecords())) {
            for (RefundAddr record : refundAddrPage.getRecords()) {
                if (Objects.nonNull(record.getReceiverMobile())) {
                    record.setReceiverMobile(PhoneUtil.hideBetween(record.getReceiverMobile()).toString());
                }
                String tel = record.getReceiverTelephone();
                if (StrUtil.isNotBlank(tel)) {
                    String after = !tel.contains("-") ? tel : tel.split("-")[1];
                    String afterHide = PhoneUtil.hideBetween(after).toString();
                    String before = !tel.contains("-") ? "" : tel.split("-")[0] + "-";
                    record.setReceiverTelephone(before + afterHide);
                }
            }
        }
        return ServerResponseEntity.success(refundAddrPage);
    }


    /**
     * 通过id查询
     *
     * @param refundAddrId id
     * @return 单个数据
     */
    @GetMapping("/info/{refundAddrId}")
    @Operation(summary = "通过id查询退货地址" , description = "通过id查询退货地址")
    @PreAuthorize("@pms.hasPermission('shop:refundAddr:info')")
    public ServerResponseEntity<RefundAddr> getById(@PathVariable("refundAddrId") Long refundAddrId) {
        RefundAddr refundAddr = refundAddrService.getById(refundAddrId);
        if (!Objects.equals(SecurityUtils.getShopUser().getShopId(), refundAddr.getShopId())) {
            throw new YamiShopBindException("yami.no.auth");
        }
        if (Objects.nonNull(refundAddr.getReceiverMobile())) {
            refundAddr.setReceiverMobile(PhoneUtil.hideBetween(refundAddr.getReceiverMobile()).toString());
        }
        String tel = refundAddr.getReceiverTelephone();
        if (StrUtil.isNotBlank(tel)) {
            String after = !tel.contains("-") ? tel : tel.split("-")[1];
            String afterHide = PhoneUtil.hideBetween(after).toString();
            String before = !tel.contains("-") ? "" : tel.split("-")[0] + "-";
            refundAddr.setReceiverTelephone(before + afterHide);
        }
        return ServerResponseEntity.success(refundAddr);
    }

    /**
     * 新增
     *
     * @param refundAddr
     * @return 是否新增成功
     */
    @SysLog("新增")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('shop:refundAddr:save')")
    @Operation(summary = "新增退货地址" , description = "新增退货地址")
    public ServerResponseEntity<Long> save(@RequestBody @Valid RefundAddr refundAddr) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        Date now = new Date();
        refundAddr.setShopId(shopId);
        refundAddr.setCreateTime(now);
        refundAddr.setUpdateTime(now);
        // 添加成功
        return ServerResponseEntity.success(refundAddrService.addRefundAddr(refundAddr));
    }

    /**
     * 修改
     *
     * @param refundAddr
     * @return 是否修改成功
     */
    @SysLog("修改")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('shop:refundAddr:update')")
    @Operation(summary = "修改退货地址" , description = "修改退货地址")
    public ServerResponseEntity<String> updateById(@RequestBody @Valid RefundAddr refundAddr) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        RefundAddr dbRefundAddr = refundAddrService.getById(refundAddr.getRefundAddrId());
        if (Objects.isNull(dbRefundAddr)) {
            throw new YamiShopBindException("yami.prod.common.invalid");
        }
        if (StrUtil.isNotBlank(refundAddr.getReceiverMobile()) && PrincipalUtil.isAsteriskMobile(refundAddr.getReceiverMobile())) {
            if (!PrincipalUtil.isDbPhone(refundAddr.getReceiverMobile(), dbRefundAddr.getReceiverMobile(), false)) {
                throw new YamiShopBindException("yami.user.err.phone");
            }
            if (refundAddr.getReceiverMobile().contains(Constant.ASTERISK)) {
                refundAddr.setReceiverMobile(dbRefundAddr.getReceiverMobile());
            }
        }
        if (StrUtil.isNotBlank(refundAddr.getReceiverTelephone()) && PrincipalUtil.isAsteriskTel(refundAddr.getReceiverTelephone())) {
            if (!PrincipalUtil.isDbPhone(refundAddr.getReceiverTelephone(), dbRefundAddr.getReceiverTelephone(), true)) {
                throw new YamiShopBindException("yami.user.err.phone");
            }
            if (refundAddr.getReceiverTelephone().contains(Constant.ASTERISK)) {
                refundAddr.setReceiverTelephone(dbRefundAddr.getReceiverTelephone());
            }
        }
        refundAddr.setUpdateTime(new Date());
        refundAddrService.updateRefundAddr(shopId, refundAddr);
        // 修改成功
        return ServerResponseEntity.success(ResponseEnum.SHOW_SUCCESS.value(), I18nMessage.getMessage("yami.activity.update.success"));
    }

    /**
     * 通过id删除
     *
     * @param refundAddrId id
     * @return 是否删除成功
     */
    @SysLog("删除")
    @DeleteMapping("/{refundAddrId}")
    @PreAuthorize("@pms.hasPermission('shop:refundAddr:delete')")
    @Operation(summary = "删除退货地址" , description = "删除退货地址")
    public ServerResponseEntity<Boolean> removeById(@PathVariable Long refundAddrId) {
        return ServerResponseEntity.success(refundAddrService.removeById(refundAddrId));
    }

    /**
     * 获取店铺的所有收获地址
     */
    @GetMapping("/list")
    @Operation(summary = "获取店铺的所有退货地址" , description = "获取店铺的所有退货地址")
    @PreAuthorize("@pms.hasPermission('shop:refundAddr:list')")
    public ServerResponseEntity<List<RefundAddr>> list() {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        List<RefundAddr> list = refundAddrService.list(new LambdaQueryWrapper<RefundAddr>()
                .eq(RefundAddr::getShopId, shopId)
                .eq(RefundAddr::getStatus, 1)
                .orderByDesc(RefundAddr::getUpdateTime));
        if (CollUtil.isNotEmpty(list)) {
            for (RefundAddr record : list) {
                if (Objects.nonNull(record.getReceiverMobile())) {
                    record.setReceiverMobile(PhoneUtil.hideBetween(record.getReceiverMobile()).toString());
                }
                String tel = record.getReceiverTelephone();
                if (StrUtil.isNotBlank(tel)) {
                    String after = !tel.contains("-") ? tel : tel.split("-")[1];
                    String afterHide = PhoneUtil.hideBetween(after).toString();
                    String before = !tel.contains("-") ? "" : tel.split("-")[0] + "-";
                    record.setReceiverTelephone(before + afterHide);
                }
            }
        }
        return ServerResponseEntity.success(list);
    }

}
