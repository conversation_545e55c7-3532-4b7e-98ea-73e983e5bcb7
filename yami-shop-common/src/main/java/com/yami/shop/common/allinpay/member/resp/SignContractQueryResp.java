package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;
/**
 * 查询签约信息响应
 * <AUTHOR>
 */
public class SignContractQueryResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private String bizUserId;

    private String contractNo;

    private String accProtocolNo;

    private String status;

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getAccProtocolNo() {
        return accProtocolNo;
    }

    public void setAccProtocolNo(String accProtocolNo) {
        this.accProtocolNo = accProtocolNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "SignContractQueryResp{" +
                "bizUserId='" + bizUserId + '\'' +
                ", contractNo='" + contractNo + '\'' +
                ", accProtocolNo='" + accProtocolNo + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
