package com.yami.shop.bean.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
public class VoucherVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long voucherId;
    @Schema(description = "卡券名称")
    private String name;
    @Schema(description = "创建时间")
    private Date createTime;
    @Schema(description = "更新时间")
    private Date updateTime;
    @Schema(description = "关联商品名称")
    private String prodName;
    @Schema(description = "关联商品id")
    private Long prodId;
    @Schema(description = "关联商品sku名称")
    private String skuName;
    @Schema(description = "关联商品skuId")
    private Long skuId;
    @Schema(description = "已发放数量")
    private Integer startUsingNum;
    @Schema(description = "未发放数量")
    private Integer notEnabledNum;
    @Schema(description = "已失效数量")
    private Integer expiredNum;
    @Schema(description = "状态 -1：已删除 0：正常")
    private Integer status;
    @Schema(description = "店铺id")
    private Long shopId;
}
