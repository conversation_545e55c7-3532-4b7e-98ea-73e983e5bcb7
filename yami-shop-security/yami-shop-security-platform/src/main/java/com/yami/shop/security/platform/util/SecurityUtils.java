package com.yami.shop.security.platform.util;

import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.security.platform.model.YamiSysUser;
import lombok.experimental.UtilityClass;

/**
 *
 * <AUTHOR>
 */
@UtilityClass
public class SecurityUtils {


    /**
     * 获取用户
     */
    public YamiSysUser getSysUser() {
        YamiSysUser details = new YamiSysUser();
        details.setUserId(AuthUserContext.getSysUserId());
        return details;
    }
}
