package com.yami.shop.bean.vo.wukong;

import com.yami.shop.bean.model.ImChannel;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 创建或更新频道
 * <AUTHOR>
 */
public class ChannelVO {

    @Schema(description = "频道的唯一ID")
    private String channelId;

    @Schema(description = "平台/商家订阅者 uid")
    private String uid;

    @Schema(description = "平台/商家管理员昵称")
    private String nickName;

    @Schema(description = "头像")
    private String pic;

    @Schema(description = "店铺状态")
    private Integer shopStatus;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "店铺logo")
    private String shopLogo;

    @Schema(description = "员工名称")
    private String employeeName;

    @Schema(description = "频道信息")
    private ImChannel imChannel;

    @Schema(description = "用户在线情况")
    private Integer userIsOnline;

    @Schema(description = "当前用户是否在该频道的白名单内 1是0否")
    private Integer isWhiteUser;

    @Schema(description = "是否为注销用户")
    private Integer isDestroy;

    public ChannelVO(String channelId) {
        this.channelId = channelId;
    }

    public ChannelVO() {
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public Integer getShopStatus() {
        return shopStatus;
    }

    public void setShopStatus(Integer shopStatus) {
        this.shopStatus = shopStatus;
    }

    public ImChannel getImChannel() {
        return imChannel;
    }

    public void setImChannel(ImChannel imChannel) {
        this.imChannel = imChannel;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopLogo() {
        return shopLogo;
    }

    public void setShopLogo(String shopLogo) {
        this.shopLogo = shopLogo;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public Integer getUserIsOnline() {
        return userIsOnline;
    }

    public void setUserIsOnline(Integer userIsOnline) {
        this.userIsOnline = userIsOnline;
    }

    public Integer getIsWhiteUser() {
        return isWhiteUser;
    }

    public void setIsWhiteUser(Integer isWhiteUser) {
        this.isWhiteUser = isWhiteUser;
    }

    public Integer getIsDestroy() {
        return isDestroy;
    }

    public void setIsDestroy(Integer isDestroy) {
        this.isDestroy = isDestroy;
    }

    @Override
    public String toString() {
        return "ChannelVO{" +
                "channelId='" + channelId + '\'' +
                ", uid='" + uid + '\'' +
                ", nickName='" + nickName + '\'' +
                ", pic='" + pic + '\'' +
                ", shopStatus=" + shopStatus +
                ", shopName='" + shopName + '\'' +
                ", shopLogo='" + shopLogo + '\'' +
                ", employeeName='" + employeeName + '\'' +
                ", imChannel=" + imChannel +
                ", userIsOnline=" + userIsOnline +
                ", isWhiteUser=" + isWhiteUser +
                ", isDestroy=" + isDestroy +
                '}';
    }
}
