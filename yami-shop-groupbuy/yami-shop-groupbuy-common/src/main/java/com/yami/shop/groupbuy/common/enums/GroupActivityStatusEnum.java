package com.yami.shop.groupbuy.common.enums;


import lombok.Getter;

/**
 * 拼团活动状态类型(对应数据库中的活动状态)
 *
 * <AUTHOR>
 */
@Getter
public enum GroupActivityStatusEnum {
    /**
     * 删除
     */
    DELETE(-1, "删除"),

    /**
     * 已失效
     */
    INVALID(0, "已失效"),

    /**
     * 启用
     */
    ENABLE(1, "启用"),

    /**
     * 未启用
     */
    DISABLE(2, "未启用"),

    /**
     * 违规下架
     */
    OFFLINE(3, "违规下架"),

    /**
     * 等待审核
     */
    WAIT_AUDIT(4, "等待审核"),

    /**
     * 已结束
     */
    END(5, "已结束");


    private final Integer code;

    private final String title;

    public Integer value() {
        return code;
    }

    GroupActivityStatusEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }
}
