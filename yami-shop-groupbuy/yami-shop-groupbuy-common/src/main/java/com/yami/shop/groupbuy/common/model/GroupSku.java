package com.yami.shop.groupbuy.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
@Data
@TableName("tz_group_sku")
@Schema(description = "拼团活动商品规格")
public class GroupSku implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "拼团活动商品规格id" )
    @TableId
    private Long groupSkuId;

    @Schema(description = "拼团活动id" )
    private Long groupActivityId;

    @Schema(description = "商品规格id" )
    private Long skuId;

    @Schema(description = "活动价格" )
    private Double actPrice;

    @Schema(description = "已售数量" )
    private Long sellNum;

    @Schema(description = "规格名称" )
    @TableField(exist = false)
    private String skuName;

    @Schema(description = "规格单价" )
    @TableField(exist = false)
    private Double price;

    @Schema(description = "sku库存" )
    @TableField(exist = false)
    private Long stocks;

    @Schema(description = "商品id" )
    @TableField(exist = false)
    private Long prodId;
}
