package com.yami.shop.groupbuy.common.enums;


import lombok.Getter;

/**
 * 拼团活动商品状态类型
 *
 * <AUTHOR>
 */
@Getter
public enum GroupProdStatusEnum {

    /**
     * 正常
     */
    NORMAL(1, "正常"),

    /**
     * 已失效
     */
    EXPIRED(2, "已失效"),

    /**
     * 已结束
     */
    DELETE(-1, "已删除"),
    ;

    private final Integer code;

    private final String title;

    public Integer value() {
        return code;
    }

    GroupProdStatusEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }
}
