package com.yami.shop.distribution.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.model.DistributionUserBind;
import com.yami.shop.distribution.common.service.DistributionUserBindService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> on 2019/04/01.
 */
@RestController
@RequestMapping("/distribution/distributionUserBind")
@Tag(name = "平台端分销员绑定接口")
@AllArgsConstructor
public class DistributionUserBindController {

    private final DistributionUserBindService distributionUserBindService;

    /**
     * 分页获取
     */
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserBind:page')")
    @Operation(summary = "分页获取分销员绑定列表")
    @Parameters({
            @Parameter(name = "nickName", description = "分销员昵称" ),
            @Parameter(name = "parentNickName", description = "上级分销员昵称" )
    })
    public ServerResponseEntity<IPage<DistributionUserBind>> page(DistributionUserBind distributionUserBind, PageParam<DistributionUserBind> page,
                                                                  String nickName, String parentNickName, String cUserMobile, String dUserMobile,
                                                                  @RequestParam(value = "sort", defaultValue = "1") Integer sort,
                                                                  @RequestParam(value = "orderBy", defaultValue = "1") Integer orderBy) {
        IPage<DistributionUserBind> list = distributionUserBindService.distributionMsgsAndUserPage(page, distributionUserBind, null, null, nickName, parentNickName,cUserMobile, dUserMobile, sort, orderBy);
        return ServerResponseEntity.success(list);
    }


    @GetMapping("/info/{id}")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserBind:info')")
    @Operation(summary = "根据id获取分销员绑定信息")
    @Parameter(name = "id", description = "分销员绑定id" )
    public ServerResponseEntity<DistributionUserBind> info(@PathVariable("id") Long id) {
        DistributionUserBind distributionUserBind = distributionUserBindService.getById(id);
        return ServerResponseEntity.success(distributionUserBind);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserBind:save')")
    @Operation(summary = "保存分销员绑定信息")
    public ServerResponseEntity<Void> save(@RequestBody @Valid DistributionUserBind distributionUserBind) {
        distributionUserBindService.save(distributionUserBind);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserBind:update')")
    @Operation(summary = "修改分销员绑定信息")
    public ServerResponseEntity<Void> update(@RequestBody @Valid DistributionUserBind distributionUserBind) {
        distributionUserBindService.updateById(distributionUserBind);
        return ServerResponseEntity.success();
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserBind:delete')")
    @Operation(summary = "根据id删除分销员绑定信息")
    @Parameter(name = "id", description = "分销员绑定id" )
    public ServerResponseEntity<Void> delete(@PathVariable Long id) {
        distributionUserBindService.removeById(id);
        return ServerResponseEntity.success();
    }
}
