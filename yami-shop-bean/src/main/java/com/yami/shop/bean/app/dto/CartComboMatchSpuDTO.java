package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2022/8/26 10:04
 */
@Data
public class CartComboMatchSpuDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "套餐搭配商品id")
    private Long prodId;

    @Schema(description = "套餐搭配商品skuId")
    private Long skuId;

    @Schema(description = "套餐搭配商品旧skuId")
    private Long oldSkuId;

    @Schema(description = "数量")
    private Integer count;
}
