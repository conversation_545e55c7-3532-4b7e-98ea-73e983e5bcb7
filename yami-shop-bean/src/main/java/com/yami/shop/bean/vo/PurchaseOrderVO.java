package com.yami.shop.bean.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.yami.shop.bean.model.PurchaseProd;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-01-29
 */
@Data
public class PurchaseOrderVO {

    @TableId
    @Schema(description = "采购id" )
    private Long purchaseOrderId;

    @Schema(description = "采购编号" )
    private String purchaseNumber;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "创建时间" )
    private Date createTime;

    @Schema(description = "更新时间" )
    private Date updateTime;

    @Schema(description = "送达时间" )
    private Date deliverTime;

    @Schema(description = "配送类型 1:快递 3无需快递" )
    private Integer dvyType;

    @Schema(description = "配送方式ID" )
    private Long dvyId;

    @Schema(description = "物流单号" )
    private String dvyFlowId;

    @Schema(description = "供应商id" )
    private Long supplierId;

    @Schema(description = "总采购金额" )
    private Double totalAmount;

    @Schema(description = "总采购库存数量" )
    private Integer totalStock;

    @Schema(description = "实际总库存数量" )
    private Integer actualTotalStock;

    @Schema(description = "状态 0:已作废 1:待入库 2:部分入库 3:已完成" )
    private Integer status;

    @Schema(description = "操作员工id" )
    private Long employeeId;

    @Schema(description = "备注" )
    private String remark;

    @Schema(description = "仓库id")
    private Long warehouseId;

    @Schema(description = "库存点类型(1:仓库, 2:门店)")
    private Integer stockPointType;

    @Schema(description = "采购商品列表" )
    private List<PurchaseProd> purchaseProds;

    @Schema(description = "供应商" )
    private String supplierName;

    @Schema(description = "仓库名称")
    private String warehouseName;
}
