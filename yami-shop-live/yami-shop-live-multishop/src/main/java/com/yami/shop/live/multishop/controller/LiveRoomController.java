package com.yami.shop.live.multishop.controller;

import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.enums.LiveRoomStatusType;
import com.yami.shop.bean.model.User;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.common.util.PrincipalUtil;
import com.yami.shop.live.common.model.LiveRoom;
import com.yami.shop.live.common.service.LiveRoomProdService;
import com.yami.shop.live.common.service.LiveRoomService;
import com.yami.shop.live.common.vo.LiveRoomProdVO;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2020-08-05 08:53:17
 */
@RestController
@AllArgsConstructor
@RequestMapping("/multishop/live/liveRoom")
@Tag(name = "商家端直播间接口")
public class LiveRoomController {

    private final LiveRoomService liveRoomService;
    private final LiveRoomProdService liveRoomProdService;
    private final UserService userService;

    @GetMapping("/page")
    @Operation(summary = "分页查找直播间信息")
    @PreAuthorize("@pms.hasPermission('live:liveRoom:page')")
    public ServerResponseEntity<IPage<LiveRoom>> getLiveRoomPage(PageParam<LiveRoom> page,  LiveRoom liveRoom) {
        liveRoom.setShopId(SecurityUtils.getShopUser().getShopId());
        IPage<LiveRoom> roomPage = liveRoomService.pageLiveRoom(page, liveRoom);
        for (LiveRoom room : roomPage.getRecords()) {
            if (StrUtil.isNotBlank(room.getUserMobile())) {
                room.setUserMobile(PhoneUtil.hideBetween(room.getUserMobile()).toString());
            }
            //昵称符合正则时就**
            if (PrincipalUtil.isMobile(room.getNickName())) {
                room.setNickName(PhoneUtil.hideBetween(room.getNickName()).toString());
            }
        }
        return ServerResponseEntity.success(roomPage);
    }

    @GetMapping("/info/{roomId}")
    @Operation(summary = "通过id查询直播间")
    @Parameter(name = "id", description = "直播间id", required = true)
    @PreAuthorize("@pms.hasPermission('live:liveRoom:info')")
    public ServerResponseEntity<LiveRoom> getById(@PathVariable("roomId") Long roomId) {
        LiveRoom liveRoomDb = liveRoomService.getById(roomId);
        if (Objects.isNull(liveRoomDb)) {
            // 直播间不存在，请刷新后重试
            throw new YamiShopBindException("yami.live.not.exist");
        }
        if (!Objects.equals(SecurityUtils.getShopUser().getShopId(), liveRoomDb.getShopId())) {
            throw new YamiShopBindException("yami.no.auth");
        }
        User user = userService.getUserByUserId(liveRoomDb.getUserId());
        if (Objects.isNull(user)) {
            // 该主播已经注销账号
            liveRoomDb.setNickName("*用户已注销*");
            liveRoomDb.setUserMobile("*用户已注销*");
            liveRoomDb.setStatus(-1);
        } else {
            liveRoomDb.setNickName(user.getNickName());
            liveRoomDb.setUserMobile(PhoneUtil.hideBetween(user.getUserMobile()).toString());
            liveRoomDb.setStatus(user.getStatus());
        }
        return ServerResponseEntity.success(liveRoomDb);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('live:liveRoom:save')")
    @Operation(summary = "新增直播间")
    public ServerResponseEntity<Boolean> save(@RequestBody @Valid LiveRoom liveRoom) {
        liveRoom.setShopId(SecurityUtils.getShopUser().getShopId());
        liveRoom.setUpdateTime(new Date());
        liveRoom.setCreateTime(new Date());
        liveRoom.setLiveStatus(LiveRoomStatusType.NO_START.value());
        liveRoomService.saveLiveRoom(liveRoom);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('live:liveRoom:update')")
    @Operation(summary = "更新直播间")
    public ServerResponseEntity<Boolean> update(@RequestBody @Valid LiveRoom liveRoom) {
        LiveRoom dbLiveRoom = liveRoomService.getById(liveRoom.getRoomId());
        if (Objects.isNull(dbLiveRoom)) {
            // 直播间不存在，请刷新后重试
            throw new YamiShopBindException("yami.live.not.exist");
        }
        if (!Objects.equals(SecurityUtils.getShopUser().getShopId(), dbLiveRoom.getShopId())) {
            throw new YamiShopBindException("yami.no.auth");
        }
        if (Objects.equals(dbLiveRoom.getLiveStatus(), LiveRoomStatusType.LIVING.value())) {
            // 直播已开始，无法修改信息
            throw new YamiShopBindException("yami.live.status.upate.error");
        }
        if (Objects.equals(dbLiveRoom.getLiveStatus(), LiveRoomStatusType.OFFLINE.value())) {
            // 直播间已违规下架，无法修改信息
            throw new YamiShopBindException("yami.live.status.upate.offline");
        }
        User user = userService.getUserByUserId(dbLiveRoom.getUserId());
        if (!PrincipalUtil.isDbPhone(liveRoom.getUserMobile(), user.getUserMobile(), false)) {
            throw new YamiShopBindException("yami.user.err.phone");
        }
        if (liveRoom.getUserMobile().contains(Constant.ASTERISK)) {
            liveRoom.setUserMobile(user.getUserMobile());
        }
        liveRoom.setShopId(SecurityUtils.getShopUser().getShopId());
        liveRoom.setUpdateTime(new Date());
        liveRoomService.updateLiveRoom(liveRoom);
        return ServerResponseEntity.success();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "通过id删除直播间信息")
    @PreAuthorize("@pms.hasPermission('live:liveRoom:delete')")
    @Parameter(name = "id", description = "直播间id", required = true)
    public ServerResponseEntity<Boolean> removeById(@PathVariable Long id) {
        return ServerResponseEntity.success(liveRoomService.removeRoomById(id, SecurityUtils.getShopUser().getShopId()));
    }

    @GetMapping("/listLiveRoomProd")
    @Operation(summary = "获取直播间的直播商品列表")
    @PreAuthorize("@pms.hasPermission('live:liveRoom:listProd')")
    public ServerResponseEntity<List<LiveRoomProdVO>> listLiveRoomProd(@RequestParam Long roomId) {
        return ServerResponseEntity.success(liveRoomProdService.listLiveRoomProdInfo(roomId));
    }
}
