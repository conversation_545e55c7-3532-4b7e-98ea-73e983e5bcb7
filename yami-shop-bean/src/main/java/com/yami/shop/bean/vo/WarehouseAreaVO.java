package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 仓库项和供货城市关联信息VO
 *
 * <AUTHOR>
 * @date 2023-11-08 11:11:44
 */
@Data
public class WarehouseAreaVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "")
    private Long warehouseAreaId;

    @Schema(description = "仓库id")
    private Long warehouseId;

    @Schema(description = "城市id")
    private Long areaId;
}
