package com.yami.shop.bean.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 订单快递信息项VO
 *
 * <AUTHOR>
 * @date 2020-05-18 15:10:00
 */
@Data
public class DeliveryHundredItemInfoBO {

    @Schema(description = "接受站点" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String context;
    @Schema(description = "接受时间" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String ftime;
}
