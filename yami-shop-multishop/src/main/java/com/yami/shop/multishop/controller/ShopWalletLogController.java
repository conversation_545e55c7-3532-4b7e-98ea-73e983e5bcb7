package com.yami.shop.multishop.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.ShopWalletLog;
import com.yami.shop.bean.param.CustomerReqParam;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.ShopWalletLogService;
import com.yami.shop.service.StationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;


/**
 * 商家钱包记录
 *
 * <AUTHOR>
 * @date 2019-09-19 14:02:57
 */
@RestController
@AllArgsConstructor
@RequestMapping("/shop/shopWalletLog")
@Tag(name = "商家钱包记录接口")
public class ShopWalletLogController {

    private final ShopWalletLogService shopWalletLogService;

    private final StationService stationService;

    @GetMapping("/page")
    @Operation(summary = "分页查询商家收入明细" , description = "分页查询商家收入明细")
    @PreAuthorize("@pms.hasPermission('shop:shopWalletLog:page')")
    public ServerResponseEntity<IPage<ShopWalletLog>> getShopWalletLogPage(PageParam<ShopWalletLog> page, ShopWalletLog shopWalletLog){
        if (Objects.isNull(shopWalletLog.getShopId())){
            shopWalletLog.setShopId(SecurityUtils.getShopUser().getShopId());
        }
        IPage<ShopWalletLog> shopWalletLogPage = shopWalletLogService.pageAndStationNameByParam(page, shopWalletLog);
        return ServerResponseEntity.success(shopWalletLogPage);
    }

    @Operation(summary = "excel导出")
    @GetMapping("/export")
    public void export(HttpServletResponse response, CustomerReqParam customerReqParam) {
        customerReqParam.setShopId(SecurityUtils.getShopUser().getShopId());
        shopWalletLogService.excelShopWalletLog(customerReqParam, response);
    }
}
