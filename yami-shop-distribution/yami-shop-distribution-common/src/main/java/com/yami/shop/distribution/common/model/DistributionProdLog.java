package com.yami.shop.distribution.common.model;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "分销记录表")
public class DistributionProdLog {

    @Schema(description = "分销记录表Id" )
    private Long incomeId;

    @Schema(description = "佣金数额" )
    private Double incomeAmount;

    @Schema(description = "佣金状态(0:待支付、1:待结算、2:已结算、-1:订单失效)" )
    private Integer state;

    @Schema(description = "佣金类型 1直推 2间推" )
    private Integer incomeType;

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "商品主图" )
    private String pic;

    @Schema(description = "店铺Id" )
    private Long shopId;

    @Schema(description = "店铺名称" )
    private String shopName;

    @Schema(description = "分销员昵称" )
    private String nickName;

    @Schema(description = "分销员手机号" )
    private String userMobile;

    @Schema(description = "下单时间" )
    private Date placeTime;

    @Schema(description = "订单号" )
    private String orderNumber;

    @Schema(description = "失效原因：" )
    private Integer reson;

}
