package com.yami.shop.distribution.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AutoCheckEnum {
    /**
     * 人工判定
     */
    ARTIFICIAL(0, "人工判定"),
    /**
     * 系统判定
     */
    SYSTEM(1,"系统判定"),
    /**
     * 无需审核
     */
    NO_AUDIT(2,"无需审核");

    private final Integer value;
    private final String type;
}
