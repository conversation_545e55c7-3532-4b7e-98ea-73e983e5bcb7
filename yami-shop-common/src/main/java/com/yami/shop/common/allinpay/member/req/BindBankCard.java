package com.yami.shop.common.allinpay.member.req;

import java.io.Serial;
import java.io.Serializable;
/**
 * 确认绑定银行卡
 *
 * <AUTHOR>
 */
public class BindBankCard extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "bindBankCard";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    /**
     * 流水号
     */
    private String tranceNum;

    /**
     * 申请时间 （四要素时必传）
     */
    private String transDate;

    /**
     * 银行预留手机
     */
    private String phone;

    /**
     * 短信验证码
     */
    private String verificationCode;


    public BindBankCard() {
    }

    public BindBankCard(String bizUserId, String tranceNum, String transDate, String phone, String verificationCode) {
        this.bizUserId = bizUserId;
        this.transDate = transDate;
        this.tranceNum = tranceNum;
        this.phone = phone;
        this.verificationCode = verificationCode;
    }

    public String getTranceNum() {
        return tranceNum;
    }

    public void setTranceNum(String tranceNum) {
        this.tranceNum = tranceNum;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }

}
