package com.yami.shop.common.i18n;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Locale;
import java.util.Objects;

/**
 * 国际化 语言枚举类
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum LanguageEnum {

    /**
     * 简体中文
     */
    LANGUAGE_ZH_CN("zh_CN", 0),
    /**
     * 英文
     */
    LANGUAGE_EN("en", 1),
    /**
     * 法语
     */
    LANGUAGE_FR("fr", 2),
    /**
     * 德语
     */
    LANGUAGE_DE("de", 3),
    /**
     * 俄语
     */
    LANGUAGE_RU("ru", 4),
    /**
     * 日语
     */
    LANGUAGE_JA("ja", 5),
    /**
     * 希腊语
     */
    LANGUAGE_EL("el", 6),
    /**
     * 丹麦语
     */
    LANGUAGE_DA("da", 7),
    /**
     * 阿拉伯语
     */
    LANGUAGE_AR("ar", 8),
    /**
     * 西班牙语
     */
    LANGUAGE_ES("es", 9),
    ;

    private final String language;

    private final Integer lang;


    /**
     * 获取指定语言类型(如果没有对应的语言类型,则返回中文)
     *
     * @param language 语言类型
     * @return
     */
    public static String getLanguageType(String language){
        if (StrUtil.isEmpty(language)) {
            return LANGUAGE_ZH_CN.language;
        }
        for (LanguageEnum languageEnum : LanguageEnum.values()) {
            if (languageEnum.language.equalsIgnoreCase(language)) {
                return languageEnum.language;
            }
        }
        return LANGUAGE_ZH_CN.language;
    }

    public static LanguageEnum valueOf(int lang) {
        for (LanguageEnum languageEnum : values()) {
            if (languageEnum.lang == lang) {
                return languageEnum;
            }
        }
        return LanguageEnum.LANGUAGE_ZH_CN;
    }

    public static LanguageEnum valueOf(Locale locale) {
        for (LanguageEnum languageEnum : values()) {
            if (Objects.equals(languageEnum.language, locale.toString())) {
                return languageEnum;
            }
        }
        return LanguageEnum.LANGUAGE_ZH_CN;
    }
}
