package com.yami.shop.common.allinpay;

/**
 * <AUTHOR>
 */
public abstract class AbstractReq {

    /**
     * 商户系统用户标识，商户系统中唯一编号
     */
    protected String bizUserId;

    /**
     * 服务对象名
     *
     * @return 服务对象名
     */
    public abstract String getService();

    /**
     * 调用方法
     *
     * @return 调用方法
     */
    public abstract String getMethod();

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

}
