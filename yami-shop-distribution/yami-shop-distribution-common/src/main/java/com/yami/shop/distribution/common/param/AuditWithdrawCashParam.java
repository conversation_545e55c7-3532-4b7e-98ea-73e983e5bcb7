package com.yami.shop.distribution.common.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 分销提现审核参数
 * <AUTHOR>
 */
@Data
public class AuditWithdrawCashParam {

    /**
     * 提现记录id
     */
    @Schema(description = "提现记录id" )
    @NotNull(message = "提现记录Id不能为空")
    private Long withdrawCashId;

    @Schema(description = "状态" )
    @NotNull(message = "状态不能为空")
    private Integer state;
}
