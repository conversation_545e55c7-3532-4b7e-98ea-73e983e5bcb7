package com.yami.shop.distribution.common.po;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class DistributionProdPO  implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 店铺id
     */
    private  Long shopId;

    /**
     *
     * 商品id
     */
    private Long prodId;

    /**
     * 商品名字
     */
    private String prodName;

    /**
     * 商品类型(0普通商品 1拼团 2秒杀 3积分)
     */
    private Integer prodType;

    /**
     * 商品主图
     */
    private String pic;

    /**
     * 商品价格
     */
    private Double price;


    /**
     * 是否使用默认奖励(0 不使用 1使用)
     */
    private Integer defaultReward;

    /**
     * 奖励比例(0 按比例 1 按固定数值)
     */
    private Integer awardProportion;

    /**
     * 奖励数额设置(0 固定奖励,1 根据等级奖励)
     */
    private Integer awardNumberSet;

    /**
     * 奖励数额(Double)
     */
    private Double awardNumbers;

    /**
     * 上级奖励数额
     */
    private Double parentAwardNumbers;

    /**
     * 上级奖励设置(0 关闭 1开启)
     */
    private Integer parentAwardSet;
}
