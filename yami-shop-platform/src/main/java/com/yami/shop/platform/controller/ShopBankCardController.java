package com.yami.shop.platform.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yami.shop.allinpay.service.AllinpayCompanyService;
import com.yami.shop.bean.dto.allinpay.AllinpayShopBankCardDTO;
import com.yami.shop.bean.enums.AuditStatus;
import com.yami.shop.bean.model.ShopBankCard;
import com.yami.shop.bean.model.ShopCompany;
import com.yami.shop.bean.model.ShopDetail;
import com.yami.shop.bean.vo.ShopCompanyVO;
import com.yami.shop.common.allinpay.constant.CompanyInfoProcessStatus;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.service.ShopBankCardService;
import com.yami.shop.service.ShopCompanyService;
import com.yami.shop.service.ShopDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/8/11 15:04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/platform/shopBankCard")
@Tag(name = "银行卡相关接口")
public class ShopBankCardController {

    private final ShopBankCardService shopBankCardService;
    private final ShopCompanyService shopCompanyService;
    private final ShopDetailService shopDetailService;
    private final AllinpayCompanyService allinpayCompanyService;

    @GetMapping("/listByShopId")
    @Operation(summary = "根据店铺id批量获取银行卡信息")
    @Parameter(name = "shopId", description = "店铺id")
    @PreAuthorize("@pms.hasPermission('platform:shopBankCard:list')")
    public ServerResponseEntity<List<ShopBankCard>> getShopBankCardList(@RequestParam(value = "shopId") Long shopId) {
        List<ShopBankCard> list = shopBankCardService.list(new LambdaQueryWrapper<ShopBankCard>().eq(ShopBankCard::getShopId, shopId).eq(ShopBankCard::getStatus, 1));
        return ServerResponseEntity.success(list);
    }

    @PostMapping("/allinpaySaveAndApplyShop")
    @Operation(summary = "通联支付--批量保存店铺银行卡信息并提交店铺审核信息", description = "批量保存店铺银行卡信息并提交店铺审核信息")
    @PreAuthorize("@pms.hasPermission('platform:shopBankCard:save')")
    public ServerResponseEntity<Void> allinpaySaveAndApplyShop(@Valid @RequestBody AllinpayShopBankCardDTO allinpayShopBankCardDTO) {
        Long shopId = allinpayShopBankCardDTO.getShopId();
        if (Objects.isNull(shopId)) {
            // 店铺id不能为空
            throw new YamiShopBindException("yami.shop.bank.card.exception.idNotNull");
        }
        // 查找工商信息
        ShopCompanyVO shopCompany = shopCompanyService.getShopCompanyByShopIdAndStatus(shopId, AuditStatus.WAITAUDIT.value());
        if (Objects.isNull(shopCompany)) {
            // 该店铺工商信息为空，请刷新页面重新填写
            throw new YamiShopBindException("yami.shop.bank.card.exception.companyEmpty");
        }
        checkAllinpay(shopCompany);
        ShopDetail shopDetail = shopDetailService.getShopDetailByShopId(shopId);
        if (Objects.equals(shopDetail.getCompanyInfoProcessStatus(), CompanyInfoProcessStatus.SUCCESS.value())) {
            // 已经审核成功就不必要多次设置企业信息了
            throw new YamiShopBindException("yami.shop.bank.card.exception.passed");
        }

        shopBankCardService.insertAndSetCompanyInfo(allinpayShopBankCardDTO, shopId, BeanUtil.map(shopCompany, ShopCompany.class));
        shopDetailService.removeShopDetailCacheByShopId(shopId);
        return ServerResponseEntity.success();
    }

    private void checkAllinpay(ShopCompanyVO shopCompany) {
        boolean needInfo = Objects.isNull(shopCompany.getLegalIds()) || Objects.isNull(shopCompany.getLegalPhone());
        if (allinpayCompanyService.getIsAllinpay() && needInfo) {
            // 工商信息需要提交的内容有所变更，请重新填写
            throw new YamiShopBindException("yami.shop.bank.card.exception.infoChange");
        }
    }

}
