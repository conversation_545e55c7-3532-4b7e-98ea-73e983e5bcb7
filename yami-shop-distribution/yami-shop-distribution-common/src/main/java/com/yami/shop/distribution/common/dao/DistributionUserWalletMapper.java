package com.yami.shop.distribution.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.distribution.common.dto.DistributionUserWalletDto;
import com.yami.shop.distribution.common.model.DistributionUserWallet;
import com.yami.shop.distribution.common.param.DistributionParam;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 */
public interface DistributionUserWalletMapper extends BaseMapper<DistributionUserWallet> {

    /**
     * 根据分销员id获取钱包id
     *
     * @param distributionUserId 分销员id
     * @return 钱包id
     */
    Long getWalletIdByDistributionUserId(@Param("distributionUserId") Long distributionUserId);

    /**
     * 返回钱包对象和用户vo分页
     *
     * @param page       分页参数
     * @param distributionParam 分销查询参数
     * @return 钱包对象和用户vo
     */
    Page<DistributionUserWallet> getDistributionUserWalletAndDistributionUserVoPage(Page page,
                                                                                    @Param("distributionParam") DistributionParam distributionParam);

    /**
     * 获取分销员钱包信息
     *
     * @param distributionUserId 分销员id
     * @return 分销员钱包信息
     */
    DistributionUserWalletDto getDistributionUserWalletDtoByDistributionUserId(@Param("distributionUserId") Long distributionUserId);

    /**
     * 永久封禁时根据分销员id更改金额为0
     *
     * @param distributionUserId 分销员id
     */
    void updateAmountByDistributionUserId(@Param("distributionUserId") Long distributionUserId);

    /**
     * 更新可提现金额
     *
     * @param distributionUserId
     * @param settledAmount
     */
    void updateSettledAmount(@Param("distributionUserId") Long distributionUserId, @Param("settledAmount") Double settledAmount);

    /**
     * 更新金额
     * @param amount
     * @param distributionUserId
     * @return
     */
    int updateAmount(@Param("amount")Double amount, @Param("distributionUserId")Long distributionUserId);

}
