package com.yami.shop.common.allinpay.order.req;

/**
 * 确认支付（前台+密码验证)
 *
 * <AUTHOR>
 */
public class PayByPwd extends AbstractOrderReq {
    /**
     * 跳转页面类型,非必填
     */
    private Long jumpPageType;
    /**
     * 订单申请的商户订单号（支付订单）
     */
    private String bizOrderNo;
    /**
     * 确认支付失败后，跳转商户页面
     * 若上送则失败页面显示“关闭”按钮可跳转至商户失败跳转地址，云商通不响应内容
     * 若正常域名或无特殊字符，以http/https开头明文请求，无需加密
     * 若含IP地址形式或“#”号，需要加密后再请求
     * RSA加密
     */
    private String errorJumpUrl;
    /**
     * 确认支付之后，跳转返回的页面地址
     * 若正常域名或无特殊字符，以http/https开头明文请求，无需加密
     * 若含IP地址形式或“#”号，需要加密后再请求
     * RSA加密
     */
    private String jumpUrl;

    private String consumerIp;

    private static final String METHOD_NAME = "payByPwd";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public Long getJumpPageType() {
        return jumpPageType;
    }

    public void setJumpPageType(Long jumpPageType) {
        this.jumpPageType = jumpPageType;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getErrorJumpUrl() {
        return errorJumpUrl;
    }

    public void setErrorJumpUrl(String errorJumpUrl) {
        this.errorJumpUrl = errorJumpUrl;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getConsumerIp() {
        return consumerIp;
    }

    public void setConsumerIp(String consumerIp) {
        this.consumerIp = consumerIp;
    }

    @Override
    public String toString() {
        return "PayByPwd{" +
                "jumpPageType=" + jumpPageType +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                ", errorJumpUrl='" + errorJumpUrl + '\'' +
                ", jumpUrl='" + jumpUrl + '\'' +
                ", consumerIp='" + consumerIp + '\'' +
                '}';
    }
}
