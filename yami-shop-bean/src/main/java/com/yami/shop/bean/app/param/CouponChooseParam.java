package com.yami.shop.bean.app.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class CouponChooseParam implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "店铺id不能为空")
    private Long shopId;

    @Schema(description = "用户选择的自提点id - 自提&同城参数")
    private Long stationId;

    @Schema(description = "选择的优惠券用户id")
    private Long couponUserId;
}
