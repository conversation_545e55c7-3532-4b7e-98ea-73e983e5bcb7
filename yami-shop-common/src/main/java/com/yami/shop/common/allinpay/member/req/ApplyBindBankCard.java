package com.yami.shop.common.allinpay.member.req;



import com.yami.shop.common.allinpay.member.constant.IdentityType;

import java.io.Serial;
import java.io.Serializable;

/**
 * 请求绑定银行卡
 *
 * <AUTHOR>
 */
public class ApplyBindBankCard extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    public static final String METHOD_NAME = "applyBindBankCard";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    /**
     * 银行卡号
     */
    protected String cardNo;


    /**
     * 姓名
     */
    protected String name;

    /**
     * 证件类型
     */
    protected Integer identityType = IdentityType.ID_CARD.value();

    /**
     * 证件号码 AES加密。
     */
    protected String identityNo;

    /**
     * 支付行号
     */
    protected String unionBank;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 绑卡方式 （不传则默认值7）
     */
    private Integer cardCheck;

    /**
     * 有效期 信用卡必填，格式为月年
     */
    private String validate;

    /**
     * 信用卡必填。
     */
    private String cvv2;


    public ApplyBindBankCard() {
    }

    public ApplyBindBankCard(String bizUserId, String cardNo, String phone, String name,
                             String identityNo) {
        this.bizUserId = bizUserId;
        this.cardNo = cardNo;
        this.phone = phone;
        this.name = name;
        this.identityNo = identityNo;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCardCheck() {
        return cardCheck;
    }

    public void setCardCheck(Integer cardCheck) {
        this.cardCheck = cardCheck;
    }

    public Integer getIdentityType() {
        return identityType;
    }

    public void setIdentityType(Integer identityType) {
        this.identityType = identityType;
    }

    public String getIdentityNo() {
        return identityNo;
    }

    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo;
    }

    public String getValidate() {
        return validate;
    }

    public void setValidate(String validate) {
        this.validate = validate;
    }

    public String getCvv2() {
        return cvv2;
    }

    public void setCvv2(String cvv2) {
        this.cvv2 = cvv2;
    }

    public String getUnionBank() {
        return unionBank;
    }

    public void setUnionBank(String unionBank) {
        this.unionBank = unionBank;
    }

}
