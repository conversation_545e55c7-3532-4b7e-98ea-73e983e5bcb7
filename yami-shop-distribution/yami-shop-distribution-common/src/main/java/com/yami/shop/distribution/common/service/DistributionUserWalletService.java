package com.yami.shop.distribution.common.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.distribution.common.dto.DistributionUserWalletDto;
import com.yami.shop.distribution.common.model.DistributionUserWallet;
import com.yami.shop.distribution.common.param.DistributionParam;
import org.apache.ibatis.annotations.Param;

/**
 *
 * <AUTHOR> on 2019/04/01.
 */
public interface DistributionUserWalletService extends IService<DistributionUserWallet> {


    /**
     * 根据分销员id获取钱包id
     * @param distributionUserId 分销员id
     * @return 钱包id
     */
    Long getWalletIdByDistributionUserId(@Param("distributionUserId")Long distributionUserId);

    /**
     *  返回钱包对象和用户vo分页
     * @param page 分页参数
     * @param distributionParam 分销查询参数
     * @return 钱包对象和用户vo
     */
    Page<DistributionUserWallet> getDistributionUserWalletAndDistributionUserVoPage(Page page,
                                                                                    DistributionParam distributionParam);

    /**
     * 人工修改钱包 并且添加一条钱包流水记录
     * @param distributionUserWallet 钱包信息
     * @param modifier 操作人id
     */
    void updateDistributionUserWallet(DistributionUserWallet distributionUserWallet,Long modifier);


    /**
     * 获取分销员钱包信息
     * @param distributionUserId 分销员id
     * @return 分销员钱包信息
     */
    DistributionUserWalletDto getDistributionUserWalletDtoByDistributionUserId(Long distributionUserId);

}
