package com.yami.shop.platform.controller;


import com.yami.shop.bean.model.OrderRefundIntervention;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.service.OrderRefundInterventionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 订单退款介入记录
 *
 * <AUTHOR>
 * @date 2023-10-26 15:52:39
 */
@RestController
@RequestMapping("/platform/orderRefundIntervention")
@Tag(name = "订单退款介入记录")
@AllArgsConstructor
public class OrderRefundInterventionController {

    private final OrderRefundInterventionService orderRefundInterventionService;

    @PostMapping("/saveInterventionVoucher")
    @PreAuthorize("@pms.hasPermission('seckill:orderRefundIntervention:save')")
    @Operation(summary = "添加介入凭证", description = "添加介入凭证")
    public ServerResponseEntity<Void> saveInterventionVoucher(@RequestBody @Valid OrderRefundIntervention orderRefundIntervention) {
        orderRefundIntervention.setSysType(AuthUserContext.getSysType());
        orderRefundIntervention.setBizId(AuthUserContext.getShopId());
        orderRefundInterventionService.saveInterventionVoucher(orderRefundIntervention);
        return ServerResponseEntity.success();
    }
}
