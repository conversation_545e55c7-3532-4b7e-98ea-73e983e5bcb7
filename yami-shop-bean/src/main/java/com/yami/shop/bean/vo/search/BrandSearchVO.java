package com.yami.shop.bean.vo.search;

import com.yami.shop.bean.bo.BrandLangBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BrandSearchVO {

    @Schema(description = "品牌名称" )
    private String brandName;

    @Schema(description = "品牌id" )
    private Long brandId;

    @Schema(description = "品牌图片" )
    private String brandImg;

    /**
     * 品牌名称列表
     */
    private List<BrandLangBO> brandLangList;
}
