package com.yami.shop.distribution.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.distribution.common.dto.AchievementDataDto;
import com.yami.shop.distribution.common.dto.DistributionUserAchievementDataDto;
import com.yami.shop.distribution.common.dto.DistributionUserSimpleDto;
import com.yami.shop.distribution.common.model.DistributionUser;
import com.yami.shop.distribution.common.param.RangeTimeParam;
import com.yami.shop.distribution.common.vo.DistributionSettleInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface DistributionUserMapper extends BaseMapper<DistributionUser> {

//    String getParentIdsByDistributionUserId(@Param("distributionUserId") Long distributionUserId);

    /**
     * 分销员业绩数据
     * @param id 分销员id
     * @return 分销员业绩数据
     */
    AchievementDataDto getAchievementDataDtoById(Long id);

    /**
     * 查询分销员列表
     * @param page 分页参数
     * @param distributionUser 查询参数
     * @param rangeTimeParam 分销员上级绑定时间
     * @param mobile 手机号
     * @param parentMobile 上级手机号
     * @param sortChange 排序规则
     * @param state 状态
     * @return 分销员列表
     */
    IPage<DistributionUser> distributionUserPage(Page page, @Param("distributionUser") DistributionUser distributionUser
            , @Param("rangeTimeParam") RangeTimeParam rangeTimeParam, @Param("mobile") String mobile
            , @Param("parentMobile") String parentMobile, @Param("sortChange") Integer sortChange, @Param("state")Integer state);

    /**
     * 查询分销员列表
     * @param page 分页参数
     * @param distributionUser 查询参数
     * @param userMobile 手机号
     * @return 分销员列表
     */
    IPage<DistributionUser> getDistributionUserAchievementPage(Page page,
                                                               @Param("distributionUser") DistributionUser distributionUser,
                                                               @Param("userMobile") String userMobile,
                                                               @Param("paySysType") Integer paySysType);

    /**
     * 获取分销员与等级条件匹配的数据
     * @param distributionUserId 分销员id
     * @return 分销员与等级条件匹配的数据
     */
    DistributionUserAchievementDataDto getLevelDistributionUserAchievementDataByDistributionUserId(@Param("distributionUserId") Long distributionUserId);

//    Long getParentDistributionUserIdByDistributionUserId(@Param("distributionUserId") Long distributionUserId);

//    String getUserIdByDistributionUserId(@Param("distributionUserId") Long distributionUserId);

    /**
     * 分页获取精简版分销员数据
     * @param page 分页参数
     * @param parentDistributionUserId 上级分销员id
     * @return 精简版分销员分页数据
     */
    IPage<DistributionUserSimpleDto> getDistributionUserSimpleDtoByParentUserIdPage(Page page, @Param("parentDistributionUserId") Long parentDistributionUserId);

    /**
     * 获取分销员列表
     * @param identityCardNumber 分销员身份证信息
     * @param userMobile 手机号
     * @return 分销员列表
     */
    List<DistributionUser> getDistributionUserByIdCardNumberAndUserMobile(@Param("identityCardNumber") String identityCardNumber, @Param("userMobile") String userMobile);

    /**
     * 修改分销员状态
     * @param distributionUser 修改信息
     */
    void updateStatusById(@Param("distributionUser") DistributionUser distributionUser);

    /**
     * 根据分销员id清除他的下级关系
     * @param distributionUserId 被永久封禁的分销员id
     */
    void updateParentIdById(@Param("distributionUserId")Long distributionUserId);

//     int getLevelIdByDistributionUserId(Long distributionUserId);

    /**
     * 根据分销员推广号获取有效的分销订单orderNumber
     * @param cardNo
     * @return
     */
    List<String> selectDistributionOrderId(String cardNo);

    /**
     * 获取分销结算信息
     * @param userId 用户id
     * @param paySysType 支付系统类型
     * @return 分销结算信息
     */
    DistributionSettleInfoVO getSettleInfo(@Param("userId") String userId,
                                           @Param("paySysType") Integer paySysType);

    /**
     * 根据分销员卡号获取分销员信息
     * @param cardNo 卡号
     * @return 分销员信息
     */
    DistributionUser getByCardNo(@Param("cardNo") String cardNo);
}
