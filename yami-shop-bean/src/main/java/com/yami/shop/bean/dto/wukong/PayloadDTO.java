package com.yami.shop.bean.dto.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
public class PayloadDTO {

    @Schema(description = "消息类型 1为文字类型 99是cmd消息")
    private Integer type;

    @Schema(description = "指令 unreadClear:红点消除 memberUpdate:群成员信息有更新 onlineUpdate:商家/平台上下线")
    private String cmd;

    @Schema(description = "cmd指令内容")
    private String param;

    @Schema(description = "消息内容")
    private String content;

    public PayloadDTO(Integer type, String content) {
        this.type = type;
        this.content = content;
    }

    public PayloadDTO(Integer type, String cmd, String param) {
        this.type = type;
        this.cmd = cmd;
        this.param = param;
    }

    public PayloadDTO() {
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    @Override
    public String toString() {
        return "PayloadDTO{" +
                "type=" + type +
                ", cmd='" + cmd + '\'' +
                ", param='" + param + '\'' +
                ", content='" + content + '\'' +
                '}';
    }
}
