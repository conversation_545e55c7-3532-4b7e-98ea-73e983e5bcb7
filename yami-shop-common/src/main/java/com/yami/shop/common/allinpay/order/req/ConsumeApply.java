package com.yami.shop.common.allinpay.order.req;

/**
 * 消费申请
 *
 * <AUTHOR>
 * @date 2023-04-04
 */
public class ConsumeApply extends AbstractOrderReq {

    public static final String METHOD_NAME = "consumeApply";

    /**
     * 付款方（商户系统userId）
     */
    private String payerId;

    /**
     * 收款方（商户系统userId）（平台参数为:#yunBizUserId_B2C#）
     */
    private String recieverId;

    /**
     * 商户订单编号
     */
    private String bizOrderNo;

    /**
     * 订单金额
     */
    private Long amount;

    /**
     * 手续费
     */
    private Long fee;

    /**
     * 交易验证方式
     */
    private Long validateType;

    /**
     * 分账规则
     */
    private String splitRule;

    /**
     * 前台通知地址
     */
    private String frontUrl;

    /**
     * 后台通知地址
     */
    private String backUrl;

    /**
     * 订单过期时间
     */
    private String orderExpireDatetime;

    /**
     * 支付方式
     */
    private String payMethod;

    /**
     * 商户商品编码
     */
    private String bizGoodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 行业代码
     */
    private String industryCode;

    /**
     * 行业名称
     */
    private String industryName;

    /**
     * 访问终端类型
     */
    private Long source;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 拓展参数
     */
    private String extendInfo;

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public String getRecieverId() {
        return recieverId;
    }

    public void setRecieverId(String recieverId) {
        this.recieverId = recieverId;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Long getFee() {
        return fee;
    }

    public void setFee(Long fee) {
        this.fee = fee;
    }

    public Long getValidateType() {
        return validateType;
    }

    public void setValidateType(Long validateType) {
        this.validateType = validateType;
    }

    public String getSplitRule() {
        return splitRule;
    }

    public void setSplitRule(String splitRule) {
        this.splitRule = splitRule;
    }

    public String getFrontUrl() {
        return frontUrl;
    }

    public void setFrontUrl(String frontUrl) {
        this.frontUrl = frontUrl;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public String getOrderExpireDatetime() {
        return orderExpireDatetime;
    }

    public void setOrderExpireDatetime(String orderExpireDatetime) {
        this.orderExpireDatetime = orderExpireDatetime;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getBizGoodsNo() {
        return bizGoodsNo;
    }

    public void setBizGoodsNo(String bizGoodsNo) {
        this.bizGoodsNo = bizGoodsNo;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getIndustryCode() {
        return industryCode;
    }

    public void setIndustryCode(String industryCode) {
        this.industryCode = industryCode;
    }

    public String getIndustryName() {
        return industryName;
    }

    public void setIndustryName(String industryName) {
        this.industryName = industryName;
    }

    public Long getSource() {
        return source;
    }

    public void setSource(Long source) {
        this.source = source;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }

    @Override
    public String toString() {
        return "ConsumeApply{" +
                "payerId='" + payerId + '\'' +
                ", recieverId='" + recieverId + '\'' +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                ", amount=" + amount +
                ", fee=" + fee +
                ", validateType=" + validateType +
                ", splitRule='" + splitRule + '\'' +
                ", frontUrl='" + frontUrl + '\'' +
                ", backUrl='" + backUrl + '\'' +
                ", orderExpireDatetime='" + orderExpireDatetime + '\'' +
                ", payMethod='" + payMethod + '\'' +
                ", bizGoodsNo='" + bizGoodsNo + '\'' +
                ", goodsName='" + goodsName + '\'' +
                ", industryCode='" + industryCode + '\'' +
                ", industryName='" + industryName + '\'' +
                ", source='" + source + '\'' +
                ", summary='" + summary + '\'' +
                ", extendInfo='" + extendInfo + '\'' +
                '}';
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }
}
