package com.yami.shop.security.multishop.util;

import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.security.multishop.model.YamiShopUser;
import lombok.experimental.UtilityClass;

/**
 *
 * <AUTHOR>
 */
@UtilityClass
public class SecurityUtils {


    /**
     * 获取用户
     */
    public YamiShopUser getShopUser() {

        YamiShopUser details = new YamiShopUser();
        details.setUserId(AuthUserContext.getUserId());
        details.setShopId(AuthUserContext.getShopId());
        details.setEmployeeId(AuthUserContext.getEmployeeId());
        return details;
    }
}
