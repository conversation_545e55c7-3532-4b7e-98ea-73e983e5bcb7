package com.yami.shop.common.allinpay.order.req;

import java.util.List;

/**
 * 退款申请(allinpay.yunst.orderService.refund)
 *
 * <AUTHOR>
 */

public class Refund extends AbstractOrderReq {
    /**
     * 商户订单号（支付订单）
     * 全局唯一，不可重复，商户侧务必保障此订单号全局唯一，不可重复，如订单号重复，则影响订单代付及退款。
     * 不可包含“|”字符
     */
    private String bizOrderNo;
    /**
     * 商户原订单号
     */
    private String oriBizOrderNo;
    /**
     * 原云商通订单号,上送，则按照此字段进行退款
     * 未上送，则按照商户订单号进行退款
     */
    private String oriOrderNo;

    /**
     * 退款方式,默认D1
     * D1：D+1日14:30——18:00分批向渠道发起退款，退款到账时间以实际到账为准
     * D0：D+0实时向渠道发起退款
     * 说明：此参数仅对支持退款金额原路返回的支付订单有效
     */
    private String refundType;
    /**
     * 商户系统用户标识，商户系统中唯一编号。
     * 付款人
     */
    private List<RefundAccountInfo> refundList;
    /**
     * 后台通知地址
     */
    private String backUrl;
    /**
     * 本次退款总金额
     */
    private Long amount;

    private static final String METHOD_NAME = "refund";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getOriBizOrderNo() {
        return oriBizOrderNo;
    }

    public void setOriBizOrderNo(String oriBizOrderNo) {
        this.oriBizOrderNo = oriBizOrderNo;
    }

    public String getOriOrderNo() {
        return oriOrderNo;
    }

    public void setOriOrderNo(String oriOrderNo) {
        this.oriOrderNo = oriOrderNo;
    }

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public List<RefundAccountInfo> getRefundList() {
        return refundList;
    }

    public void setRefundList(List<RefundAccountInfo> refundList) {
        this.refundList = refundList;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    @Override
    public String toString() {
        return "Refund{" +
                "bizOrderNo='" + bizOrderNo + '\'' +
                ", oriBizOrderNo='" + oriBizOrderNo + '\'' +
                ", oriOrderNo='" + oriOrderNo + '\'' +
                ", refundType='" + refundType + '\'' +
                ", refundList=" + refundList +
                ", backUrl='" + backUrl + '\'' +
                ", amount='" + amount + '\'' +
                '}';
    }

    public static class RefundAccountInfo {
        /**
         * 非必填，账户集编号；不送：默认从平台中间账户集退款（标准版代收付需原扣减原recieverList金额）
         * ；上送:云商通分配的托管专用账户集的编号，则从bizUserId用户退款。
         */
        private String accountSetNo;
        /**
         * 商户系统用户标识，商户系统中唯一编号。
         */
        private String bizUserId;
        /**
         * 金额，单位：分
         */
        private Long amount;

        public String getAccountSetNo() {
            return accountSetNo;
        }

        public void setAccountSetNo(String accountSetNo) {
            this.accountSetNo = accountSetNo;
        }

        public String getBizUserId() {
            return bizUserId;
        }

        public void setBizUserId(String bizUserId) {
            this.bizUserId = bizUserId;
        }

        public Long getAmount() {
            return amount;
        }

        public void setAmount(Long amount) {
            this.amount = amount;
        }

        @Override
        public String toString() {
            return "RefundItem{" +
                    "accountSetNo='" + accountSetNo + '\'' +
                    ", bizUserId='" + bizUserId + '\'' +
                    ", amount=" + amount +
                    '}';
        }
    }


}
