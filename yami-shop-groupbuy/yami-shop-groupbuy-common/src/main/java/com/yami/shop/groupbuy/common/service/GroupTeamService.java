package com.yami.shop.groupbuy.common.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupTeamDto;
import com.yami.shop.groupbuy.common.api.dto.ApiJoinGroupTeamDto;
import com.yami.shop.groupbuy.common.dto.GroupOrderDTO;
import com.yami.shop.groupbuy.common.dto.GroupTeamDto;
import com.yami.shop.groupbuy.common.model.GroupOrder;
import com.yami.shop.groupbuy.common.model.GroupTeam;

import java.util.List;

/**
 * 拼团团队表
 *
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
public interface GroupTeamService extends IService<GroupTeam> {

    /**
     * 获取拼团团队分页列表
     *
     * @param page 分页
     * @param groupTeamDto 拼团团队
     * @return 拼团团队列表
     */
    IPage<GroupTeamDto> getPage(Page page, GroupTeamDto groupTeamDto);

    /**
     * 根据团队id分页获取拼团订单信息
     * @param page
     * @param groupOrder
     * @return
     */
    IPage<GroupOrderDTO> getGroupOrderPage(PageParam<GroupOrder> page, GroupOrder groupOrder);

    /**
     * 获取拼团单（拼团团队）
     *
     * @param groupTeamId 团单ID
     * @return ApiGroupTeamDto对象
     */
    ApiGroupTeamDto getApiGroupTeamDto(Long groupTeamId);

    /**
     * 获取参加的拼团团队信息
     *
     * @param groupActivityId 活动ID
     * @return 拼团团队
     */
    ApiJoinGroupTeamDto getJoinGroupTeamHasMaxNum(Long groupActivityId);

    /**
     * 获取活动结束或团单结束未成团的拼团团队
     *
     * @return ApiGroupTeamDto对象
     */
    List<GroupTeam> getNotGroupTeam();

    /**
     * 处理活动结束或团单结束未成团的拼团团队
     *
     * @param groupTeam 未成团的拼团团队
     */
    void updateNotGroupTeam(GroupTeam groupTeam);

    /**
     * 通过拼团活动id获取未成团的团队列表
     *
     * @param groupActivityId 团购活动id
     * @return 拼团团队列表
     */
    List<GroupTeam> getNotGroupTeamsByActivityId(Long groupActivityId);

    /**
     * 通过商品id获取未成团的团队列表
     *
     * @param prodId 商品id
     * @return 拼团团队列表
     */
    List<GroupTeam> getNotGroupTeamByProdId(Long prodId);

    /**
     * 可加入的团列表
     * @param groupActivityId
     * @param showSize
     * @return
     */
    List<ApiGroupTeamDto> listJoinGroup(Long groupActivityId, Integer showSize);

}
