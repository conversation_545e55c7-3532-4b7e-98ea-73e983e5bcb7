package com.yami.shop.distribution.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yami.shop.bean.model.Product;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName( "tz_distribution_prod")
public class DistributionProd {

    @TableId
    @Schema(description = "分销商品表id" )
    private Long distributionProdId;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "商品id" )
    private Long prodId;

    @Schema(description = "奖励id" )
    private Long awardId;

    @Schema(description = "状态(0:商家下架 1:商家上架 2:违规下架 3:平台审核)" )
    private Integer state;

    @Schema(description = "修改时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "操作人id" )
    private Long modifier;

    @Schema(description = "是否使用默认奖励(0 不使用 1使用)" )
    private Integer defaultReward;

    @Schema(description = "关联商品" )
    @TableField(exist = false)
    private Product product;

    @Schema(description = "奖励比例(0 按比例 1 按固定数值)" )
    private Integer awardProportion;

    @Schema(description = "奖励数额设置(0 固定奖励,1 根据等级奖励)" )
    private Integer awardNumberSet;

    @Schema(description = "奖励数额(json)" )
    private String awardNumbers;

    @Schema(description = "上级奖励数额(json)" )
    private String parentAwardNumbers;

    @Schema(description = "上级奖励设置(0 关闭 1开启)" )
    private Integer parentAwardSet;
}
