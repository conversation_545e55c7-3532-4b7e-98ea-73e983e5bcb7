package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 小程序发货订单支付单号以及状态
 * <AUTHOR>
 */
@Data
public class OrderWxDeliveryVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "微信小程序订单状态  (1) 待发货；(2) 已发货；(3) 确认收货；(4) 交易完成；(5) 已退款" )
    private Integer orderStatus;

    @Schema(description = "微信支付单号" )
    private String bizPayNo;

}
