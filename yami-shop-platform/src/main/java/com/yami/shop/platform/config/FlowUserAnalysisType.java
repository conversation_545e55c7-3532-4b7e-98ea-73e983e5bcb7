package com.yami.shop.platform.config;

/**
 * <AUTHOR>
 */
public enum FlowUserAnalysisType {
    /** 团购商品类型 */
    WEEK(1),

    /** 近30天 */
    MONTH(2),

    /** 自定义 */
    CUSTOM(3);

    private final Integer num;

    public Integer value() {
        return num;
    }

    FlowUserAnalysisType(Integer num){
        this.num = num;
    }

    public static FlowUserAnalysisType instance(Integer value) {
        FlowUserAnalysisType[] enums = values();
        for (FlowUserAnalysisType statusEnum : enums) {
            if (statusEnum.value().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }
}
