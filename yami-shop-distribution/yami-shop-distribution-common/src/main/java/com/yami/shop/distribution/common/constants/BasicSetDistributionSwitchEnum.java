package com.yami.shop.distribution.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 基本设置分销开关枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BasicSetDistributionSwitchEnum {
    /**
     * 关闭
     */
    DISABLE(0, "关闭"),
    /**
     * 启动
     */
    ENABLE(1, "启动"),
    /**
     * 违规下线
     */
    OFFLINE(2, "平台禁用"),
    /**
     * 等待审核
     */
    WAIT_AUDIT(3, "等待审核");

    private final int value;
    private final String desc;
}
