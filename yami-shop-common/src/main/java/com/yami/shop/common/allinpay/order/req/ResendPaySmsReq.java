package com.yami.shop.common.allinpay.order.req;

/**
 * 重发支付短信验证码 请求参数
 *
 * <AUTHOR>
 */
public class ResendPaySmsReq extends AbstractOrderReq {
    public static final String METHOD_NAME = "resendPaySMS";

    /**
     * 商户订单号
     */
    private String bizOrderNo;

    public ResendPaySmsReq() {

    }

    public ResendPaySmsReq(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    @Override
    public String toString() {
        return "ResendPaySmsReq{" +
                "bizOrderNo='" + bizOrderNo + '\'' +
                '}';
    }
}
