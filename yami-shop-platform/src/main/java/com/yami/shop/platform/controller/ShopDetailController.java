package com.yami.shop.platform.controller;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.yami.shop.bean.app.dto.ShopHeadInfoDto;
import com.yami.shop.bean.app.param.SendSmsParam;
import com.yami.shop.bean.dto.ShopCreateInfoDTO;
import com.yami.shop.bean.dto.ShopSigningInfoDTO;
import com.yami.shop.bean.enums.*;
import com.yami.shop.bean.event.EsProductUpdateEvent;
import com.yami.shop.bean.model.OfflineHandleEvent;
import com.yami.shop.bean.model.ShopDetail;
import com.yami.shop.bean.param.AuditingInfoParam;
import com.yami.shop.bean.param.OfflineHandleEventAuditParam;
import com.yami.shop.bean.param.ShopSearchParam;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.enums.StatusEnum;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.common.util.PrincipalUtil;
import com.yami.shop.search.common.service.EsStationService;
import com.yami.shop.security.platform.util.SecurityUtils;
import com.yami.shop.service.OfflineHandleEventService;
import com.yami.shop.service.ShopDetailService;
import com.yami.shop.service.SmsLogService;
import com.yami.shop.sys.common.model.ShopEmployee;
import com.yami.shop.sys.common.service.ShopEmployeeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;


/**
 * 商家详细信息
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/platform/shopDetail")
@Tag(name = "店铺基本信息相关接口")
public class ShopDetailController {

    @Value("${yami.expose.operation.auth:}")
    private Boolean permission;

    private final ShopDetailService shopDetailService;
    private final OfflineHandleEventService offlineHandleEventService;
    private final ApplicationEventPublisher eventPublisher;
    private final SmsLogService smsLogService;
    private final ShopEmployeeService shopEmployeeService;
    private final EsStationService esStationService;


    @GetMapping("/info")
    @Operation(summary = "根据店铺id获取店铺基本信息", description = "根据店铺id获取店铺基本信息")
    @Parameter(name = "shopId", description = "店铺id")
    @PreAuthorize("@pms.hasPermission('platform:shopDetail:info')")
    public ServerResponseEntity<ShopDetail> getInfo(@RequestParam Long shopId) {
        ShopDetail shopDetail = shopDetailService.getShopDetailByShopId(shopId);
        ShopEmployee shopEmployee = shopEmployeeService.getOne(Wrappers.lambdaQuery(ShopEmployee.class).eq(ShopEmployee::getShopId, shopId).eq(ShopEmployee::getType, PositionType.ADMIN.value()));
        if (Objects.nonNull(shopEmployee)) {
            shopDetail.setMerchantAccount(shopEmployee.getUsername());
            shopDetail.setAccountStatus(shopEmployee.getStatus());
            if (StrUtil.isNotBlank(shopDetail.getTel())) {
                shopDetail.setTel(PhoneUtil.hideBetween(shopDetail.getTel()).toString());
            }
            if (PrincipalUtil.isMobile(shopDetail.getMerchantAccount())) {
                shopDetail.setMerchantAccount(PhoneUtil.hideBetween(shopDetail.getMerchantAccount()).toString());
            }
        }
        return ServerResponseEntity.success(shopDetail);
    }

    @GetMapping("/getMerchantInfo")
    @Operation(summary = "根据店铺id获取店铺商家账号信息", description = "根据店铺id获取店铺商家账号信息")
    @Parameter(name = "shopId", description = "店铺id")
    @PreAuthorize("@pms.hasPermission('platform:shopDetail:merchantInfo')")
    public ServerResponseEntity<ShopEmployee> getMerchantInfo(@RequestParam Long shopId) {
        ShopEmployee shopEmployee = shopEmployeeService.getMerchantInfoByShopId(shopId);
        if (PrincipalUtil.isMobile(shopEmployee.getMobile())) {
            shopEmployee.setMobile(PhoneUtil.hideBetween(shopEmployee.getMobile()).toString());
        }
        return ServerResponseEntity.success(shopEmployee);
    }

    @PutMapping("/updateMerchantInfo")
    @Operation(summary = "更新店铺商家账号信息", description = "更新店铺商家账号信息")
    @PreAuthorize("@pms.hasPermission('platform:shopDetail:merchantUpdate')")
    public ServerResponseEntity<Void> updateMerchantInfo(@RequestBody ShopEmployee shopEmployee) {
        if (Objects.equals(shopEmployee.getShopId(), Constant.MAIN_SHOP) && BooleanUtil.isFalse(permission)) {
            throw new YamiShopBindException("yami.no.auth");
        }
        shopEmployeeService.updateMerchantInfo(shopEmployee);
        return ServerResponseEntity.success();
    }

    @PostMapping("/createShop")
    @Operation(summary = "新建店铺", description = "新建店铺")
    @PreAuthorize("@pms.hasPermission('platform:shopDetail:save')")
    public ServerResponseEntity<Void> createShop(@RequestBody @Valid ShopCreateInfoDTO shopCreateInfoDTO) {
        Long userId = SecurityUtils.getSysUser().getUserId();
        shopDetailService.platformCreateShop(shopCreateInfoDTO, userId);
        return ServerResponseEntity.success();
    }

    @PostMapping("/sendCode")
    @Operation(summary = "发送申请开店验证码", description = "发送申请开店验证码")
    @PreAuthorize("@pms.hasPermission('platform:shopDetail:sendCode')")
    public ServerResponseEntity<Void> sendCode(@Valid @RequestBody SendSmsParam sendSmsParam) {
        long count = shopEmployeeService.count(Wrappers.lambdaQuery(ShopEmployee.class).eq(ShopEmployee::getMobile, sendSmsParam.getMobile()));
        if (count > 0) {
            // 手机号已存在
            throw new YamiShopBindException("yami.phone.number.already.exists");
        }
        smsLogService.sendSms(SendType.CAPTCHA, null, sendSmsParam.getMobile(), Maps.newLinkedHashMap());
        return ServerResponseEntity.success();
    }

    @PutMapping
    @Operation(summary = "编辑店铺基本信息", description = "编辑店铺基本信息")
    @PreAuthorize("@pms.hasPermission('platform:shopDetail:update')")
    public ServerResponseEntity<Void> editShop(@RequestBody ShopDetail shopDetail) {
        if (Objects.isNull(shopDetail.getShopId())) {
            // 店铺id不能为空
            throw new YamiShopBindException("yami.shop.detail.exception.idNotNull");
        }
        shopDetailService.updateShopDetail(shopDetail);
        eventPublisher.publishEvent(new EsProductUpdateEvent(shopDetail.getShopId(), null, EsOperationType.UPDATE_BY_SHOP_ID));
        return ServerResponseEntity.success();
    }

    @PutMapping("/updateSigningInfo")
    @Operation(summary = "更新店铺签约信息", description = "更新店铺签约信息")
    @PreAuthorize("@pms.hasPermission('platform:shopDetail:update')")
    public ServerResponseEntity<Void> updateSigningInfo(@RequestBody @Valid ShopSigningInfoDTO shopSigningInfoDTO) {
        if (Objects.isNull(shopSigningInfoDTO.getShopId())) {
            // 店铺id不能为空
            throw new YamiShopBindException("yami.shop.detail.exception.idNotNull");
        }
        boolean updateProdStatus = shopDetailService.updateSigningInfo(shopSigningInfoDTO);
        if (updateProdStatus) {
            eventPublisher.publishEvent(new EsProductUpdateEvent(shopSigningInfoDTO.getShopId(), null, EsOperationType.UPDATE_BY_SHOP_ID));
        }
        return ServerResponseEntity.success();
    }

    @GetMapping("/getOfflineHandleEventByShopId/{shopId}")
    @PreAuthorize("@pms.hasPermission('shop:shopAuditing:lowerShelfShop')")
    @Parameter(name = "shopId", description = "店铺id")
    public ServerResponseEntity<OfflineHandleEvent> getOfflineHandleEventByShopId(@PathVariable("shopId") Long shopId) {
        OfflineHandleEvent offlineHandleEvent = offlineHandleEventService.getProcessingEventByHandleTypeAndHandleId(OfflineHandleEventType.SHOP.getValue(), shopId);
        return ServerResponseEntity.success(offlineHandleEvent);
    }

    /**
     * 下线店铺
     */
    @PostMapping("/offline")
    @PreAuthorize("@pms.hasPermission('shop:shopAuditing:lowerShelfShop')")
    public ServerResponseEntity<Void> offline(@RequestBody OfflineHandleEvent offlineHandleEvent) {
        Long sysUserId = SecurityUtils.getSysUser().getUserId();
        ShopDetail shopDetail = shopDetailService.getShopDetailByShopId(offlineHandleEvent.getHandleId());
        if (shopDetail == null) {
            // 未找到该店铺信息
            throw new YamiShopBindException("yami.store.not.exist");
        }
        if (!Objects.equals(shopDetail.getShopStatus(), ShopStatus.OPEN.value()) && !Objects.equals(shopDetail.getShopStatus(), ShopStatus.STOP.value())) {
            // 店铺不处于营业或停业状态,不能进行下线
            throw new YamiShopBindException("yami.store.offline.check");
        }
        shopDetailService.offline(shopDetail, offlineHandleEvent.getOfflineReason(), sysUserId);
        eventPublisher.publishEvent(new EsProductUpdateEvent(shopDetail.getShopId(), null, EsOperationType.UPDATE_BY_SHOP_ID));
        // 关闭店铺下的门店
        esStationService.colseStationByShopId(shopDetail.getShopId());
        return ServerResponseEntity.success();
    }

    /**
     * 店铺审核重新开店
     */
    @PostMapping("/auditShop")
    @PreAuthorize("@pms.hasPermission('shop:shopAuditing:audit')")
    @Operation(summary = "店铺违规审核", description = "店铺违规审核")
    public ServerResponseEntity<Void> auditOfflineShop(@RequestBody OfflineHandleEventAuditParam offlineHandleEventAuditParam) {
        Long sysUserId = SecurityUtils.getSysUser().getUserId();
        shopDetailService.auditOfflineShop(offlineHandleEventAuditParam, sysUserId);
        shopDetailService.removeShopDetailCacheByShopId(offlineHandleEventAuditParam.getHandleId());
        return ServerResponseEntity.success();
    }

    @PostMapping("/onlineOpenShop")
    @Operation(summary = "店铺违规下线，平台直接上线店铺", description = "开通通联支付可用")
    @PreAuthorize("@pms.hasPermission('platform:shopDetail:update')")
    public ServerResponseEntity<Void> onlineOpenShop(@RequestBody OfflineHandleEventAuditParam offlineHandleEventAuditParam) {
        Long sysUserId = SecurityUtils.getSysUser().getUserId();
        offlineHandleEventAuditParam.setStatus(OfflineHandleEventStatus.AGREE_BY_PLATFORM.getValue());
        shopDetailService.onlineOpenShop(offlineHandleEventAuditParam, sysUserId);
        shopDetailService.removeShopDetailCacheByShopId(offlineHandleEventAuditParam.getHandleId());
        return ServerResponseEntity.success();
    }

    @GetMapping("/checkShopName")
    @Operation(summary = "检查店铺名称是否已存在", description = "检查店铺名称是否已存在")
    @PreAuthorize("@pms.hasPermission('platform:shopDetail:check')")
    public ServerResponseEntity<Boolean> checkShopName(@RequestParam(value = "shopName") String shopName) {
        long count = shopDetailService.count(Wrappers.lambdaQuery(ShopDetail.class)
                .eq(ShopDetail::getShopName, shopName)
                .ne(ShopDetail::getShopStatus, ShopStatus.NOTOPEN.value())
        );
        return ServerResponseEntity.success(count > 0);
    }

    @GetMapping("/exportShop")
    @Operation(summary = "导出店铺列表", description = "导出店铺列表")
    @PreAuthorize("@pms.hasPermission('platform:shopDetail:export')")
    public void exportShop(AuditingInfoParam auditingInfoParam, HttpServletResponse response) {
        shopDetailService.exportShop(auditingInfoParam, response);
    }

    @GetMapping("/searchShops")
    @Operation(summary = "搜索店铺", description = "根据店铺名称搜索店铺")
    public ServerResponseEntity<IPage<ShopHeadInfoDto>> searchShops(PageParam<ShopDetail> page, ShopSearchParam shopSearchParam) {
        IPage<ShopHeadInfoDto> shopPage = shopDetailService.renovationShopPage(page, shopSearchParam);
        return ServerResponseEntity.success(shopPage);
    }

}
