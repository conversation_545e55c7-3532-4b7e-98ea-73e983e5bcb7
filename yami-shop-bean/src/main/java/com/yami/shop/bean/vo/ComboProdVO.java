package com.yami.shop.bean.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yami.shop.bean.model.ProdLang;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 套餐商品项
 *
 * <AUTHOR>
 * @date 2021-11-02 10:35:08
 */
@Data
public class ComboProdVO implements Serializable{
    private static final long serialVersionUID = 1L;

    @TableId
    @Schema(description = "套餐商品项id" )
    private Long comboProdId;

    @Schema(description = "套餐id" )
    private Long comboId;

    @Schema(description = "商品id" )
    @NotNull(message = "商品id不能为空")
    private Long prodId;

    @Schema(description = "套餐价格" )
    private Double comboPrice;

    @Schema(description = "商品价格" )
    @TableField(exist = false)
    private Double price;

    @Schema(description = "类型：1：主商品 2：搭配商品" )
    private Integer type;

    @Schema(description = "是否必选：1：是 0：否" )
    @NotNull(message = "是否必选不能为空")
    private Integer required;

    @Schema(description = "起搭数量" )
    @NotNull(message = "起搭数量不能为空")
    private Integer leastNum;

    @Schema(description = "状态， -1：已删除 1：正常" )
    private Integer status;

    @Schema(description = "商品图片" )
    private String pic;

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "商品状态" )
    private Integer prodStatus;

    @Schema(description = "sku项列表" )
    private List<ComboProdSkuVO> skuList;

    @Schema(description = "商品语言列表" )
    private List<ProdLang> prodLangList;

    @Schema(description = "配送方式" )
    private String deliveryMode;

}
