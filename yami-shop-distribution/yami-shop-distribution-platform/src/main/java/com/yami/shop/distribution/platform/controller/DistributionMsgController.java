package com.yami.shop.distribution.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.model.DistributionMsg;
import com.yami.shop.distribution.common.service.DistributionMsgService;
import com.yami.shop.security.platform.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> on 2019/04/01.
 */
@RestController
@RequestMapping("/distribution/distributionMsg")
@Tag(name = "平台端分销通知接口")
@AllArgsConstructor
public class DistributionMsgController {

    private final DistributionMsgService distributionMsgService;

    @GetMapping("/page")
    @Operation(summary = "分页获取分销通知列表")
    @PreAuthorize("@pms.hasPermission('distribution:distributionMsg:page')")
    public ServerResponseEntity<IPage<DistributionMsg>> page(DistributionMsg distributionMsg, PageParam page) {
        IPage<DistributionMsg> list = distributionMsgService.getDistributionMsgsAndSysUserPage(page, distributionMsg);
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/info/{id}")
    @PreAuthorize("@pms.hasPermission('distribution:distributionMsg:info')")
    @Operation(summary = "根据分销通知id查看分销通知信息")
    @Parameter(name = "id", description = "分销通知id" , required = true)
    public ServerResponseEntity<DistributionMsg> info(@PathVariable("id") Long id) {
        DistributionMsg distributionMsg = distributionMsgService.getById(id);
        return ServerResponseEntity.success(distributionMsg);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionMsg:save')")
    @Operation(summary = "保存分销通知信息")
    public ServerResponseEntity<Void> save(@RequestBody @Valid DistributionMsg distributionMsg) {
        distributionMsg.setUpdateTime(new Date());
        distributionMsg.setModifier(SecurityUtils.getSysUser().getUserId());
        distributionMsg.setLevel(1);
        distributionMsg.setShopId(Constant.PLATFORM_SHOP_ID);
        distributionMsgService.save(distributionMsg);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionMsg:update')")
    @Operation(summary = "修改分销通知信息")
    public ServerResponseEntity<Void> update(@RequestBody @Valid DistributionMsg distributionMsg) {
        distributionMsg.setUpdateTime(new Date());
        distributionMsg.setModifier(SecurityUtils.getSysUser().getUserId());
        distributionMsgService.updateById(distributionMsg);
        return ServerResponseEntity.success();
    }

    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionMsg:delete')")
    @Operation(summary = "根据分销通知id列表批量删除分销通知信息")
    @Parameter(name = "ids", description = "分销通知id" , required = true)
    public ServerResponseEntity<Void> delete(@RequestBody List<Long> ids) {
        distributionMsgService.removeByIds(ids);
        return ServerResponseEntity.success();
    }
}
