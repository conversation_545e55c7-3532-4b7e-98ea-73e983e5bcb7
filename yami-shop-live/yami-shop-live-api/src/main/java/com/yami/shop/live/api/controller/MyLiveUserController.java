package com.yami.shop.live.api.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.yami.shop.bean.model.User;
import com.yami.shop.bean.vo.wukong.RegisterOrLoginVO;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.live.common.dto.LiveMessageDTO;
import com.yami.shop.live.common.service.LiveRoomService;
import com.yami.shop.live.common.vo.LiveRoomVO;
import com.yami.shop.security.api.util.SecurityUtils;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/p/live/liveUser")
@Tag(name = "直播用户接口")
@AllArgsConstructor
public class MyLiveUserController {

    private final LiveRoomService liveRoomService;
    private final UserService userService;

    @PostMapping("/registerOrLogin")
    @Operation(summary = "用户注册或登录到im")
    public ServerResponseEntity<RegisterOrLoginVO> registerOrLogin(@RequestBody LiveMessageDTO liveMessageDTO) {
        Long roomId = liveMessageDTO.getRoomId();
        List<String> list = StpUtil.getTokenValueListByLoginId(AuthUserContext.getUid());
        // 使用最开始登录的token
        String token = list.get(0);
        String userId = SecurityUtils.getUser().getUserId();
        LiveRoomVO livingRoomInfo = liveRoomService.getLivingRoomInfo(null, roomId);
        RegisterOrLoginVO registerOrLoginVO = liveRoomService.liveUserLogin(livingRoomInfo, roomId, token, userId);
        User user = userService.getUserByUserId(userId);
        registerOrLoginVO.setNickName(user.getNickName());
        return ServerResponseEntity.success(registerOrLoginVO);
    }
}
