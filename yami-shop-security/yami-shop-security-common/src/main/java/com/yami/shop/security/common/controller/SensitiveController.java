package com.yami.shop.security.common.controller;

import com.anji.captcha.model.common.RepCodeEnum;
import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.yami.shop.common.response.ServerResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020/7/30
 */
@RestController
@RequestMapping("/sensitive")
@Tag(name = "敏感词")
public class SensitiveController {

    @PostMapping( "/replace" )
    @Operation(summary = "敏感词替换" , description = "将输入的敏感词内容替换成*")
    public ServerResponseEntity<String> get(@RequestBody String input) {
        return ServerResponseEntity.success(input);
    }

}
