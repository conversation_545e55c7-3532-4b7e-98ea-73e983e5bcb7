<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yami.shop.groupbuy.common.dao.GroupOrderMapper">

    <resultMap id="groupOrderMap" type="com.yami.shop.groupbuy.common.model.GroupOrder">
        <id column="group_order_id" property="groupOrderId"/>
        <result column="shop_id" property="shopId"/>
        <result column="group_team_id" property="groupTeamId"/>
        <result column="group_activity_id" property="groupActivityId"/>
        <result column="user_id" property="userId"/>
        <result column="identity_type" property="identityType"/>
        <result column="activity_prod_price" property="activityProdPrice"/>
        <result column="pay_price" property="payPrice"/>
        <result column="order_number" property="orderNumber"/>
        <result column="create_time" property="createTime"/>
        <result column="status" property="status"/>
    </resultMap>

    <select id="listApiGroupUserDto" resultType="com.yami.shop.groupbuy.common.api.dto.ApiGroupUserDto">
        SELECT
        go.user_id AS user_id,
        go.identity_type AS identity_type,
        go.order_number AS order_number,
        go.create_time AS create_time,
        IF(go.user_id = '0', '系统参团', IFNULL(u.nick_name, '用户已注销')) AS nick_name,
        u.pic AS pic
        FROM tz_group_order go
        LEFT JOIN tz_user u ON go.user_id = u.user_id
        WHERE go.group_team_id = #{groupTeamId} AND go.`status` = 1
    </select>
<!--    <select id="getHadGroupNumberCount" resultType="java.lang.Long">-->
<!--        SELECT COUNT(1) FROM-->
<!--        (SELECT go.`user_id` FROM tz_group_order go-->
<!--        LEFT JOIN tz_group_team gt ON gt.`group_team_id` = go.`group_team_id`-->
<!--        WHERE gt.`status` = 2 AND gt.`group_activity_id` = #{groupActivityId} AND gt.`group_prod_id` = #{groupProdId}-->
<!--        GROUP BY go.user_id) AS temp-->
<!--    </select>-->

<!--    <select id="listByGroupProdId" resultType="com.yami.shop.groupbuy.common.model.GroupOrder">-->
<!--        SELECT * FROM tz_group_order go-->
<!--        JOIN tz_group_team  gt ON go.`group_team_id` = gt.`group_team_id`-->
<!--        WHERE gt.`group_prod_id` =#{groupProdId}  AND go.`user_id` = #{userId} and go.status <![CDATA[<>]]> -1-->
<!--    </select>-->

    <select id="getUserHadOrderCountByGroupProdId" resultType="int">
        SELECT IFNULL(SUM(o.`product_nums`),0) AS num FROM tz_group_order go
        LEFT JOIN tz_group_team gt ON go.`group_team_id` = gt.`group_team_id`
        LEFT JOIN tz_order o ON o.`order_number` = go.`order_number`
        WHERE go.`user_id` = #{userId} and o.status  <![CDATA[ <> ]]> 6
        AND gt.`group_activity_id` = #{groupActivityId}
    </select>

    <select id="getUserUnGroupOrderCount" resultType="int">
        SELECT COUNT(1) AS num FROM tz_group_order go
        JOIN tz_group_team gt ON go.`group_team_id` = gt.`group_team_id`
        WHERE go.`user_id` = #{userId}
          AND go.`status` != -1
          AND go.`group_activity_id` = #{groupActivityId}
          AND gt.`prod_id` = #{prodId}
          AND (gt.status = 0 OR gt.status = 1)
    </select>

    <update id="cancelGroupOrder">
      update tz_group_order set status = #{status} where order_number = #{orderNumber}
    </update>
</mapper>
