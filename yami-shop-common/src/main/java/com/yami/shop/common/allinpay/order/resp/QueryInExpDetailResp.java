package com.yami.shop.common.allinpay.order.resp;

import java.util.List;

/**
 * 账单明细
 *
 * <AUTHOR>
 */
public class QueryInExpDetailResp {

    /**
     * 用户标志
     */
    private String bizUserId;
    /**
     * 总条数
     */
    private Long totalNum;
    /**
     * 账单明细集合
     */
    private List<InExpDetail> inExpDetail;

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public Long getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Long totalNum) {
        this.totalNum = totalNum;
    }

    public List<InExpDetail> getInExpDetail() {
        return inExpDetail;
    }

    public void setInExpDetail(List<InExpDetail> inExpDetail) {
        this.inExpDetail = inExpDetail;
    }

    @Override
    public String toString() {
        return "QueryInExpDetailResp{" +
                "bizUserId='" + bizUserId + '\'' +
                ", totalNum=" + totalNum +
                ", inExpDetail=" + inExpDetail +
                '}';
    }
}
