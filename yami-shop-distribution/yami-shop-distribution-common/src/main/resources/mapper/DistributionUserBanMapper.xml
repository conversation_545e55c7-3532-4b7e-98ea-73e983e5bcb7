<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.distribution.common.dao.DistributionUserBanMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.distribution.common.model.DistributionUserBan">
    <id column="ban_id" jdbcType="BIGINT" property="banId" />
    <result column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId" />
    <result column="ban_reason" jdbcType="TINYINT" property="banReason" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="state" jdbcType="TINYINT" property="state" />
  </resultMap>

  <resultMap id="DistributionUserBan_DistributionUser_SysUser" type="com.yami.shop.distribution.common.model.DistributionUserBan">
    <id column="ban_id" jdbcType="BIGINT" property="banId" />
    <result column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId" />
    <result column="ban_reason" jdbcType="TINYINT" property="banReason" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <association property="distributionUser" javaType="com.yami.shop.distribution.common.vo.DistributionUserVO">
      <id column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId" />
        <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
        <result column="user_mobile" jdbcType="VARCHAR" property="userMobile" />
    </association>
    <association property="sysUser" javaType="com.yami.shop.bean.vo.SysUserVO">
      <id column="sys_user_id" jdbcType="BIGINT" property="userId" />
      <result column="username" jdbcType="VARCHAR" property="username" />
    </association>
  </resultMap>

  <select id="distributionUserBanPage" resultMap="DistributionUserBan_DistributionUser_SysUser">
    select
    dub.*,
    du.nick_name,du.user_mobile,
    su.username,su.user_id as sys_user_id
    from
    tz_distribution_user_ban dub
    left join tz_distribution_user du on du.distribution_user_id=dub.distribution_user_id
    left join tz_sys_user su on su.user_id=dub.modifier
    <where>
      <if test="shopId!=null ">
        and du.shop_id=#{shopId}
      </if>
      <if test="distributionParam.userMobile != null and distributionParam.userMobile != ''">
        and du.user_mobile like concat( '%', #{distributionParam.userMobile}, '%' )
      </if>
      <if test="distributionParam.nickName != null and distributionParam.nickName != ''">
        AND du.nick_name LIKE CONCAT( '%', #{distributionParam.nickName}, '%' )
      </if>
      <if test="distributionUserBan.state!=null">
        and dub.state =#{distributionUserBan.state}
      </if>
      <if test="distributionUserBan.banReason!=null">
        and ban_reason =#{distributionUserBan.banReason}
      </if>
    </where>
    GROUP BY dub.ban_id
    <if test="distributionUserBan.sortParam != 0 and distributionUserBan.sortType != 0">
      ORDER BY
      <choose>
        <when test="distributionUserBan.sortParam == 1">
          dub.update_time
        </when>
        <otherwise>
          dub.ban_id
        </otherwise>
      </choose>
      <choose>
        <when test="distributionUserBan.sortType == 1">
          ASC
        </when>
        <when test="distributionUserBan.sortType == 2">
          DESC
        </when>
      </choose>
    </if>

  </select>
</mapper>
