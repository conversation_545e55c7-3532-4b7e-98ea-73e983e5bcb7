package com.yami.shop.platform.task;

import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yami.shop.allinpay.service.AllinpayCompanyService;
import com.yami.shop.bean.model.ShopWithdrawCash;
import com.yami.shop.bean.enums.EsOperationType;
import com.yami.shop.bean.event.EsProductUpdateEvent;
import com.yami.shop.service.ShopDetailService;
import com.yami.shop.service.ShopWithdrawCashService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/8/9 14:45
 */
@Slf4j
@Component
@AllArgsConstructor
public class ShopTask {

    private final ShopDetailService shopDetailService;
    private final AllinpayCompanyService allinpayCompanyService;
    private final ShopWithdrawCashService shopWithdrawCashService;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 根据签约时间改变店铺状态
     */
    @XxlJob("changeShopStatusByContractTime")
    public void changeShopStatusByContractTime() {
        // 根据签约时间修改店铺状态
        if (allinpayCompanyService.getIsAllinpay()) {
            return;
        }
        log.info("根据签约时间改变店铺状态");
        Date now = new Date();
        List<Long> shopIds = shopDetailService.changeShopStatusByContractTime(now);
        // 在es更新店铺下的商品（用于刷新商品在es的appDisplay）
        eventPublisher.publishEvent(new EsProductUpdateEvent(null, shopIds, EsOperationType.UPDATE_BY_SHOP_ID));
    }

    /**
     * 获取一天前申请提现还未成功的提现申请,将提现冻结金额回退(通联环境)
     */
    @XxlJob("returnAmountByTimeOut")
    public void returnAmountByTimeOut() {
        Date now = new Date();
        if (!allinpayCompanyService.getIsAllinpay()) {
            return;
        }
        log.info("开始执行冻结金额回退定时任务》》》》》》》》》》》》》》》》》》》》》");
        Date outTime = DateUtil.offsetDay(new Date(), -1).toJdkDate();
        List<ShopWithdrawCash> shopWithdrawCashList = shopWithdrawCashService.listShopWithdrawCashByTimeOut(outTime);
        for (ShopWithdrawCash shopWithdrawCash : shopWithdrawCashList) {
            shopWithdrawCashService.returnAmountByTimeOut(shopWithdrawCash);
        }
        log.info("执行冻结金额回退定时任务完成》》》》》》》》》》》》》》》》》》》》》");
    }
}
