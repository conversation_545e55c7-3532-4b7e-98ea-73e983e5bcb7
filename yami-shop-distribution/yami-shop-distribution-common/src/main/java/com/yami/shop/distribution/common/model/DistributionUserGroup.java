package com.yami.shop.distribution.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yami.shop.bean.vo.SysUserVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
/**
 * <AUTHOR>
 */
@Data
@TableName( "tz_distribution_user_group")
public class DistributionUserGroup {

    @TableId
    @Schema(description = "分组表id" )
    private Long groupId;

    @Schema(description = "分组名" )
    private String groupName;

    @Schema(description = "修改时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "操作人id" )
    private Long modifier;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "操作人" )
    @TableField(exist = false)
    private SysUserVO sysUser;
}
