package com.yami.shop.common.allinpay.order.req;

/**
 * 提现申请请求
 * <AUTHOR>
 */
public class WithdrawApplyReq extends AbstractOrderReq {

    public static final String METHOD_NAME = "withdrawApply";

    /**
     *商户订单号（支付订单） 全局唯一
     */
    private String bizOrderNo;

    /**
     *	商户系统用户标识，商户系统中唯一编号。
     */
    private String bizUserId;

    /**
     *	托管账户集编号(个人，企业会员使用)
     */
    private String accountSetNo;

    /**
     *	订单金额 单位：分，包含手续费
     */
    private Long amount;

    /**
     *	手续费 内扣，如果不存在，则填0。 默认0
     */
    private Long fee;

    /**
     * 交易验证方式 0.仅渠道验证，通商云不做交易验证 1.通商云发送并验证短信验证码，有效期3分钟。2.验证通商云支付密码
     */
    private Long validateType;

    /**
     *后台通知地址
     */
    private String backUrl;

    /**
     * 订单过期时间 yyyy-MM-dd HH:mm:ss
     * 控制订单可支付时间，订单最长时效为24小时，即过期时间不能大于订单创建时间24小时；
     */
    private String orderExpireDatetime;

    /**
     *银行卡号/账号
     */
    private String bankCardNo;

    /**
     *银行卡/账户属性 0：个人银行卡 1：企业对公账户
     */
    private Integer bankCardPro;

    /**
     * 行业代码 默认1910
     */
    private String industryCode;

    /**
     * 行业名称 默认其他
     */
    private String industryName;

    /**
     * 	访问终端类型
     */
    private Integer source;

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    @Override
    public String getBizUserId() {
        return bizUserId;
    }

    @Override
    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getAccountSetNo() {
        return accountSetNo;
    }

    public void setAccountSetNo(String accountSetNo) {
        this.accountSetNo = accountSetNo;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Long getFee() {
        return fee;
    }

    public void setFee(Long fee) {
        this.fee = fee;
    }

    public Long getValidateType() {
        return validateType;
    }

    public void setValidateType(Long validateType) {
        this.validateType = validateType;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public String getOrderExpireDatetime() {
        return orderExpireDatetime;
    }

    public void setOrderExpireDatetime(String orderExpireDatetime) {
        this.orderExpireDatetime = orderExpireDatetime;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public Integer getBankCardPro() {
        return bankCardPro;
    }

    public void setBankCardPro(Integer bankCardPro) {
        this.bankCardPro = bankCardPro;
    }

    public String getIndustryCode() {
        return industryCode;
    }

    public void setIndustryCode(String industryCode) {
        this.industryCode = industryCode;
    }

    public String getIndustryName() {
        return industryName;
    }

    public void setIndustryName(String industryName) {
        this.industryName = industryName;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    @Override
    public String toString() {
        return "WithdrawApplyReq{" +
                "bizOrderNo='" + bizOrderNo + '\'' +
                ", bizUserId='" + bizUserId + '\'' +
                ", accountSetNo='" + accountSetNo + '\'' +
                ", amount=" + amount +
                ", fee=" + fee +
                ", validateType=" + validateType +
                ", backUrl='" + backUrl + '\'' +
                ", orderExpireDatetime='" + orderExpireDatetime + '\'' +
                ", bankCardNo='" + bankCardNo + '\'' +
                ", bankCardPro=" + bankCardPro +
                ", industryCode='" + industryCode + '\'' +
                ", industryName='" + industryName + '\'' +
                ", source=" + source +
                '}';
    }
}
