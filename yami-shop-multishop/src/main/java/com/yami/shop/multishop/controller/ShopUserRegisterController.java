package com.yami.shop.multishop.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.yami.shop.bean.app.param.CheckRegisterSmsParam;
import com.yami.shop.bean.app.param.SendSmsParam;
import com.yami.shop.bean.dto.ShopUserRegisterDto;
import com.yami.shop.bean.enums.SendType;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.enums.SysTypeEnum;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.i18n.LanguageEnum;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PrincipalUtil;
import com.yami.shop.common.util.RedisUtil;
import com.yami.shop.security.common.bo.UidInfoBO;
import com.yami.shop.security.common.manager.PasswordManager;
import com.yami.shop.security.common.manager.TokenStore;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.security.common.vo.TokenInfoVO;
import com.yami.shop.service.SmsLogService;
import com.yami.shop.service.SysConfigService;
import com.yami.shop.sys.common.model.ShopEmployee;
import com.yami.shop.sys.common.service.ShopEmployeeService;
import com.yami.shop.sys.common.service.ShopMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2021/7/7 14:43
 */
@RestController
@RequestMapping("/shop/shopUserRegister")
@Tag(name = "店铺注册接口")
@AllArgsConstructor
public class ShopUserRegisterController {

    private final SysConfigService sysConfigService;
    private final SmsLogService smsLogService;
    private final ShopEmployeeService shopEmployeeService;
    private final PasswordEncoder passwordEncoder;
    private final PasswordManager passwordManager;
    private final ShopMenuService shopMenuService;
    private final TokenStore tokenStore;

    private static final String CHECK_UPDATE_PWD_SMS_FLAG = "updateShopEmployeePwdSmsFlag";

    @GetMapping("/getMerchantRegisterProtocol")
    @Operation(summary = "获取商家注册协议", description = "获取商家注册协议")
    public ServerResponseEntity<String> getMerchantRegisterProtocol() {
        Integer dbLang = I18nMessage.getDbLang();
        if (Objects.equals(LanguageEnum.LANGUAGE_EN.getLang(), dbLang)) {
            return ServerResponseEntity.success(sysConfigService.getValue(Constant.MERCHANT_REGISTER_PROTOCOL_EN));
        }
        return ServerResponseEntity.success(sysConfigService.getValue(Constant.MERCHANT_REGISTER_PROTOCOL_CN));
    }

    @GetMapping("/getShopProtocol")
    @Operation(summary = "获取开店协议", description = "获取开店协议")
    public ServerResponseEntity<String> getShopProtocol() {
        Integer dbLang = I18nMessage.getDbLang();
        if (Objects.equals(LanguageEnum.LANGUAGE_EN.getLang(), dbLang)) {
            return ServerResponseEntity.success(sysConfigService.getValue(Constant.SHOP_PROTOCOL_EN));
        }
        return ServerResponseEntity.success(sysConfigService.getValue(Constant.SHOP_PROTOCOL_CN));
    }

    @PostMapping("/sendCode")
    @Operation(summary = "发送申请开店验证码", description = "发送申请开店验证码")
    public ServerResponseEntity<Void> sendCode(@Valid @RequestBody SendSmsParam sendSmsParam) {
        long count = shopEmployeeService.count(Wrappers.lambdaQuery(ShopEmployee.class).eq(ShopEmployee::getMobile, sendSmsParam.getMobile()));
        if (count > 0) {
            // 手机号已存在
            throw new YamiShopBindException("yami.phone.number.already.exists");
        }
        smsLogService.sendSms(SendType.CAPTCHA, sendSmsParam.getMobile(), sendSmsParam.getMobile(), Maps.newLinkedHashMap());
        return ServerResponseEntity.success();
    }

    @PostMapping
    @Operation(summary = "注册商家", description = "注册商家")
    public ServerResponseEntity<TokenInfoVO> register(@Valid @RequestBody ShopUserRegisterDto shopUserRegisterDTO) {
        ShopEmployee shopEmployee = shopEmployeeService.registerMerchant(shopUserRegisterDTO);

        UidInfoBO uidInfoBO = new UidInfoBO(SysTypeEnum.MULTISHOP, shopEmployee.getEmployeeId().toString(), shopEmployee.getShopId());
        // 注册的商家是管理员
        uidInfoBO.setAdmin(1);
        // 存储token返回vo
        TokenInfoVO tokenInfoVO = tokenStore.storeAndGetVo(uidInfoBO);
        return ServerResponseEntity.success(tokenInfoVO);
    }

    @PostMapping("/sendUpdatePwdCode")
    @Operation(summary = "发送修改密码验证码接口", description = "发送修改密码验证码接口")
    public ServerResponseEntity<Void> sendUpdatePwdCode(@Valid @RequestBody SendSmsParam sendSmsParam) {
        ShopEmployee shopEmployee = shopEmployeeService.getOne(Wrappers.lambdaQuery(ShopEmployee.class).eq(ShopEmployee::getMobile, sendSmsParam.getMobile()));
        if (Objects.isNull(shopEmployee)) {
            // 手机号不存在
            throw new YamiShopBindException("yami.phone.number.not.exists");
        }
        smsLogService.sendSms(SendType.CAPTCHA, shopEmployee.getEmployeeId().toString(), sendSmsParam.getMobile(), Maps.newLinkedHashMap());
        return ServerResponseEntity.success();
    }

    @PutMapping("/checkUpdatePwdSms")
    @Operation(summary = "修改密码校验验证码", description = "校验验证码返回校验成功的标识")
    public ServerResponseEntity<String> checkUpdatePwdSms(@Valid @RequestBody CheckRegisterSmsParam checkRegisterSmsParam) {
        boolean isCheckPass = false;
        if (Objects.nonNull(checkRegisterSmsParam) && Objects.nonNull(checkRegisterSmsParam.getMobile())) {
            Matcher m = Pattern.compile(PrincipalUtil.MOBILE_REGEXP).matcher(checkRegisterSmsParam.getMobile());
            isCheckPass = m.matches();
        }
        if (!isCheckPass) {
            throw new YamiShopBindException("yami.user.err.phone");
        }

        ShopEmployee shopEmployee = shopEmployeeService.getOne(Wrappers.lambdaQuery(ShopEmployee.class).eq(ShopEmployee::getMobile, checkRegisterSmsParam.getMobile()));
        if (Objects.isNull(shopEmployee)) {
            // 手机号不存在
            throw new YamiShopBindException("yami.phone.number.not.exists");
        }
        if (StrUtil.isBlank(checkRegisterSmsParam.getValidCode())) {
            throw new YamiShopBindException("yami.user.code.empty");
        }
        if (!smsLogService.checkValidCode(shopEmployee.getMobile(), checkRegisterSmsParam.getValidCode(), SendType.CAPTCHA)) {
            // 验证码有误或已过期
            throw new YamiShopBindException("yami.user.code.error");
        }
        String checkRegisterSmsFlag = IdUtil.simpleUUID();
        RedisUtil.set(CHECK_UPDATE_PWD_SMS_FLAG + checkRegisterSmsFlag, checkRegisterSmsParam.getMobile(), 600);
        return ServerResponseEntity.success(checkRegisterSmsFlag);
    }

    @PutMapping("/updatePwd")
    @Operation(summary = "修改密码", description = "修改密码")
    public ServerResponseEntity<Void> updatePwd(@RequestBody ShopUserRegisterDto shopUserRegisterDto) {
        ShopEmployee shopEmployee = shopEmployeeService.getOne(Wrappers.lambdaQuery(ShopEmployee.class).eq(ShopEmployee::getMobile, shopUserRegisterDto.getMobile()));
        if (Objects.isNull(shopEmployee)) {
            // 手机号不存在
            throw new YamiShopBindException("yami.phone.number.not.exists");
        }
        // 看看有没有校验验证码成功的标识
        if (StrUtil.isBlank(shopUserRegisterDto.getCheckRegisterSmsFlag())) {
            // 验证码已过期，请重新发送验证码校验
            throw new YamiShopBindException("yami.verification.expire");
        } else {
            String checkRegisterSmsFlag = CHECK_UPDATE_PWD_SMS_FLAG + shopUserRegisterDto.getCheckRegisterSmsFlag();
            String checkRegisterSmsFlagMobile = RedisUtil.get(checkRegisterSmsFlag);
            if (!Objects.equals(checkRegisterSmsFlagMobile, shopUserRegisterDto.getMobile())) {
                // 验证码已过期，请重新发送验证码校验
                throw new YamiShopBindException("yami.verification.expire");
            }
        }
        String decryptPassword = passwordManager.decryptPassword(shopUserRegisterDto.getPassword());
        if (StrUtil.isBlank(decryptPassword)) {
            // 新密码不能为空
            throw new YamiShopBindException("yami.user.password.no.exist");
        }
        if (StrUtil.equals(passwordEncoder.encode(decryptPassword), shopEmployee.getPassword())) {
            // 新密码不能与原密码相同!
            throw new YamiShopBindException("yami.user.password.check");
        }
        Boolean updateRes = shopEmployeeService.updatePasswordByEmployeeId(shopEmployee.getEmployeeId(), decryptPassword);
        if (!updateRes) {
            // 更新失败
            throw new YamiShopBindException("yami.shop.user.register.exception.updateError");
        } else {
            RedisUtil.del(CHECK_UPDATE_PWD_SMS_FLAG + shopUserRegisterDto.getCheckRegisterSmsFlag());
        }
        tokenStore.deleteTokenByUidInfo(AuthUserContext.get());
        return ServerResponseEntity.success();
    }

}
