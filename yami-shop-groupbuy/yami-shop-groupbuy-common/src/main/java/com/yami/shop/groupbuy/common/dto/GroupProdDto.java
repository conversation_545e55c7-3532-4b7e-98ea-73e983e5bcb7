package com.yami.shop.groupbuy.common.dto;

import com.yami.shop.groupbuy.common.model.GroupSku;
import com.yami.shop.groupbuy.common.utils.GroupActivityUtil;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GroupProdDto {
    /**
     * 活动商品id
     */
    private Long groupProdId;
    /**
     * 店铺id
     */
    private Long shopId;
    /**
     * 活动id
     */
    private Long groupActivityId;
    /**
     * 商品id
     */
    private Long prodId;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 已成团订单数（统计）
     */
    private Long groupOrderCount;
    /**
     * 已成团人数（统计）
     */
    private Long groupNumberCount;

    /**
     * 状态（1:正常 2:失效 -1:已删除）
     */
    private Integer groupProdStatus;

    /**
     * 商品名称
     */
    private String prodName;
    /**
     * 商品图片
     */
    private String pic;

    /**
     * 活动商品规格属性
     */
    private List<GroupSku> groupSkuList;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 状态
     */
    private Integer groupActivityStatus;

    /**
     * 活动状态
     */
    private Integer activityStatus;

    /**
     * 获取活动状态（活动状态：1:未开始、2:进行中、3:已结束、4:已失效）
     */
    public Integer getActivityStatus() {
        return GroupActivityUtil.getActivityStatus(startTime, endTime, groupActivityStatus);
    }
}
