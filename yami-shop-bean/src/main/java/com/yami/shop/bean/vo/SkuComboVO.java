package com.yami.shop.bean.vo;

import com.yami.shop.bean.model.ProdLang;
import com.yami.shop.bean.model.SkuLang;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 规格组合商品管理表VO
 *
 * <AUTHOR>
 * @date 2023-11-06 11:00:41
 */
@Data
public class SkuComboVO{
    private static final long serialVersionUID = 1L;

    @Schema(description = "规格id")
    private Long skuId;

    @Schema(description = "主商品id")
    private Long prodId;

    @Schema(description = "组合的skuid")
    private Long comboSkuId;

    @Schema(description = "图片")
    private String imgUrl;

    @Schema(description = "组合数量")
    private Integer comboCount;

    @Schema(description = "组合总价")
    private Long totalComboAmount;

    @Schema(description = "商品id")
    private Long comboProdId;
    @Schema(description = "商品名称")
    private String prodName;

    @Schema(description = "规格名称")
    private String skuName;

    @Schema(description = "原销售价")
    private Double price;

    @Schema(description = "库存")
    private Integer stock;

    @Schema(description = "区域库存列表")
    private List<StockPointSkuVO> stockPointSkuList;

    @Schema(description = "商品多语言列表")
    private List<ProdLang> prodLangList;

    @Schema(description = "商品sku多语言列表")
    private List<SkuLang> skuLangList;

    @Schema(description = "仓库可售卖库存")
    private Integer warehouseStock;

    @Schema(description = "门店可售卖库存")
    private Integer stationStock;
}
