package com.yami.shop.distribution.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.yami.shop.distribution.common.vo.DistributionUserVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
/**
 * <AUTHOR>
 */
@Data
@TableName( "tz_distribution_user_wallet")
public class DistributionUserWallet {

    @TableId
    @Schema(description = "分销员钱包id" )
    private Long walletId;

    @Schema(description = "分销员id" )
    private Long distributionUserId;

    @Schema(description = "待结算金额(用户付款后 增加该金额,结算时减少该金额,取消订单或退货减少该金额)" )
    private Double unsettledAmount;

    @Schema(description = "可提现金额(用户收货后或者维权期过后增加该金额,提现减少该金额)" )
    private Double settledAmount;

    @Schema(description = "积累收益(用户收货后或者维权期过后增加该金额,提现不减少该金额)" )
    private Double addupAmount;

    @Schema(description = "失效金额(用户取消订单或退货增加该金额)" )
    private Double invalidAmount;

    @Version
    @Schema(description = "乐观锁" )
    private Integer version;

    @Schema(description = "钱包状态( 0未生效(分销审核未通过) 1正常)" )
    private Integer state;

    @Schema(description = "关联分销员" )
    @TableField(exist = false)
    private DistributionUserVO distributionUser;


    @Schema(description = "可提现的改变金额" )
    @TableField(exist = false)
    private Double changeAmount;
}
