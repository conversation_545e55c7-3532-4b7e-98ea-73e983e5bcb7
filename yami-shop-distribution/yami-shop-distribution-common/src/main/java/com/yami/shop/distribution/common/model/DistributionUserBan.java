package com.yami.shop.distribution.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yami.shop.bean.vo.SysUserVO;
import com.yami.shop.distribution.common.vo.DistributionUserVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
/**
 * <AUTHOR>
 */
@Data
@TableName( "tz_distribution_user_ban")
public class DistributionUserBan {

    @TableId
    @Schema(description = "封禁id" )
    private Long banId;

    @Schema(description = "分销员id" )
    private Long distributionUserId;

    @Schema(description = "封禁原因(0 失去联系 1恶意刷单 2其他)" )
    private Integer banReason;

    @Schema(description = "备注" )
    private String remarks;

    @Schema(description = "操作时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "操作人" )
    private Long modifier;

    @Schema(description = "状态(0 正常 1暂时封禁 -1永久封禁)" )
    private Integer state;

    @Schema(description = "操作人" )
    @TableField(exist = false)
    private SysUserVO sysUser;

    @Schema(description = "关联分销员" )
    @TableField(exist = false)
    private DistributionUserVO distributionUser;

    @Schema(description = "排序字段 分销员-封禁记录：0无 1操作时间" )
    @TableField(exist = false)
    private Integer sortParam = 0;

    @Schema(description = "排序类型 0无 1 正序 2倒序" )
    @TableField(exist = false)
    private Integer sortType = 0;
}
