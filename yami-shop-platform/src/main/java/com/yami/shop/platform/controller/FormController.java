package com.yami.shop.platform.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.Form;
import com.yami.shop.bean.model.FormItem;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.FormService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> on 2018/11/26.
 */
@RestController
@RequestMapping("/platform/form")
@Tag(name = "数据报表")
@AllArgsConstructor
public class FormController {

    private final FormService formService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('platform:form:page')")
    @Operation(summary = "分页获取报表" , description = "分页获取报表")
    public ServerResponseEntity<IPage<Form>> page(Form form, PageParam<Form> page) {
        IPage<Form> formPage = formService.page(page, new LambdaQueryWrapper<Form>()
                .eq(Form::getShopId,Constant.PLATFORM_SHOP_ID)
                .like(StrUtil.isNotBlank(form.getFormName()),Form::getFormName,form.getFormName())
                .eq(Objects.nonNull(form.getTimeType()),Form::getTimeType,form.getTimeType())
                .orderByDesc(Form::getSeq)
                .orderByDesc(Form::getUpdateTime)
        );
        return ServerResponseEntity.success(formPage);
    }

    @GetMapping("/info/{formId}")
    @PreAuthorize("@pms.hasPermission('platform:form:info')")
    @Operation(summary = "获取信息" , description = "获取信息")
    @Parameter(name = "formId", description = "报表id" )
    public ServerResponseEntity<Form> info(@PathVariable("formId") Long formId) {
        Form form = formService.getById(formId);
        return ServerResponseEntity.success(form);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('platform:form:save')")
    @Operation(summary = "保存" , description = "保存")
    public ServerResponseEntity<Void> save(@RequestBody @Valid Form form) {
        formService.saveForm(form);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('platform:form:update')")
    @Operation(summary = "修改" , description = "修改")
    public ServerResponseEntity<Void> update(@RequestBody @Valid Form form) {
        formService.updateForm(form);
        return ServerResponseEntity.success();
    }

    @DeleteMapping("/{formId}")
    @PreAuthorize("@pms.hasPermission('platform:form:delete')")
    @Operation(summary = "删除报表" , description = "删除报表")
    @Parameter(name = "formId", description = "报表id" )
    public ServerResponseEntity<Void> delete(@PathVariable("formId") Long formId) {
        Form formDb = formService.getById(formId);
        formService.removeById(formId);
        //如果是推荐报表，则清除缓存
        if (Objects.isNull(formDb.getShopId())){
            formService.removeCache();
        }
        return ServerResponseEntity.success();
    }

    @GetMapping("/getFormItem")
    @Operation(summary = "获取报表项列表" , description = "获取报表项列表")
    @Parameter(name = "type", description = "1:平台端  2：商家端" )
    @PreAuthorize("@pms.hasPermission('platform:form:listItem')")
    public ServerResponseEntity<List<FormItem>> getFormItem(@RequestParam("type")Integer type) {
        List<FormItem> formItemEnumList = formService.getFormItem(type, I18nMessage.getDbLang());
        return ServerResponseEntity.success(formItemEnumList);
    }

    @GetMapping("/formExcel")
    @PreAuthorize("@pms.hasPermission('platform:form:excel')")
    @Operation(summary = "生成报表" , description = "生成报表")
    public void formExcel(@RequestParam("formId") Long formId, HttpServletResponse response) {
        formService.formExcel(formId,response);
    }

    @GetMapping("/getRecommendFormPage")
    @Operation(summary = "分页获取推荐报表" , description = "分页获取推荐报表")
    @PreAuthorize("@pms.hasPermission('platform:form:pageRecommend')")
    public ServerResponseEntity<IPage<Form>> getRecommendFormPage(Form form, PageParam<Form> page) {
        IPage<Form> formPage = formService.getRecommendFormPage(page,form);
        return ServerResponseEntity.success(formPage);
    }

    @GetMapping("/getRecommendFormList")
    @Operation(summary = "获取推荐报表" , description = "获取推荐报表")
    @PreAuthorize("@pms.hasPermission('platform:form:listRecommend')")
    public ServerResponseEntity<List<Form>> getRecommendFormList() {
        List<Form> formList = formService.getRecommendFormList();
        return ServerResponseEntity.success(formList);
    }
}
