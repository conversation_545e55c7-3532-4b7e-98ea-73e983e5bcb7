package com.yami.shop.common.constants;

/**
 * <AUTHOR>
 * @Date 2021/8/17 13:09
 */
public interface CacheNames extends ProductCacheNames, ShopCacheNames {
    /**
     *
     * 参考CacheKeyPrefix
     * cacheNames 与 key 之间的默认连接字符
     */
    String UNION = "::";
    /**
     * key内部的连接字符（自定义）
     */
    String UNION_KEY = ":";

    /**
     * 订单锁,用于支付成功或者取消订单
     */
    String REDISSON_ORDER_LOCK = "redisson_order_lock:";
    /**
     * 积分签到锁
     */
    String SCORE_SIGN_LOCK = "redisson_score_sign_lock:";
    /**
     * 优惠券领取锁
     */
    String COUPON_RECEIVE_LOCK = "redisson_coupon_receive_lock:";
}
