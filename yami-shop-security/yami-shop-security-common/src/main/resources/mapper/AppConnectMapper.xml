<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.security.common.dao.AppConnectMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.security.common.model.AppConnect">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="app_id" jdbcType="TINYINT" property="appId" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="biz_user_id" jdbcType="VARCHAR" property="bizUserId" />
    <result column="biz_unionid" jdbcType="VARCHAR" property="bizUnionid" />
    <result column="temp_uid" jdbcType="VARCHAR" property="tempUid" />
    <result column="biz_temp_session" jdbcType="VARCHAR" property="bizTempSession" />

  </resultMap>

  <select id="getByBizUserId" resultType="com.yami.shop.security.common.model.AppConnect">
      select * from tz_app_connect where biz_user_id = #{bizUserId} and app_id = #{appId}
  </select>

  <select id="getByTempUid" resultType="com.yami.shop.security.common.model.AppConnect">
    select * from tz_app_connect where temp_uid = #{tempUid}
  </select>

  <update id="bindUserIdByTempUid">
    update tz_app_connect set user_id = #{userId} where temp_uid = #{tempUid}
  </update>

  <update id="unBindUser">
    update tz_app_connect set user_id = null where biz_user_id = #{bizUserId} and app_id = #{socialType}
  </update>

  <update id="unBindUserByUserId">
    update tz_app_connect set user_id = null where user_id = #{userId}
  </update>
</mapper>
