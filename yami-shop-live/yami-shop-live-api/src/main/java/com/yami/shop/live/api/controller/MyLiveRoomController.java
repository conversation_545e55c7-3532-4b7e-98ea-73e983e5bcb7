package com.yami.shop.live.api.controller;

import cn.hutool.core.util.StrUtil;
import com.yami.shop.common.bean.AliLive;
import com.yami.shop.common.bean.LiveConfig;
import com.yami.shop.common.bean.TencentLive;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.config.ShopConfig;
import com.yami.shop.live.common.service.LiveRoomService;
import com.yami.shop.live.common.util.AliLiveUtil;
import com.yami.shop.live.common.util.TencentLiveUtil;
import com.yami.shop.live.common.vo.LiveRoomVO;
import com.yami.shop.security.api.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/p/live/liveRoom")
@Tag(name = "我的直播接口")
@AllArgsConstructor
public class MyLiveRoomController {

    private final LiveRoomService liveRoomService;
    private final ShopConfig shopConfig;

    @GetMapping("/getUserLivingRoom")
    @Operation(summary = "主播获取当前正在开播的直播间信息")
    public ServerResponseEntity<LiveRoomVO> getUserLivingRoom() {
        String userId = SecurityUtils.getUser().getUserId();
        LiveRoomVO livingRoom = getLivingRoom(userId);
        return ServerResponseEntity.success(livingRoom);
    }

    private LiveRoomVO getLivingRoom(String userId) {
        LiveRoomVO livingRoom = liveRoomService.getByUserId(userId);
        if (Objects.isNull(livingRoom)) {
            // 当前未有直播权限
            throw new YamiShopBindException("yami.live.user.auth.not.exist");
        }
        long currentTimeMillis = System.currentTimeMillis();
        if (livingRoom.getStartTime().getTime() > currentTimeMillis) {
            // 未到直播开始时间
            throw new YamiShopBindException("yami.live.user.auth.not.start");
        }
        return livingRoom;
    }

    @GetMapping("/getPushUrl")
    @Operation(summary = "获取推流地址", description = "获取推流地址")
    public ServerResponseEntity<String> getPushUrl() {
        LiveConfig liveConfig = shopConfig.getLiveConfig();
        if (Objects.isNull(liveConfig) || StrUtil.isBlank(liveConfig.getAppName())) {
            // 无法获取到直播配置
            throw new YamiShopBindException("yami.live.config.not.exist");
        }
        String userId = SecurityUtils.getUser().getUserId();
        LiveRoomVO livingRoom = getLivingRoom(userId);
        String pushUrl = null;
        if (liveConfig.getLiveType() == 1) {
            // 不超过256字符 支持数字、大小写字母、短划线(-)、下划线(_)、等号(=)
            // {appName}-{roomId}-{shopId}-{userId}
            String streamName = StrUtil.format("{}-{}-{}-{}", liveConfig.getAppName(), livingRoom.getRoomId(), livingRoom.getShopId(), livingRoom.getUserId());
            pushUrl = AliLiveUtil.getPushUrl(streamName, livingRoom.getEndTime(), BeanUtil.map(liveConfig, AliLive.class));
        } else {
            // 推荐用随机数字或数字与字母组合
            // {appName}+{roomId}+{shopId}+{userId}
            String streamName = liveConfig.getAppName() + livingRoom.getRoomId() + livingRoom.getShopId() + livingRoom.getUserId();
            pushUrl = TencentLiveUtil.getPushUrl(streamName, livingRoom.getEndTime(), BeanUtil.map(liveConfig, TencentLive.class));
        }
        return ServerResponseEntity.success(pushUrl);
    }
}
