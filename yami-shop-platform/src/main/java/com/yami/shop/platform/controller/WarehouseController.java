package com.yami.shop.platform.controller;

import com.yami.shop.bean.vo.WarehouseVO;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.service.WarehouseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 *
 * <AUTHOR>
 * @date 2023-11-08 11:11:44
 */
@RestController("platformWarehouseController")
@RequestMapping("/p/warehouse")
@Tag(name = "平台仓库管理")
@AllArgsConstructor
public class WarehouseController {

    private final WarehouseService warehouseService;


    @GetMapping
    @Operation(summary = "获取仓库", description = "根据warehouseId获取")
    public ServerResponseEntity<WarehouseVO> getByWarehouseId() {
        return ServerResponseEntity.success(warehouseService.getDefaultWarehouseByShopId(AuthUserContext.get().getShopId(), AuthUserContext.get().getSysType().value()));
    }
}
