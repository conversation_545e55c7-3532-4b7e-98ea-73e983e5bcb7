package com.yami.shop.distribution.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.distribution.common.dto.DistributionMsgDto;
import com.yami.shop.distribution.common.model.DistributionMsg;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface DistributionMsgMapper extends BaseMapper<DistributionMsg> {
    /**
     * 查询分销通知
     * @param page 分页信息
     * @param distributionMsg 查询参数
     * @return 分销通知列表
     */
    IPage<DistributionMsg> getDistributionMsgsAndSysUserPage(Page page, @Param("distributionMsg") DistributionMsg distributionMsg);

    /**
     * 查询分销通知
     * @param page 分页信息
     * @param isTop 是否置顶
     * @return 分销通知列表
     */
    IPage<DistributionMsgDto> getDistributionMsgDtoByShopId(Page page, @Param("isTop") Integer isTop);

    /**
     * 获取分销通知详情
     * @param msgId 消息id
     * @return 分销通知详情
     */
    DistributionMsgDto getDistributionMsgDtoByMsgId(@Param("msgId") Long msgId);
}
