package com.yami.shop.common.enums;


/**
 * 自提门店库存状态
 * <AUTHOR>
 */
public enum StationStockStatus {
    /**
     * 没有自提门店列表
     */
    NOT_STATION_LIST(0),
    /**
     * 该区域自提门店没有库存
     */
    NOT_STOCK(1),
    /**
     * 门店库存充足
     */
    HAS_STOCK(2),

    ;

    private final Integer status;


    StationStockStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }
}
