package com.yami.shop.bean.dto.allinpay;

import com.yami.shop.common.allinpay.constant.PicType;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class IdCardCollectDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "商户号")
    private String bizUserId;

    @Schema(description = "影印件核对结果异步通知地址 前端不填")
    private String ocrComparisonResultBackUrl;

    /**
     * @see PicType
     */
    @Schema(description = "影印件类型 1.营业执照 8.身份证正面（人像面） 9.身份证反面（国徽面）")
    private Integer picType;

    @Schema(description = "影印件图片 图片大小不超过 700k 图片格式 jpg、png")
    private String picture;

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getOcrComparisonResultBackUrl() {
        return ocrComparisonResultBackUrl;
    }

    public void setOcrComparisonResultBackUrl(String ocrComparisonResultBackUrl) {
        this.ocrComparisonResultBackUrl = ocrComparisonResultBackUrl;
    }

    public Integer getPicType() {
        return picType;
    }

    public void setPicType(Integer picType) {
        this.picType = picType;
    }

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    @Override
    public String toString() {
        return "IdCardCollectDTO{" +
                "bizUserId='" + bizUserId + '\'' +
                ", ocrComparisonResultBackUrl='" + ocrComparisonResultBackUrl + '\'' +
                ", picType=" + picType +
                ", picture='" + picture + '\'' +
                '}';
    }
}
