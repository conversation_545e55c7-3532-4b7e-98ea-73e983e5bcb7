package com.yami.shop.common.allinpay.member.req;

import com.yami.shop.common.allinpay.member.constant.IdentityType;
import com.yami.shop.common.allinpay.member.constant.PageType;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-03-23
 */
public class UpdatePhoneByPayPwd extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "updatePhoneByPayPwd";

    /**
     * 跳转页面类型（默认H5）
     *
     * @see PageType
     */
    private Long jumpPageType = PageType.H5.value();

    /**
     * 姓名
     */
    private String name;

    /**
     * 证件类型（暂时只能身份证）
     *
     * @see IdentityType
     */
    private Integer identityType = IdentityType.ID_CARD.value();

    /**
     * 身份证号
     */
    private String identityNo;

    /**
     * 原手机号
     */
    private String oldPhone;

    /**
     * 成功后跳转地址
     */
    private String jumpUrl;

    /**
     * 失败后跳转地址
     */
    private String errorJumpUrl;

    /**
     * 通知地址
     */
    private String backUrl;

    public Long getJumpPageType() {
        return jumpPageType;
    }

    public void setJumpPageType(Long jumpPageType) {
        this.jumpPageType = jumpPageType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getIdentityType() {
        return identityType;
    }

    public void setIdentityType(Integer identityType) {
        this.identityType = identityType;
    }

    public String getIdentityNo() {
        return identityNo;
    }

    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo;
    }

    public String getOldPhone() {
        return oldPhone;
    }

    public void setOldPhone(String oldPhone) {
        this.oldPhone = oldPhone;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getErrorJumpUrl() {
        return errorJumpUrl;
    }

    public void setErrorJumpUrl(String errorJumpUrl) {
        this.errorJumpUrl = errorJumpUrl;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    @Override
    public String toString() {
        return "UpdatePhoneByPayPwd{" +
                "jumpPageType=" + jumpPageType +
                ", name='" + name + '\'' +
                ", identityType=" + identityType +
                ", identityNo='" + identityNo + '\'' +
                ", oldPhone='" + oldPhone + '\'' +
                ", jumpUrl='" + jumpUrl + '\'' +
                ", errorJumpUrl='" + errorJumpUrl + '\'' +
                ", backUrl='" + backUrl + '\'' +
                ", bizUserId='" + bizUserId + '\'' +
                '}';
    }
}
