<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.distribution.common.dao.DistributionUserMapper">
    <resultMap id="BaseResultMap" type="com.yami.shop.distribution.common.model.DistributionUser">
        <id column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="card_no" jdbcType="BIGINT" property="cardNo"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="parent_ids" jdbcType="VARCHAR" property="parentIds"/>
        <result column="grade" jdbcType="INTEGER" property="grade"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
        <result column="user_mobile" jdbcType="VARCHAR" property="userMobile"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="identity_card_number" jdbcType="VARCHAR" property="identityCardNumber"/>
        <result column="identity_card_pic_front" jdbcType="VARCHAR" property="identityCardPicFront"/>
        <result column="identity_card_pic_back" jdbcType="VARCHAR" property="identityCardPicBack"/>
        <result column="identity_card_pic_hold" jdbcType="VARCHAR" property="identityCardPicHold"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
    </resultMap>


    <!--业绩统计-->
    <resultMap id="distributionUser_Achievement" type="com.yami.shop.distribution.common.model.DistributionUser">
        <id column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId"/>
        <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
        <result column="user_mobile" jdbcType="VARCHAR" property="userMobile"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <association property="distributionUserAchievementDataDto"
                     javaType="com.yami.shop.distribution.common.dto.DistributionUserAchievementDataDto">
            <association property="distributionUserIncome"
                         javaType="com.yami.shop.distribution.common.vo.DistributionUserIncomeVO">
                <result property="aGenerationCommission" jdbcType="DECIMAL" column="a_generation_commission"/>
                <result property="secondGenerationCommission" jdbcType="DECIMAL" column="second_generation_commission"/>
                <result property="invitationRewards" jdbcType="DECIMAL" column="invitation_rewards"/>
            </association>
            <association property="distributionUserWallet"
                         javaType="com.yami.shop.distribution.common.dto.DistributionUserWalletDto">
                <result property="addupAmount" jdbcType="DECIMAL" column="addup_amount"/>
                <result property="unsettledAmount" jdbcType="DECIMAL" column="unsettled_amount"/>
                <result property="settledAmount" jdbcType="DECIMAL" column="settled_amount"/>
                <result column="invalid_amount" jdbcType="DECIMAL" property="invalidAmount"/>
            </association>
        </association>
    </resultMap>

    <!--<select id="getParentIdsByDistributionUserId" resultType="string">-->
    <!--select parent_ids from tz_distribution_user where distribution_user_id = #{distributionUserId}-->
    <!--</select>-->


    <select id="getDistributionUserAchievementPage" resultMap="distributionUser_Achievement">
        SELECT
        du.distribution_user_id,
        du.nick_name,
        du.user_mobile,
        du.state,
        da.addup_amount as addup_amount,
        da.settled_amount as settled_amount,
        da.unsettled_amount as unsettled_amount,
        da.invalid_amount as invalid_amount,
        sum( CASE WHEN dui.income_type = 1 THEN dui.income_amount ELSE 0 END ) AS a_generation_commission,
        sum( CASE WHEN dui.income_type = 2 THEN dui.income_amount ELSE 0 END ) AS second_generation_commission,
        sum( CASE WHEN dui.income_type = 3 THEN dui.income_amount ELSE 0 END ) AS invitation_rewards
        FROM
        tz_distribution_user du
        LEFT JOIN tz_distribution_user_wallet da ON da.distribution_user_id = du.distribution_user_id
        LEFT JOIN tz_distribution_user_income dui ON dui.wallet_id = da.wallet_id AND dui.state != 0 AND dui.state != -1 AND dui.pay_sys_type = #{paySysType}
        WHERE
            du.state IN (-1,1,2)
            <if test="distributionUser.state != null">
                and du.state = #{distributionUser.state}
            </if>
            <if test="distributionUser.shopId!= null">
                and du.shop_id = #{distributionUser.shopId}
            </if>
            <if test="userMobile!= null and userMobile!=''">
                and du.user_mobile like concat('%',#{userMobile},'%')
            </if>
            <if test="distributionUser.nickName != null and distributionUser.nickName != ''">
                AND du.nick_name LIKE CONCAT( '%', #{distributionUser.nickName}, '%' )
            </if>
        GROUP BY
        du.distribution_user_id
        <if test="distributionUser.sortParam != 0 and distributionUser.sortType != 0">
            ORDER BY
            <choose>
                <when test="distributionUser.sortParam == 1">
                    a_generation_commission
                </when>
                <when test="distributionUser.sortParam == 2">
                    second_generation_commission
                </when>
                <when test="distributionUser.sortParam == 3">
                    invitation_rewards
                </when>
                <when test="distributionUser.sortParam == 4">
                    unsettled_amount
                </when>
                <when test="distributionUser.sortParam == 5">
                    settled_amount
                </when>
                <when test="distributionUser.sortParam == 6">
                    invalid_amount
                </when>
                <otherwise>
                    distribution_user_id
                </otherwise>
            </choose>
            <choose>
                <when test="distributionUser.sortType == 1">
                    ASC
                </when>
                <when test="distributionUser.sortType == 2">
                    DESC
                </when>
            </choose>
        </if>
    </select>


    <!--获取分销员与等级条件匹配的数据-->
    <resultMap id="DistributionUserAchievementData"
               type="com.yami.shop.distribution.common.dto.DistributionUserAchievementDataDto">
        <result property="boundCustomers" jdbcType="INTEGER" column="bound_customers"/>
        <result property="invitedVeeker" jdbcType="INTEGER" column="invited_veeker"/>
        <result property="payNumber" jdbcType="INTEGER" column="pay_number"/>
        <result property="successOrderNumber" jdbcType="INTEGER" column="success_order_number"/>
        <result property="successTradingVolume" jdbcType="DECIMAL" column="success_trading_volume"/>
        <result property="sumOfConsumption" jdbcType="DECIMAL" column="sum_of_consumption"/>

        <association property="distributionUserWallet"
                     javaType="com.yami.shop.distribution.common.dto.DistributionUserWalletDto">
            <result property="addupAmount" jdbcType="DECIMAL" column="addup_amount"/>
        </association>
    </resultMap>
    <!--获取分销员与等级条件匹配的数据-->
    <select id="getLevelDistributionUserAchievementDataByDistributionUserId"
            resultMap="DistributionUserAchievementData">
        select
            -- 积累绑定客户数
            count(distinct dub.user_id)                 as bound_customers,
            -- 积累邀请分销员数
            count(distinct sub_du.distribution_user_id) as invited_veeker,
            -- 积累支付单数
            count(distinct o.order_id)                  as pay_number,
            -- 积累成功成交单数
            count(distinct dui.income_id)               as success_order_number,
            -- 积累成功成交金额
            ifnull(sum(oi.product_total_amount), 0)     as success_trading_volume,
            -- 总消费
            sum(o.actual_total)                         as sum_of_consumption,
            -- 积累收益
            duw.addup_amount                as addup_amount
        from tz_distribution_user du
                 -- 绑定客户信息
                 left join tz_distribution_user_bind dub on dub.distribution_user_id = du.distribution_user_id
            -- 下级信息
                 left join tz_distribution_user sub_du on sub_du.parent_id = du.distribution_user_id
            -- 消费信息
                 left join tz_order o on o.user_id = du.user_id and (o.status = 4 or o.status = 5)
            -- 钱包
                 left join tz_distribution_user_wallet duw on duw.distribution_user_id = du.distribution_user_id
            -- 收益信息(限一级佣金和成功的订单)
                 left join tz_distribution_user_income dui
                           on dui.wallet_id = duw.wallet_id and dui.income_type = 1 and dui.state = 2
            -- 成交的订单项
                 left join tz_order_item oi on oi.order_item_id = dui.order_item_id
        where du.distribution_user_id = #{distributionUserId}
    </select>


    <!--分销员管理页面-->
    <resultMap id="distributionUserPageMap" type="com.yami.shop.distribution.common.model.DistributionUser">
        <id column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="card_no" jdbcType="BIGINT" property="cardNo"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="parent_ids" jdbcType="VARCHAR" property="parentIds"/>
        <result column="grade" jdbcType="INTEGER" property="grade"/>
        <result column="level" jdbcType="BIGINT" property="level"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
        <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
        <result column="user_mobile" jdbcType="VARCHAR" property="userMobile"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="identity_card_number" jdbcType="VARCHAR" property="identityCardNumber"/>
        <result column="identity_card_pic_front" jdbcType="VARCHAR" property="identityCardPicFront"/>
        <result column="identity_card_pic_back" jdbcType="VARCHAR" property="identityCardPicBack"/>
        <result column="identity_card_pic_hold" jdbcType="VARCHAR" property="identityCardPicHold"/>
        <!--上级分销员-->
        <association property="parentDistributionUser"
                     javaType="com.yami.shop.distribution.common.vo.DistributionUserVO">
            <result column="parent_nick_name" jdbcType="VARCHAR" property="nickName"/>
            <result column="parent_user_mobile" jdbcType="VARCHAR" property="userMobile"/>
        </association>
        <!--分销员数据-->
        <association property="distributionUserAchievementDataDto"
                     javaType="com.yami.shop.distribution.common.dto.DistributionUserAchievementDataDto">
            <result property="boundCustomers" jdbcType="INTEGER" column="bound_customers"/>
            <result property="invitedVeeker" jdbcType="INTEGER" column="invited_veeker"/>
            <!--分销员钱包-->
            <association property="distributionUserWallet"
                         javaType="com.yami.shop.distribution.common.dto.DistributionUserWalletDto">
                <result property="addupAmount" jdbcType="DECIMAL" column="addup_amount"/>
            </association>
        </association>
    </resultMap>
    <select id="distributionUserPage" resultMap="distributionUserPageMap">
        select
        -- 分销员信息
        du.distribution_user_id,
        du.shop_id,
        du.card_no,
        du.parent_id,
        du.grade,
        du.level,
        du.group_id,
        du.bind_time,
        du.state,
        du.nick_name,
        du.user_mobile,
        du.real_name,
        du.identity_card_number,
        du.identity_card_pic_front,
        du.identity_card_pic_back,
        du.identity_card_pic_hold,
        -- 上级分销员信息
        pdu.user_mobile as parent_user_mobile,
        pdu.nick_name as parent_nick_name,
        -- 积累客户
        ifnull(MAX(dub.bound_customers),0) AS bound_customers,
        -- 积累邀请
        ifnull(count(da.distribution_user_id),0) as invited_veeker,
        -- 积累收益
        ifnull(duw.addup_amount,0) as addup_amount
        from tz_distribution_user du
        left join tz_distribution_user_wallet duw on duw.distribution_user_id = du.distribution_user_id
        left join tz_distribution_user pdu on pdu.distribution_user_id=du.parent_id
        LEFT JOIN (
        SELECT dub.distribution_user_id,COUNT(dub.distribution_user_id) AS bound_customers FROM
        tz_distribution_user_bind dub
        WHERE dub.state = 1
        GROUP BY dub.distribution_user_id
        ) AS dub ON dub.distribution_user_id = du.distribution_user_id
        left join tz_distribution_auditing da on da.parent_distribution_user_id=du.distribution_user_id and da.state = 1
        WHERE
            du.state != 0
            and du.state != 3
            <if test="state != null">
                and du.state=#{state}
            </if>
            <if test="distributionUser.shopId != null">
                and du.shop_id=#{distributionUser.shopId}
            </if>
            <if test="mobile!= null and mobile != ''">
                and du.user_mobile LIKE concat('%',#{mobile},'%')
            </if>
            <if test="parentMobile!= null and parentMobile != ''">
                and pdu.user_mobile LIKE concat('%',#{parentMobile},'%')
            </if>
            <if test="distributionUser.nickName != null and distributionUser.nickName != ''">
                AND du.nick_name LIKE CONCAT( '%', #{distributionUser.nickName}, '%' )
            </if>
            <if test="rangeTimeParam.startTime!= null">
                and du.bind_time &gt;= #{rangeTimeParam.startTime}
            </if>
            <if test="rangeTimeParam.endTime!= null ">
                and du.bind_time &lt;= #{rangeTimeParam.endTime}
            </if>
        GROUP BY du.distribution_user_id
        <if test="distributionUser.sortParam != 0 and distributionUser.sortType != 0">
            ORDER BY
            <choose>
                <when test="distributionUser.sortParam == 1">
                    bind_time
                </when>
                <when test="distributionUser.sortParam == 2">
                    bound_customers
                </when>
                <when test="distributionUser.sortParam == 3">
                    invited_veeker
                </when>
                <when test="distributionUser.sortParam == 4">
                    addup_amount
                </when>
                <otherwise>
                    distribution_user_id
                </otherwise>
            </choose>
            <choose>
                <when test="distributionUser.sortType == 1">
                    ASC
                </when>
                <when test="distributionUser.sortType == 2">
                    DESC
                </when>
            </choose>
        </if>
    </select>

    <!--分销中心-->
    <resultMap id="DistributionUserAchievementDataAppDto"
               type="com.yami.shop.distribution.common.dto.AchievementDataDto">
        <result property="boundCustomers" column="bound_customers"/>
        <result property="invitedVeeker" column="invited_veeker"/>
        <result property="orderCount" column="order_number"/>
        <association property="distributionUserWallet"
                     javaType="com.yami.shop.distribution.common.dto.DistributionUserWalletDto">
            <result column="unsettled_amount" jdbcType="DECIMAL" property="unsettledAmount"/>
            <result column="settled_amount" jdbcType="DECIMAL" property="settledAmount"/>
            <result column="addup_amount" jdbcType="DECIMAL" property="addupAmount"/>
            <result column="invalid_amount" jdbcType="DECIMAL" property="invalidAmount"/>
            <result column="extracted_amount" jdbcType="DECIMAL" property="extractedAmount"/>
            <result column="apply_withdraw_amount" jdbcType="DECIMAL" property="applyWithdrawAmount"/>
        </association>
    </resultMap>
    <select id="getAchievementDataDtoById" resultMap="DistributionUserAchievementDataAppDto">
        SELECT temp.*,
               SUM( CASE WHEN temp.state = 1 THEN temp.amount ELSE 0 END ) AS extracted_amount,
               SUM( CASE WHEN temp.state = 0 THEN temp.amount ELSE 0 END ) AS apply_withdraw_amount
        FROM
            (SELECT duw.unsettled_amount,
               duw.settled_amount,
               duw.addup_amount,
               duw.invalid_amount,
               bud.bound_customers,
               pdu.invited_veeker,
               dui.order_number,
               dwc.`amount`,
               dwc.`state`
        FROM tz_distribution_user_wallet duw
        LEFT JOIN tz_distribution_withdraw_cash dwc ON duw.wallet_id = dwc.wallet_id,
             (SELECT count(distribution_user_id) AS bound_customers
              FROM tz_distribution_user_bind
              WHERE distribution_user_id = #{id}) AS bud,
             (SELECT count(distribution_user_id) AS invited_veeker
              FROM tz_distribution_user
              WHERE parent_id = #{id}) AS pdu,
             (SELECT count(distribution_user_id) AS order_number
              FROM tz_distribution_user_income
              WHERE distribution_user_id = #{id}) AS dui
        WHERE duw.distribution_user_id = #{id}) AS temp
    </select>

    <!--<select id="getParentDistributionUserIdByDistributionUserId" resultType="long">-->
    <!--select parent_user_id from tz_distribution_user-->
    <!--where distribution_user_id = #{distributionUserId}-->
    <!--</select>-->

    <!--<select id="getUserIdByDistributionUserId" resultType="string">-->
    <!--select user_id from tz_distribution_user-->
    <!--where distribution_user_id = #{distributionUserId}-->
    <!--</select>-->

    <select id="getDistributionUserSimpleDtoByParentUserIdPage"
            resultType="com.yami.shop.distribution.common.dto.DistributionUserSimpleDto">
        select distribution_user_id,
               nick_name,
               pic,
               bind_time,
               real_name
        from tz_distribution_user
        where parent_id = #{parentDistributionUserId}
    </select>

    <update id="updateStatusById">
        update
        tz_distribution_user
        set
        `state` = #{distributionUser.state},bind_time = #{distributionUser.bindTime},state_record =
        #{distributionUser.stateRecord},
        <if test="distributionUser.level != null">
            `level`= #{distributionUser.level}
        </if>
        where distribution_user_id = #{distributionUser.distributionUserId}
    </update>
    <update id="updateParentIdById">
        update tz_distribution_user
        SET parent_id=NULL,
            parent_ids = replace(parent_ids, concat(',', #{distributionUserId}), ''),
            grade=grade - 1
        WHERE parent_id = #{distributionUserId}
    </update>
    <select id="getDistributionUserByIdCardNumberAndUserMobile"
            resultType="com.yami.shop.distribution.common.model.DistributionUser">
        SELECT identity_card_number, user_mobile
        FROM tz_distribution_user
        WHERE (identity_card_number = #{identityCardNumber}
           OR user_mobile = #{userMobile}) AND state != 3
    </select>
    <select id="selectDistributionOrderId" resultType="java.lang.String">
        SELECT o.order_number
        FROM tz_distribution_user u
        LEFT JOIN tz_order_item oi ON u.card_no = oi.distribution_card_no
        LEFT JOIN tz_order o ON oi.order_number = o.order_number
        WHERE u.card_no = #{cardNo}
        AND 1 &lt; o.status
        AND u.state = 1
    </select>

    <select id="getSettleInfo" resultType="com.yami.shop.distribution.common.vo.DistributionSettleInfoVO">
        SELECT
            IFNULL( SUM( CASE WHEN dui.`state` = 1 THEN `income_amount` ELSE 0 END ), 0 ) AS unSettledAmount,
            IFNULL( SUM( CASE WHEN dui.`state` = 2 THEN `income_amount` ELSE 0 END ), 0 ) AS settledAmount
        FROM
            tz_distribution_user_income dui
            JOIN tz_distribution_user du ON dui.distribution_user_id = du.distribution_user_id
            AND du.user_id = #{userId}
        WHERE
            dui.pay_sys_type = #{paySysType}
    </select>

    <select id="getByCardNo" resultType="com.yami.shop.distribution.common.model.DistributionUser">
        SELECT
            *
        FROM
            tz_distribution_user
        WHERE
            BINARY card_no = #{cardNo}
    </select>
</mapper>
