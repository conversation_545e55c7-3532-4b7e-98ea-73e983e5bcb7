package com.yami.shop.discount.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yami.shop.discount.common.dao.DiscountProdMapper;
import com.yami.shop.discount.common.model.DiscountProd;
import com.yami.shop.discount.common.service.DiscountProdService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> on 2018/11/21.
 */
@Service
@AllArgsConstructor
public class DiscountProdServiceImpl extends ServiceImpl<DiscountProdMapper, DiscountProd> implements DiscountProdService {

    private final DiscountProdMapper discountProdMapper;

}
