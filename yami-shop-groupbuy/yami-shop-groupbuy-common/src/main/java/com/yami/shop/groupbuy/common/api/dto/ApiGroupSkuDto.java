package com.yami.shop.groupbuy.common.api.dto;

import com.yami.shop.bean.vo.SkuComboVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/30 10:25
 */
@Data
public class ApiGroupSkuDto {

    @Schema(description = "拼团活动商品规格id" )
    private Long groupSkuId;

    @Schema(description = "商品规格Id" )
    private Long skuId;

    @Schema(description = "原售价格" )
    private Double price;

    @Schema(description = "活动价格" )
    private Double actPrice;

    @Schema(description = "sku名称" )
    private String skuName;

    @Schema(description = "已售数量" )
    private String sellNum;

    @Schema(description = "sku图片" )
    private String pic;

    @Schema(description = "销售属性组合字符串 格式是p1:v1;p2:v2" )
    private String properties;

    @Schema(description = "sku状态： 1启用 0禁用" )
    private Integer status;

    @Schema(description = "组合商品列表")
    private List<SkuComboVO> skuComboList;
}
