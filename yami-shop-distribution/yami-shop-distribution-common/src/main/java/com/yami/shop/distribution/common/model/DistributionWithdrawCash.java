package com.yami.shop.distribution.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.yami.shop.distribution.common.vo.DistributionUserVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
/**
 * <AUTHOR>
 */
@Data
@TableName( "tz_distribution_withdraw_cash")
public class DistributionWithdrawCash {

    @TableId
    @Schema(description = "提现记录id" )
    private Long withdrawCashId;

    @Schema(description = "钱包id" )
    private Long walletId;

    @Schema(description = "金额" )
    private Double amount;

    @Schema(description = "手续费" )
    private Double fee;

    @Schema(description = "类型(0 手动提现 1自动提现)" )
    private Integer type;

    @Schema(description = "资金流向(1企业付款到微信零钱)" )
    private Integer moneyFlow;

    @Schema(description = "流水号" )
    private String merchantOrderId;

    @Version
    @Schema(description = "乐观锁" )
    private Integer version;

    @Schema(description = "创建时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "更新时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "提现状态(0:申请中 1:提现成功 2:拒绝提现 -1:提现失败)" )
    private Integer state;

    @Schema(description = "第三方登录userId" )
    private String bizUserId;

    @Schema(description = "关联分销员" )
    @TableField(exist = false)
    private DistributionUserVO distributionUser;

    @Schema(description = "排序字段 分销钱包-提现记录：0无 1金额 2手续费 3提现时间 4更新时间" )
    @TableField(exist = false)
    private Integer sortParam = 0;

    @Schema(description = "排序类型 0无 1 正序 2倒序" )
    @TableField(exist = false)
    private Integer sortType = 0;
}
