package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/12 9:23
 */
@Data
@Schema(description = "赠品vo")
public class GiveawayVO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "赠品id" )
    private Long giveawayId;

    @Schema(description = "赠品名称" )
    private String name;

    @Schema(description = "主商品id" )
    private Long prodId;

    @Schema(description = "状态 -1：已删除 0：关闭 1:开启" )
    private Integer status;

    @Schema(description = "开始时间" )
    private Date startTime;

    @Schema(description = "结束时间" )
    private Date endTime;

    @Schema(description = "购买数量（购买了多少件才赠送赠品）" )
    private Integer buyNum;

    @Schema(description = "赠送商品列表" )
    private List<GiveawayProdVO> giveawayProds;
}
