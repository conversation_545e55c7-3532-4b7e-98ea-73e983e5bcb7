package com.yami.shop.groupbuy.common.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 拼团商品规格详细信息
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "拼团商品规格详细信息")
public class ApiGroupSkuInfoDto {

    @Schema(description = "拼团活动商品规格id" )
    private Long groupSkuId;

    @Schema(description = "拼团活动商品id" )
    private Long groupProdId;

    @Schema(description = "商品规格id" )
    private Long skuId;

    @Schema(description = "商品规格id" )
    private Long categoryId;

    @Schema(description = "规格原价" )
    private Double price;

    @Schema(description = "活动价格" )
    private Double actPrice;

    @Schema(description = "规格名称" )
    private String skuName;

    @Schema(description = "sku库存" )
    private Integer stocks;

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "商品图片" )
    private String prodPic;

    @Schema(description = "规格图片" )
    private String skuPic;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "店铺名称" )
    private String shopName;

    @Schema(description = "商品状态（-1:删除、0:商家下架、1:上架、2:违规下架、3:平台审核）" )
    private int prodStatus;

    /**
     * 虚拟商品信息
     */
    @Schema(description = "商品类别 0.实物商品 1. 虚拟商品 2.组合商品" )
    private Integer mold = 0;

    @Schema(description = "虚拟商品的留言备注" )
    private String virtualRemark;

    @Schema(description = "核销次数类型 -1.多次核销 0.无需核销 1.单次核销" )
    private Integer writeOffNum;

    @Schema(description = "多次核销次数 -1.无限次" )
    private Integer writeOffMultipleCount;

    @Schema(description = "核销有效期 -1.长期有效 0.自定义  x.x天内有效" )
    private Integer writeOffTime;

    @Schema(description = "核销开始时间" )
    private Date writeOffStart;

    @Schema(description = "核销结束时间" )
    private Date writeOffEnd;

    @Schema(description = "是否可以退款 1.可以 0不可以" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer isRefund;
}
