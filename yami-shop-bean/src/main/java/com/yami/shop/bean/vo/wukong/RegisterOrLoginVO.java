package com.yami.shop.bean.vo.wukong;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RegisterOrLoginVO {

    @Schema(description = "通信的用户唯一ID，可以随机uuid（建议自己服务端的用户唯一uid）")
    private String uid;

    @Schema(description = "校验的token，随机uuid")
    private String token;

    @Schema(description = "用户昵称")
    private String nickName;

    public RegisterOrLoginVO() {
    }

    public RegisterOrLoginVO(String uid, String token) {
        this.uid = uid;
        this.token = token;
    }
}
