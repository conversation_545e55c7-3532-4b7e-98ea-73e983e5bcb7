package com.yami.shop.common.allinpay.member.req;

import cn.hutool.poi.word.PicType;

import java.io.Serial;
import java.io.Serializable;

/**
 * 影印件核对
 *
 * <AUTHOR>
 */
public class IdCardCollect extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    public static final String METHOD_NAME = "idcardCollect";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    private String bizUserId;

    /**
     * 影印件核对结果异步通知地址
     */
    private String ocrComparisonResultBackUrl;

    /**
     * 影印件类型
     *
     * @see PicType
     */
    private Integer picType;

    /**
     * 影印件图片
     * 影印件图片的 base64 码
     * 图片大小不超过 700k
     * 图片格式 jpg、png
     */
    private String picture;

    @Override
    public String getBizUserId() {
        return bizUserId;
    }

    @Override
    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getOcrComparisonResultBackUrl() {
        return ocrComparisonResultBackUrl;
    }

    public void setOcrComparisonResultBackUrl(String ocrComparisonResultBackUrl) {
        this.ocrComparisonResultBackUrl = ocrComparisonResultBackUrl;
    }

    public Integer getPicType() {
        return picType;
    }

    public void setPicType(Integer picType) {
        this.picType = picType;
    }

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    @Override
    public String toString() {
        return "IdCardCollect{" +
                "bizUserId='" + bizUserId + '\'' +
                ", ocrComparisonResultBackUrl='" + ocrComparisonResultBackUrl + '\'' +
                ", picType=" + picType +
                ", picture='" + picture + '\'' +
                '}';
    }
}
