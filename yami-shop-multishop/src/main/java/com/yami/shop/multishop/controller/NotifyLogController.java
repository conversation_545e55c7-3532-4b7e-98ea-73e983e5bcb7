package com.yami.shop.multishop.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.enums.RemindType;
import com.yami.shop.bean.model.NotifyLog;
import com.yami.shop.bean.param.OrderParam;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.NotifyLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 *
 *
 * <AUTHOR>
 * @date 2020-07-05 16:19:10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/multishop/notifyLog" )
@Tag(name = "消息日志接口")
public class NotifyLogController {

    private final NotifyLogService notifyLogService;


    @GetMapping("/page")
    @Operation(summary = "分页查询消息日志" , description = "分页查询消息日志")
    @PreAuthorize("@pms.hasPermission('shop:notifyLog:page')")
    public ServerResponseEntity<IPage<NotifyLog>> getSmsRechargeLogPage(PageParam<NotifyLog> page, OrderParam orderParam) {
        return ServerResponseEntity.success(notifyLogService.page(page, new LambdaQueryWrapper<NotifyLog>()
                .eq(NotifyLog::getShopId, SecurityUtils.getShopUser().getShopId())
                .eq(NotifyLog::getRemindType, RemindType.SMS.value())
                .gt(Objects.nonNull(orderParam.getStartTime()),NotifyLog::getCreateTime,orderParam.getStartTime())
                .lt(Objects.nonNull(orderParam.getEndTime()),NotifyLog::getCreateTime,orderParam.getEndTime())
                .orderByDesc(NotifyLog::getCreateTime)));
    }

    @GetMapping("/unReadCount" )
    @Operation(summary = "查询未读消息数量" , description = "查询未读消息数量")
    @PreAuthorize("@pms.hasPermission('shop:notifyLog:count')")
    public ServerResponseEntity<Long> getNotifyCount() {
        return ServerResponseEntity.success(notifyLogService.count(new LambdaQueryWrapper<NotifyLog>()
                .eq(NotifyLog::getRemindId, SecurityUtils.getShopUser().getShopId())
                .eq(NotifyLog::getRemindType, 1)
                .ge(NotifyLog::getSendType, 100)
                .eq(NotifyLog::getStatus,0)));
    }

    @GetMapping("/unReadCountList" )
    @Operation(summary = "查询未读消息列表" , description = "查询未读消息列表")
    @PreAuthorize("@pms.hasPermission('shop:notifyLog:page')")
    public ServerResponseEntity<IPage<NotifyLog>> getUnReadCountList(PageParam<NotifyLog> page, NotifyLog notifyLog) {
        notifyLogService.pageByNotifyLogParam(page, notifyLog);
        IPage<NotifyLog> notifyLogs = notifyLogService.page(page,new LambdaQueryWrapper<NotifyLog>()
                .eq(NotifyLog::getRemindId, SecurityUtils.getShopUser().getShopId())
                .eq(NotifyLog::getRemindType, 1)
                .ge(NotifyLog::getSendType, 100)
                .orderByAsc(NotifyLog::getStatus)
                .orderByDesc(NotifyLog::getCreateTime));

        List<NotifyLog> recordsDb = notifyLogs.getRecords();
        List<NotifyLog> records = new ArrayList<>();
        for (NotifyLog record : recordsDb) {
            NotifyLog remind = BeanUtil.map(record,NotifyLog.class);
            record.setStatus(1);
            records.add(remind);
        }
        notifyLogs.setRecords(records);
        if(CollectionUtils.isNotEmpty(recordsDb)) {
            notifyLogService.updateBatchById(recordsDb);
        }
        return ServerResponseEntity.success(notifyLogs);
    }

    @GetMapping("/pageByNotifyLogParam")
    @Operation(summary = "根据条件分页查询消息日志" , description = "根据条件分页查询消息日志")
    public ServerResponseEntity<IPage<NotifyLog>> pageByNotifyLogParam(PageParam<NotifyLog> page, NotifyLog notifyLog) {
        notifyLog.setShopId(SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success(notifyLogService.pageByNotifyLogParam(page, notifyLog));
    }

    @PutMapping("/is_read")
    @Operation(summary = "批量设置消息为已读状态 " , description = "批量设置消息为已读状态")
    @Parameters(value = {
            @Parameter(name = "logIds", description = "消息id" , required = true),
            @Parameter(name = "type", description = "数据类型 1全部 0已选")
    })
    public ServerResponseEntity<Void> isRead(@RequestBody List<Long> logIds, @RequestParam(value="type", required = false, defaultValue = "0")Integer type) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        if(Objects.equals(type, 1)){
            notifyLogService.isReadByShopId(shopId);
        }
        else if(Objects.equals(type, 0) && CollUtil.isNotEmpty(logIds)){
            notifyLogService.isReadByIds(logIds);
        }
        return ServerResponseEntity.success();
    }
}
