package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "店铺运费信息")
public class ShopTransFeeVO {
    public ShopTransFeeVO() {
    }

    public ShopTransFeeVO(Double freeTransFee, Double transFee) {
        this.freeTransFee = freeTransFee;
        this.transFee = transFee;
    }

    @Schema(description = "免运费金额" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Double freeTransFee;

    @Schema(description = "运费" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Double transFee;

    @Schema(description = "运费模板id" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long deliveryTemplateId;

}
