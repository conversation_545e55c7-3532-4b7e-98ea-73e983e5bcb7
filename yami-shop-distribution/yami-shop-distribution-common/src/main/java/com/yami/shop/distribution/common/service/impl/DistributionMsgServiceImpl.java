package com.yami.shop.distribution.common.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yami.shop.distribution.common.dao.DistributionMsgMapper;
import com.yami.shop.distribution.common.dto.DistributionMsgDto;
import com.yami.shop.distribution.common.model.DistributionMsg;
import com.yami.shop.distribution.common.service.DistributionMsgService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> on 2019/04/03.
 */
@Service
@AllArgsConstructor
public class DistributionMsgServiceImpl extends ServiceImpl<DistributionMsgMapper, DistributionMsg> implements DistributionMsgService {

    private final DistributionMsgMapper distributionMsgMapper;


    @Override
    public DistributionMsgDto getDistributionMsgDtoByMsgId(Long msgId) {
        return distributionMsgMapper.getDistributionMsgDtoByMsgId(msgId);
    }

    @Override
    public IPage<DistributionMsg> getDistributionMsgsAndSysUserPage(Page page, DistributionMsg distributionMsg) {
        return distributionMsgMapper.getDistributionMsgsAndSysUserPage(page,distributionMsg);
    }

    @Override
    public IPage<DistributionMsgDto> getDistributionMsgDtoShopId(Page page,Integer isTop) {
        return distributionMsgMapper.getDistributionMsgDtoByShopId(page,isTop);
    }
}
