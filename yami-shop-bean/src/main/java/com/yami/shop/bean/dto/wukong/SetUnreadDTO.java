package com.yami.shop.bean.dto.wukong;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

/**
 * 标为已读
 * <AUTHOR>
 */
public class SetUnreadDTO {

    @Schema(description = "频道的唯一ID")
    @NotBlank(message = "频道id不能为空")
    private String channel_id;

    @Schema(description = "频道的类型 1.个人频道 2.群聊频道")
    private Integer channel_type;

    @Schema(description = "当前用户uid")
    private String uid;

    @Schema(description = "未读消息计数")
    private Integer unread;

    @Schema(description = "已读的最大消息序号")
    private Long maxSeq;

    public String getChannel_id() {
        return channel_id;
    }

    public void setChannel_id(String channel_id) {
        this.channel_id = channel_id;
    }

    public Integer getChannel_type() {
        return channel_type;
    }

    public void setChannel_type(Integer channel_type) {
        this.channel_type = channel_type;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getUnread() {
        return unread;
    }

    public void setUnread(Integer unread) {
        this.unread = unread;
    }

    public Long getMaxSeq() {
        return maxSeq;
    }

    public void setMaxSeq(Long maxSeq) {
        this.maxSeq = maxSeq;
    }

    @Override
    public String toString() {
        return "SetUnreadDTO{" +
                "channel_id='" + channel_id + '\'' +
                ", channel_type=" + channel_type +
                ", uid='" + uid + '\'' +
                ", unread=" + unread +
                ", maxSeq=" + maxSeq +
                '}';
    }
}
