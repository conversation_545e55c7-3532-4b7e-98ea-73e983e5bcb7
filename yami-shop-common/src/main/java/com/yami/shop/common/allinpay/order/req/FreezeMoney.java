package com.yami.shop.common.allinpay.order.req;

/**
 * 冻结金额
 *
 * <AUTHOR>
 */
public class FreezeMoney extends AbstractOrderReq {
    /**
     * 商户冻结金额订单号
     */
    private String bizFreezenNo;
    /**
     * 账户集编号
     */
    private String accountSetNo;
    /**
     * 冻结金额
     */
    private Integer amount;


    public static final String METHOD_NAME = "freezeMoney";

    public FreezeMoney() {
    }

    public FreezeMoney(String bizUserId, String bizFreezenNo, String accountSetNo, Integer amount) {
        this.bizUserId = bizUserId;
        this.bizFreezenNo = bizFreezenNo;
        this.accountSetNo = accountSetNo;
        this.amount = amount;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public String getBizFreezenNo() {
        return bizFreezenNo;
    }

    public void setBizFreezenNo(String bizFreezenNo) {
        this.bizFreezenNo = bizFreezenNo;
    }

    public String getAccountSetNo() {
        return accountSetNo;
    }

    public void setAccountSetNo(String accountSetNo) {
        this.accountSetNo = accountSetNo;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }


}
