package com.yami.shop.distribution.platform.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yami.shop.bean.model.Product;
import com.yami.shop.bean.param.ProductParam;
import com.yami.shop.bean.vo.DistributionConfigVO;
import com.yami.shop.common.bean.SysConfig;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.Json;
import com.yami.shop.distribution.common.constants.DistributionAudit;
import com.yami.shop.distribution.common.dto.DistributionConfigDTO;
import com.yami.shop.distribution.common.dto.DistributionRecruitConfigDTO;
import com.yami.shop.distribution.common.vo.DistributionRecruitConfigVO;
import com.yami.shop.service.ProductService;
import com.yami.shop.service.SysConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021-08-06 14:18:03
 */
@RestController
@RequestMapping("/platform/distributionConfig")
@Tag(name = "平台端分销配置接口")
@AllArgsConstructor
public class DistributionConfigController {

    private final SysConfigService sysConfigService;
    private final ProductService productService;

    @GetMapping("/info")
    @Operation(summary = "获取分销配置信息")
    @PreAuthorize("@pms.hasPermission('platform:distributionConfig:info')")
    public ServerResponseEntity<DistributionConfigVO> info() {
        DistributionConfigVO distributionConfigVO = sysConfigService.getSysConfigObject(Constant.DISTRIBUTION_CONFIG, DistributionConfigVO.class);
        List<Long> prodIds = distributionConfigVO.getProdIdList();
        if (CollUtil.isEmpty(prodIds)) {
            return ServerResponseEntity.success(distributionConfigVO);
        }
        ProductParam product = new ProductParam();
        product.setProdIds(prodIds);
        List<Product> products = productService.listProdByIdsAndType(product);
        distributionConfigVO.setProductVOList(products);
        return ServerResponseEntity.success(distributionConfigVO);
    }

    @GetMapping("/recruit_info")
    @Operation(summary = "获取分销推广配置信息")
    @PreAuthorize("@pms.hasPermission('platform:distributionConfig:recruitInfo')")
    public ServerResponseEntity<DistributionRecruitConfigVO> recruitInfo() {
        DistributionRecruitConfigVO recruitConfigVO = sysConfigService.getSysConfigObject(Constant.DISTRIBUTION_RECRUIT_CONFIG, DistributionRecruitConfigVO.class);
        return ServerResponseEntity.success(recruitConfigVO);
    }

    @PostMapping
    @Operation(summary = "保存分销配置")
    @PreAuthorize("@pms.hasPermission('platform:distributionConfig:save')")
    public ServerResponseEntity<Void> save(@RequestBody @Valid DistributionConfigDTO distributionConfigDTO) {
        //提现发放方式： 0.无需审核直接发放，1.审核后系统发放，2.审核后人工发放 默认：审核后系统发放
        if (Objects.isNull(distributionConfigDTO.getWithdrawal())){
            distributionConfigDTO.setWithdrawal(DistributionAudit.TWithdrawals_TWO.getValue());
        }
        SysConfig config = new SysConfig();
        String paramValue = Json.toJsonString(distributionConfigDTO);
        config.setParamKey(Constant.DISTRIBUTION_CONFIG);
        config.setParamValue(paramValue);
        config.setRemark(Constant.DISTRIBUTION_REMARKS);
        if (sysConfigService.count(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getParamKey, config.getParamKey())) > 0) {
            sysConfigService.updateValueByKey(config.getParamKey(), config.getParamValue());
        } else {
            sysConfigService.save(config);
        }
        sysConfigService.removeSysConfig(config.getParamKey());
        return ServerResponseEntity.success();
    }

    @PostMapping("/recruit")
    @Operation(summary = "保存分销招募推广配置")
    @PreAuthorize("@pms.hasPermission('platform:distributionConfig:saveRecruit')")
    public ServerResponseEntity<Void> saveRecruit(@RequestBody @Valid DistributionRecruitConfigDTO distributionRecruitConfigDTO) {
        SysConfig config = new SysConfig();
        String paramValue = Json.toJsonString(distributionRecruitConfigDTO);
        config.setParamKey(Constant.DISTRIBUTION_RECRUIT_CONFIG);
        config.setParamValue(paramValue);
        config.setRemark(Constant.DISTRIBUTION_RECRUIT_REMARKS);
        if (sysConfigService.count(new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getParamKey, config.getParamKey())) > 0) {
            sysConfigService.updateValueByKey(config.getParamKey(), config.getParamValue());
        } else {
            sysConfigService.save(config);
        }
        sysConfigService.removeSysConfig(config.getParamKey());
        return ServerResponseEntity.success();
    }
}
