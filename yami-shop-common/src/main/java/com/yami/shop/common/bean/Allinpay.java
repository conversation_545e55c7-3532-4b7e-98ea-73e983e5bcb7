package com.yami.shop.common.bean;

/**
 * 通联支付
 * <AUTHOR>
 */
public class Allinpay {

    /**
     * 请求地址
     */
    private String requestUrl;

    /**
     * 应用编号
     */
    private String appId;

    /**
     * 签名密钥
     */
    private String appSecret;

    /**
     * 商户私钥
     */
    private String appPrivateKey;

    /**
     * 托管账户集编号(个人，企业会员使用)
     */
    private String accountSetNo;

    /**
     * 应用公钥证书路径
     */
    private String appCertPath;

    /**
     * 应用私钥证书路径
     */
    private String appPrivateCertPath;

    /**
     * 通联支付分配的商户号
     */
    private String vspCusId;

    /**
     * 微信子商户号
     */
    private String wechatSubAppId;

    /**
     * 支付宝子商户号
     */
    private String alipaySubAppId;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getAppPrivateKey() {
        return appPrivateKey;
    }

    public void setAppPrivateKey(String appPrivateKey) {
        this.appPrivateKey = appPrivateKey;
    }

    public String getAppCertPath() {
        return appCertPath;
    }

    public void setAppCertPath(String appCertPath) {
        this.appCertPath = appCertPath;
    }

    public String getAppPrivateCertPath() {
        return appPrivateCertPath;
    }

    public void setAppPrivateCertPath(String appPrivateCertPath) {
        this.appPrivateCertPath = appPrivateCertPath;
    }

    public String getAccountSetNo() {
        return accountSetNo;
    }

    public void setAccountSetNo(String accountSetNo) {
        this.accountSetNo = accountSetNo;
    }

    public String getRequestUrl() {
        return requestUrl;
    }

    public void setRequestUrl(String requestUrl) {
        this.requestUrl = requestUrl;
    }

    public String getVspCusId() {
        return vspCusId;
    }

    public void setVspCusId(String vspCusId) {
        this.vspCusId = vspCusId;
    }

    public String getWechatSubAppId() {
        return wechatSubAppId;
    }

    public void setWechatSubAppId(String wechatSubAppId) {
        this.wechatSubAppId = wechatSubAppId;
    }

    public String getAlipaySubAppId() {
        return alipaySubAppId;
    }

    public void setAlipaySubAppId(String alipaySubAppId) {
        this.alipaySubAppId = alipaySubAppId;
    }

    @Override
    public String toString() {
        return "Allinpay{" +
                "requestUrl='" + requestUrl + '\'' +
                ", appId='" + appId + '\'' +
                ", appSecret='" + appSecret + '\'' +
                ", appPrivateKey='" + appPrivateKey + '\'' +
                ", accountSetNo='" + accountSetNo + '\'' +
                ", appCertPath='" + appCertPath + '\'' +
                ", appPrivateCertPath='" + appPrivateCertPath + '\'' +
                ", vspCusId='" + vspCusId + '\'' +
                ", wechatSubAppId='" + wechatSubAppId + '\'' +
                ", alipaySubAppId='" + alipaySubAppId + '\'' +
                '}';
    }
}
