package com.yami.shop.distribution.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
@Data
public class DistributionUserVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分销员id
     */
    private Long distributionUserId;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 分销员手机
     */
    private String userMobile;

    /**
     * 分销员等级信息
     */
    private DistributionLevelVO distributionLevel;

    @Schema(description = "头像" )
    private String pic;



}
