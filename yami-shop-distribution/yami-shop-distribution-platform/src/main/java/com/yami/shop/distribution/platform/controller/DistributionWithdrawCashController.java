package com.yami.shop.distribution.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.model.DistributionUser;
import com.yami.shop.distribution.common.model.DistributionUserWallet;
import com.yami.shop.distribution.common.model.DistributionWithdrawCash;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.param.RangeTimeParam;
import com.yami.shop.distribution.common.service.DistributionUserService;
import com.yami.shop.distribution.common.service.DistributionUserWalletService;
import com.yami.shop.distribution.common.service.DistributionWithdrawCashService;
import com.yami.shop.security.common.service.AppConnectService;
import com.yami.shop.service.SysConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR> on 2019/04/01.
 */
@RestController
@RequestMapping("/platform/distributionWithdrawCash")
@Tag(name = "平台端分销员提现申请接口")
@AllArgsConstructor
public class DistributionWithdrawCashController {

    private final DistributionWithdrawCashService distributionWithdrawCashService;
    private final DistributionUserWalletService distributionUserWalletService;
    private final DistributionUserService distributionUserService;
    private final AppConnectService appConnectService;
    private final SysConfigService sysConfigService;

    /**
     * 分页获取
     */
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('platform:distributionWithdrawCash:page')")
    @Operation(summary = "分页获取分销员提现申请列表")
    @Parameter(name = "userMobile", description = "分销员手机号码" )
    public ServerResponseEntity<IPage<DistributionWithdrawCash>> page(PageParam<DistributionWithdrawCash> page, RangeTimeParam rangeTimeParam,
                                                                      DistributionParam distributionParam, DistributionWithdrawCash distributionWithdrawCash) {
        IPage<DistributionWithdrawCash> list = distributionWithdrawCashService.distributionWithdrawCashsPage(page, null, rangeTimeParam, distributionParam, distributionWithdrawCash);
        return ServerResponseEntity.success(list);
    }

    /**
     * 修改
     */
    @PutMapping("/toSuccess/{withdrawCashId}")
    @PreAuthorize("@pms.hasPermission('platform:distributionWithdrawCash:update')")
    @Operation(summary = "修改分销员提现申请信息")
    @Parameter(name = "withdrawCashId", description = "提现记录id" )
    public ServerResponseEntity<Void> update(@PathVariable String withdrawCashId) {
        DistributionWithdrawCash distributionWithdrawCash = distributionWithdrawCashService.getById(withdrawCashId);
        if (distributionWithdrawCash == null) {
            throw new YamiShopBindException("yami.get.user.cash.info");
        }
        Long walletId = distributionWithdrawCash.getWalletId();
        DistributionUserWallet wallet = distributionUserWalletService.getById(walletId);
        if (wallet == null) {
            throw new YamiShopBindException("yami.get.user.withdraw.info");
        }
        DistributionUser distributionUser = distributionUserService.getById(wallet.getDistributionUserId());
        if (distributionUser == null) {
            throw new YamiShopBindException("yami.user.no.exist");
        }
        if (!Objects.equals(distributionUser.getShopId(), Constant.PLATFORM_SHOP_ID)) {
            throw new YamiShopBindException("yami.withdrawCash.update.no.auth");
        }
        distributionWithdrawCashService.updateWithDraw(distributionWithdrawCash, wallet, distributionUser);
        return ServerResponseEntity.success();
    }
}
