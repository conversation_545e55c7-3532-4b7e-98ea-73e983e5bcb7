package com.yami.shop.distribution.platform.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.model.DistributionUserIncome;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.param.RangeTimeParam;
import com.yami.shop.distribution.common.service.DistributionUserIncomeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> on 2019/04/01.
 */
@RestController
@RequestMapping("/distribution/distributionUserIncome")
@Tag(name = "平台端分销员收入接口")
@AllArgsConstructor
public class DistributionUserIncomeController {

    private final DistributionUserIncomeService distributionUserIncomeService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserIncome:page')")
    @Operation(summary = "分页获取分销员收入记录列表")
    public ServerResponseEntity<IPage<DistributionUserIncome>> page(DistributionUserIncome distributionUserIncome,
                                                              PageParam<DistributionUserIncome> page) {
        IPage<DistributionUserIncome> list = distributionUserIncomeService.page(page, new LambdaQueryWrapper<DistributionUserIncome>());
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/page/anduser")
    @Operation(summary = "分页获取分销员以及收入记录列表")
    @Parameters({
            @Parameter(name = "userMobile", description = "分销员手机号码" ),
            @Parameter(name = "orderNumber", description = "订单编号" ),
            @Parameter(name = "state", description = "收入状态" )
    })
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserIncome:page')")
    public ServerResponseEntity<IPage<DistributionUserIncome>> page(PageParam<DistributionUserIncome> page,
                                                                    RangeTimeParam rangeTimeParam, DistributionParam distributionParam, String orderNumber,
                                                                    Integer state, DistributionUserIncome distributionUserIncome) {
        IPage<DistributionUserIncome> list = distributionUserIncomeService.incomeAndDistributionUserPage(page, Constant.PLATFORM_SHOP_ID,
                rangeTimeParam, distributionParam, orderNumber, state, distributionUserIncome);
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/info/{id}")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserIncome:info')")
    @Operation(summary = "根据id获取分销员收入信息")
    @Parameter(name = "id", description = "分销员收入id" )
    public ServerResponseEntity<DistributionUserIncome> info(@PathVariable("id") Long id) {
        DistributionUserIncome distributionUserIncome = distributionUserIncomeService.getById(id);
        return ServerResponseEntity.success(distributionUserIncome);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserIncome:save')")
    @Operation(summary = "保存分销员收入信息")
    public ServerResponseEntity<Void> save(@RequestBody @Valid DistributionUserIncome distributionUserIncome) {
        distributionUserIncomeService.save(distributionUserIncome);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserIncome:update')")
    @Operation(summary = "修改分销员收入信息")
    public ServerResponseEntity<Void> update(@RequestBody @Valid DistributionUserIncome distributionUserIncome) {
        distributionUserIncomeService.updateById(distributionUserIncome);
        return ServerResponseEntity.success();
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserIncome:delete')")
    @Operation(summary = "根据id删除分销员收入信息")
    @Parameter(name = "id", description = "分销员收入id" )
    public ServerResponseEntity<Void> delete(@PathVariable Long id) {
        distributionUserIncomeService.removeById(id);
        return ServerResponseEntity.success();
    }
}
