package com.yami.shop.groupbuy.common.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/30 10:32
 */
@Data
public class ApiGroupTeamDto {

    @Schema(description = "拼团团队id" )
    private Long groupTeamId;

    @Schema(description = "拼团活动id" )
    private Long groupActivityId;

    @Schema(description = "活动成团人数" )
    private Integer groupNumber;

    @Schema(description = "已参团人数" )
    private Integer joinNum;

    @Schema(description = "结束时间" )
    private Date endTime;

    @Schema(description = "服务器当前时间" )
    private Date nowTime;



    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 拼团商品ID
     */
    private Long groupProdId;

    @Schema(description = "团订单状态(0:待成团，1:拼团中，2:拼团成功，3:拼团失败)" )
    private String status;

    @Schema(description = "参团的用户列表")
    List<ApiGroupUserDto> groupUserDtoList;



    /**
     * 获取服务器当前时间
     */
    public Date getNowTime() {
        return new Date();
    }
}
