package com.yami.shop.common.allinpay.order.req;

import java.util.List;

/**
 * 支付方式
 *
 * <AUTHOR>
 */
public class PayMethod {

    /**
     * 收银宝子商户号
     */
    private String vspCusid;
    /**
     * subAppid，通联的微信app支付和h5支付必传
     * 微信端应用ID：appid
     */
    private String subAppid;
    /**
     * 用户下单及调起支付的终端IP
     */
    private String cusip;
    /**
     * 非贷记卡：no_credit
     * 借、贷记卡：””，需要传空字符串，不能不传
     */
    private String limitPay;
    /**
     * 支付金额，单位：分
     */
    private Long amount;
    /**
     * （支付平台用户标识）
     * 微信小程序支付openid——微信分配
     */
    private String acct;
    /**
     * 场景信息
     * 注：
     * //IOS移动应用
     * {"h5_info": {"type":"IOS","app_name": "王者荣耀","bundle_id": "com.tencent.wzryIOS"}}
     * <p>
     * //安卓移动应用
     * {"h5_info": {"type":"Android","app_name": "王者荣耀","package_name": "com.tencent.tmgp.sgame"}}
     * <p>
     * //WAP网站应用
     * <a href="{"h5_info":">{"type":"Wap","wap_url": "<a href="https://pay.qq.com">...</a>","wap_name": "腾讯充值"}}
     */
    private String sceneInfo;

    private List<BalancePay> balancePayList;

    public String getVspCusid() {
        return vspCusid;
    }

    public void setVspCusid(String vspCusid) {
        this.vspCusid = vspCusid;
    }

    public String getSubAppid() {
        return subAppid;
    }

    public void setSubAppid(String subAppid) {
        this.subAppid = subAppid;
    }

    public String getCusip() {
        return cusip;
    }

    public void setCusip(String cusip) {
        this.cusip = cusip;
    }

    public String getSceneInfo() {
        return sceneInfo;
    }

    public void setSceneInfo(String sceneInfo) {
        this.sceneInfo = sceneInfo;
    }

    public List<BalancePay> getBalancePayList() {
        return balancePayList;
    }

    public void setBalancePayList(List<BalancePay> balancePayList) {
        this.balancePayList = balancePayList;
    }

    public String getLimitPay() {
        return limitPay;
    }

    public void setLimitPay(String limitPay) {
        this.limitPay = limitPay;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getAcct() {
        return acct;
    }

    public void setAcct(String acct) {
        this.acct = acct;
    }

    public static class BalancePay {
        /**
         * 账户余额，账户内转账必填
         * 账户集编号
         */
        private String accountSetNo;
        /**
         * 支付金额，单位：分
         */
        private Long amount;

        public String getAccountSetNo() {
            return accountSetNo;
        }

        public void setAccountSetNo(String accountSetNo) {
            this.accountSetNo = accountSetNo;
        }

        public Long getAmount() {
            return amount;
        }

        public void setAmount(Long amount) {
            this.amount = amount;
        }

        @Override
        public String toString() {
            return "BalancePay{" +
                    "accountSetNo='" + accountSetNo + '\'' +
                    ", amount=" + amount +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "PayMethod{" +
                "subAppId='" + subAppid + '\'' +
                ", cusip='" + cusip + '\'' +
                ", limitPay='" + limitPay + '\'' +
                ", vspCusid='" + vspCusid + '\'' +
                ", amount=" + amount +
                ", acct='" + acct + '\'' +
                ", sceneInfo='" + sceneInfo + '\'' +
                ", balancePayList=" + balancePayList +
                '}';
    }
}
