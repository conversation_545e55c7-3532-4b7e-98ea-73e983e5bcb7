package com.yami.shop.bean.app.dto;

import com.yami.shop.bean.model.OrderRefundIntervention;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "退款订单对象")
public class ApiOrderRefundDto {

    @Schema(description = "记录ID" )
    private Long refundId;

    @Schema(description = "订单编号" )
    private String orderNumber;

    @Schema(description = "订单总金额" )
    private BigDecimal orderAmount;

    @Schema(description = "退款编号" )
    private String refundSn;

    @Schema(description = "第三方退款单号(微信/支付宝退款单号)" )
    private String orderPayNo;

    @Schema(description = "订单支付方式(1 微信支付 2 支付宝)" )
    private Integer payType;


    @Schema(description = "订单类别 0.实物商品订单 1. 虚拟商品订单" )
    private Integer orderMold;

    @Schema(description = "订单支付名称" )
    private String payTypeName;

    @Schema(description = "退货数量" )
    private Integer goodsNum;

    @Schema(description = "退款金额" )
    private Double refundAmount;

    @Schema(description = "退还积分" )
    private Integer refundScore;

    @Schema(description = "申请类型:1,仅退款,2退款退货" )
    private Integer applyType;

    @Schema(description = "退款单类型（1:整单退款,2:单个物品退款）" )
    private Integer refundType;

    @Schema(description = "处理退款状态:(1.买家申请 2.卖家接受 3.买家发货 4.卖家收货 5.退款成功 6.买家撤回申请 7.商家拒绝 -1.退款关闭)" )
    private Integer returnMoneySts;

    @Schema(description = "最大退款金额" )
    private Double maxRefundAmount;

    @Schema(description = "申请时间" )
    private Date applyTime;

    @Schema(description = "卖家处理时间" )
    private Date handelTime;

    @Schema(description = "退款时间" )
    private Date refundTime;

    @Schema(description = "更新时间" )
    private Date updateTime;

    @Schema(description = "订单支付时间" )
    private Date orderPayTime;

    @Schema(description = "用户退货发货时间" )
    private Date shipTime;

    @Schema(description = "卖家收到用户退货的货物时间" )
    private Date receiveTime;

    @Schema(description = "撤销时间" )
    private Date cancelTime;

    @Schema(description = "同意退款时间" )
    private Date decisionTime;

    @Schema(description = "卖家拒绝时间" )
    private Date rejectTime;

    @Schema(description = "文件凭证json" )
    private String photoFiles;

    @Schema(description = "店铺/供应商文件凭证(逗号隔开)")
    private String shopImgUrls;

    @Schema(description = "收货地址对象" )
    private RefundDeliveryDto refundDelivery;

    @Schema(description = "拒绝原因" )
    private String rejectMessage;

    @Schema(description = "申请原因" )
    private String buyerReason;

    @Schema(description = "申请说明" )
    private String buyerDesc;

    @Schema(description = "联系方式" )
    private String buyerMobile;

    @Schema(description = "订单项" )
    private List<RefundOrderItemDto> orderItems = new ArrayList<>();

    @Schema(description = "店铺名称" )
    private String shopName;

    @Schema(description = "店铺ID" )
    private Long shopId;

    @Schema(description = "true:可以取消退款  false：不可以取消退款" )
    private Boolean isCancel;

    @Schema(description = "卖家备注" )
    private String sellerMsg;

    @NotNull(message = "是否接收到商品(1:已收到,0:未收到)")
    private Boolean isReceiver;

    @Schema(description = "物流信息" )
    private DeliveryDto deliveryDto;

    @Schema(description = "订单类型" )
    private Integer orderType;

    @Schema(description = "平台介入状态 -1.没有介入 1.用户申请介入 2.平台同意介入 3.平台拒绝介入 5.平台同意退款成功")
    private Integer platformInterventionStatus;

    @Schema(description = "用户申请介入理由")
    private String applyInterventionReason;

    @Schema(description = "用户申请介入凭证")
    private String applyInterventionImgUrls;

    @Schema(description = "平台介入退款方式 1.商家承担 2.商家供应商承担（供应商发货订单） 3.不同意退款")
    private Integer interventionRefundType;

    @Schema(description = "平台介入前的退款金额")
    private Long afterInterventionRefundAmount;

    @Schema(description = "平台留言")
    private String platformMessage;

    @Schema(description = "申请介入时间")
    private Date applyInterventionTime;

    @Schema(description = "用户/商家/供应商补充凭证截至时间，平台开始处理时间")
    private Date interventionEndTime;

    @Schema(description = "平台介入处理截至时间")
    private Date platformProcessingEndTime;

    @Schema(description = "介入完成时间")
    private Date interventionFinishTime;

    @Schema(description = "平台介入凭证列表")
    private List<OrderRefundIntervention> orderRefundInterventionList;

    @Schema(description = "能否申请平台介入or申请退款")
    private Boolean canApplyRefund;

    @Schema(description = "超时退款时是否自动退款")
    private Boolean refundTimeOutType;

    @Schema(description = "配送类型")
    private Integer dvyType;

    @Schema(description = "配送方式ID")
    private Long dvyId;

    @Schema(description = "门店名称")
    private String stationName;

    @Schema(description = "门店电话")
    private String stationPhone;

    @Schema(description = "门店电话区号")
    private String stationPhonePrefix;
}
