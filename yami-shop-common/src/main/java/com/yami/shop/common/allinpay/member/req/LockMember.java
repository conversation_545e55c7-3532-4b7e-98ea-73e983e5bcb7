package com.yami.shop.common.allinpay.member.req;

import java.io.Serial;
import java.io.Serializable;
/**
 * 锁定会员
 *
 * <AUTHOR>
 */
public class LockMember extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "lockMember";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public LockMember(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public LockMember() {
    }

}
