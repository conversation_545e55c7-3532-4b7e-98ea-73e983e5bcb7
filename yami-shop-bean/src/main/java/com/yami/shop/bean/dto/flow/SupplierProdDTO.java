package com.yami.shop.bean.dto.flow;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-01-30
 */
@Data
public class SupplierProdDTO {

    @Schema(description = "供应商商品id" )
    private Long supplierProdId;

    @Schema(description = "供应商id" )
    private Long supplierId;

    @NotNull(message = "商品id不能为空")
    private Long prodId;

    @Schema(description = "skuId" )
    private Long skuId;

    @Schema(description = "最小订货量" )
    private Integer minOrderQuantity;

    @Schema(description = "采购价" )
    private Double purchasePrice;

    @Schema(description = "商品编码")
    private String partyCode;

    @Schema(description = "仓库id")
    private Long warehouseId;

    @Hidden
    @Schema(description = "商品类别(1:实物, 2:虚拟, 3:组合)")
    private Integer prodMold;

    @Hidden
    @Schema(description = "店铺id")
    private Long shopId;

    @Hidden
    @Schema(description = "商品id集合")
    private List<Long> prodIds;
}
