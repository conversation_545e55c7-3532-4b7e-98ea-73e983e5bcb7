package com.yami.shop.bean.vo.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 用户在线状态返回
 * <AUTHOR>
 */
public class UserOnlineVO {

    @Schema(description = "平台/商家订阅者 uid")
    private String uid;

    @Schema(description = "设备标识  0.app 1.web 相同用户相同设备标记的主设备登录会互相踢 从设备将共存")
    private Integer deviceFlag;

    @Schema(description = "在线状态 0否1是")
    private Integer online;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getDeviceFlag() {
        return deviceFlag;
    }

    public void setDeviceFlag(Integer deviceFlag) {
        this.deviceFlag = deviceFlag;
    }

    public Integer getOnline() {
        return online;
    }

    public void setOnline(Integer online) {
        this.online = online;
    }

    @Override
    public String toString() {
        return "UserOnlineVO{" +
                "uid='" + uid + '\'' +
                ", deviceFlag=" + deviceFlag +
                ", online=" + online +
                '}';
    }
}
