package com.yami.shop.groupbuy.common.listener;

import com.yami.shop.bean.enums.OrderType;
import com.yami.shop.bean.event.CancelOrderEvent;
import com.yami.shop.bean.order.CancelOrderOrder;
import com.yami.shop.groupbuy.common.service.GroupOrderService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 取消订单事件监听
 *
 * <AUTHOR>
 */
@Slf4j
@Component("cancelOrderListener")
@AllArgsConstructor
public class CancelOrderListener {

    final private GroupOrderService groupOrderService;

    /**
     * 取消团购订单
     */
    @EventListener(CancelOrderEvent.class)
    @Order(CancelOrderOrder.GROUPBUY)
    public void grouponCancelOrderListener(CancelOrderEvent event) {
        if (!Objects.equals(event.getOrder().getOrderType(), OrderType.GROUP.value())) {
            return;
        }
        String orderNumber = event.getOrder().getOrderNumber();
        int i = groupOrderService.cancelGroupOrder(orderNumber, -1);
        if(i < 1){
            log.error("取消团购订单失败  => {}", orderNumber);
        }
    }

}
