package com.yami.shop.common.bo;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "redis.aof")
@Component
public class AofRedisBO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private String password;

    private String redisAddr;

    private Integer database;

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getDatabase() {
        return database;
    }

    public void setDatabase(Integer database) {
        this.database = database;
    }

    public String getRedisAddr() {
        return redisAddr;
    }

    public void setRedisAddr(String redisAddr) {
        this.redisAddr = redisAddr;
    }

    @Override
    public String toString() {
        return "AofRedisBO{" +
                "password='" + password + '\'' +
                ", redisAddr='" + redisAddr + '\'' +
                ", database=" + database +
                '}';
    }
}
