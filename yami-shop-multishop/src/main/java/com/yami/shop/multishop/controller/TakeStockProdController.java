package com.yami.shop.multishop.controller;

import com.yami.shop.bean.model.TakeStock;
import com.yami.shop.bean.vo.TakeStockProdVO;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.TakeStockExcelService;
import com.yami.shop.service.TakeStockProdService;
import com.yami.shop.service.TakeStockService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;


/**
 * 盘点商品列表
 * <AUTHOR>
 * @date 2021-09-15 11:18:33
 */
@Tag(name = "盘点商品接口")
@AllArgsConstructor
@RestController
@RequestMapping("/stock/takeStockProd" )
public class TakeStockProdController {

    private final TakeStockProdService takeStockProdService;
    private final TakeStockService takeStockService;
    private final TakeStockExcelService takeStockExcelService;

    @PreAuthorize("@pms.hasPermission('stock:takeStockProd:list')" )
    @Operation(summary = "根据盘点id获取盘点商品列表" , description = "根据盘点id获取盘点商品列表")
    @GetMapping("/list" )
    public ServerResponseEntity<List<TakeStockProdVO>> getTakeStockProdPage(Long takeStockId) {
        return ServerResponseEntity.success(takeStockProdService.listShopProd(takeStockId));
    }

    @PreAuthorize("@pms.hasPermission('multishop:takeStockProd:download')" )
    @Operation(summary = "下载盘点商品导入模板" )
    @GetMapping("/downLoadModel")
    public void downLoadModel(HttpServletResponse response) {
        takeStockExcelService.downLoadModel(response);
    }

    @PreAuthorize("@pms.hasPermission('multishop:takeStockProd:import')" )
    @Operation(summary = "导入盘点商品" , description = "导入盘点商品")
    @Parameter(name = "takeStockId", description = "实物盘点id")
    @PostMapping("/importExcel")
    public ServerResponseEntity<Object> importExcel(@RequestPart MultipartFile excelFile,
                                                    Long takeStockId) throws Exception {
        if (Objects.isNull(excelFile)) {
            throw new YamiShopBindException("yami.network.busy");
        }
        if (Objects.isNull(takeStockId)){
            throw new YamiShopBindException("yami.supplier.not.null");
        }
        Object o = takeStockExcelService.parseFile(excelFile, SecurityUtils.getShopUser().getShopId(), takeStockId);
        return ServerResponseEntity.success(o);
    }

    @PreAuthorize("@pms.hasPermission('multishop:takeStockProd:export')" )
    @Operation(summary = "导出盘点商品" )
    @Parameter(name = "takeStockId", description = "实物盘点id")
    @GetMapping("/exportTakeStockProd")
    public void exportTakeStockProd(HttpServletResponse response,
                                    Long takeStockId) {
        if (Objects.isNull(takeStockId)) {
            //盘点Id不能为空
            throw new YamiShopBindException("yami.take.stock.id.not.null");
        }
        TakeStock takeStock = takeStockService.getById(takeStockId);
        if (!Objects.equals(takeStock.getShopId(),SecurityUtils.getShopUser().getShopId())) {
            //当前盘点信息不属于你的店铺
            throw new YamiShopBindException("yami.take.stock.message.error");
        }
        takeStockExcelService.exportTakeStockProd(takeStockId,response);
    }
}
