package com.yami.shop.sys.multishop.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yami.shop.bean.enums.PositionType;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.enums.SysTypeEnum;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.common.util.PasswordUtil;
import com.yami.shop.common.util.PrincipalUtil;
import com.yami.shop.common.util.RedisUtil;
import com.yami.shop.security.common.bo.UidInfoBO;
import com.yami.shop.security.common.manager.PasswordManager;
import com.yami.shop.security.common.manager.TokenStore;
import com.yami.shop.security.common.model.UpdatePasswordDto;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.ImChannelService;
import com.yami.shop.sys.common.model.ShopEmployee;
import com.yami.shop.sys.common.service.ShopEmployeeService;
import com.yami.shop.sys.common.service.ShopRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;


/**
 * 商家端用户
 *
 * <AUTHOR>
 * @date 2021-03-01 17:42:09
 */
@RestController
@RequestMapping("/sys/shopEmployee")
@Tag(name = "商家端用户接口")
@RequiredArgsConstructor
public class ShopEmployeeController {

    @Value("${yami.expose.operation.auth:}")
    private Boolean permission;
    private final ShopEmployeeService shopEmployeeService;
    private final ShopRoleService shopRoleService;
    private final PasswordEncoder passwordEncoder;
    private final TokenStore tokenStore;
    private final PasswordManager passwordManager;
    private final ImChannelService imChannelService;


    /**
     * 分页查询
     * @param page 分页对象
     * @param shopEmployee 系统用户
     * @return 分页数据
     */
    @GetMapping("/page")
    @Operation(summary = "分页获取用户", description = "分页获取用户")
    @PreAuthorize("@pms.hasPermission('sys:shopEmployee:page')")
    public ServerResponseEntity<IPage<ShopEmployee>> getShopEmployeePage(PageParam<ShopEmployee> page, ShopEmployee shopEmployee) {
        // 只显示店铺自己的管理员
        PageParam<ShopEmployee> resPage = shopEmployeeService.page(page, new LambdaQueryWrapper<ShopEmployee>()
                .eq(ShopEmployee::getShopId, SecurityUtils.getShopUser().getShopId())
                .like(StrUtil.isNotBlank(shopEmployee.getUsername()), ShopEmployee::getUsername, shopEmployee.getUsername())
                .like(StrUtil.isNotBlank(shopEmployee.getNickname()), ShopEmployee::getNickname, shopEmployee.getNickname()));
        for (ShopEmployee employee : resPage.getRecords()) {
            if (StrUtil.isNotBlank(employee.getMobile())) {
                employee.setMobile(PhoneUtil.hideBetween(employee.getMobile()).toString());
            }
        }
        return ServerResponseEntity.success(resPage);
    }

    /**
     * 获取登录的用户信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取登录的用户信息", description = "获取登录的用户信息")
    public ServerResponseEntity<ShopEmployee> info() {
        ShopEmployee employee = shopEmployeeService.getShopEmployeeById(SecurityUtils.getShopUser().getEmployeeId());
        if (Objects.isNull(employee)) {
            throw new YamiShopBindException("yami.shop.data.is.removed");
        }
        if (!Objects.equals(SecurityUtils.getShopUser().getShopId(), employee.getShopId())) {
            throw new YamiShopBindException("yami.no.auth");
        }
        if (StrUtil.isNotBlank(employee.getMobile())) {
            employee.setMobile(PhoneUtil.hideBetween(employee.getMobile()).toString());
        }
        return ServerResponseEntity.success(employee);
    }

    /**
     * 修改登录用户密码
     */
    @SysLog("商家端-修改密码")
    @PostMapping("/password")
    @Operation(summary = "修改密码", description = "修改当前登陆用户的密码")
    public ServerResponseEntity<String> password(@RequestBody @Valid UpdatePasswordDto param) {
        Long employeeId = SecurityUtils.getShopUser().getEmployeeId();
        if (Objects.equals(employeeId.intValue(), Constant.SUPER_ADMIN_ID) && BooleanUtil.isFalse(permission)) {
            throw new YamiShopBindException("yami.no.auth");
        }
        ShopEmployee dbShopEmployee = shopEmployeeService.getShopEmployeeById(employeeId);
        String password = passwordManager.decryptPassword(param.getPassword());
        if (!passwordEncoder.matches(password, dbShopEmployee.getPassword())) {
            // 原密码不正确
            return ServerResponseEntity.showFailMsg(I18nMessage.getMessage("yami.password.error"));
        }
        //新密码
        String decryptPassword = passwordManager.decryptPassword(param.getNewPassword());
//        更新密码
        shopEmployeeService.updatePasswordByEmployeeId(employeeId, decryptPassword);
        tokenStore.deleteTokenByUidInfo(AuthUserContext.get());
        return ServerResponseEntity.success();
    }


    @GetMapping("/info/{employeeId}")
    @PreAuthorize("@pms.hasPermission('sys:shopEmployee:info')")
    @Operation(summary = "通过id查询用户信息", description = "通过id查询用户信息")
    @Parameter(name = "employeeId", description = "系统用户id")
    public ServerResponseEntity<ShopEmployee> getById(@PathVariable("employeeId") Long employeeId) {
        ShopEmployee dbShopEmployee = shopEmployeeService.getShopEmployeeById(employeeId);
        dbShopEmployee.setUserId(null);
        //获取用户所属的角色列表
        List<Long> roleIdList = shopRoleService.listRoleIdByEmployeeId(employeeId);
        dbShopEmployee.setRoleIdList(roleIdList);
        if (StrUtil.isNotBlank(dbShopEmployee.getMobile())) {
            dbShopEmployee.setMobile(PhoneUtil.hideBetween(dbShopEmployee.getMobile()).toString());
        }
        return ServerResponseEntity.success(dbShopEmployee);
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/shopEmployeeInfo")
    @PreAuthorize("@pms.hasPermission('sys:shopEmployee:info')")
    @Operation(summary = "获取用户信息", description = "获取用户信息")
    public ServerResponseEntity<ShopEmployee> shopEmployeeInfo() {
        ShopEmployee shopEmployee = shopEmployeeService.getShopEmployeeById(SecurityUtils.getShopUser().getEmployeeId());
        shopEmployee.setEmployeeId(null);
        shopEmployee.setPassword(null);
        if (StrUtil.isNotBlank(shopEmployee.getMobile())) {
            shopEmployee.setMobile(PhoneUtil.hideBetween(shopEmployee.getMobile()).toString());
        }
        return ServerResponseEntity.success(shopEmployee);
    }

    /**
     * 新增系统用户
     *
     * @param shopEmployee 系统用户
     * @return 是否新增成功
     */
    @PostMapping
    @PreAuthorize("@pms.hasPermission('sys:shopEmployee:save')")
    @Operation(summary = "新增用户信息", description = "新增用户信息")
    public ServerResponseEntity<String> save(@RequestBody @Valid ShopEmployee shopEmployee) {
        shopEmployee.setShopId(SecurityUtils.getShopUser().getShopId());
        shopEmployee.setCreateEmployeeId(SecurityUtils.getShopUser().getEmployeeId());
        shopEmployee.setType(PositionType.STAFF.value());
        String decryptPassword = passwordManager.decryptPassword(shopEmployee.getPassword());
        PasswordUtil.check(decryptPassword);
        shopEmployee.setPassword(passwordEncoder.encode(decryptPassword));
        shopEmployeeService.saveUserAndUserRole(shopEmployee);
        return ServerResponseEntity.success();
    }

    /**
     * 修改系统用户
     *
     * @param shopEmployee 系统用户
     * @return 是否修改成功
     */
    @PutMapping
    @PreAuthorize("@pms.hasPermission('sys:shopEmployee:update')")
    @Operation(summary = "修改用户信息", description = "修改用户信息")
    public ServerResponseEntity<String> updateById(@RequestBody @Valid ShopEmployee shopEmployee) {
        if (Objects.isNull(shopEmployee.getEmployeeId())) {
            // 员工id不能为空
            throw new YamiShopBindException("yami.shop.employee.exception.employeeIdNull");
        }
        Long employeeId = SecurityUtils.getShopUser().getEmployeeId();
        if (Objects.equals(shopEmployee.getEmployeeId().intValue(), Constant.SUPER_ADMIN_ID) && BooleanUtil.isFalse(permission)) {
            throw new YamiShopBindException("yami.no.auth");
        }
        String password = passwordManager.decryptPassword(shopEmployee.getPassword());
        ShopEmployee dbEmployee = shopEmployeeService.getShopEmployeeById(shopEmployee.getEmployeeId());
        //修改管理员账号但修改人不是管理员，则抛出异常（只有管理员可以改管理员信息）
        if (Objects.equals(PositionType.ADMIN.value(), dbEmployee.getType()) &&
                !Objects.equals(shopEmployee.getEmployeeId(), employeeId)) {
            // 您没有权限修改管理员信息
            return ServerResponseEntity.showFailMsg(I18nMessage.getMessage("yami.not.permission.modify.administrator.info"));
        }
        if (StrUtil.isBlank(password)) {
            shopEmployee.setPassword(null);
        } else {
            PasswordUtil.check(password);
            shopEmployee.setPassword(passwordEncoder.encode(password));
        }
        if (!PrincipalUtil.isDbPhone(shopEmployee.getMobile(), dbEmployee.getMobile(), false)) {
            throw new YamiShopBindException("yami.user.err.phone");
        }
        if (shopEmployee.getMobile().contains(Constant.ASTERISK)) {
            shopEmployee.setMobile(dbEmployee.getMobile());
        }
        shopEmployee.setShopId(AuthUserContext.getShopId());
        shopEmployeeService.updateUserAndUserRole(shopEmployee);
        if (Objects.equals(dbEmployee.getStatus(), 1) && Objects.equals(shopEmployee.getStatus(), 0)) {
            // 禁用用户
            ShopEmployee admin = shopEmployeeService.getOne(Wrappers.lambdaQuery(ShopEmployee.class)
                    .eq(ShopEmployee::getShopId, AuthUserContext.getShopId())
                    .eq(ShopEmployee::getType, PositionType.ADMIN.value()));
            imChannelService.removeSubscribers(dbEmployee.getEmployeeId(), dbEmployee.getShopId(), 0, admin.getNickname());
        } else if (!Objects.equals(dbEmployee.getNickname(), shopEmployee.getNickname())) {
            // 更新群资料
            imChannelService.updateMember(dbEmployee.getEmployeeId(), dbEmployee.getShopId(), null);
        }
        // 禁用用户之后将该用户登录信息清除
        tokenStore.deleteTokenByUidInfo(new UidInfoBO(SysTypeEnum.MULTISHOP, shopEmployee.getEmployeeId().toString(), AuthUserContext.getShopId(), 0));
        return ServerResponseEntity.success();
    }

    /**
     * 通过id删除系统用户
     *
     * @return 是否删除成功
     */
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('sys:shopEmployee:delete')")
    @Operation(summary = "通过id删除用户信息", description = "通过id删除用户信息")
    @Parameter(name = "employeeIds", description = "系统用户id列表")
    public ServerResponseEntity<String> removeById(@RequestBody List<Long> employeeIds) {
        if (CollUtil.isEmpty(employeeIds)) {
            // 请选择需要删除的用户
            return ServerResponseEntity.showFailMsg(I18nMessage.getMessage("yami.sys.select.user"));
        }
        // 获取该店铺的商家信息
        Long shopId = SecurityUtils.getShopUser().getShopId();
        ShopEmployee admin = shopEmployeeService.getOne(Wrappers.lambdaQuery(ShopEmployee.class)
                .eq(ShopEmployee::getShopId, shopId)
                .eq(ShopEmployee::getType, PositionType.ADMIN.value()));
        // 商家id
        Long adminId = admin.getEmployeeId();
        // 当前员工id
        Long currentEmployeeId = SecurityUtils.getShopUser().getEmployeeId();
        // 检查删除的员工中是否存在商家与当前员工
        for (Long id : employeeIds) {
            if (Objects.equals(id, adminId)) {
                throw new YamiShopBindException("yami.sys.admin.error");
            }
            if (Objects.equals(id, currentEmployeeId)) {
                throw new YamiShopBindException("yami.sys.delete.error");
            }
        }
        List<ShopEmployee> shopEmployees = shopEmployeeService.list(new LambdaQueryWrapper<ShopEmployee>().in(ShopEmployee::getEmployeeId, employeeIds));
        shopEmployeeService.removeEmployeesByIds(employeeIds);
        RedisUtil.del("defaultShopEmployee" + Constant.UNION + shopId);
        for (ShopEmployee shopEmployee : shopEmployees) {
            imChannelService.removeSubscribers(shopEmployee.getEmployeeId(), shopId, 1, admin.getNickname());
            tokenStore.deleteTokenByUidInfo(new UidInfoBO(SysTypeEnum.MULTISHOP, shopEmployee.getEmployeeId().toString(), shopEmployee.getShopId(), 0));
        }
        return ServerResponseEntity.success();
    }

    @GetMapping("/list")
    @Operation(summary = "获取店铺下的所有员工列表", description = "获取店铺下的所有员工列表")
    public ServerResponseEntity<List<ShopEmployee>> list(ShopEmployee shopEmployee) {
        shopEmployee.setShopId(SecurityUtils.getShopUser().getShopId());
        List<ShopEmployee> shopEmployeeList = shopEmployeeService.listByParam(shopEmployee);
        for (ShopEmployee employee : shopEmployeeList) {
            if (StrUtil.isNotBlank(employee.getMobile())) {
                employee.setMobile(PhoneUtil.hideBetween(employee.getMobile()).toString());
            }
        }
        return ServerResponseEntity.success(shopEmployeeList);
    }
}
