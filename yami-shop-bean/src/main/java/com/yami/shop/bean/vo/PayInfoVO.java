package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/6/10 9:13
 */
@Data
public class PayInfoVO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "序号" )
    private Long index;

    @Schema(description = "外部订单流水号" )
    private String bizPayNo;

    @Schema(description = "关联订单号" )
    private String orderIds;

    @Schema(description = "支付入口[0订单 1充值 2开通会员]" )
    private Integer payEntry;

    /**
     * 支付方式 [0积分支付    1微信小程序支付    2支付宝支付    3微信扫码支付
     * 4微信H5支付    5微信公众号支付    6支付宝H5支付    7支付宝APP支付    8微信APP支付    9余额支付]
     */
    @Schema(description = "支付方式" )
    private Integer payType;

    @Schema(description = "支付状态[-1退款 0未支付 1已支付]" )
    private Integer payStatus;

    @Schema(description = "支付积分" )
    private Long payScore;

    @Schema(description = "支付金额" )
    private Long payAmount;

    @Schema(description = "回调时间" )
    private Date callbackTime;

}
