package com.yami.shop.groupbuy.common.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.Product;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupActivityDto;
import com.yami.shop.groupbuy.common.dto.GroupActivityDto;
import com.yami.shop.groupbuy.common.model.GroupActivity;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 拼团活动表
 *
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
public interface GroupActivityMapper extends BaseMapper<GroupActivity> {

    /**
     * 根据商品id获取团购活动信息
     *
     * @param prodId
     * @return
     */
    ApiGroupActivityDto getByProdId(@Param("prodId") Long prodId);

    /**
     * 获取团购活动分页数据
     *
     * @param page             分页对象
     * @param groupActivityDto 查询数据
     * @return 团购活动列表
     */
    IPage<GroupActivityDto> getGroupActivityPage(PageParam<GroupActivityDto> page, @Param("groupActivityDto") GroupActivityDto groupActivityDto);

    /**
     * 获取拼团活动信息
     *
     * @param groupActivityId 拼团活动ID
     * @param prodId          商品ID
     * @return Api活动对象
     */
    ApiGroupActivityDto getApiGroupActivityInfo(@Param("groupActivityId") Long groupActivityId, @Param("prodId") Long prodId);

    /**
     * 更新活动为已删除状态
     *
     * @param groupActivityId 团购活动id
     */
    void updateToDelete(@Param("groupActivityId") Long groupActivityId);

    /**
     * 获取拼团物品列表
     *
     * @param date 当前时间
     * @return 商品
     */
    List<Product> getActivityFinishProduct(@Param("newDate") Date date);

    /**
     * 更新活动状态
     *
     * @param activityId 活动id
     * @param status     状态
     * @return 更新的数量
     */
    int updateStatus(@Param("activityId") Long activityId, @Param("status") Integer status);

    /**
     * 批量改变团购商品状态类型
     *
     * @param groupActivityIds 团购活动id列表
     * @param status           改变的状态
     */
    void batchUpdateGroupActivityStatus(@Param("groupActivityIds") List<Long> groupActivityIds, @Param("status") Integer status);

    /**
     * 根据商品获取指定未失效的活动
     *
     * @param prodId
     * @return
     */
    GroupActivity getUnInvalidGroupActivityByProdId(@Param("prodId") Long prodId);

    /**
     * 更新团购活动的状态
     *
     * @param groupActivityId
     * @param status
     */
    void updateGroupActivityStatus(@Param("groupActivityId") Long groupActivityId, @Param("status") Integer status);

    /**
     * 获取团购价格
     * @param prodIds
     * @return
     */
    List<GroupActivity> listActivityPrice(@Param("prodIds") List<Long> prodIds);


}
