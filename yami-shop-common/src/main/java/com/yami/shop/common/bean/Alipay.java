package com.yami.shop.common.bean;

import lombok.Data;

/**
 * 支付宝配置
 * <AUTHOR>
 */
@Data
public class Alipay {

    private String appId;

    /**
     * 应用公钥证书
     */
    private String appCertPath;

    /**
     * 应用公钥证书内容
     */
    private String appCertContent;

    /**
     * 支付宝公钥证书
     */
    private String alipayCertPath;

    /**
     * 支付宝公钥证书内容
     */
    private String alipayCertContent;

    /**
     * 支付宝根证书
     */
    private String alipayRootCertPath;

    /**
     * 支付宝根证书内容
     */
    private String alipayRootCertContent;

    /**
     * 应用私钥
     */
    private String appPrivateKey;
}
