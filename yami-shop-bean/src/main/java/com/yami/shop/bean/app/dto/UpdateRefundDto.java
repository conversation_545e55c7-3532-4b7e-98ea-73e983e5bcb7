package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UpdateRefundDto {

    @Schema(description = "退款金额" , requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "退款金额不能为空")
    private Double refundAmount;

    @NotNull(message = "退款编号不能为空")
    @Schema(description = "退款编号" )
    private String refundSn;
}
