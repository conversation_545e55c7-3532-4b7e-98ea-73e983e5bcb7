package com.yami.shop.groupbuy.api.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yami.shop.bean.app.vo.ProductVO;
import com.yami.shop.bean.model.OrderItem;
import com.yami.shop.bean.model.Product;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.config.ShopConfig;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupSkuDto;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupTeamDto;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupUserDto;
import com.yami.shop.groupbuy.common.api.vo.ApiGroupTeamInfoVo;
import com.yami.shop.groupbuy.common.model.GroupOrder;
import com.yami.shop.groupbuy.common.service.GroupOrderService;
import com.yami.shop.groupbuy.common.service.GroupSkuService;
import com.yami.shop.groupbuy.common.service.GroupTeamService;
import com.yami.shop.security.api.util.SecurityUtils;
import com.yami.shop.service.OrderItemService;
import com.yami.shop.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/9/2 9:01
 */
@RestController
@RequestMapping("/p/groupTeam")
@Tag(name = "拼团信息接口")
@AllArgsConstructor
public class GroupTeamController {

    private final GroupOrderService groupOrderService;
    private final GroupTeamService groupTeamService;
    private final OrderItemService orderItemService;
    private final GroupSkuService groupSkuService;
    private final ProductService productService;
    private final ShopConfig shopConfig;


    @GetMapping("/info")
    @Operation(summary = "拼团详情" , description = "拼团详情,至少携带一个参数,查看拼团单（团队）信息")
    @Parameters({
            @Parameter(name = "groupTeamId",description = "拼团团队ID"),
            @Parameter(name = "orderNumber", description = "订单编号")
    })
    public ServerResponseEntity<ApiGroupTeamInfoVo> teamInfo(
            @RequestParam(value = "groupTeamId", required = false) Long groupTeamId,
            @RequestParam("orderNumber") String orderNumber) {
        // 通过订单编号获取团ID
        if (StrUtil.isNotBlank(orderNumber)) {
            GroupOrder groupOrder = groupOrderService.getOne(new LambdaQueryWrapper<GroupOrder>()
                    .eq(GroupOrder::getOrderNumber, orderNumber));
            if (Objects.nonNull(groupOrder) && Objects.nonNull(groupOrder.getGroupTeamId())) {
                groupTeamId = groupOrder.getGroupTeamId();
            }
        }
        // 获取拼团团队信息
        ApiGroupTeamDto groupTeam = groupTeamService.getApiGroupTeamDto(groupTeamId);
        if (groupTeam == null) {
            // 该拼团单不存在
            throw new YamiShopBindException("yami.group.order.no.exist");
        }
        // 获取参团的用户列表
        List<ApiGroupUserDto> groupUserList = groupOrderService.listApiGroupUserDto(groupTeamId);
        // 如果昵称为空则为机器人参团
        for (ApiGroupUserDto apiGroupUserDto : groupUserList) {
            apiGroupUserDto.setNickName(StrUtil.isBlank(apiGroupUserDto.getNickName()) ? "系统参团" : apiGroupUserDto.getNickName());
            if (StrUtil.isBlank(orderNumber)) {
                orderNumber = apiGroupUserDto.getOrderNumber();
            }
        }
        // 获取商品信息
        Product product = productService.getProductAndLang(groupTeam.getGroupProdId());
//        if (product == null || !Objects.equals(product.getStatus(), ProdStatusEnums.NORMAL.getValue())) {
//            // 活动商品不在正常状态
//            throw new YamiShopBindException("yami.group.prod.status.error");
//        }
        List<OrderItem> orderItems = orderItemService.getOrderItemsByOrderNumber(orderNumber,true);
        Long skuId = orderItems.get(0).getSkuId();
        product.setActivityPrice(orderItems.get(0).getPrice());
        // 获取SKU列表
        List<ApiGroupSkuDto> groupSkuList = groupSkuService.getApiByGroupActivityIdAndProdId(product.getProdId(),product.getMold(),groupTeam.getGroupActivityId());
        Map<Long, List<ApiGroupSkuDto>> apiGroupSkuMap = groupSkuList.stream().collect(Collectors.groupingBy(ApiGroupSkuDto::getSkuId));
        product.setPrice(apiGroupSkuMap.get(skuId).get(0).getPrice());
        // 获取团购订单信息
        String userId = SecurityUtils.getUser().getUserId();
        GroupOrder groupOrder = groupOrderService.getOne(new LambdaQueryWrapper<GroupOrder>()
                .eq(GroupOrder::getGroupTeamId, groupTeamId).eq(GroupOrder::getUserId, userId).eq(GroupOrder::getStatus, 1));
        // 拼装拼团详情信息
        ApiGroupTeamInfoVo infoVo = new ApiGroupTeamInfoVo();
        infoVo.setGroupTeam(groupTeam);
        infoVo.setApiGroupUserList(groupUserList);
        infoVo.setProductVO(BeanUtil.map(product, ProductVO.class));
        infoVo.setGroupSkuList(groupSkuList);
        if (groupOrder != null) {
            infoVo.setOrderNumber(groupOrder.getOrderNumber());
        }
        return ServerResponseEntity.success(infoVo);
    }

    @GetMapping("/joinUsers")
    @Operation(summary = "参团的用户列表" , description = "参团的用户列表")
    @Parameter(name = "groupTeamId", description = "拼团团队ID" , required = true)
    public ServerResponseEntity<List<ApiGroupUserDto>> joinGroupUsers(
            @RequestParam("groupTeamId") Long groupTeamId) {
        // 获取参团的用户列表
        List<ApiGroupUserDto> groupUserList = groupOrderService.listApiGroupUserDto(groupTeamId);
        return ServerResponseEntity.success(groupUserList);
    }
}
