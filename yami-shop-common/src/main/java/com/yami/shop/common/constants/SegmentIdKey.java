package com.yami.shop.common.constants;

/**
 * <AUTHOR>
 */
public interface SegmentIdKey {

    /**
     * 支付单号
     */
    String PAY = "mall4j-pay";

    /**
     * 订单遍号
     */
    String ORDER = "mall4j-order";

    /**
     * 采购订单遍号
     */
    String PURCHASES_ORDER = "mall4j-purchases-order";

    /**
     * 退款订单号
     */
    String REFUND = "mall4j-refund";

    /**
     * 商品编号
     */
    String PRODUCT = "mall4j-product";

    /**
     * 商品库存
     */
    String STOCK = "mall4j-stock";

    /**
     *  系统权限
     */
    String AUTHORIZATION = "mall4j-authorization";

    /**
     * 自提订单提货码
     */
    String STATION = "mall4j-station";

    /**
     * 虚拟订单核销码
     */
    String VIRTUAL = "mall4j-virtual";

    /**
     * Allinpay更新企业信息的流水号
     */
    String UPDATE_COMPANT_SN = "mall4j-alllinpay-update-company-sn";

    /**
     * 通联文件上传单号
     */
    String ALLINPAY_FILE_UPLOAD = "mall4j-file-upload";

    /**
     * 通联提现单号
     */
    String ALLINPAY_WITHDRAW_CASH = "mall4j-withdraw-cash";

    /**
     * 分销-商家转账订单号
     */
    String DISTRIBUTION_PAY_ORDER_NO = "mall4j-ent-pay-order-no";

    /**
     * 分销-分销员卡号
     */
    String DISTRIBUTION_CARD_NO = "mall4j-distribution-card-no";

    /**
     * 分销-二维码
     */
    String DISTRIBUTION_TICKET = "mall4j-distribution-ticket";

    /**
     * 秒杀订单路径
     */
    String SECKILL_ORDER_PATH = "mall4j-seckill-order-path";

    /**
     * 库存点id
     */
    String STOCK_POINT = "mall4j-stock-point";

    /**
     * 调拨订单
     */
    String ALLOT_ORDER = "mall4j-allot-order";
}

