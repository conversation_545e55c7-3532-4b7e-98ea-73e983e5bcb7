package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/8/12 15:20
 */
@Data
public class ShopStatusInfoVO {
    @Schema(description = "账号状态， 1:启用 0:禁用 -1:删除" )
    private Integer accountStatus;

    @Schema(description = "店铺状态(-1:已删除 0: 停业中 1:营业中 2:平台下线 3:平台下线待审核 4:开店申请中 5:开店申请待审核)" )
    private Integer shopStatus;

    @Schema(description = "下线状态 1平台进行下线 2 重新申请，等待审核 3.审核通过 4 审核未通过" )
    private Integer offlineStatus;

    @Schema(description = "下线原因" )
    private String offlineReason;

    @Schema(description = "签约起始时间" )
    private Date contractStartTime;

    @Schema(description = "签约终止时间" )
    private Date contractEndTime;
}
