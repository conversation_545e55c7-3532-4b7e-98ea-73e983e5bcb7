package com.yami.shop.common.allinpay.member.req;

import java.io.Serial;
import java.io.Serializable;
/**
 * 解锁会员
 *
 * <AUTHOR>
 */
public class UnlockMember extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "unlockMember";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public UnlockMember(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public UnlockMember() {
    }

}
