package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * VO
 *
 * <AUTHOR>
 * @date 2023-11-09 18:00:43
 */
@Data
public class StockPointSkuVO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "库存商品关联skuid")
    private Long stockPointSkuId;

    @Schema(description = "库存点id")
    private Long stockPointId;

    @Schema(description = "库存点类型 1仓库 2门店 ")
    private Integer stockPointType;

    @Schema(description = "prodId")
    private Long prodId;

    @Schema(description = "skuid")
    private Long skuId;

    @Schema(description = "状态（对应sku的状态） 1:enable, 0:disable, -1:deleted")
    private Integer status;

    @Schema(description = "库存")
    private Integer stock;

    @Schema(description = "仓库类型 0默认仓库 1区域仓库 ")
    private Integer type;

    @Schema(description = "库存模式")
    private Integer stockMode;

    @Schema(description = "自提门店用途是否支持自提（0：不支持，1：支持）")
    private Integer selfPickup;

    @Schema(description = "自提门店用途是否支持同城配送（0：不支持，1：支持）")
    private Integer sameCityDelivery;

    @Schema(description = "锁定库存数量")
    private Integer lockStock;

    @Schema(description = "销量")
    private Integer sale;

    /**
     * 更新时，变化的库存
     */
    @Schema(description = "更新时，变化的库存" )
    private Integer changeStock;

    public StockPointSkuVO() {
    }

    public StockPointSkuVO(Long skuId, Long stockPointId) {
        this.skuId = skuId;
        this.stockPointId = stockPointId;
    }
    public StockPointSkuVO(Long stockPointId, Integer stockPointType, Long prodId, Long skuId, Integer status, Integer type, Integer stockMode) {
        this.stockPointId = stockPointId;
        this.stockPointType = stockPointType;
        this.prodId = prodId;
        this.skuId = skuId;
        this.status = status;
        this.type = type;
        this.stockMode = stockMode;
    }

}
