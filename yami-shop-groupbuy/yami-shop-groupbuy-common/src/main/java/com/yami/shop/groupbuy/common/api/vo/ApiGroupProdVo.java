package com.yami.shop.groupbuy.common.api.vo;

import com.yami.shop.groupbuy.common.api.dto.ApiGroupTeamDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/23 15:33
 */
@Data
public class ApiGroupProdVo {


    @Schema(description = "团队列表")
    List<ApiGroupTeamDto> teamList;
    @Schema(description = "正在拼团总人数")
    private Integer sumJoinNum;


}
