package com.yami.shop.discount.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yami.shop.discount.common.dao.DiscountItemMapper;
import com.yami.shop.discount.common.model.DiscountItem;
import com.yami.shop.discount.common.service.DiscountItemService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> on 2018/11/21.
 */
@Service
@AllArgsConstructor
public class DiscountItemServiceImpl extends ServiceImpl<DiscountItemMapper, DiscountItem> implements DiscountItemService {

    private final DiscountItemMapper discountItemMapper;

}
