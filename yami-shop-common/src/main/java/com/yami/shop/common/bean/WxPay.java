package com.yami.shop.common.bean;


import lombok.Data;

/**
 * 微信支付参数
 * <AUTHOR>
 */
@Data
public class WxPay {

    public static final Integer VERSION_3 = 3;

    /**
     * 微信支付mchId
     */
    private String mchId;

    /**
     * 微信支付mchKey
     */
    private String mchKey;

    /**
     * 签名类型
     */
    private String signType;

    /**
     * 支付证书路径
     */
    private String keyPath;

    // ======== v3 需要=======
    /**
     * 微信支付版本 2代表用旧的v2支付 3代表用v3支付
     */
    private Integer version;

    /**
     * 证书序列号
     */
    private String certSerialNo;

    /**
     * apiV3秘钥
     */
    private String apiv3Key;

    /**
     * apiv3 商户apiclient_key.pem
     */
    private String privateKeyPath;

    /**
     * apiv3 商户apiclient_key.pem内容
     */
    private String privateKeyContent;

    /**
     * apiv3 商户apiclient_cert.pem
     */
    private String privateCertPath;

    /**
     * apiv3 商户apiclient_cert.pem内容
     */
    private String privateCertContent;
}
