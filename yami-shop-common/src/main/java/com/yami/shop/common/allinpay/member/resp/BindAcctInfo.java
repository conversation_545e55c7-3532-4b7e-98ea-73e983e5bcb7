package com.yami.shop.common.allinpay.member.resp;

import com.yami.shop.common.allinpay.member.constant.PayAcctType;

import java.io.Serial;
import java.io.Serializable;
/**
 * <AUTHOR>
 * @date 2023-04-25
 */
public class BindAcctInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 支付账户类型
     * @see PayAcctType
     */
    private String acctType;
    /**
     * 支付账户用户标识
     */
    private String acct;

    public String getAcctType() {
        return acctType;
    }

    public void setAcctType(String acctType) {
        this.acctType = acctType;
    }

    public String getAcct() {
        return acct;
    }

    public void setAcct(String acct) {
        this.acct = acct;
    }

    @Override
    public String toString() {
        return "BindAcctInfo{" +
                "acctType='" + acctType + '\'' +
                ", acct='" + acct + '\'' +
                '}';
    }
}
