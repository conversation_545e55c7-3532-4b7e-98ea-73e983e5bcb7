package com.yami.shop.common.allinpay.order.req;

/**
 * 查询余额
 *
 * <AUTHOR>
 */
public class QueryBalance extends AbstractOrderReq {

    /**
     * 账户集编号
     */
    private String accountSetNo;
    /**
     * 是否为用户钱包
     */
    private Integer walletType;

    /**
     * 方法名称
     */
    public static final String METHOD_NAME = "queryBalance";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public String getAccountSetNo() {
        return accountSetNo;
    }

    public void setAccountSetNo(String accountSetNo) {
        this.accountSetNo = accountSetNo;
    }

    public Integer getWalletType() {
        return walletType;
    }

    public void setWalletType(Integer walletType) {
        this.walletType = walletType;
    }

    public QueryBalance() {
    }

    public QueryBalance(String bizUserId, String accountSetNo) {
        this.bizUserId = bizUserId;
        this.accountSetNo = accountSetNo;
    }


}
