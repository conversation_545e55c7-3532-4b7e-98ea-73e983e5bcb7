package com.yami.shop.distribution.multishop.config;


import lombok.AllArgsConstructor;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration("distributionSwaggerConfiguration")
@AllArgsConstructor
public class SwaggerConfiguration {


    @Bean
    public GroupedOpenApi distributionRestApi() {
        return GroupedOpenApi.builder()
                .group("分销接口")
                .packagesToScan("com.yami.shop.distribution.multishop.controller")
                .pathsToMatch("/**")
                .build();
    }

}
