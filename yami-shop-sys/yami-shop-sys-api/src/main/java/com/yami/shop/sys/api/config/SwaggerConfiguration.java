package com.yami.shop.sys.api.config;


import lombok.AllArgsConstructor;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration("sysSwaggerConfiguration")
@AllArgsConstructor
public class SwaggerConfiguration {

    @Bean
    public GroupedOpenApi sysRestApi() {
        return GroupedOpenApi.builder()
                .group("系统配置")
                .packagesToScan("com.yami.shop.sys.api.controller")
                .pathsToMatch("/**")
                .build();
    }

}
