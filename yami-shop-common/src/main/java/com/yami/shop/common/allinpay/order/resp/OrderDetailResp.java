package com.yami.shop.common.allinpay.order.resp;

/**
 * 订单状态 返回
 *
 * <AUTHOR>
 */
public class OrderDetailResp {
    /**
     * 未支付 1
     * 交易失败 3 交易过程中出现错误
     * 交易成功 4
     * 交易成功 -发生退款 5 交易成功，但是发生了退款。
     * 关闭 6  整型 订单在过期时间内，没有进行确认操作。
     * 进行中 99
     */

    private Integer orderStatus;

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }


}
