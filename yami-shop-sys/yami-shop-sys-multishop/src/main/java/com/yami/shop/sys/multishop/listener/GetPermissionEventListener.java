package com.yami.shop.sys.multishop.listener;

import com.yami.shop.bean.event.GetPermissionEvent;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.sys.common.service.ShopMenuService;
import lombok.AllArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023-12-14
 */
@Component
@AllArgsConstructor
public class GetPermissionEventListener {
    private final ShopMenuService shopMenuService;

    @EventListener(GetPermissionEvent.class)
    public void getShopSuperAdmin(GetPermissionEvent event) {
        Set<String> shopPermissions
                = shopMenuService.getShopPermissions(AuthUserContext.getEmployeeId(), AuthUserContext.getIsAdmin());
        event.setPerms(shopPermissions);
    }
}
