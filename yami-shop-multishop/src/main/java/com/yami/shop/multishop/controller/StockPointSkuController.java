package com.yami.shop.multishop.controller;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.app.vo.ProductVO;
import com.yami.shop.bean.app.vo.SkuVO;
import com.yami.shop.bean.dto.StockPointSkuDTO;
import com.yami.shop.bean.enums.StockModeEnum;
import com.yami.shop.bean.model.Station;
import com.yami.shop.bean.vo.StockPointVO;
import com.yami.shop.common.enums.SysTypeEnum;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.service.ProductService;
import com.yami.shop.service.SkuService;
import com.yami.shop.service.StationService;
import com.yami.shop.service.WarehouseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController("multishopStockPointController")
@RequestMapping("/m/stockPointSku")
@Tag(name = "商家端库存点关联商品")
@AllArgsConstructor
public class StockPointSkuController {
    private WarehouseService warehouseService;
    private ProductService productService;
    private SkuService skuService;
    private StationService stationService;

    @GetMapping("/page")
    @Operation(summary = "分页查询库存点商品信息", description = "分页查询库存点商品信息")
    public ServerResponseEntity<IPage<ProductVO>> pageStockPointSpuSku(PageParam<ProductVO> pageDTO, StockPointSkuDTO spuDTO) {
        Long shopId = AuthUserContext.get().getShopId();
        spuDTO.setShopId(shopId);
        // 判断是否默认仓库或是共享库存模式的门店，是就获取全部商品
        if (Objects.equals(spuDTO.getType(), 0) || Objects.equals(spuDTO.getStockMode(), StockModeEnum.ALL_STOCK.value())) {
            Long warehouseId = warehouseService.getDefaultWarehouseByShopId(shopId, SysTypeEnum.MULTISHOP.value()).getWarehouseId();
            spuDTO.setStockPointId(warehouseId);
        }
        IPage<ProductVO> pageVO = productService.pageStockPointSpu(pageDTO, spuDTO);
        if (CollectionUtil.isEmpty(pageVO.getRecords())) {
            return ServerResponseEntity.success(pageVO);
        }
        List<SkuVO> skuDtoList = skuService.listSkuAndStockByStockPointId(spuDTO.getStockPointId(), spuDTO.getInStockPointId(), spuDTO.getQueryHasStock());

        Map<Long, List<SkuVO>> skuMap = skuDtoList.stream().collect(Collectors.groupingBy(SkuVO::getProdId));
        for (ProductVO productVO : pageVO.getRecords()) {
            // TODO stock
            productVO.setSkuList(BeanUtil.mapAsList(skuMap.get(productVO.getProdId()), SkuVO.class));
        }
        return ServerResponseEntity.success(pageVO);
    }

    @GetMapping("/pageHasStockSpu")
    @Operation(summary = "分页查询库存点商品信息(有库存)", description = "分页查询库存点商品信息")
    public ServerResponseEntity<IPage<ProductVO>> pageHasStockSpuByStockId(PageParam<ProductVO> pageDTO, StockPointSkuDTO spuDTO) {
        Long shopId = AuthUserContext.get().getShopId();
        spuDTO.setShopId(shopId);
        // 判断是否默认仓库或是共享库存模式的门店，是就获取全部商品
        if (Objects.equals(spuDTO.getType(), 0) || Objects.equals(spuDTO.getStockMode(), StockModeEnum.ALL_STOCK.value())) {
            Long warehouseId = warehouseService.getDefaultWarehouseByShopId(shopId, SysTypeEnum.MULTISHOP.value()).getWarehouseId();
            spuDTO.setStockPointId(warehouseId);
        }
        // 获取有库存的商品ids,在进行分页
        List<SkuVO> skuVOList = skuService.listSkuAndStockByStockPointId(spuDTO.getStockPointId(), spuDTO.getInStockPointId(), spuDTO.getQueryHasStock());
        if (CollectionUtils.isEmpty(skuVOList)) {
            return ServerResponseEntity.success(new PageParam<>());
        }
        Map<Long, List<SkuVO>> prodSkuMap = skuVOList.stream().filter(skuVO -> skuVO.getStocks() > 0).collect(Collectors.groupingBy(SkuVO::getProdId));
        spuDTO.setProdIds(prodSkuMap.keySet().stream().toList());
        IPage<ProductVO> pageVO = productService.pageStockPointSpu(pageDTO, spuDTO);
        if (CollectionUtil.isEmpty(pageVO.getRecords())) {
            return ServerResponseEntity.success(pageVO);
        }
        for (ProductVO productVO : pageVO.getRecords()) {
            productVO.setSkuList(prodSkuMap.get(productVO.getProdId()));
        }
        return ServerResponseEntity.success(pageVO);
    }

    @GetMapping("/stockPoint")
    @Operation(summary = "获取店铺库存点数据")
    public ServerResponseEntity<StockPointVO> pointList() {
        Long shopId = AuthUserContext.get().getShopId();
        StockPointVO stockPointVO = warehouseService.getWarehousePoint(shopId, SysTypeEnum.MULTISHOP.value());
        Integer stationSize = (int) stationService.count(new LambdaQueryWrapper<>(Station.class).eq(Station::getShopId, shopId)
                .eq(Station::getStockMode, StockModeEnum.SINGLE_STOCK.value())
                .ne(Station::getStatus, -1));
        stockPointVO.setStationSize(stationSize);
        return ServerResponseEntity.success(stockPointVO);
    }
}
