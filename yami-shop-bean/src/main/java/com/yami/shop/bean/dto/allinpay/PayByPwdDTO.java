package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-05-05
 */
public class PayByPwdDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "跳转页面类型(1:h5, 2:小程序), 默认h5")
    private Long jumpPageType;

    @Schema(description = "订单号")
    private String bizOrderNo;

    @Schema(description = "失败后跳转页面")
    private String errorJumpUrl;

    @Schema(description = "成功后跳转页面")
    private String jumpUrl;

    public Long getJumpPageType() {
        return jumpPageType;
    }

    public void setJumpPageType(Long jumpPageType) {
        this.jumpPageType = jumpPageType;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getErrorJumpUrl() {
        return errorJumpUrl;
    }

    public void setErrorJumpUrl(String errorJumpUrl) {
        this.errorJumpUrl = errorJumpUrl;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    @Override
    public String toString() {
        return "PayByPwdDTO{" +
                "jumpPageType=" + jumpPageType +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                ", errorJumpUrl='" + errorJumpUrl + '\'' +
                ", jumpUrl='" + jumpUrl + '\'' +
                '}';
    }
}
