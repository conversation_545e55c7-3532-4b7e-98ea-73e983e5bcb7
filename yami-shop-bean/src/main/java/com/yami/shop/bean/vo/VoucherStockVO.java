package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/12/22
 */
@Data
public class VoucherStockVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "voucherId")
    private Long voucherId;

    @Schema(description = "skuId")
    private Long skuId;

    @Schema(description = "库存数量")
    private Integer stock;

    @Schema(description = "库存点id")
    private Long stockPointId;

    @Schema(description = "库存点类型 1仓库 2门店")
    private Integer stockPointType;

    @Schema(description = "操作类型 LuaOperateEnum")
    private Integer operateType;

    public VoucherStockVO() {
    }

    public VoucherStockVO(Long voucherId, Integer stock, Long stockPointId, Integer stockPointType) {
        this.voucherId = voucherId;
        this.stock = stock;
        this.stockPointId = stockPointId;
        this.stockPointType = stockPointType;
    }

}
