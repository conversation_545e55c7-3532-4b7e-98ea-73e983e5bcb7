package com.yami.shop.distribution.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.bean.model.OrderItem;
import com.yami.shop.bean.param.OfflineHandleEventAuditParam;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.dto.DistributionProdDto;
import com.yami.shop.distribution.common.model.DistributionProd;
import com.yami.shop.distribution.common.model.DistributionProdLog;
import com.yami.shop.distribution.common.model.DistributionUser;
import com.yami.shop.distribution.common.po.DistributionProdPO;
import com.yami.shop.distribution.common.vo.DistributionAwardDataVO;

import java.util.List;

/**
 * <AUTHOR> on 2019/04/01.
 */
public interface DistributionProdService extends IService<DistributionProd> {

    /**
     * 查询分销商品列表
     * @param page 分页参数
     * @param distributionProd 分销商品
     * @param prodName 商品名称
     * @return 分销商品列表
     */
    IPage<DistributionProd> distributionProdsPage(Page page, DistributionProd distributionProd, String prodName);

    /**
     * 根据获得商品id获得po对象
     *
     * @param prodId 商品id
     * @return 分销商品详情
     */
    DistributionProdPO getDistributionProdPoByProdId(Long prodId);

    /**
     * 移除分销商品缓存
     * @param prodId 商品id
     */
    void removeDistributionProdPoCacheByProdId(Long prodId);

    /**
     * 根据分销员id获得分销商品 分页
     * @param page  分页参数
     * @param prodName 商品名称
     * @param sort 排序
     * @param orderBy 正序 / 倒序
     * @param distributionUser 分销员信息
     * @return 分销商品 分页
     */
    List<DistributionProdDto> distributionProdDtoPage(Page<DistributionProdDto> page, String prodName, Integer sort, Integer orderBy, DistributionUser distributionUser);

    /**
     * 计算佣金金额
     *
     * @param distributionProdPo 分销商品
     * @param distributionUserId 分销员
     * @param orderItem 订单项
     * @return 佣金金额
     */
    DistributionAwardDataVO getAwardDataVO(DistributionProdPO distributionProdPo, Long distributionUserId, OrderItem orderItem);

    /**
     * 下线分销商品
     * @param distributionProd 分销商品
     * @param offlineReason 下线原因
     * @param sysUserId 操作人
     */
    void offline(DistributionProd distributionProd, String offlineReason, Long sysUserId);

    /**
     * 审核活动商品
     * @param offlineHandleEventAuditParam 审核信息
     * @param sysUserId 操作人
     */
    void auditDistributionProd(OfflineHandleEventAuditParam offlineHandleEventAuditParam, Long sysUserId);

    /**
     * 提交审核
     * @param eventId 事件id
     * @param distributionProdId 分销商品id
     * @param reapplyReason 申请原因
     */
    void auditApply(Long eventId, Long distributionProdId, String reapplyReason);

    /**
     * 获取分销商品记录
     * @param page 分页参数
     * @param distributionProdLog 分销商品记录查询参数
     * @return 分销商品记录
     */
    IPage<DistributionProdLog> getDistributionProdLogPage(PageParam<DistributionProdLog> page, DistributionProdLog distributionProdLog);

    /**
     * 获取订单项中的分销商品信息
     * @param orderItems 订单项信息
     * @return 分销商品信息
     */
    List<DistributionProd> listByOrderItems(List<OrderItem> orderItems);

    /**
     * 根据商品id与状态查看该商品是处于该状态
     * @param prodId 商品id
     * @param state 状态
     * @return
     */
    Boolean isStateByProdId(Long prodId, Integer state);

    /**
     * 修改分销商品中的状态
     * @param distributionProdId 商品分销id
     * @param status 状态
     * @return 修改行数
     */
    int updateState(Long distributionProdId, Integer status);
}
