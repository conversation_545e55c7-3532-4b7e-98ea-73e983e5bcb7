package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Schema(description = "商品评论数据")
@Data
public class ProdCommDataDto {

    @Schema(description = "好评率" )
    private Double positiveRating;

    @Schema(description = "评论数量" )
    private Integer number;

    @Schema(description = "好评数" )
    private Integer praiseNumber;

    @Schema(description = "中评数" )
    private Integer secondaryNumber;

    @Schema(description = "差评数" )
    private Integer negativeNumber;

    @Schema(description = "有图数" )
    private Integer picNumber;

    @Schema(description = "1分评论数" )
    private Integer scoreNumber1;

    @Schema(description = "2分评论数" )
    private Integer scoreNumber2;

    @Schema(description = "3分评论数" )
    private Integer scoreNumber3;

    @Schema(description = "4分评论数" )
    private Integer scoreNumber4;

    @Schema(description = "5分评论数" )
    private Integer scoreNumber5;

}
