package com.yami.shop.distribution.common.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yami.shop.distribution.common.dao.DistributionProdBindMapper;
import com.yami.shop.distribution.common.model.DistributionProdBind;
import com.yami.shop.distribution.common.service.DistributionProdBindService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 分销商品绑定表
 *
 * <AUTHOR>
 * @date 2019-04-22 10:01:44
 */
@Service
@AllArgsConstructor
public class DistributionProdBindServiceImpl extends ServiceImpl<DistributionProdBindMapper, DistributionProdBind> implements DistributionProdBindService {

    private final DistributionProdBindMapper distributionProdBindMapper;

}
