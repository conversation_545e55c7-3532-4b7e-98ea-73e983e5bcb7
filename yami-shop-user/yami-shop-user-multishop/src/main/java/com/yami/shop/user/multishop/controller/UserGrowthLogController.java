package com.yami.shop.user.multishop.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.user.common.model.UserGrowthLog;
import com.yami.shop.user.common.service.UserGrowthLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户成长值记录
 *
 * <AUTHOR>
 * @date 2020-02-26 16:03:14
 */
@Tag(name = "用户成长值记录")
@RestController
@AllArgsConstructor
@RequestMapping("/user/userGrowthLog" )
public class UserGrowthLogController {

    private final UserGrowthLogService userGrowthLogService;

    @GetMapping("/pageByUserId" )
    @Operation(summary = "分页", description =  "分页")
    @PreAuthorize("@pms.hasPermission('user:userGrowthLog:page')")
    public ServerResponseEntity<IPage<UserGrowthLog>> getPageByUserId(PageParam<UserGrowthLog> page, String userId) {
        Long shopId = AuthUserContext.getShopId();
        IPage<UserGrowthLog> resPage = userGrowthLogService.page(page, new LambdaQueryWrapper<UserGrowthLog>()
                .eq(UserGrowthLog::getUserId, userId)
                .eq(UserGrowthLog::getShopId, shopId)
                .orderByDesc(UserGrowthLog::getCreateTime));
        return ServerResponseEntity.success(resPage);
    }
}
