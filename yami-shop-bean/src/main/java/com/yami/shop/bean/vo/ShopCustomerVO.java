package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @author: zsm
 * @date: 2023/1/13 10:47
 */
@Data
public class ShopCustomerVO {

    private Long shopCustomerId;
    @Schema(description = "用户id")
    private String userId;
    @Schema(description = "会员名称")
    private String memberName;
    @Schema(description = "店铺id")
    private Long shopId;
    @Schema(description = "会员类型（-1：客户，0：免费会员，1：付费会员）")
    private Integer levelType;
    @Schema(description = "会员等级Id")
    private Long levelId;
    @Schema(description = "会员等级")
    private Integer level;
    @Schema(description = "成长值")
    private Integer growth;
    @Schema(description = "会员注册时间")
    private Date registTime;
    @Schema(description = "付费会员到期时间")
    private Date vipEndTime;
    @Schema(description = "创建时间")
    private Date createTime;
    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "用户昵称")
    private String nickName;

    @Schema(description = "用户头像")
    private String pic;

    @Schema(description = "手机号码")
    private String userMobile;

    @Schema(description = "消费次数")
    private Integer consTimes;

    @Schema(description = "退款次数")
    private Integer afterSaleTimes;
}
