package com.yami.shop.bean.app.param;

import com.yami.shop.common.util.PrincipalUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "发送验证码参数")
public class CheckRegisterSmsParam {

    @Schema(description = "手机号" )
    @Pattern(regexp= PrincipalUtil.MOBILE_REGEXP,message = "请输入正确的手机号")
    private String mobile;

    @Schema(description = "验证码" )
    private String validCode;
}
