package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class VerificationCodeDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String phone;

    @Schema(description = "验证码类型(6:解绑手机, 9:绑定手机)，默认9")
    private Integer verificationCodeType;

    @Schema(description = "验证码(测试环境默认:11111)")
    private String verificationCode;

    public VerificationCodeDTO() {
    }

    public VerificationCodeDTO(String phone, Integer verificationCodeType) {
        this.phone = phone;
        this.verificationCodeType = verificationCodeType;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getVerificationCodeType() {
        return verificationCodeType;
    }

    public void setVerificationCodeType(Integer verificationCodeType) {
        this.verificationCodeType = verificationCodeType;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    @Override
    public String toString() {
        return "VerificationCodeDTO{" +
                "phone='" + phone + '\'' +
                ", verificationCodeType=" + verificationCodeType +
                ", verificationCode='" + verificationCode + '\'' +
                '}';
    }
}
