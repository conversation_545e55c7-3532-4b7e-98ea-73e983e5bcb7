package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class ApplyBindAcctDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "支付账户类型(weChatPublic:微信公众号, weChatMiniProgram:微信小程序, aliPayService:支付宝生活号, unionPayjs:银联JS)")
    private String acctType;

    @Schema(description = """
            支付账户用户标识(微信公众号支付openid——微信分配
            微信小程序支付openid——微信分配
            支付宝生活号支付user_id——支付宝分配
            银联JS支付user_id——银联分配，可根据【通过授权码（付款码）获取用户ID】接口获取
            附：openid示例oUpF8uMuAJO_M2pxb1Q9zNjWeS6o)""")
    private String acct;

    @Schema(description = "微信小程序/公众号登录code")
    private String wxLoginCode;

    public String getAcctType() {
        return acctType;
    }

    public void setAcctType(String acctType) {
        this.acctType = acctType;
    }

    public String getAcct() {
        return acct;
    }

    public void setAcct(String acct) {
        this.acct = acct;
    }

    public String getWxLoginCode() {
        return wxLoginCode;
    }

    public void setWxLoginCode(String wxLoginCode) {
        this.wxLoginCode = wxLoginCode;
    }

    @Override
    public String toString() {
        return "ApplyBindAcctDTO{" +
                "acctType='" + acctType + '\'' +
                ", acct='" + acct + '\'' +
                ", wxLoginCode='" + wxLoginCode + '\'' +
                '}';
    }
}
