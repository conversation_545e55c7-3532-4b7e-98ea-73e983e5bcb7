package com.yami.shop.groupbuy.common.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.bean.param.OfflineHandleEventAuditParam;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupActivityDto;
import com.yami.shop.groupbuy.common.dto.GroupActivityDto;
import com.yami.shop.groupbuy.common.model.GroupActivity;

import java.util.List;

/**
 * 拼团活动表
 *
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
public interface GroupActivityService extends IService<GroupActivity> {

    /**
     * 根据商品id获取拼团活动信息
     * @param prodId 商品id
     * @return 拼团活动信息
     */
    ApiGroupActivityDto getByProdId(Long prodId);

    /**
     * 跟新拼团活动为已删除状态
     * @param groupActivity 拼团活动信息
     */
    void updateToDelete(GroupActivity groupActivity);

    /**
     * 获取分页列表
     *
     * @param page             分页对象
     * @param groupActivityDto 拼团活动
     * @return 分页对象
     */
    IPage<GroupActivityDto> getGroupActivityPage(PageParam<GroupActivityDto> page, GroupActivityDto groupActivityDto);

    /**
     * 获取拼团活动信息
     *
     * @param groupActivityId 活动ID
     * @param prodId          商品ID
     * @return Api活动对象
     */
    ApiGroupActivityDto getApiGroupActivityInfo(Long groupActivityId, Long prodId);

    /**
     * 根据商品id清除团购活动缓存
     * @param prodId
     */
    void removeGroupActivityInfoCache(Long prodId);

    /**
     * 失效活动
     *
     * @param groupActivityId 团购活动信息
     */
    void invalidGroupActivity(Long groupActivityId);

    /**
     * 平台  -- 下架活动
     *
     * @param groupActivity 团购活动信息
     * @param offlineReason 下线原因
     * @param sysUserId 处理人id
     */
    void offline(GroupActivity groupActivity, String offlineReason, Long sysUserId);

    /**
     * 平台 -- 审核活动
     *
     * @param offlineHandleEventAuditParam 审核信息
     * @param sysUserId 处理人id
     */
    void auditGroupActivity(OfflineHandleEventAuditParam offlineHandleEventAuditParam, Long sysUserId);

    /**
     * 审核申请
     *
     * @param eventId 下线事件id
     * @param activityId 团购活动id
     * @param reapplyReason 申请理由
     */
    void auditApply(Long eventId, Long activityId, String reapplyReason);

    /**
     * 根据团购活动id列表，批量改变商品类型
     */
    void changeProdTypeByGroupActivityIdList();

    /**
     * 处理下下架商品时关联的团购活动
     * @param shopId 店铺id
     * @param prodId 商品id
     */
    void handleOfflineGroupProd(Long shopId, Long prodId);

    /**
     * 根据店铺id失效团购活动
     * @param shopId
     */
    void invalidGroupByShopId(Long shopId);

    /**
     * 保存团购信息
     * @param groupActivity
     */
    void saveGroupActivity(GroupActivity groupActivity);

    /**
     * 更新团购活动
     *
     * @param groupActivity
     * @return
     */
    void updateGroupActivity(GroupActivity groupActivity);

    /**
     * 获取团购信息（团购和绑定的商品信息）
     * @param groupActivityId
     * @return
     */
    GroupActivity getGroupActivityInfo(Long groupActivityId);

    /**
     * 根据拼团id列表查询是否有正常启动的拼团活动
     * @param groupIdList
     * @return
     */
    Boolean isHasEnableGroupActivityByIdList(List<Long> groupIdList);


}
