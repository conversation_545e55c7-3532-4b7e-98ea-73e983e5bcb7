package com.yami.shop.common.allinpay.order.req;

/**
 * 获取订单状态
 *
 * <AUTHOR>
 * @date 2023-11-01
 */
public class GetOrderStatus extends AbstractOrderReq {

    public static final String METHOD_NAME = "getOrderStatus";

    /**
     * 商户订单编号
     */
    private String bizOrderNo;

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    @Override
    public String toString() {
        return "GetOrderStatus{" +
                "bizOrderNo='" + bizOrderNo + '\'' +
                '}';
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }
}
