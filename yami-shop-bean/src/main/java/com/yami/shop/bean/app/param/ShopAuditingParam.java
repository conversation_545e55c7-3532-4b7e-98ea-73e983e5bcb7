package com.yami.shop.bean.app.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;


/**
 * 审核参数
 * <AUTHOR>
 */
@Data
public class ShopAuditingParam {

    @NotNull(message = "审核状态不能为空")
    @Schema(description = "0 未审核 1已通过 -1未通过 2平台下线 3 平台下线待审核" )
    private Integer status;

    @Schema(description = "0普通店铺 1优选好店" )
    private Integer shopType;

    @Schema(description = "签约起始时间" )
    private Date contractStartTime;

    @Schema(description = "签约终止时间" )
    private Date contractEndTime;

    @Schema(description = "备注" )
    private String remarks;

    @Schema(description = "审核人id" )
    private Long auditorId;

    @Schema(description = "店铺id" )
    private Long shopId;

}
