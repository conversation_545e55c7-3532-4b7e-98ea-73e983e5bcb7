package com.yami.shop.distribution.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "分销订单项")
public class DistributionOrderItemDto {

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "商品价格" )
    private Double price;

    @Schema(description = "商品图片" )
    private String pic;

    @Schema(description = "商品数量" )
    private Integer prodCount;

    @Schema(description = "规格名称" )
    private String skuName;
}
