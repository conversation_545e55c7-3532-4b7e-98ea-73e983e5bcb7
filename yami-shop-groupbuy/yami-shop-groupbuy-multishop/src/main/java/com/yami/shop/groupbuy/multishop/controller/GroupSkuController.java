package com.yami.shop.groupbuy.multishop.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.groupbuy.common.model.GroupSku;
import com.yami.shop.groupbuy.common.service.GroupSkuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
@RestController
@AllArgsConstructor
@RequestMapping("/group/sku")
@Tag(name = "商家端团购商品规格接口")
public class GroupSkuController {

    private final GroupSkuService groupSkuService;

    @GetMapping("/page")
    @Operation(summary = "分页查找团购商品规格列表")
    @PreAuthorize("@pms.hasPermission('group:sku:page')")
    public ServerResponseEntity<IPage<GroupSku>> getGroupSkuPage(PageParam<GroupSku> page, GroupSku groupSku) {
        return ServerResponseEntity.success(groupSkuService.page(page, new LambdaQueryWrapper<GroupSku>()));
    }

    @GetMapping("/info/{groupSkuId}")
    @Operation(summary = "通过id查询团购商品规格活动")
    @PreAuthorize("@pms.hasPermission('group:sku:info')")
    @Parameter(name = "groupSkuId", description = "团购商品规格id" , required = true)
    public ServerResponseEntity<GroupSku> getById(@PathVariable("groupSkuId") Long groupSkuId) {
        return ServerResponseEntity.success(groupSkuService.getById(groupSkuId));
    }

    @PostMapping
    @Operation(summary = "新增拼团活动商品规格")
    @PreAuthorize("@pms.hasPermission('group:sku:save')")
    public ServerResponseEntity<Boolean> save(@RequestBody @Valid GroupSku groupSku) {
        return ServerResponseEntity.success(groupSkuService.save(groupSku));
    }

    @PutMapping
    @Operation(summary = "修改拼团活动商品规格")
    @PreAuthorize("@pms.hasPermission('group:sku:update')")
    public ServerResponseEntity<Boolean> updateById(@RequestBody @Valid GroupSku groupSku) {
        return ServerResponseEntity.success(groupSkuService.updateById(groupSku));
    }

    @DeleteMapping("/{groupSkuId}")
    @Operation(summary = "通过id删除拼团活动商品规格")
    @PreAuthorize("@pms.hasPermission('group:sku:delete')")
    @Parameter(name = "groupSkuId", description = "团购商品规格id" , required = true)
    public ServerResponseEntity<Boolean> removeById(@PathVariable Long groupSkuId) {
        return ServerResponseEntity.success(groupSkuService.removeById(groupSkuId));
    }
}
