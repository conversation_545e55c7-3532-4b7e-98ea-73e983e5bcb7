package com.yami.shop.common.allinpay.constant;

import java.util.Objects;

/**
 * 支付系统 0默认 1通联支付
 * 钱包标识
 *
 * <AUTHOR>
 */
public enum PaySysType {
    /**
     * 默认
     */
    DEFAULT(0, "默认"),

    /**
     * 通联支付
     */
    ALLINPAY(1, "通联支付");

    private final Integer num;
    private final String name;

    public Integer value() {
        return num;
    }

    PaySysType(Integer num, String name) {
        this.num = num;
        this.name = name;
    }

    public static PaySysType instance(Integer value) {
        PaySysType[] enums = values();
        for (PaySysType paySysType : enums) {
            if (paySysType.value().equals(value)) {
                return paySysType;
            }
        }
        return null;
    }

    public static String getName(Integer value) {
        PaySysType paySysType = instance(value);
        if (Objects.isNull(paySysType)) {
            return null;
        }
        return paySysType.name;
    }
}
