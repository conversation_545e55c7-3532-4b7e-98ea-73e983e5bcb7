package com.yami.shop.distribution.common.dto;

import com.yami.shop.distribution.common.vo.DistributionUserVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
/**
 * <AUTHOR>
 */
@Data
public class DistributionUserDto {

    @Schema(description = "分销员id" )
    private Long distributionUserId;

    @Schema(description = "分销员昵称" )
    private String  nickName;

    @Schema(description = "分销员卡号" )
    private String cardNo;

    @Schema(description = "上级分销员信息" )
    private DistributionUserVO parentDistributionUser;

    @Schema(description = "分销员手机号" )
    private String userMobile;

    @Schema(description = "头像" )
    private String pic;

    @Schema(description = "分销员状态 (-1永久封禁 0 待审核 1 正常 2 暂时封禁 3审核失败)" )
    private Integer state;

    @Schema(description = "分销员等级" )
    private Integer level;

    @Schema(description = "分销员等级名称" )
    private String levelName;

    @Schema(description = "推广状态（0下线 1上线）" )
    private Integer recruitState;

}
