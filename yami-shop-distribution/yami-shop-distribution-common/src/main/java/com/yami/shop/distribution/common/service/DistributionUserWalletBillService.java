package com.yami.shop.distribution.common.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.distribution.common.dto.DistributionUserWalletBillDto;
import com.yami.shop.distribution.common.model.DistributionUserWalletBill;
import com.yami.shop.distribution.common.param.DistributionParam;


/**
 * 分销员钱包流水
 *
 * <AUTHOR>
 * @date 2019-04-29 16:39:13
 */
public interface DistributionUserWalletBillService extends IService<DistributionUserWalletBill> {

    /**
     * 获取分销员钱包流水记录
     * @param page 分页参数
     * @param distributionParam 分销查询参数
     * @return 分销员钱包流水记录
     */
    Page<DistributionUserWalletBill> getDistributionUserWalletBillAndUserPage(Page page, DistributionParam distributionParam);

    /**
     * 获取分销员钱包流水记录
     * @param page 分页参数
     * @param distributionUserId 分销员用户id
     * @param orderBy 排序方式
     * @return 分销员钱包流水记录
     */
    Page<DistributionUserWalletBillDto> getDistributionUserWalletBillDtoPage(Page page, Long distributionUserId,Integer orderBy);

    /**
     * 初始化分销钱包记录
     */
    void initWalletBill();
}
