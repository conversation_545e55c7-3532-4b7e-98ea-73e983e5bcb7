package com.yami.shop.distribution.common.dto;


import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "分销公告接口")
public class DistributionMsgDto {

    /**
     * 公告表
     */
    @TableId
    private Long msgId;

    /**
     * 公告级别(0 系统通知 1商家通知 2团队通知)
     */
    @Schema(description = "通知级别(0 系统通知 1商家通知 2团队通知)" )
    private Integer level;

    /**
     * 公告标题
     */
    @Schema(description = "通知标题" )
    private String msgTitle;

    /**
     * 发布时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "发布时间" )
    private Date startTime;


    /**
     * 是否置顶(0 不置顶 1 置顶)
     */
    @Schema(description = "是否置顶(0 不置顶 1 置顶)" )
    private Integer isTop;

    /**
     * 通知内容
     */
    @Schema(description = "通知内容" )
    private String content;

    /**
     * 通知封面图
     */
    @Schema(description = "通知封面图" )
    private String pic;

    /**
     * 通知类型(0:紧急通知， 1:活动通知，2:一般通知)
     */
    @Schema(description = "通知类型(0:紧急通知， 1:活动通知，2:一般通知)" )
    private Integer msgType;

}
