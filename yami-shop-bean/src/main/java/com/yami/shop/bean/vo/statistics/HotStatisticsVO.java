package com.yami.shop.bean.vo.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 热卖信息统计
 * <AUTHOR>
 */
@Data
public class HotStatisticsVO {

    @Schema(description = "店铺名称" )
    private String shopName;

    @Schema(description = "商品id")
    private Long prodId;

    @Schema(description = "商品名称")
    private String prodName;

    @Schema(description = "金额" )
    private Double amount;

    @Schema(description = "销量" )
    private Long sales;

    /**
     * 店铺id
     */
    private Long shopId;
}
