package com.yami.shop.bean.dto.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 创建或更新频道
 * <AUTHOR>
 */
public class ChannelDTO {

    @Schema(description = "频道的唯一ID")
    private String channel_id;

    @Schema(description = "频道的类型 1.个人频道 2.群聊频道")
    private Integer channel_type;

    @Schema(description = "是否是超大群，0.否 1.是")
    private Integer large;

    @Schema(description = "是否封禁此频道，0.否 1.是")
    private Integer ban;

    @Schema(description = "订阅者集合 [uid1,uid2,...]")
    private List<String> subscribers;

    public ChannelDTO(String channel_id, Integer channel_type, Integer large, List<String> subscribers) {
        this.channel_id = channel_id;
        this.channel_type = channel_type;
        this.large = large;
        this.subscribers = subscribers;
    }

    public ChannelDTO() {
    }

    public String getChannel_id() {
        return channel_id;
    }

    public void setChannel_id(String channel_id) {
        this.channel_id = channel_id;
    }

    public Integer getChannel_type() {
        return channel_type;
    }

    public void setChannel_type(Integer channel_type) {
        this.channel_type = channel_type;
    }

    public Integer getLarge() {
        return large;
    }

    public void setLarge(Integer large) {
        this.large = large;
    }

    public Integer getBan() {
        return ban;
    }

    public void setBan(Integer ban) {
        this.ban = ban;
    }

    public List<String> getSubscribers() {
        return subscribers;
    }

    public void setSubscribers(List<String> subscribers) {
        this.subscribers = subscribers;
    }

    @Override
    public String toString() {
        return "ChannelDTO{" +
                "channel_id='" + channel_id + '\'' +
                ", channel_type=" + channel_type +
                ", large=" + large +
                ", ban=" + ban +
                ", subscribers=" + subscribers +
                '}';
    }
}
