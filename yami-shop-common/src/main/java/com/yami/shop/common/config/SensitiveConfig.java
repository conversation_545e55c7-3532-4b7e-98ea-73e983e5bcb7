package com.yami.shop.common.config;

import com.github.houbb.heaven.util.io.StreamUtil;
import com.github.houbb.sensitive.word.bs.SensitiveWordBs;
import com.github.houbb.sensitive.word.support.resultcondition.WordResultConditions;


public class SensitiveConfig {

    /**
     * 一个单例的敏感词过滤器，
     */
    public static final SensitiveWordBs SENSITIVE_WORD_BS =
            SensitiveWordBs.newInstance()
                    // 该敏感词库来自于网易，经过删除大部分不必要的敏感词
                    .wordDeny(() -> StreamUtil.readAllLines("/sensitive163.txt"))
                    // 默认关闭敏感词过滤，需要的话，可以设置WordCheck为ture开启敏感词过滤，或者修改敏感词库，其他的几个false请不要修改
                    .enableWordCheck(false)
                    .enableEmailCheck(false)
                    .enableNumCheck(false)
                    .enableUrlCheck(false)
                    .ignoreNumStyle(false)
                    .wordResultCondition(WordResultConditions.englishWordMatch())
                    .init();
}
