package com.yami.shop.user.api.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.bean.PaySettlementConfig;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.api.util.SecurityUtils;
import com.yami.shop.service.SysConfigService;
import com.yami.shop.user.common.dto.UserBalanceLogDto;
import com.yami.shop.user.common.service.UserBalanceLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 余额充值记录
 *
 * <AUTHOR>
 * @date 2020-09-09 17:38:30
 */
@RestController
@AllArgsConstructor
@Tag(name = "用户余额记录接口")
@RequestMapping("/p/userBalanceLog" )
public class UserBalanceLogController {

    private final SysConfigService sysConfigService;
    private final UserBalanceLogService userBalanceLogService;

    /**
     * 分页查询用户余额记录
     * @param page 分页对象
     * @return 分页数据
     */
    @GetMapping("/log" )
    @Operation(summary = "查询用户余额记录" , description = "分页查询用户余额记录")
    public ServerResponseEntity<IPage<UserBalanceLogDto>> getUserBalanceLogPage(PageParam<UserBalanceLogDto> page) {
        String userId = SecurityUtils.getUser().getUserId();
        PaySettlementConfig config = sysConfigService.getSysConfigObject(Constant.PAY_SETTLEMENT_CONFIG, PaySettlementConfig.class);
        IPage<UserBalanceLogDto> userBalanceLogPage = userBalanceLogService.getLogPage(page, userId, config.getPaySettlementType());
        return ServerResponseEntity.success(userBalanceLogPage);
    }
}
