package com.yami.shop.multishop.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.HotSearch;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.HotSearchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 *
 * <AUTHOR> on 2019/03/27.
 */
@RestController
@RequestMapping("/admin/hotSearch")
@Tag(name = "热搜接口")
@AllArgsConstructor
public class HotSearchController {

    private final HotSearchService hotSearchService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('admin:hotSearch:page')")
    @Operation(summary = "分页获取热搜列表" , description = "分页获取热搜列表")
    public ServerResponseEntity<IPage<HotSearch>> page(HotSearch hotSearch, PageParam<HotSearch> page){

        IPage<HotSearch> hotSearchs = hotSearchService.page(page,new LambdaQueryWrapper<HotSearch>()
            .eq(HotSearch::getShopId, SecurityUtils.getShopUser().getShopId())
            .like(StrUtil.isNotBlank(hotSearch.getContent()), HotSearch::getContent,hotSearch.getContent())
                .like(StrUtil.isNotBlank(hotSearch.getTitle()), HotSearch::getTitle,hotSearch.getTitle())
            .eq(hotSearch.getStatus()!=null, HotSearch::getStatus,hotSearch.getStatus())
            .orderByDesc(HotSearch::getSeq)
        );
        return ServerResponseEntity.success(hotSearchs);
    }

    @GetMapping("/info/{id}")
    @Operation(summary = "根据热搜Id获取热搜信息" , description = "根据热搜Id获取热搜信息")
    @PreAuthorize("@pms.hasPermission('admin:hotSearch:info')")
    public ServerResponseEntity<HotSearch> info(@PathVariable("id") Long id){
        HotSearch hotSearch = hotSearchService.getById(id);
        if (!Objects.equals(SecurityUtils.getShopUser().getShopId(), hotSearch.getShopId())) {
            throw new YamiShopBindException("yami.no.auth");
        }
        return ServerResponseEntity.success(hotSearch);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('admin:hotSearch:save')")
    @Operation(summary = "新增热搜" , description = "新增热搜")
    public ServerResponseEntity<Void> save(@RequestBody @Valid HotSearch hotSearch){
        hotSearch.setRecDate(new Date());
        hotSearch.setShopId(SecurityUtils.getShopUser().getShopId());
        hotSearchService.save(hotSearch);
        //清除缓存
        hotSearchService.removeHotSearchDtoCacheByshopId(SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success();
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('admin:hotSearch:update')")
    @Operation(summary = "更新热搜" , description = "更新热搜")
    public ServerResponseEntity<Void> update(@RequestBody @Valid HotSearch hotSearch){
        hotSearchService.updateById(hotSearch);
        //清除缓存
        hotSearchService.removeHotSearchDtoCacheByshopId(SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success();
    }

    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('admin:hotSearch:delete')")
    @Operation(summary = "根据热搜id列表批量删除热搜" , description = "根据热搜id列表批量删除热搜")
    public ServerResponseEntity<Void> delete(@RequestBody List<Long> ids){
        hotSearchService.removeByIds(ids);
        //清除缓存
        hotSearchService.removeHotSearchDtoCacheByshopId(SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success();
    }
}
