package com.yami.shop.discount.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.app.dto.DiscountDto;
import com.yami.shop.bean.app.dto.ProductDto;
import com.yami.shop.bean.model.Product;
import com.yami.shop.bean.param.ProductParam;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.discount.common.model.Discount;
import com.yami.shop.discount.common.model.DiscountProd;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface DiscountMapper extends BaseMapper<Discount> {

    /**
     * 通过折扣活动ID获取满减满折优惠信息
     * @param discountId 折扣活动ID
     * @return 满减满折优惠
     */
    Discount getDiscountAndItemAndProdById(@Param("discountId") Long discountId);

    /**
     * 通过店铺ID获取满减满折优惠列表
     * @param shopId
     * @return 满减满折优惠列表
     */
    List<Discount> getDiscountsAndItemsByShopId(@Param("shopId") Long shopId);

    /**
     * 根据满减满折优惠ID与店铺ID删除满减满折优惠信息
     * @param id 满减满折优惠ID
     * @param shopId 店铺ID
     * @return 成功删除的数量
     */
    int deleteDiscounts(@Param("id") Long id, @Param("shopId") Long shopId);

    /**
     * 根据商品ID与店铺ID批量获取减满折优惠信息
     * @param prodId 商品ID
     * @param shopId 店铺ID
     * @return 减满折优惠信息列表
     */
    List<DiscountDto> listByProdId(@Param("prodId") Long prodId, @Param("shopId") Long shopId);

    /**
     * 分页获取折扣商品信息
     * @param page 分页参数
     * @param discount 减满折优惠信息
     * @return 商品列表
     */
    IPage<ProductDto> discountProdList(PageParam<ProductDto> page, @Param("discount") Discount discount);

    /**
     * 分页获取平台折扣信息
     * @param page 分页参数
     * @param discount 减满折优惠信息
     * @return 减满折优惠信息列表
     */
    IPage<Discount> getPlatformDiscountPage(@Param("page") PageParam<Discount> page, @Param("discount") Discount discount);

    /**
     * 通过折扣ID更新折扣状态
     * @param discountId 减满折优惠ID
     * @param status 状态
     * @return 成功更新数量
     */
    int updateStatusByDiscountId(@Param("discountId") Long discountId, @Param("status") Integer status);

    /**
     * 分页获取折扣信息
     * @param page 分页参数
     * @return 折扣信息列表
     */
    IPage<DiscountDto> getDiscountList(PageParam<Discount> page);

    /**
     * 根据折扣ID获取折扣信息
     * @param discountId 折扣ID
     * @return 折扣信息
     */
    DiscountDto getDiscountByDiscountId(@Param("discountId") Long discountId);

    /**
     * 通过更改状态关闭折扣
     */
    void closeDiscountBySetStatus();

    /**
     * 获取要被定时任务关闭的满减活动的店铺id
     * @return
     */
    List<Long> listShopIdsWhichWillClose();

    /**
     * 根据店铺ID获取店铺参与类型为所有商品参与的折扣活动数量
     * @param shopId 店铺ID
     * @param prodId 商品ID
     * @return 数量
     */
    Integer countProdIsSeckillAll(@Param("shopId") Long shopId, @Param("prodId") Long prodId);

    /**
     * 根据店铺ID与商品ID获取店铺参与类型为指定商品参与的折扣活动数量
     * @param shopId 店铺ID
     * @param prodId 商品ID
     * @return 数量
     */
    Integer countProdIsSeckillSome(@Param("shopId") Long shopId,@Param("prodId") Long prodId);

    /**
     * 根据店铺ID与商品ID获取店铺参与类型为指定商品不参与的折扣活动数量
     * @param shopId 店铺ID
     * @param prodId 商品ID
     * @return 数量
     */
    Integer countProdIsSeckillExcept(@Param("shopId") Long shopId, @Param("prodId") Long prodId);

    /**
     * 批量获取店铺下不是折扣商品的商品列表
     * @param shopId 店铺ID
     * @return 折扣商品列表
     */
    List<DiscountProd> getProdIsSeckillExcept(@Param("shopId") Long shopId);

    /**
     * 根据折扣分页获取商品列表
     * @param page 分页参数
     * @param product 商品参数
     * @param discount 折扣优惠信息
     * @return 商品列表
     */
    IPage<Product> pageProdByDiscount(PageParam<Product> page, @Param("product") ProductParam product, @Param("discount") Discount discount);

    /**
     * 根据店铺id与活动状态获取活动id集合
     * @param shopId
     * @param status
     * @return
     */
    List<Long> listIdByShopIdAndStatus(@Param("shopId") Long shopId, @Param("status") Integer status);

    /**
     * 获取正常的活动数目
     * @param discountIds
     * @return
     */
    int countNormalDiscount(@Param("discountIds")Set<Long> discountIds);

    /**
     * 获取在这一分钟内开始的满减活动对应的店铺id，清除缓存
     * @return
     */
    List<Long> listShopIdsWhichStart();
}
