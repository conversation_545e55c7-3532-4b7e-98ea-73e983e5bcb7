package com.yami.shop.distribution.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Schema(description = "商家提现设置")
@Data
public class DistributionWithdrawCashSetDto {

    /**
     * 提现频率(天)
     */
    @Schema(description = "提现频率(天)" )
    private Integer frequency;

    /**
     * 提现次数
     */
    @Schema(description = "提现次数(N天N次)" )
    private Integer number;

    /**
     * 单笔提现最高
     */
    @Schema(description = "单笔提现最高" )
    private Double amountMax;

    /**
     * 单笔提现最低
     */
    @Schema(description = "单笔提现最低" )
    private Double amountMin;

    /**
     * 打款说明
     */
    @Schema(description = "打款说明" )
    private String paymentExplain;




}
