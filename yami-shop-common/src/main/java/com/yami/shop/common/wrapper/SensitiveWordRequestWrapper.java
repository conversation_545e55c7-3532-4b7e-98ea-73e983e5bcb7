package com.yami.shop.common.wrapper;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.yami.shop.common.config.SensitiveConfig;
import com.yami.shop.common.util.PrincipalUtil;
import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

public class SensitiveWordRequestWrapper extends HttpServletRequestWrapper {

    private ObjectMapper mapper;

    public SensitiveWordRequestWrapper(HttpServletRequest request, ObjectMapper mapper) {
        super(request);
        this.mapper = mapper;
    }

    @Override
    public String getQueryString() {
        return filterSensitiveWordsInQuery(super.getQueryString());
    }

    @Override
    public String getParameter(String name) {
        return super.getParameter(name);
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        Map<String, String[]> paramMap = super.getParameterMap();
        paramMap.forEach((key, values) -> {
            for (int i = 0; i < values.length; i++) {
                values[i] = filterSensitiveWords(values[i]);
            }
        });
        return paramMap;
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        String body = new String(super.getInputStream().readAllBytes());
        body = filterSensitiveWordsInJson(body);
        byte[] bytes = body.getBytes();
        return new ServletInputStream() {
            private int lastIndexRetrieved = -1;
            private final InputStream inputStream = new ByteArrayInputStream(bytes);

            @Override
            public boolean isFinished() {
                return lastIndexRetrieved == bytes.length - 1;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener) {
            }

            @Override
            public int read() throws IOException {
                int i = inputStream.read();
                if (i != -1) {
                    lastIndexRetrieved++;
                }
                return i;
            }
        };
    }

    private String filterSensitiveWords(String input) {
        if (input == null) {
            return null;
        }
        return SensitiveConfig.SENSITIVE_WORD_BS.replace(input);
    }

    private String filterSensitiveWordsInQuery(String query) {
        if (query == null) {
            return null;
        }
        String[] params = query.split("&");
        StringBuilder filteredQuery = new StringBuilder();
        for (String param : params) {
            String[] keyValue = param.split("=");
            if (keyValue.length == 2) {
                filteredQuery.append(keyValue[0]).append("=")
                        .append(URLEncoder.encode(filterSensitiveWords(URLDecoder.decode(keyValue[1], StandardCharsets.UTF_8)), StandardCharsets.UTF_8)).append("&");
            } else {
                filteredQuery.append(param).append("&");
            }
        }
        return filteredQuery.toString().replaceAll("&$", "");
    }

    private String filterSensitiveWordsInJson(String json) throws IOException {
        if (!JSONUtil.isTypeJSON(json)) {
            return filterSensitiveWords(json);
        }
        JsonNode rootNode = mapper.readTree(json);

        if (rootNode.isObject()) {
            filterSensitiveWordsInJsonObject((ObjectNode) rootNode);
        } else if (rootNode.isArray()) {
            filterSensitiveWordsInJsonArray((ArrayNode) rootNode);
        } else if (rootNode.isTextual()) {
            return filterSensitiveWords(rootNode.asText());
        } else if (rootNode.isNumber()) {
            return rootNode.asText();
        }

        return mapper.writeValueAsString(rootNode);
    }

    private void filterSensitiveWordsInJsonObject(ObjectNode objectNode) {
        objectNode.fields().forEachRemaining(entry -> {
            if (entry.getValue().isTextual()) {
                objectNode.put(entry.getKey(), filterSensitiveWords(entry.getValue().asText()));
            } else if (entry.getValue().isObject()) {
                filterSensitiveWordsInJsonObject((ObjectNode) entry.getValue());
            } else if (entry.getValue().isArray()) {
                filterSensitiveWordsInJsonArray((ArrayNode) entry.getValue());
            }
        });
    }

    private void filterSensitiveWordsInJsonArray(ArrayNode arrayNode) {
        for (int i = 0; i < arrayNode.size(); i++) {
            JsonNode element = arrayNode.get(i);
            if (element.isTextual()) {
                arrayNode.set(i, new TextNode(filterSensitiveWords(element.asText())));
            } else if (element.isObject()) {
                filterSensitiveWordsInJsonObject((ObjectNode) element);
            } else if (element.isArray()) {
                filterSensitiveWordsInJsonArray((ArrayNode) element);
            }
        }
    }
}
