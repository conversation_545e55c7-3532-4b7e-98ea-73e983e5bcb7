package com.yami.shop.multishop.controller;

import com.yami.shop.bean.vo.ShopWalletVO;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.ShopWalletService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 商家钱包信息
 *
 * <AUTHOR>
 * @date 2019-09-19 14:02:57
 */
@RestController
@AllArgsConstructor
@RequestMapping("/shop/shopWallet" )
@Tag(name = "商家钱包接口")
public class ShopWalletController {

    private final ShopWalletService shopWalletService;

    /**
     * 通过id查询商家钱包信息
     * @return 单个数据
     */
    @GetMapping("/info" )
    @Operation(summary = "通过id查询商家钱包信息" , description = "通过id查询商家钱包信息")
    @PreAuthorize("@pms.hasPermission('shop:shopWallet:info')")
    public ServerResponseEntity<ShopWalletVO> info() {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        ShopWalletVO shopWallet = shopWalletService.getShopWalletAndTransaction(shopId);
        return ServerResponseEntity.success(shopWallet);
    }

}
