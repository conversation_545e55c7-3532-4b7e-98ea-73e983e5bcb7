package com.yami.shop.common.allinpay.order.req;

/**
 * 查询余额
 *
 * <AUTHOR>
 */
public class QueryOrderSplitRuleListDetail extends AbstractOrderReq {

    /**
     * 商户订单号（支付订单）
     */
    private String bizOrderNo;

    /**
     * 方法名称
     */
    public static final String METHOD_NAME = "getOrderSplitRuleListDetail";

    public QueryOrderSplitRuleListDetail(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    @Override
    public String toString() {
        return "QueryOrderSplitRuleListDetail{" +
                "bizOrderNo='" + bizOrderNo + '\'' +
                '}';
    }

}
