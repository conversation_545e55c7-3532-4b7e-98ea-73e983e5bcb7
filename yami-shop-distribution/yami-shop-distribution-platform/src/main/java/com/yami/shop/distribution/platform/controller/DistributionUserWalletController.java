package com.yami.shop.distribution.platform.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.model.DistributionUserWallet;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.service.DistributionUserWalletService;
import com.yami.shop.security.platform.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> on 2019/04/01.
 */
@RestController
@RequestMapping("/distribution/distributionUserWallet")
@Tag(name = "平台端分销员钱包接口")
@AllArgsConstructor
public class DistributionUserWalletController {

    private final DistributionUserWalletService distributionUserWalletService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('admin:distributionUserWallet:page')")
    @Operation(summary = "分页获取分销员钱包信息列表")
    public ServerResponseEntity<IPage<DistributionUserWallet>> page(DistributionUserWallet distributionUserWallet, PageParam<DistributionUserWallet> page) {
        IPage<DistributionUserWallet> list = distributionUserWalletService.page(page, new LambdaQueryWrapper<DistributionUserWallet>());
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/pageAndUser")
    @PreAuthorize("@pms.hasPermission('admin:distributionUserWallet:page')")
    @Operation(summary = "分页获取获取分销员绑定信息(携带UserVO)")
    @Parameter(name = "userMobile", description = "分销员手机号码" )
    public ServerResponseEntity<IPage<DistributionUserWallet>> pageAndUser(DistributionParam distributionParam, PageParam<DistributionUserWallet> page) {
        IPage<DistributionUserWallet> list = distributionUserWalletService.getDistributionUserWalletAndDistributionUserVoPage(page, distributionParam);
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/info/{id}")
    @PreAuthorize("@pms.hasPermission('admin:distributionUserWallet:info')")
    @Operation(summary = "根据id获取分销员钱包信息")
    @Parameter(name = "id", description = "分销员钱包id" )
    public ServerResponseEntity<DistributionUserWallet> info(@PathVariable("id") Long id) {
        DistributionUserWallet distributionUserWallet = distributionUserWalletService.getById(id);
        return ServerResponseEntity.success(distributionUserWallet);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('admin:distributionUserWallet:save')")
    @Operation(summary = "保存分销员钱包信息")
    public ServerResponseEntity<Void> save(@RequestBody @Valid DistributionUserWallet distributionUserWallet) {
        distributionUserWalletService.save(distributionUserWallet);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('admin:distributionUserWallet:update')")
    @Operation(summary = "修改分销员钱包信息")
    public ServerResponseEntity<Void> update(@RequestBody @Valid DistributionUserWallet distributionUserWallet) {
        distributionUserWalletService.updateDistributionUserWallet(distributionUserWallet, SecurityUtils.getSysUser().getUserId());
        return ServerResponseEntity.success();
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('admin:distributionUserWallet:delete')")
    @Operation(summary = "根据id删除分销员钱包信息")
    @Parameter(name = "id", description = "分销员钱包id" )
    public ServerResponseEntity<Void> delete(@PathVariable Long id) {
        distributionUserWalletService.removeById(id);
        return ServerResponseEntity.success();
    }
}
