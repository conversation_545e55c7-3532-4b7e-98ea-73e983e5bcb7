package com.yami.shop.distribution.common.listener;

import com.yami.shop.bean.enums.OrderType;
import com.yami.shop.bean.event.OrderSettlementEvent;
import com.yami.shop.bean.order.ConfirmOrderOrder;
import com.yami.shop.distribution.common.service.DistributionUserIncomeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 订单结算的事件
 * <AUTHOR>
 */
@Slf4j
@Component("orderSettlementDistributionListener")
@AllArgsConstructor
public class OrderSettlementListener {

    private final DistributionUserIncomeService distributionUserIncomeService;

    @EventListener(OrderSettlementEvent.class)
    @Order(ConfirmOrderOrder.DEFAULT)
    public void distributionSettlementOrderEvent(OrderSettlementEvent event) {
        List<String> orderNumbers = new ArrayList<>();
        Integer paySysType = event.getOrder().get(0).getPaySysType();
        for (com.yami.shop.bean.model.Order order : event.getOrder()) {
            //如果是积分订单无需操作
            if (Objects.equals(order.getOrderType(), OrderType.SCORE.value())) {
                continue;
            }
            orderNumbers.add(order.getOrderNumber());
        }
        if(CollectionUtils.isNotEmpty(orderNumbers)) {
            distributionUserIncomeService.commissionSettlementHandle(orderNumbers,paySysType);
        }
    }

}
