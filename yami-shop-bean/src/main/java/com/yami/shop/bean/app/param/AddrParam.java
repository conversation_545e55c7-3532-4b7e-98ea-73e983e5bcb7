package com.yami.shop.bean.app.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "地址参数")
public class AddrParam {

    @Schema(description = "地址ID" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Long addrId;

    @Schema(description = "邮编" )
    private String postCode;

    @NotNull(message = "收货人不能为空")
    @Schema(description = "收货人" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiver;

    @NotNull(message = "地址不能为空")
    @Schema(description = "地址" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String addr;

    @NotNull(message = "手机不能为空")
    @Schema(description = "手机" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String mobile;

    @NotNull(message = "省ID不能为空")
    @Schema(description = "省ID" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Long provinceId;

    @NotNull(message = "城市ID不能为空")
    @Schema(description = "城市ID" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Long cityId;

    @NotNull(message = "区ID不能为空")
    @Schema(description = "区ID" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Long areaId;

    @NotNull(message = "省不能为空")
    @Schema(description = "省" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @NotNull(message = "城市不能为空")
    @Schema(description = "城市" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @NotNull(message = "区不能为空")
    @Schema(description = "区" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String area;

    @Schema(description = "纬度" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Double lat;

    @Schema(description = "经度" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Double lng;

    @Schema(description = "是否默认地址 1是" )
    private Integer commonAddr;
}
