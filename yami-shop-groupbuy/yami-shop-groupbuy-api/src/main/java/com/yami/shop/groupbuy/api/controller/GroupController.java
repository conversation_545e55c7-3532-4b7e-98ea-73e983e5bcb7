package com.yami.shop.groupbuy.api.controller;

import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupTeamDto;
import com.yami.shop.groupbuy.common.api.vo.ApiGroupProdVo;
import com.yami.shop.groupbuy.common.service.GroupTeamService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/9/2 9:01
 */
@RestController
@RequestMapping("/group")
@Tag(name = "团购信息接口")
@AllArgsConstructor
public class GroupController {

    private final GroupTeamService groupTeamService;

    @GetMapping("/joinGroupList")
    @Operation(summary = "可加入的团列表" , description = "只显示最近n个团列表(默认10)")
    @Parameters({
            @Parameter(name = "groupActivityId",description = "拼团活动ID", required = true),
            @Parameter(name = "showSize", description = "显示数量（默认10）")
    })
    public ServerResponseEntity<ApiGroupProdVo> joinGroupList(@RequestParam(value = "groupActivityId") Long groupActivityId,
            @RequestParam(value = "showSize") Integer showSize) {
        ApiGroupProdVo apiGroupProdVo = new ApiGroupProdVo();
        List<ApiGroupTeamDto> list = groupTeamService.listJoinGroup(groupActivityId, showSize);
        List<ApiGroupTeamDto> sizeList = groupTeamService.listJoinGroup(groupActivityId, null);
        Integer joinNumSum  = sizeList.stream().mapToInt(ApiGroupTeamDto::getJoinNum).sum();
        apiGroupProdVo.setSumJoinNum(joinNumSum);
        apiGroupProdVo.setTeamList(list);

        return ServerResponseEntity.success(apiGroupProdVo);
    }
}
