package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class UnbindBankCardDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "银行卡号")
    private String cardNo;

    private Long shopBankCardId;

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Long getShopBankCardId() {
        return shopBankCardId;
    }

    public void setShopBankCardId(Long shopBankCardId) {
        this.shopBankCardId = shopBankCardId;
    }

    @Override
    public String toString() {
        return "UnbindBankCardDTO{" +
                "cardNo='" + cardNo + '\'' +
                ", shopBankCardId=" + shopBankCardId +
                '}';
    }
}
