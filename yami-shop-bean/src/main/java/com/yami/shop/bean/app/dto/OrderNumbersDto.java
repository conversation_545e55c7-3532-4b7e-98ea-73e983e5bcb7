package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderNumbersDto {
    @Schema(description = "多个订单号拼接的字符串" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNumbers;

    @Schema(description = "错误提交，为1时表示前端防重复提交没做好，需要返回商品详情" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer duplicateError;
    public OrderNumbersDto(String orderNumbers) {
        this.orderNumbers = orderNumbers;
    }
    public OrderNumbersDto() {
    }
}
