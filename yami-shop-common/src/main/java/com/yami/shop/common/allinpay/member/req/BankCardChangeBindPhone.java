package com.yami.shop.common.allinpay.member.req;

import com.yami.shop.common.allinpay.member.constant.IdentityType;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-03-23
 */
public class BankCardChangeBindPhone extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "bankCardChangeBindPhone";

    /**
     * 银行卡号
     */
    private String cardNo;
    /**
     * 银行预留手机
     */
    private String phone;
    /**
     * 姓名
     */
    private String name;
    /**
     * 绑卡方式
     */
    private Long cardCheck;
    /**
     * 证件类型（暂只支持身份证）
     *
     * @see IdentityType
     */
    private Integer identityType;
    /**
     * 证件号码
     */
    private String identityNo;
    /**
     * 有效期
     */
    private String validate;
    /**
     * cvv2
     */
    private String cvv2;

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCardCheck() {
        return cardCheck;
    }

    public void setCardCheck(Long cardCheck) {
        this.cardCheck = cardCheck;
    }

    public Integer getIdentityType() {
        return identityType;
    }

    public void setIdentityType(Integer identityType) {
        this.identityType = identityType;
    }

    public String getIdentityNo() {
        return identityNo;
    }

    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo;
    }

    public String getValidate() {
        return validate;
    }

    public void setValidate(String validate) {
        this.validate = validate;
    }

    public String getCvv2() {
        return cvv2;
    }

    public void setCvv2(String cvv2) {
        this.cvv2 = cvv2;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    @Override
    public String toString() {
        return "BankCardChangeBindPhone{" +
                "cardNo='" + cardNo + '\'' +
                ", phone='" + phone + '\'' +
                ", name='" + name + '\'' +
                ", cardCheck=" + cardCheck +
                ", identityType=" + identityType +
                ", identityNo='" + identityNo + '\'' +
                ", validate='" + validate + '\'' +
                ", cvv2='" + cvv2 + '\'' +
                ", bizUserId='" + bizUserId + '\'' +
                '}';
    }
}
