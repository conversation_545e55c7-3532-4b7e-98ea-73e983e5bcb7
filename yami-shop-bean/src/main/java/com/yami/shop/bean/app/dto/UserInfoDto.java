package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UserInfoDto {
    /**
     * 用户积分
     */
    @Schema(description = "用户积分" )
    private Integer score;
    /**
     * 用户余额
     */
    @Schema(description = "用户余额" )
    private Double totalBalance;
    /**
     * 通联用户余额
     */
    @Schema(description = "通联用户余额")
    private Double allinpayTotalBalance;
    /**
     * 优惠券数量
     */
    @Schema(description = "优惠券数量" )
    private Integer couponNum;
    /**
     * 消息
     */
    @Schema(description = "消息数量" )
    private Integer notifyNum;
}
