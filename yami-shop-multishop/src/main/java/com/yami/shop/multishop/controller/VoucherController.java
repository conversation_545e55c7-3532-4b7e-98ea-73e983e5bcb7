package com.yami.shop.multishop.controller;



import cn.hutool.core.collection.CollectionUtil;
import com.yami.shop.bean.dto.VoucherDTO;
import com.yami.shop.bean.enums.EsOperationType;
import com.yami.shop.bean.enums.ProdMoldEnum;
import com.yami.shop.bean.enums.VoucherItemStatus;
import com.yami.shop.bean.event.EsProductUpdateEvent;
import com.yami.shop.bean.model.Sku;
import com.yami.shop.bean.model.Voucher;
import com.yami.shop.bean.model.VoucherItem;
import com.yami.shop.bean.vo.VoucherVO;
import com.yami.shop.common.enums.StatusEnum;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.service.ProductService;
import com.yami.shop.service.SkuService;
import com.yami.shop.service.VoucherItemService;
import com.yami.shop.service.VoucherService;
import jakarta.validation.Valid;
import com.yami.shop.common.response.ServerResponseEntity;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yami.shop.common.util.PageParam;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/multishop/voucher")
@Tag(name = "卡券管理")
@AllArgsConstructor
public class VoucherController {

    private final VoucherService voucherService;

    private final VoucherItemService voucherItemService;

    private final ProductService productService;

    private final SkuService skuService;

    private final ApplicationEventPublisher eventPublisher;


    @GetMapping("/page")
    @Operation(summary = "获取列表", description = "分页获取列表")
    public ServerResponseEntity<IPage<VoucherVO>> getVoucherPage(PageParam<VoucherVO> page, VoucherDTO voucher) {
        voucher.setShopId(AuthUserContext.get().getShopId());
        return ServerResponseEntity.success(voucherService.page(page, voucher));
    }

    @GetMapping("/info/{voucherId}")
    @Operation(summary = "获取", description = "根据voucherId获取")
    @Parameter(name = "voucherId", description = "", required = true)
    public ServerResponseEntity<Voucher> getById(@PathVariable("voucherId") Long voucherId) {
        return ServerResponseEntity.success(voucherService.getById(voucherId));
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('multishop:voucher:save')")
    @Operation(summary = "保存", description = "保存")
    public ServerResponseEntity<Boolean> save(@RequestBody @Valid Voucher voucher) {
        voucher.setShopId(AuthUserContext.get().getShopId());
        return ServerResponseEntity.success(voucherService.insert(voucher));
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('multishop:voucher:update')")
    @Operation(summary = "更新", description = "更新")
    public ServerResponseEntity<Boolean> updateById(@RequestBody @Valid Voucher voucher) {
        List<Voucher> list = voucherService.list(new LambdaQueryWrapper<>(Voucher.class).eq(Voucher::getShopId, AuthUserContext.get().getShopId())
                .eq(Voucher::getName, voucher.getName())
                .eq(Voucher::getStatus, 0)
                .ne(Voucher::getVoucherId, voucher.getVoucherId()));
        if (CollectionUtil.isNotEmpty(list)) {
            throw new YamiShopBindException("yami.voucher.name.repeat");
        }
        return ServerResponseEntity.success(voucherService.updateById(voucher));
    }

    @DeleteMapping("/{voucherId}")
    @PreAuthorize("@pms.hasPermission('multishop:voucher:delete')")
    @Operation(summary = "删除",   description = "根据id删除")
    @Parameter(name = "voucherId", description = "", required = true)
    public ServerResponseEntity<Boolean> removeById(@PathVariable Long voucherId) {
        Voucher voucher = voucherService.getById(voucherId);
        if (Objects.isNull(voucher)) {
            throw new YamiShopBindException("yami.voucher.throw.tip.cardAbsent");
        }
        List<VoucherItem> itemList = voucherItemService.list(new LambdaQueryWrapper<>(VoucherItem.class).eq(VoucherItem::getVoucherId, voucherId).eq(VoucherItem::getStatus, VoucherItemStatus.NOTENABLED.value()));
        if (CollectionUtil.isNotEmpty(itemList)) {
            throw new YamiShopBindException("yami.voucher.throw.tip.cardUnissued");
        }
        voucher.setStatus(-1);
        Sku sku = skuService.getOne(new LambdaQueryWrapper<>(Sku.class).ne(Sku::getIsDelete, 1).eq(Sku::getVoucherId, voucherId));
        if (Objects.nonNull(sku)) {
            // 下架关联的卡券商品
            productService.updateProdStatus(Collections.singletonList(sku.getProdId()), StatusEnum.DISABLE.value());
            eventPublisher.publishEvent(new EsProductUpdateEvent(sku.getProdId(), null, EsOperationType.SAVE));
            // 移除sku关联的卡券
            sku.setVoucherId(null);
            skuService.updateSku(sku, ProdMoldEnum.VOUCHER.value());
        }
        return ServerResponseEntity.success(voucherService.updateById(voucher));
    }
}
