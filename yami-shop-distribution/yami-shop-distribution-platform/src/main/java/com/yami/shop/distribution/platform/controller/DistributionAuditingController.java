package com.yami.shop.distribution.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.dto.DistributionAuditingDto;
import com.yami.shop.distribution.common.model.DistributionAuditing;
import com.yami.shop.distribution.common.model.DistributionUser;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.param.RangeTimeParam;
import com.yami.shop.distribution.common.service.DistributionAuditingService;
import com.yami.shop.distribution.common.service.DistributionUserService;
import com.yami.shop.security.platform.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * <AUTHOR> on 2019/04/01.
 */
@RestController
@RequestMapping("/distribution/distributionAuditing")
@Tag(name = "平台端分销员审核接口")
@AllArgsConstructor
public class DistributionAuditingController {

    private final DistributionAuditingService distributionAuditingService;
    private final DistributionUserService distributionUserService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('distribution:distributionAuditing:page')")
    @Operation(summary = "分页查看分销员审核信息")
    @Parameters({
            @Parameter(name = "userMobile", description = "分销员手机号码" ),
            @Parameter(name = "startExpenseNumber", description = "最低订单数量" ),
            @Parameter(name = "endExpenseNumber", description = "最高订单数量" ),
            @Parameter(name = "startPayAmount", description = "最低消费金额" ),
            @Parameter(name = "endPayAmount", description = "最高消费金额" )
    })
    public ServerResponseEntity<IPage<DistributionAuditingDto>> page(DistributionAuditing distributionAuditing, RangeTimeParam rangeTimeParam,
                                                                     DistributionParam distributionParam, Integer startExpenseNumber, Integer endExpenseNumber,
                                                                     Double startPayAmount, Double endPayAmount, PageParam<DistributionAuditing> page) {
        IPage<DistributionAuditingDto> list = distributionAuditingService.distributionAuditingPage(page, distributionAuditing, rangeTimeParam, startExpenseNumber, endExpenseNumber, startPayAmount, endPayAmount, distributionParam);
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/info/{id}")
    @PreAuthorize("@pms.hasPermission('distribution:distributionAuditing:info')")
    @Operation(summary = "根据分销员申请id获取分销员申请信息")
    @Parameter(name = "id", description = "分销员申请信息id" )
    public ServerResponseEntity<DistributionAuditing> info(@PathVariable("id") Long id) {
        DistributionAuditing distributionAuditing = distributionAuditingService.getById(id);
        return ServerResponseEntity.success(distributionAuditing);
    }

    /**
     * 审核
     */
    @PutMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionAuditing:update')")
    @Operation(summary = "审核分销员")
    public ServerResponseEntity<Void> update(@RequestBody @Valid DistributionAuditing distributionAuditing) {
        distributionAuditing.setUpdateTime(new Date());
        distributionAuditing.setModifier(SecurityUtils.getSysUser().getUserId());
        distributionAuditingService.examine(distributionAuditing);
        DistributionUser distributionUser = distributionUserService.getById(distributionAuditing.getDistributionUserId());
        distributionUserService.removeCacheByUserIdAndShopId(distributionUser.getUserId(), distributionAuditing.getShopId());
        distributionUserService.removeCacheByCardNo(distributionUser.getCardNo());
        return ServerResponseEntity.success();
    }
}
