package com.yami.shop.live.multishop.config;



import lombok.AllArgsConstructor;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration("liveSwaggerConfiguration")
@AllArgsConstructor
public class SwaggerConfiguration {


    @Bean
    public GroupedOpenApi liveRestApi() {
        return GroupedOpenApi.builder()
                .group("直播接口")
                .packagesToScan("com.yami.shop.live.multishop.controller")
                .pathsToMatch("/**")
                .build();
    }

}
