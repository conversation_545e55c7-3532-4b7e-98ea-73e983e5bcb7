package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class UpdateCompanyInfoResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 商户的用户id
     */
    private String bizUserId;
    /**
     * 商户请求流水号
     */
    private String reqsn;
    /**
     * 修改结果(2:成功, 3:失败 4.处理中)
     */
    private Integer result;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * OCR识别与企业工商认证信息是否一致 0否1是
     */
    private Integer ocrRegnumComparisonResult;
    /**
     * OCR识别与企业法人实名信息是否一致 0否1是
     */
    private Integer ocrIdcardComparisonResult;
    /**
     * 比对结果信息 	存在多种结果信息一起返回，使用“;”进行拼接
     */
    private String resultInfo;

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getReqsn() {
        return reqsn;
    }

    public void setReqsn(String reqsn) {
        this.reqsn = reqsn;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public Integer getOcrRegnumComparisonResult() {
        return ocrRegnumComparisonResult;
    }

    public void setOcrRegnumComparisonResult(Integer ocrRegnumComparisonResult) {
        this.ocrRegnumComparisonResult = ocrRegnumComparisonResult;
    }

    public Integer getOcrIdcardComparisonResult() {
        return ocrIdcardComparisonResult;
    }

    public void setOcrIdcardComparisonResult(Integer ocrIdcardComparisonResult) {
        this.ocrIdcardComparisonResult = ocrIdcardComparisonResult;
    }

    public String getResultInfo() {
        return resultInfo;
    }

    public void setResultInfo(String resultInfo) {
        this.resultInfo = resultInfo;
    }

    @Override
    public String toString() {
        return "UpdateCompanyInfoResp{" +
                "bizUserId='" + bizUserId + '\'' +
                ", reqsn='" + reqsn + '\'' +
                ", result=" + result +
                ", failReason='" + failReason + '\'' +
                ", ocrRegnumComparisonResult=" + ocrRegnumComparisonResult +
                ", ocrIdcardComparisonResult=" + ocrIdcardComparisonResult +
                ", resultInfo='" + resultInfo + '\'' +
                '}';
    }
}
