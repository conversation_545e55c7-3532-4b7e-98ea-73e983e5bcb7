package com.yami.shop.bean.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @since 2024/6/13 14:04
 */
@Data
public class StationPeoductVO {
    @Schema(description = "门店id")
    private Long stationId;

    @Schema(description = "商品id")
    private Long prodId;

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "商品价格")
    private Double price;
}
