package com.yami.shop.bean.vo.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 消息头
 * <AUTHOR>
 */
public class HeaderVO {

    @Schema(description = "是否不存储消息 0.存储 1.不存储")
    private Integer noPersist;

    @Schema(description = "是否显示红点计数，0.不显示 1.显示")
    private Integer redDot;

    @Schema(description = "是否是写扩散，这里一般是0，只有cmd消息才是1")
    private Integer syncOnce;

    public Integer getNoPersist() {
        return noPersist;
    }

    public void setNoPersist(Integer noPersist) {
        this.noPersist = noPersist;
    }

    public Integer getRedDot() {
        return redDot;
    }

    public void setRedDot(Integer redDot) {
        this.redDot = redDot;
    }

    public Integer getSyncOnce() {
        return syncOnce;
    }

    public void setSyncOnce(Integer syncOnce) {
        this.syncOnce = syncOnce;
    }

    @Override
    public String toString() {
        return "HeaderVO{" +
                "noPersist=" + noPersist +
                ", redDot=" + redDot +
                ", syncOnce=" + syncOnce +
                '}';
    }
}
