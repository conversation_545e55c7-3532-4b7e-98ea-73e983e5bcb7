package com.yami.shop.groupbuy.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 拼团订单状态类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum GroupOrderStatusEnum {

    /** 待支付 */
    WAITING_PAY(0, "待支付"),

    /** 支付成功 */
    SUCCESS(1, "支付成功"),

    /** 失效 */
    FAIL(-1, "失效");

    private final Integer code;

    private final String title;

    public Integer value() {
        return code;
    }

}
