package com.yami.shop.multishop.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alipay.api.AlipayApiException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.yami.shop.bean.bo.RefundInfoBo;
import com.yami.shop.bean.dto.OrderRefundDto;
import com.yami.shop.bean.enums.*;
import com.yami.shop.bean.model.*;
import com.yami.shop.bean.param.OrderRefundParam;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.constants.PlatformInterventionStatus;
import com.yami.shop.common.enums.PayType;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.delivery.common.model.DeliveryOrder;
import com.yami.shop.delivery.common.service.DeliveryOrderService;
import com.yami.shop.manager.impl.PayManager;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/order/refund")
@Tag(name = "订单退款记录接口")

public class OrderRefundController {

    private final OrderRefundService orderRefundService;
    private final OrderVirtualInfoService orderVirtualInfoService;
    private final PayManager payManager;
    private final OrderItemService orderItemService;
    private final OrderService orderService;
    private final DeliveryOrderService deliveryOrderService;
    private final RefundInfoService refundInfoService;
    private static final Logger LOG = LoggerFactory.getLogger(OrderRefundController.class);

    @GetMapping("/page")
    @Operation(summary = "分页获取退款订单" , description = "分页获取退款订单")
    @PreAuthorize("@pms.hasPermission('order:refund:page')")
    public ServerResponseEntity<IPage<OrderRefundDto>> getOrderRefundPage(PageParam<OrderRefundDto> page, OrderRefundDto orderRefundDto,
                                                                    @RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime) {
        orderRefundDto.setShopId(SecurityUtils.getShopUser().getShopId());
        IPage<OrderRefundDto> refundPage = orderRefundService.getPage(page, orderRefundDto, startTime, endTime, 1);
        List<OrderRefundDto> records = refundPage.getRecords();
        Date platformHandleTime = DateUtil.offsetDay(new Date(), -3).toJdkDate();
        for (OrderRefundDto record : records) {
            if(Objects.nonNull(record.getPlatformInterventionStatus()) && Objects.equals(record.getPlatformInterventionStatus(),PlatformInterventionStatus.APPLY.value())) {
                record.setPlatformInterventionStatus(DateUtil.compare(platformHandleTime, record.getApplyInterventionTime()) > 0 ? PlatformInterventionStatus.HANDLE.value() : record.getPlatformInterventionStatus());
            }
        }
        return ServerResponseEntity.success(refundPage);
    }

    @GetMapping("/info/{refundId}")
    @Operation(summary = "根据退款订单id获取退款信息" , description = "根据退款订单id获取退款信息")
    @PreAuthorize("@pms.hasPermission('order:refund:info')")
    public ServerResponseEntity<OrderRefund> getById(@PathVariable("refundId") Long refundId) {
        OrderRefund orderRefund = orderRefundService.getOrderRefundById(refundId, SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success(orderRefund);
    }

    /**
     * 自动化测试的接口，目前未在功能中使用
     * @param refundSn
     * @return
     */
    @GetMapping("/infoByRefundSn")
    @Operation(summary = "根据退款编号获取退款信息" , description = "根据退款编号获取退款信息")
    public ServerResponseEntity<OrderRefund> infoByRefundSn(@RequestParam("refundSn") String refundSn) {
        OrderRefund orderRefund = orderRefundService.getOrderRefundByRefundSn(refundSn, SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success(orderRefund);
    }

    /**
     * 进入这个方法，会出现两种情况：
     * 1. 仅退款，此时商家同意买家的退款申请，执行发放退款的操作
     * 2. 退货退款操作:
     *   2.1)退货退款的第一步，商家允许买家退款的申请，商家进行设置退货地址，不执行发放退款的操作
     *   2.2)退货退款的第二步，当商家收到货之后，同意买家退款，此时需要发放退款，但不会执行这个方法，执行的是下面这个方法
     *   @see com.yami.shop.multishop.controller.OrderRefundController#returnMoney(OrderRefundParam)
     *
     *
     */
    @SysLog("退款处理 -商家处理退款订单")
    @PutMapping("/process")
    @Operation(summary = "处理退款订单" , description = "处理退款订单")
    @PreAuthorize("@pms.hasPermission('order:refund:update')")
    public ServerResponseEntity<Void> processRefundOrder(@RequestBody OrderRefundParam orderRefundParam) {

        // 处理退款操作
        orderRefundParam.setIsPaltform(false);
        OrderRefundDto orderRefundDto = orderRefundService.processRefundOrder(orderRefundParam, SecurityUtils.getShopUser().getShopId());

        // 仅退款，执行退款操作
        if (Objects.equals(orderRefundDto.getApplyType(), 1)) {
            this.submitWxRefund(orderRefundDto);
        }
        if (Objects.equals(orderRefundDto.getReturnMoneySts(), ReturnMoneyStsType.SUCCESS.value())) {
            // 将orderItem.status改成 0 表示可以发货
            OrderItem orderItem = orderItemService.getOne(new LambdaQueryWrapper<OrderItem>().eq(OrderItem::getOrderItemId, orderRefundDto.getOrderItemId()));
            if(Objects.isNull(orderItem)){
                return ServerResponseEntity.success();
            }
            String orderNumber = orderItem.getOrderNumber();
            List<OrderItem> orderItems1 = orderItemService.list(new LambdaQueryWrapper<OrderItem>().eq(OrderItem::getOrderNumber, orderNumber));
            if (orderItems1.size() <=1 ) {
                return ServerResponseEntity.success();
            }
            orderItemService.update(new LambdaUpdateWrapper<OrderItem>()
                    .set(OrderItem::getStatus,0)
                    .eq(OrderItem::getOrderItemId,orderItem.getOrderItemId()));
            // 如果该单是商品项目的所有订单都是已发货，其他的都是退款成功，则可以改成订单项发货状态
            List<OrderItem> orderItems = orderItemService.list(new LambdaQueryWrapper<OrderItem>().eq(OrderItem::getOrderNumber, orderNumber));
            // 如果所有的订单都是申请退款，并且退款成功，则不改变
            int total = 0;
            for (OrderItem item : orderItems) {
                long count = orderRefundService.count(new LambdaQueryWrapper<OrderRefund>()
                        .eq(OrderRefund::getOrderItemId, item.getOrderItemId())
                        .eq(OrderRefund::getReturnMoneySts, 5));
                if (count > 0) {
                    total++;
                }
            }
            // 所有订单都是退款成功
            if (total == orderItems.size()) {
                return ServerResponseEntity.success();
            }
            boolean isOk = true;
            for (OrderItem item : orderItems) {
                if (item.getStatus() != 0) {
                    isOk = false;
                    break;
                }
            }
            // 所有的订单，有已发货的订单

            if (isOk) {
                Order order = new Order();
                order.setOrderId(orderRefundDto.getOrderId());
                // 更新到发货状态
                Date date = new Date();
                order.setStatus(OrderStatus.CONSIGNMENT.value());
                order.setUpdateTime(date);
                order.setDvyTime(date);
                Integer type = null;
                for (OrderItem item : orderItems) {
                    if (Objects.nonNull(item.getDvyType())){
                        type = item.getDvyType();
                    }
                }
                List<DeliveryOrder> deliveryOrders = deliveryOrderService.list(new LambdaQueryWrapper<DeliveryOrder>().eq(DeliveryOrder::getOrderNumber, orderNumber));
                if (!CollectionUtils.isEmpty(deliveryOrders)){
                    order.setDvyType(1);
                } else {
                    order.setDvyType(type);
                }
                orderService.updateById(order);
            }

        }
        return ServerResponseEntity.success();
    }

    @SysLog("退款处理 -商家退货处理")
    @PutMapping("/returnMoney")
    @Operation(summary = "退款处理-商家退货处理" , description = "退款处理-商家退货处理")
    @PreAuthorize("@pms.hasPermission('order:refund:update')")
    public ServerResponseEntity<Void> returnMoney(@RequestBody OrderRefundParam orderRefundParam) {
        if (Objects.isNull(orderRefundParam.getIsReceiver())) {
            // 退货状态不能为空
            throw new YamiShopBindException("yami.order.refund.status.exist");
        }
        // 退货处理
        OrderRefundDto orderRefundDto = orderRefundService.returnMoney(orderRefundParam, SecurityUtils.getShopUser().getShopId());
        // 执行退款操作
        this.submitWxRefund(orderRefundDto);
        return ServerResponseEntity.success();
    }

    @RequestMapping("/result/{paySysType}/{payType}")
    @Operation(summary = "退款结果通知" , description = "退款结果通知")
    public ServerResponseEntity<String> result(HttpServletRequest request,
                                               @RequestBody(required = false) String data,
                                               @PathVariable Integer paySysType,
                                               @PathVariable Integer payType) {

        RefundInfoBo refundInfoBo = null;
        try {
            refundInfoBo = payManager.validateAndGetRefundInfo(request,paySysType, PayType.instance(payType), data);
        } catch (UnsupportedEncodingException | WxPayException | AlipayApiException e) {
            LOG.error("退款回调校验异常",e);
            throw new YamiShopBindException(e.toString());
        }
        RefundInfo refundInfo = refundInfoService.getOne(new LambdaQueryWrapper<RefundInfo>().eq(RefundInfo::getRefundId, refundInfoBo.getRefundNo()));
        refundInfo.setPayRefundId(refundInfoBo.getBizRefundNo());
        if (!refundInfoBo.getIsRefundSuccess()) {
            if (StrUtil.isNotBlank(refundInfoBo.getCallbackContent())) {
                refundInfo.setCallbackContent(refundInfoBo.getCallbackContent());
                refundInfo.setCallbackTime(new Date());
                refundInfoService.updateById(refundInfo);
            }
            return ServerResponseEntity.success(refundInfoBo.getSuccessString());
        }
        this.refundSuccess(refundInfo);
        return ServerResponseEntity.success(refundInfoBo.getSuccessString());
    }

    @GetMapping("/isLastRefund")
    @Operation(summary ="是否为最后一单退款")
    @PreAuthorize("@pms.hasPermission('order:refund:info')")
    public ServerResponseEntity<Boolean> isLastRefund(String refundSn) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        return ServerResponseEntity.success(orderRefundService.getIsLastRefund(refundSn, shopId));
    }

    @PostMapping("/doActiveRefund")
    @Operation(summary = "虚拟商品订单未结算超过确认收货后七天,商家主动退款")
    public ServerResponseEntity<Long> executeRefundByVitrualOrder(@RequestBody OrderRefundDto orderRefundParam) {
        Date date = new Date();
        Order orderDb = orderService.getOrderAndOrderItemByOrderNumber(orderRefundParam.getOrderNumber());
        DateTime dateTime = DateUtil.offsetDay(date, Constant.MAX_REFUND_APPLY_TIME);
        if(Objects.isNull(orderDb) || !Objects.equals(orderDb.getShopId(),AuthUserContext.get().getShopId())){
            throw new YamiShopBindException("订单不存在");
        }
        if(!Objects.equals(orderDb.getOrderMold(), ProdMoldEnum.VIRTUAL.value())){
            throw new YamiShopBindException("不是虚拟商品订单,商家不能进行主动退款");
        }
        if(!Objects.equals(orderDb.getStatus(), OrderStatus.SUCCESS.value()) || DateUtil.compare(orderDb.getFinallyTime(),date) > 0){
            throw new YamiShopBindException("当前未到商家可以主动退款的时间,不能进行主动退款");
        }

        // 判断虚拟商品能否退款
        //已使用或者已到期，不支持主动退款
        boolean virtualConditionNum =  Objects.equals(orderDb.getWriteOffStatus(),1) || (Objects.nonNull(orderDb.getWriteOffEnd()) && DateUtil.compare(orderDb.getWriteOffEnd(),new Date()) <= 0);
        if(virtualConditionNum){
            throw new YamiShopBindException("虚拟商品订单已使用过或者已到期，不支持主动退款");
        }

        // 获取所有正在进行中的退款订单
        List<OrderRefund> orderRefunds = orderRefundService.getProcessingOrderRefundByOrderId(orderDb.getOrderId());
        if (CollectionUtil.isNotEmpty(orderRefunds)) {
            return ServerResponseEntity.showFailMsg("该订单正在进行整单退款或者正在进行平台介入中，无法进行新的退款操作");
        }
        Double virtualOrderMaxRefundAmount = orderVirtualInfoService.getVirtualOrderMaxRefundAmount(orderDb);

        if(orderRefundParam.getRefundAmount() > virtualOrderMaxRefundAmount){
            return ServerResponseEntity.showFailMsg("当前退款金额大于订单剩余可退款金额");
        }

        OrderRefundDto orderRefundDto = orderRefundService.executeRefundByVitrualOrder(orderRefundParam, orderDb);
        // 提交退款请求
        this.submitWxRefund(orderRefundDto);
        return ServerResponseEntity.success();
    }

    private void submitWxRefund(OrderRefundDto orderRefundDto) {
        orderRefundService.submitWxRefund(orderRefundDto);
        orderRefundService.handleRefundStock(orderRefundDto.getStockKeys());
    }

    private void refundSuccess(RefundInfo refundInfo) {
        refundInfoService.refundSuccess(refundInfo);
        orderRefundService.handleRefundStock(refundInfo.getStockKeys());
    }
}
