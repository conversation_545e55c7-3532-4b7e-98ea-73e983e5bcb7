package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-09
 */
@Data
public class DateUserWithdrawCashVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "提现年月")
    private Date date;

    @Schema(description = "提现记录")
    private List<UserWithdrawCashVO> withdrawCashVOs;
}
