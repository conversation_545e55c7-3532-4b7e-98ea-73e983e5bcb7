package com.yami.shop.platform.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yami.shop.bean.model.UserAddr;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.UserAddrService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR> on 2018/10/16.
 */
@RestController
@RequestMapping("/platform/userAddr")
@Tag(name = "用户地址")
@AllArgsConstructor
public class UserAddrController {

    private final UserAddrService userAddrService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('plateform:user:page')")
    @Operation(summary = "分页获取" , description = "分页获取")
    public ServerResponseEntity<List<UserAddr>> page(UserAddr userAddr, PageParam<UserAddr> page) {
        List<UserAddr> userAddrPage = userAddrService.list(new LambdaQueryWrapper<UserAddr>()
                .eq(UserAddr::getUserId, userAddr.getUserId()));
        return ServerResponseEntity.success(userAddrPage);
    }

}
