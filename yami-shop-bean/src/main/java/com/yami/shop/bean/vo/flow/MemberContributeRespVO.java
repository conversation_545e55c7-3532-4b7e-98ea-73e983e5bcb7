package com.yami.shop.bean.vo.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
@Data
public class MemberContributeRespVO {

    @Schema(description = "普通会员" )
    private MemberContributeValueVO publicMember;

    @Schema(description = "付费会员" )
    private MemberContributeValueVO paidMember;

    @Schema(description = "符合条件的普通会员userIds" )
    private List<Long> userIds;

    @Schema(description = "符合条件的付费会员userIds" )
    private List<Long> paidUserIds;

}
