package com.yami.shop.platform.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yami.shop.bean.bo.RefundInfoBo;
import com.yami.shop.bean.dto.OrderRefundDto;
import com.yami.shop.bean.enums.ReturnMoneyStsType;
import com.yami.shop.bean.enums.SendType;
import com.yami.shop.bean.model.OrderRefund;
import com.yami.shop.bean.model.RefundInfo;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.enums.PayType;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.manager.impl.PayManager;
import com.yami.shop.service.NotifyTemplateService;
import com.yami.shop.service.OrderRefundService;
import com.yami.shop.service.RefundInfoService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrderRefundTask {

    private final OrderRefundService orderRefundService;
    private final NotifyTemplateService notifyTemplateService;
    private final RefundInfoService refundInfoService;
    private final PayManager payManager;
    private final RedissonClient redissonClient;

    /**
     * 超时取消退款和发放退款互斥锁
     */
    private static final String LOCK_REFUND_HANDLE_PREFIX = "redisson_lock:refund_handle:";


    /**
     * 申请超时的退款订单，无论该超时订单处于任何状态直接进行退款
     */
    @XxlJob("cancelWhenTimeOut")
    public void cancelWhenTimeOut() {
        log.info("==============订单退款超时处理开始===================");
        // 设定时间值
        Date date = DateUtil.offsetDay(new Date(), -Constant.MAX_REFUND_APPLY_TIME);
        // 获取待处理的退款订单
        List<OrderRefundDto> orderRefundList = orderRefundService.listRefundTimeOut(ReturnMoneyStsType.closeStatus(),date);
        if (CollectionUtils.isNotEmpty(orderRefundList)) {
            for (OrderRefund orderRefund : orderRefundList) {
                RLock lock = redissonClient.getLock(LOCK_REFUND_HANDLE_PREFIX + orderRefund.getRefundSn());
                try {
                    lock.lock();
                    OrderRefund dbOrderRefund = orderRefundService.getById(orderRefund.getRefundId());
                    if (ReturnMoneyStsType.shouldClous(dbOrderRefund.getReturnMoneySts())) {
                        log.info("退款订单超时直接退款成功" + dbOrderRefund.getRefundSn());
                        orderRefundService.cancelWhenTimeOut(orderRefund);
                    }
                } finally {
                    lock.unlock();
                }
            }
        }
        log.info("==============订单退款超时处理结束===================");
    }

    private void refundSuccess(RefundInfo dbRefundInfo, RefundInfoBo refundInfoBo) {
        log.info("退款单号{}退款成功，回调开始....", dbRefundInfo.getRefundId());
        RefundInfo refundInfo = refundInfoService.getOne(new LambdaQueryWrapper<RefundInfo>().eq(RefundInfo::getRefundId, dbRefundInfo.getRefundId()));
        refundInfo.setPayRefundId(refundInfoBo.getBizRefundNo());
        // 主动查单，退款成功的给与回调
        refundInfoService.refundSuccess(refundInfo);
    }

    /**
     * 退款临近超时提醒,每12小时执行发送一次的提醒
     */
    @XxlJob("pressRefundOrder")
    public void pressRefundOrder(){
        log.info("==============订单退款超时提醒开始===================");
        // 临时超时时间为 最大申请时间 - 12小时
        Integer overTime = Constant.MAX_REFUND_APPLY_TIME * 24;
        Date date = DateUtil.offsetHour(new Date(), Constant.MAX_REFUND_HOUR - overTime);
        Date overDate = DateUtil.offsetDay(new Date(), -Constant.MAX_REFUND_APPLY_TIME);
        // 获取临近超时的退款订单,大于超时时间，小于临时时间
        List<OrderRefund> orderRefundList = orderRefundService.list(new LambdaQueryWrapper<OrderRefund>()
                .in(OrderRefund::getReturnMoneySts, ReturnMoneyStsType.closeStatus())
                .gt(OrderRefund::getApplyTime,overDate)
                .lt(OrderRefund::getApplyTime, date));
        if (CollectionUtils.isNotEmpty(orderRefundList)) {
            List<OrderRefundDto> orderRefundDtos = BeanUtil.mapAsList(orderRefundList, OrderRefundDto.class);
            orderRefundDtos = orderRefundDtos.stream().filter(distinctByKey(OrderRefundDto::getUserId)).collect(Collectors.toList());
            for (OrderRefundDto orderRefundDto : orderRefundDtos) {
                notifyTemplateService.sendNotifyByRefund(orderRefundDto,SendType.REFUND_OUT_TIME);
            }
        }
        log.info("==============退款临近超时提醒结束===================");
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    /**
     * 发放退款定时任务，给十分钟之前还没有收到退款回调的订单重新发放退款
     * 一分钟执行一次
     */
    @XxlJob("refundRequest")
    public void refundRequest() {
        log.info("==============发放退款定时任务开始===================");
        // 找到十分钟之前已经处理退款但是发放退款失败的退款订单
        List<OrderRefundDto> shouldRefundRequestList = orderRefundService.listShouldRefundRequest();
        if (CollUtil.isEmpty(shouldRefundRequestList)) {
            return;
        }
        // 给发放退款失败的订单重新发放一次
        for (OrderRefundDto orderRefundDto : shouldRefundRequestList) {
            RLock lock = redissonClient.getLock(LOCK_REFUND_HANDLE_PREFIX + orderRefundDto.getRefundSn());
            try {
                lock.lock();
                OrderRefund dbOrderRefund = orderRefundService.getById(orderRefundDto.getRefundId());
                if (Objects.equals(dbOrderRefund.getReturnMoneySts(), ReturnMoneyStsType.PROCESSING.value())
                        || Objects.equals(dbOrderRefund.getReturnMoneySts(), ReturnMoneyStsType.RECEIVE.value())) {
                    // 商家处理状态，查询退款是否成功
                    RefundInfo dbRefundInfo = refundInfoService.getByRefundId(dbOrderRefund.getRefundSn());
                    if (Objects.nonNull(dbRefundInfo)) {
                        // 申请退款成功，没有回调
                        log.info("退款单号为{}的订单开始进行退款查单", orderRefundDto.getRefundSn());
                        RefundInfoBo refundInfoBo = payManager.getRefundInfo(PayType.instance(dbRefundInfo.getPayType()), dbRefundInfo.getPayNo(), dbRefundInfo.getPaySysType(), dbRefundInfo.getRefundId());
                        if (refundInfoBo.getIsRefundSuccess()) {
                            // 退款回调
                            refundSuccess(dbRefundInfo, refundInfoBo);
                        }
                    } else {
                        // 申请退款失败，重新申请
                        log.info("退款单号为{}的订单开始进行发放退款", orderRefundDto.getRefundSn());
                        // 提交退款请求
                        orderRefundService.submitWxRefund(orderRefundDto);
                    }
                }
            } catch (Exception e) {
                log.info("退款单号:{}订单发放退款失败，原因为:{}", orderRefundDto.getRefundSn(), e.getMessage());
            } finally {
                lock.unlock();
            }
        }
        this.refundOrderStock();
        log.info("==============发放退款定时任务结束===================");
    }

    /**
     * 因为支付宝电脑支付订单全额退款成功是没有退款回调的，所以需要该定时任务定时执行
     * 调用主动查单 查十秒钟前,两小时内还没回调的支付宝电脑支付订单退款的状态
     * 一分钟执行一次
     */
    @XxlJob("updateAliPayRefundStatus")
    public void updateAliPayRefundStatus() {
        log.info("==============支付宝退款订单查单开始===================");
        List<RefundInfo> refundInfoList = refundInfoService.listAliPayRefund();
        if (CollUtil.isEmpty(refundInfoList)) {
            return;
        }
        for (RefundInfo dbRefundInfo : refundInfoList) {
            RLock lock = redissonClient.getLock(LOCK_REFUND_HANDLE_PREFIX + dbRefundInfo.getRefundId());
            try {
                lock.lock();
                RefundInfoBo refundInfoBo = payManager.getRefundInfo(PayType.instance(dbRefundInfo.getPayType()), dbRefundInfo.getPayNo(), dbRefundInfo.getPaySysType(),dbRefundInfo.getRefundId());
                if (!refundInfoBo.getIsRefundSuccess()) {
                    continue;
                }
                // 退款回调
                refundSuccess(dbRefundInfo, refundInfoBo);
            } finally {
                lock.unlock();
            }
        }
        log.info("==============支付宝退款订单查单结束===================");
    }

    /**
     * 执行平台介入超时处理，超时则默认平台不同意退款
     */
    @XxlJob("refundInterventionTimeOut")
    public void cancelRefundInterventionTimeOut() {
        XxlJobHelper.log("执行平台介入超时处理，超时默认平台不同意退款");
        orderRefundService.cancelRefundInterventionTimeOut();
    }

    /**
     * 执行订单退款库存处理，根据退款订单的状态，决定回退退款还原的库存或者确认库存还原
     */
    @XxlJob("refundOrderStock")
    public void refundOrderStock() {
        XxlJobHelper.log("执行订单退款库存处理，根据退款订单的状态，决定回退退款还原的库存或者确认库存还原");
        try {
            orderRefundService.refundOrderStock();
        } catch (Exception e) {
            log.error("退款库存", e);
        }
    }
}
