package com.yami.shop.groupbuy.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yami.shop.bean.model.Sku;
import com.yami.shop.bean.model.SkuLang;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupSkuDto;
import com.yami.shop.groupbuy.common.dao.GroupSkuMapper;
import com.yami.shop.groupbuy.common.model.GroupSku;
import com.yami.shop.groupbuy.common.service.GroupSkuService;
import com.yami.shop.manager.impl.LangManager;
import com.yami.shop.service.SkuService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 拼团活动商品规格
 *
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
@Service
@AllArgsConstructor
public class GroupSkuServiceImpl extends ServiceImpl<GroupSkuMapper, GroupSku> implements GroupSkuService {

    private final GroupSkuMapper groupSkuMapper;
    private final SkuService skuService;
    private final LangManager langManager;

    /**
     * 获取SKU列表数据
     *
     * @param prodId
     * @param mold
     * @param groupActivityId 拼团活动ID
     * @return SKU列表
     */
    @Override
    public List<ApiGroupSkuDto> getApiByGroupActivityIdAndProdId(Long prodId, Integer mold, Long groupActivityId) {
        List<ApiGroupSkuDto> groupSkuList = groupSkuMapper.getApiByGroupActivityIdAndProdId(groupActivityId);
        if (CollUtil.isEmpty(groupSkuList)) {
            return groupSkuList;
        }
        List<Sku> skuList = skuService.listPutOnSkuAndSkuStock(prodId,mold, null, true, null);
        Map<Long, Sku> skuMap = skuList.stream().collect(Collectors.toMap(Sku::getSkuId, s -> s));
        List<Long> ids = groupSkuList.stream().map(ApiGroupSkuDto::getSkuId).filter(skuId -> Objects.nonNull(skuId)).collect(Collectors.toList());
        Map<Long, SkuLang> skuLangMap = langManager.getSkuLangMap(ids);
        for (ApiGroupSkuDto item : groupSkuList) {
            SkuLang skuLang = skuLangMap.get(item.getSkuId());
            if (Objects.nonNull(skuLang)) {
                item.setSkuName(skuLang.getSkuName());
                item.setProperties(skuLang.getProperties());
            }
            Sku sku = skuMap.get(item.getSkuId());
            if(Objects.nonNull(sku)){
                item.setSkuComboList(sku.getSkuComboList());
            }
        }
        return groupSkuList;
    }

}
