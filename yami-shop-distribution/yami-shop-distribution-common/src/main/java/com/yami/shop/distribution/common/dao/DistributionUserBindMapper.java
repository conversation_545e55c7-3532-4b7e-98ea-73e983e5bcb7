package com.yami.shop.distribution.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.distribution.common.dto.BindUserInfoDto;
import com.yami.shop.distribution.common.model.DistributionUserBind;
import com.yami.shop.distribution.common.param.RangeTimeParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DistributionUserBindMapper extends BaseMapper<DistributionUserBind> {
    /**
     * 分销员和用户绑定关系列表
     * @param page 分页参数
     * @param distributionUserBind 绑定信息
     * @param bindTime 绑定时间
     * @param invalidTime 失效时间
     * @param userName 用户昵称
     * @param parentName 分销员昵称
     * @param cUserMobile 用户号码
     * @param dUserMobile 分销员号码
     * @param sort 排序字段
     * @param orderBy 排序方式
     * @return 分销员和用户绑定关系列表
     */
    IPage<DistributionUserBind> distributionMsgsAndUserPage(Page page, @Param("distributionUserBind") DistributionUserBind distributionUserBind,
                                                            @Param("bindTime") RangeTimeParam bindTime, @Param("invalidTime") RangeTimeParam invalidTime,
                                                            @Param("userName") String userName, @Param("parentName") String parentName,
                                                            @Param("cUserMobile") String cUserMobile, @Param("dUserMobile") String dUserMobile,
                                                            @Param("sort") Integer sort, @Param("orderBy") Integer orderBy);

    /**
     * 通过绑定的分销员id 更新 分销员绑定状态
     * @param distributionUserId 绑定的分销员id
     * @param state 分销员绑定状态
     */
    void updateStateAndReasonByDistributionUserId(@Param("distributionUserId") Long distributionUserId, @Param("state") Integer state);
    /**
     * 通过绑定的分销员userId 更新 分销员绑定状态
     * @param userId 绑定的分销员userId
     * @param state 分销员绑定状态
     */
    void updateStateAndReasonByUserId(@Param("userId") String userId, @Param("state") Integer state);

    /**
     * 获取绑定的用户信息
     * @param page 分页参数
     * @param shopId 店铺id
     * @param userId 用户id
     * @return 绑定的用户信息
     */
    IPage<BindUserInfoDto> bindUserList(Page page, @Param("shopId") Long shopId, @Param("userId") String userId);

    /**
     * 查询出所有的暂时封禁但已经被抢的用户
     * @param distributionUserBindList 绑定信息
     * @return 没有被抢的用户id列表
     */
    List<String> selectClearUserByDistributionUserId(@Param("distributionUserBindList") List<DistributionUserBind> distributionUserBindList);

    /**
     * 将没有被抢的，失效的绑定用户设为正常
     * @param distributionUserBinds 绑定用户列表
     */
    void recoveryRelationsByUserId(@Param("distributionUserBinds") List<DistributionUserBind> distributionUserBinds);
}
