package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;
/**
 * 申请绑定银行卡返回值
 *
 * <AUTHOR>
 */
public class ApplyBindBankCardResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 本地用户id
     */
    private String bizUserId;
    /**
     * 流水号
     */
    private String tranceNum;
    /**
     * 申请时间
     */
    private String transDate;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 银行代码
     */
    private String bankCode;
    /**
     * 银行卡类型 1储蓄卡 2信用卡
     */
    private Integer cardType;

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getTranceNum() {
        return tranceNum;
    }

    public void setTranceNum(String tranceNum) {
        this.tranceNum = tranceNum;
    }

    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

}
