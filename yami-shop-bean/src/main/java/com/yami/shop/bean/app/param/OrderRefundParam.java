package com.yami.shop.bean.app.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderRefundParam {

    @Schema(description = "订单编号" , requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "订单编号不能为空")
    private String orderNumber;

    @Schema(description = "退款单类型（1:整单退款,2:单个物品退款）" , requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "退款单类型不能为空")
    private Integer refundType;

    @Schema(description = "订单项ID（单个物品退款时使用）" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long orderItemId;

    @Schema(description = "退款数量（0或不传值则为全部数量）" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer goodsNum;

    @Schema(description = "申请类型(1:仅退款 2退款退货)" , requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "申请类型不能为空")
    private Integer applyType;

    @Schema(description = "货物状态(1:已收到货 0:未收到货)" , requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "货物状态不能为空")
    private Boolean isReceiver;

    /**
     * 仅退款-未收到货申请原因
     * 11(质量问题)
     * 12(拍错/多拍/不喜欢)
     * 3(商品描述不符)
     * 14(假货), 15(商家发错货)
     * 16(商品破损/少件)
     * 17(其他)
     * 仅退款-已收到货申请原因
     * 51(多买/买错/不想要)
     * 52(快递无记录)
     * 53(少货/空包裹)
     * 54(未按约定时间发货)
     * 55(快递一直未送达)
     * 56(其他)
     * 退货退款-申请原因
     * 101(商品破损/少件)
     * 102(商家发错货)
     * 103(商品描述不符)
     * 104(拍错/多拍/不喜欢)
     * 105(质量问题)
     * 107(其他)
     */
    @Schema(description = "申请原因(下拉选择)" , requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "申请原因不能为空")
    private String buyerReason;

    @Schema(description = "退款金额" , requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "退款金额不能为空")
    private Double refundAmount;

    @Schema(description = "手机号码（默认当前订单手机号码）" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String buyerMobile;

    @Schema(description = "备注说明" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String buyerDesc;

    @Schema(description = "凭证图片列表" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String photoFiles;

    @Schema(description = "选中的赠品orderItemId列表" , requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Long> giveawayItemIds;
}
