package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 商家钱包信息
 *
 * <AUTHOR>
 * @date 2019-09-19 14:02:57
 */
@Data
public class ShopWalletVO {

    @Schema(description = "店铺钱包id" )
    private Long shopWalletId;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "未结算金额（用户支付）" )
    private Double unsettledAmount;

    @Schema(description = "已结算金额（用户确认收货后，可以提现）" )
    private Double settledAmount;

    @Schema(description = "冻结金额（用户提现申请）" )
    private Double freezeAmount;

    @Schema(description = "累积结算金额" )
    private Double totalSettledAmount;

    @Schema(description = "交易笔数（支付订单数）" )
    private Long transactionCount;

    @Schema(description = "昨天结算金额" )
    private Double yesterdaySettledAmount;
}
