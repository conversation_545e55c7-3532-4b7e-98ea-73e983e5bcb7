package com.yami.shop.platform.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yami.shop.bean.dto.wukong.ChannelDTO;
import com.yami.shop.bean.model.ImChannel;
import com.yami.shop.common.wukongim.constant.WuKongConstant;
import com.yami.shop.dao.ImChannelMapper;
import com.yami.shop.wukongim.service.WuKongImService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 悟空im的定时任务
 * <AUTHOR>
 * @Date 2024/06/05 14:45
 */
@Slf4j
@Component
@AllArgsConstructor
public class WoKongImTask {

    private final ImChannelMapper imChannelMapper;
    private final WuKongImService wuKongImService;

    /**
     * 根据tz_im_channel恢复悟空im的频道
     */
    @XxlJob("restoreChannelData")
    public void restoreChannelData() {
        // 1.只需要恢复channelType=0(用户和商家/平台的频道)
        // 查询channelType=0的频道
        List<ImChannel> imChannelList = imChannelMapper.selectList(new LambdaQueryWrapper<ImChannel>().eq(ImChannel::getChannelType, 0));
        // 根据tz_im_channel中频道的数据恢复悟空im的频道
        for (ImChannel imChannelDb : imChannelList) {
            // 组装频道数据
            // 频道id，用户id + 店铺id
            String adminUid = imChannelDb.getShopId() == 0 ? WuKongConstant.getSysUid(imChannelDb.getEmployeeId()) : WuKongConstant.getShopUid(imChannelDb.getEmployeeId(), imChannelDb.getShopId());
            List<String> subscribers = new ArrayList<>();
            subscribers.add(WuKongConstant.getUid(imChannelDb.getChannelId()));
            subscribers.add(adminUid);
            ChannelDTO channelDTO = new ChannelDTO(imChannelDb.getChannelId(), WuKongConstant.GROUP_CHAT, 0, subscribers);
            // 创建或者更新频道
            wuKongImService.createChannel(channelDTO);
        }

        // 2.channelType=1(商家/平台内部的频道直接删除即可，等待下一次请求/registerOrLogin接口就会重新创建或者更新频道了)
        imChannelMapper.delete(new LambdaQueryWrapper<ImChannel>().eq(ImChannel::getChannelType, 1));
    }
}
