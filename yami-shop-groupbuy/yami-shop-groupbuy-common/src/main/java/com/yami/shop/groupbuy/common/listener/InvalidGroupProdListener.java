package com.yami.shop.groupbuy.common.listener;

import com.yami.shop.bean.enums.ProdStatusEnums;
import com.yami.shop.bean.enums.ProdType;
import com.yami.shop.bean.event.ProdChangeStatusEvent;
import com.yami.shop.bean.model.Product;
import com.yami.shop.bean.order.GeneralActivitiesOrder;
import com.yami.shop.groupbuy.common.service.GroupActivityService;
import lombok.AllArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component("InvalidGroupProStatus")
@AllArgsConstructor
public class InvalidGroupProdListener {

    private final GroupActivityService groupActivityService;

    /**
     * 使团购商品失效
     */
    @EventListener(ProdChangeStatusEvent.class)
    @Order(GeneralActivitiesOrder.GROUPON)
    public void invalidGroupProStatusListener(ProdChangeStatusEvent event) {
        if (Objects.equals(ProdStatusEnums.NORMAL.getValue(), event.getStatus())) {
            return;
        }
        if (Objects.equals(ProdStatusEnums.PLATFORM_AUDIT.getValue(), event.getStatus())) {
            return;
        }
        Product product = event.getProduct();
        // 不是团购商品，就不用管他
        if(!Objects.equals(product.getProdType(), ProdType.PROD_TYPE_GROUP.value())){
            return;
        }
        Long prodId = product.getProdId();
        // 下架时检查是否有拼团活动，有的话使之全部失效
        groupActivityService.handleOfflineGroupProd(product.getShopId(),prodId);

    }
}
