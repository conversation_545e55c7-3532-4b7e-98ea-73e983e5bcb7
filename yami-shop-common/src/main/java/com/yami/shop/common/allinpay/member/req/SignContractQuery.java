package com.yami.shop.common.allinpay.member.req;

import com.yami.shop.common.allinpay.member.constant.PageType;

import java.io.Serial;
import java.io.Serializable;
/**
 * <AUTHOR>
 */
public class SignContractQuery extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "signContractQuery";

    /**
     * 签约户名
     */
    private String signAcctName;

    /**
     * 跳转页面类型
     *
     * @see PageType
     */
    private Long jumpPageType;

    /**
     * 成功后跳转地址
     */
    private String jumpUrl;

    /**
     * 访问终端类型
     */
    private Integer source;

    public String getSignAcctName() {
        return signAcctName;
    }

    public void setSignAcctName(String signAcctName) {
        this.signAcctName = signAcctName;
    }

    public Long getJumpPageType() {
        return jumpPageType;
    }

    public void setJumpPageType(Long jumpPageType) {
        this.jumpPageType = jumpPageType;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    @Override
    public String toString() {
        return "SignContractQuery{" +
                "signAcctName='" + signAcctName + '\'' +
                ", jumpPageType=" + jumpPageType +
                ", jumpUrl='" + jumpUrl + '\'' +
                ", source='" + source + '\'' +
                ", bizUserId='" + bizUserId + '\'' +
                '}';
    }
}
