package com.yami.shop.bean.app.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "支付信息参数")
public class PayInfoParam {

    @Schema(description = "1：支付成功，2: 用户信息异常，3：余额不足" )
    private Integer type;

    @Schema(description = "支付异常信息" )
    private String message;

}
