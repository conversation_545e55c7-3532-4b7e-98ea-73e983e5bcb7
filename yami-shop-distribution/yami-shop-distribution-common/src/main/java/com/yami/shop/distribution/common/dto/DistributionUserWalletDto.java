package com.yami.shop.distribution.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "分销员钱包数据")
public class DistributionUserWalletDto {
    /**
     * 待结算金额
     */
    @Schema(description = "待结算金额" )
    private Double unsettledAmount;

    /**
     * 可提现金额
     */
    @Schema(description = "可提现金额" )
    private Double settledAmount;

    /**
     * 已失效金额
     */
    @Schema(description = "已失效金额" )
    private Double invalidAmount;


    /**
     * 申请提现金额
     */
    @Schema(description = "提现申请中金额" )
    private Double applyWithdrawAmount;


    /**
     * 已提现金额
     */
    @Schema(description = "已提现金额" )
    private Double extractedAmount;

    /**
     * 积累收益
     */
    @Schema(description = "积累收益(包含已经提现的佣金和可提现佣金)" )
    private Double addupAmount;



}
