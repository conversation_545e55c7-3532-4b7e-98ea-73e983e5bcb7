package com.yami.shop.common.allinpay.order.req;


import com.yami.shop.common.allinpay.constant.AllinpayConstant;
import com.yami.shop.common.allinpay.member.constant.VisitSourceType;

/**
 * <AUTHOR>
 * @date 2023-03-29
 */
public class DepositApply extends AbstractOrderReq {

    public static final String METHOD_NAME = "depositApply";

    /**
     * 商户订单号
     */
    private String bizOrderNo;
    /**
     * 账户集编号
     */
    private String accountSetNo;
    /**
     * 金额（单位:分）
     */
    private Long amount;
    /**
     * 手续费
     */
    private Long fee;
    /**
     * 交易验证方式()
     */
    private Long validateType;
    /**
     * 前台通知地址
     */
    private String frontUrl;
    /**
     * 后台通知地址
     */
    private String backUrl;
    /**
     * 支付方式(JSON)
     */
    private String payMethod;
    /**
     * 行业代码
     */
    private String industryCode = AllinpayConstant.INDUSTRY_CODE;
    /**
     * 行业名称
     */
    private String industryName = AllinpayConstant.INDUSTRY_NAME;
    /**
     * 访问终端类型
     */
    private Integer source = VisitSourceType.PC.value();

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getAccountSetNo() {
        return accountSetNo;
    }

    public void setAccountSetNo(String accountSetNo) {
        this.accountSetNo = accountSetNo;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Long getFee() {
        return fee;
    }

    public void setFee(Long fee) {
        this.fee = fee;
    }

    public Long getValidateType() {
        return validateType;
    }

    public void setValidateType(Long validateType) {
        this.validateType = validateType;
    }

    public String getFrontUrl() {
        return frontUrl;
    }

    public void setFrontUrl(String frontUrl) {
        this.frontUrl = frontUrl;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getIndustryCode() {
        return industryCode;
    }

    public void setIndustryCode(String industryCode) {
        this.industryCode = industryCode;
    }

    public String getIndustryName() {
        return industryName;
    }

    public void setIndustryName(String industryName) {
        this.industryName = industryName;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    @Override
    public String toString() {
        return "DepositApply{" +
                "bizOrderNo='" + bizOrderNo + '\'' +
                ", accountSetNo='" + accountSetNo + '\'' +
                ", amount=" + amount +
                ", fee=" + fee +
                ", validateType=" + validateType +
                ", frontUrl='" + frontUrl + '\'' +
                ", backUrl='" + backUrl + '\'' +
                ", payMethod='" + payMethod + '\'' +
                ", industryCode='" + industryCode + '\'' +
                ", industryName='" + industryName + '\'' +
                ", source=" + source +
                ", bizUserId='" + bizUserId + '\'' +
                '}';
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }
}
