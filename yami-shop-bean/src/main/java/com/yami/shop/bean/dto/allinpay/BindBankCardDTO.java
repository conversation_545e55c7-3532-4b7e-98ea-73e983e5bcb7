package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class BindBankCardDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "流水号(请求绑卡接口返回数据)")
    private String tranceNum;

    @Schema(description = "银行预留手机")
    private String phone;

    @Schema(description = "短信验证码(测试环境默认111111[6个1]，其他验证码是5个1，注意区分)")
    private String verificationCode;

    public String getTranceNum() {
        return tranceNum;
    }

    public void setTranceNum(String tranceNum) {
        this.tranceNum = tranceNum;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    @Override
    public String toString() {
        return "BindBankCardDTO{" +
                "tranceNum='" + tranceNum + '\'' +
                ", phone='" + phone + '\'' +
                ", verificationCode='" + verificationCode + '\'' +
                '}';
    }
}
