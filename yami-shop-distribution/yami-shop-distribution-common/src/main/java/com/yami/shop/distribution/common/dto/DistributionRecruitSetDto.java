package com.yami.shop.distribution.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "分校推广数据")
public class DistributionRecruitSetDto {

    @Schema(description = "推广图" )
    private String pic;

    @Schema(description = "标题" )
    private String title;

    @Schema(description = "推广内容" )
    private String content;

    @Schema(description = "状态（0下线 1上线）" )
    private Integer state;
}
