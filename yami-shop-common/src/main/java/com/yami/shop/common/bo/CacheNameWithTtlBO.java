package com.yami.shop.common.bo;

import java.io.Serial;
import java.io.Serializable;
/**
 * 通过 cacheName 配置 和 时间告诉缓存多久清楚一遍
 *
 * <AUTHOR>
 * @date 2020/7/4
 */
public class CacheNameWithTtlBO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private String cacheName;

    private Integer ttl;

    public CacheNameWithTtlBO(String cacheName, Integer ttl) {
        this.cacheName = cacheName;
        this.ttl = ttl;
    }

    public String getCacheName() {
        return cacheName;
    }

    public void setCacheName(String cacheName) {
        this.cacheName = cacheName;
    }

    public Integer getTtl() {
        return ttl;
    }

    public void setTtl(Integer ttl) {
        this.ttl = ttl;
    }

    @Override
    public String toString() {
        return "CacheNameWithTtlBO{" + "cacheName='" + cacheName + '\'' + ", ttl=" + ttl + '}';
    }

}
