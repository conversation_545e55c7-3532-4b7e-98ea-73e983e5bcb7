package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;

/**
 * 基本的支付信息
 *
 * <AUTHOR>
 * @date 2021/5/13
 */
public class BasePayInfoDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @NotNull(message = "支付方式不能为空")
    @Schema(description = "支付方式 (0积分支付 1:微信小程序支付 2:支付宝 3微信扫码支付 4 微信h5支付 5微信公众号支付 6支付宝H5支付 7支付宝APP支付 8微信APP支付 9余额支付)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer payType;

    @Schema(description = "支付完成回跳地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String returnUrl;

    @Schema(description = "系统类型 1:移动端 2:pc", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long systemType;

    @Schema(description = "验证类型(0:无验证, 1:短信验证码, 2：支付密码), 默认短信验证")
    private Long validateType;

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    public Long getSystemType() {
        return systemType;
    }

    public void setSystemType(Long systemType) {
        this.systemType = systemType;
    }

    public Long getValidateType() {
        return validateType;
    }

    public void setValidateType(Long validateType) {
        this.validateType = validateType;
    }

    @Override
    public String toString() {
        return "BasePayInfoDTO{" +
                "payType=" + payType +
                ", returnUrl='" + returnUrl + '\'' +
                ", systemType=" + systemType +
                ", validateType=" + validateType +
                '}';
    }
}
