package com.yami.shop.sys.multishop.controller;


import com.yami.shop.common.bean.Quick100;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.SysConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Tag(name = "商家系统配置")
@RestController
@RequestMapping("/sys/shopConfig")
@RequiredArgsConstructor
public class ShopSysConfigController {

    private final SysConfigService sysConfigService;

    @GetMapping("/quick100")
    @Operation(summary = "获取快递100配置", description = "获取快递100配置")
    public ServerResponseEntity<Boolean> getQuick100() {
        Quick100 quick100 = sysConfigService.getSysConfigObject(Constant.QUICK100_CONFIG, Quick100.class);
        return ServerResponseEntity.success(quick100.getIsPrint());
    }
}
