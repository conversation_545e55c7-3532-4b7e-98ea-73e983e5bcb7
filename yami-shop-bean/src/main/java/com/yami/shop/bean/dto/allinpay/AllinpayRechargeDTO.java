package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class AllinpayRechargeDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "充值订单号")
    private String bizOrderNo;

    @Schema(description = "短信验证码")
    private String verificationCode;

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    @Override
    public String toString() {
        return "AllinpayRechargeDTO{" +
                "bizOrderNo='" + bizOrderNo + '\'' +
                ", verificationCode='" + verificationCode + '\'' +
                '}';
    }
}
