package com.yami.shop.distribution.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.distribution.common.dto.DistributionWithdrawCashDto;
import com.yami.shop.distribution.common.model.DistributionWithdrawCash;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.param.RangeTimeParam;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface DistributionWithdrawCashMapper extends BaseMapper<DistributionWithdrawCash> {
    /**
     * 分页获取分销员提现申请记录
     * @param page 分页参数
     * @param shopId 店铺id
     * @param rangeTimeParam 申请时间
     * @param distributionParam 分销查询参数
     * @param distributionWithdrawCash 查询参数
     * @return 分销员提现申请记录
     */
    IPage<DistributionWithdrawCash> distributionWithdrawCashsPage(Page page,
                                                                  @Param("shopId") Long shopId,
                                                                  @Param("rangeTimeParam") RangeTimeParam rangeTimeParam,
                                                                  @Param("distributionParam") DistributionParam distributionParam,
                                                                  @Param("distributionWithdrawCash") DistributionWithdrawCash distributionWithdrawCash);

    /**
     * 分页获取分销员提现申请记录
     * @param page 分页参数
     * @param distributionUserId 分销员id
     * @return 分销员提现申请记录
     */
    IPage<DistributionWithdrawCashDto> distributionWithdrawCashDtoPageByUserId(Page page, @Param("distributionUserId") Long distributionUserId);

    /**
     * 根据时间区间获取用户的提现次数
     * @param rangeTimeParam 时间范围
     * @param distributionuserId 分销员id
     * @return 分销员提现申请记录数量
     */
    Integer getCountByRangeTimeAndDistributionUserId(@Param("rangeTimeParam") RangeTimeParam rangeTimeParam, @Param("distributionuserId") Long distributionuserId);

    /**
     * 查看分销员总提现金额
     * @param walletId 钱包id
     * @return 总提现金额
     */
    Double getUserTotalWithdrawCash(@Param("walletId") Long walletId);

    /**
     * 永久封禁时，根据用户id更新其提现中的记录为拒绝提现
     * @param distributionUserId 用户id
     */
    void updateUserByDistributionUserId(@Param("distributionUserId") Long distributionUserId);

    /**
     * 更新提现成功
     * @param withdrawCashId
     * @return
     */
    int updateSuccess(@Param("withdrawCashId") Long withdrawCashId);
}
