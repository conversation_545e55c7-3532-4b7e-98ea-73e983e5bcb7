package com.yami.shop.groupbuy.common.dto;

import com.yami.shop.groupbuy.common.model.GroupSku;
import com.yami.shop.groupbuy.common.utils.GroupActivityUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 活动商品和商品下所有规格属性
 *
 * <AUTHOR>
 */
@Data
public class GroupProdAndSkuListDto {

    @Schema(description = "活动商品id" )
    private Integer groupProdId;

    @Schema(description = "商品id" )
    private Integer prodId;

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "活动开始时间" )
    private Date startTime;

    @Schema(description = "活动结束时间" )
    private Date endTime;

    @Schema(description = "活动状态" )
    private Integer status;

    @Schema(description = "活动状态" )
    private Integer activityStatus;

    @Schema(description = "规格列表" )
    private List<GroupSku> skuDtoList;

    /**
     * 获取活动状态（活动状态：1:未开始、2:进行中、3:已结束、4:已失效）
     */
    public Integer getActivityStatus() {
        return GroupActivityUtil.getActivityStatus(startTime, endTime, status);
    }
}
