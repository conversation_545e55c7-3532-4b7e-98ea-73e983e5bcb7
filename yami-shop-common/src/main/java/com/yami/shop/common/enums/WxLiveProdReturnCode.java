package com.yami.shop.common.enums;

/**
 * 微信直播商品返回码
 * @author: zsm
 * @date: 2023/5/10 11:32
 */
public enum WxLiveProdReturnCode {

    // 300022 房间号不存在
    WECHAT_LIVE_ROOM_NOT_EXIST(300022, "房间号不存在"),

    // 300024 商品不存在

    WECHAT_LIVE_PRODUCT_NOT_EXIST(300024, "商品不存在"),

    // 300015 商品ID不存在
    GOODIDS_NOT_EXIST(300015, "goodsId is not exist"),

    // 300018 商品图片尺寸过大
    PRODUCT_IMAGE_SIZE_TOO_LARGE(300018, "商品图片尺寸过大"),

    // 300006 图片上传失败（如：mediaID过期）
    PRODUCT_IMAGE_UPLOAD_FAIL(300006, "图片上传失败"),

    // 300004 商品名称存在违规违法内容
    PRODUCT_NAME_ILLEGAL(300004, "商品名称存在违规违法内容"),


    ;


    private final Integer code;

    private final String msg;

    WxLiveProdReturnCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer value() {
        return this.code;
    }

    public String msg() {
        return this.msg;
    }
}
