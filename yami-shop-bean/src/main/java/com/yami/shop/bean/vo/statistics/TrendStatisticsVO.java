package com.yami.shop.bean.vo.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 热卖信息统计
 * <AUTHOR>
 */
@Data
public class TrendStatisticsVO {

    @Schema(description = "时间" )
    private String date;

    @Schema(description = "浏览量" )
    private Long visitNum;

    @Schema(description = "访客数" )
    private Long userNum;

    @Schema(description = "交易金额" )
    private Double amount;
}
