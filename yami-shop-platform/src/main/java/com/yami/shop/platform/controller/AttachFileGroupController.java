package com.yami.shop.platform.controller;

import com.yami.shop.bean.model.AttachFileGroup;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.AttachFileGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/7/7 17:18
 */
@RestController
@RequestMapping("/admin/fileGroup")
@Tag(name = "文件分组")
@AllArgsConstructor
public class AttachFileGroupController {

    private final AttachFileGroupService attachFileGroupService;

    @GetMapping("/list")
    @Operation(summary = "获取列表" , description = "分页获取列表")
    @Parameter(name = "type", description = "文件类型： 1:图片 2:视频 3:文件" )
    @PreAuthorize("@pms.hasPermission('admin:fileGroup:list')")
    public ServerResponseEntity<List<AttachFileGroup>> list(@RequestParam(value = "type", defaultValue = "1") Integer type) {
        List<AttachFileGroup> attachFileGroupPage = attachFileGroupService.list(Constant.PLATFORM_SHOP_ID,type);
        return ServerResponseEntity.success(attachFileGroupPage);
    }

    @GetMapping
    @Operation(summary = "获取" , description = "根据attachFileGroupId获取")
    @Parameter(name = "attachFileGroupId", description = "文件分组id" )
    @PreAuthorize("@pms.hasPermission('admin:fileGroup:info')")
    public ServerResponseEntity<AttachFileGroup> getByAttachFileGroupId(@RequestParam Long attachFileGroupId) {
        return ServerResponseEntity.success(attachFileGroupService.getByAttachFileGroupId(attachFileGroupId));
    }

    @PostMapping
    @Operation(summary = "保存" , description = "保存")
    @PreAuthorize("@pms.hasPermission('admin:fileGroup:save')")
    public ServerResponseEntity<Void> save(@Valid @RequestBody AttachFileGroup attachFileGroup) {
        attachFileGroup.setAttachFileGroupId(null);
        attachFileGroup.setShopId(Constant.PLATFORM_SHOP_ID);
        attachFileGroupService.saveGroup(attachFileGroup);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @Operation(summary = "更新" , description = "更新")
    @PreAuthorize("@pms.hasPermission('admin:fileGroup:update')")
    public ServerResponseEntity<Void> update(@Valid @RequestBody AttachFileGroup attachFileGroup) {
        attachFileGroup.setShopId(Constant.PLATFORM_SHOP_ID);
        attachFileGroupService.update(attachFileGroup);
        return ServerResponseEntity.success();
    }

    @DeleteMapping
    @Operation(summary = "删除" , description = "根据id删除")
    @Parameter(name = "attachFileGroupId", description = "文件分组id" )
    @PreAuthorize("@pms.hasPermission('admin:fileGroup:delete')")
    public ServerResponseEntity<Void> delete(@RequestParam Long attachFileGroupId) {
        AttachFileGroup dbAttachFileGroup = attachFileGroupService.getByAttachFileGroupId(attachFileGroupId);
        if (!Objects.equals(dbAttachFileGroup.getShopId(), Constant.PLATFORM_SHOP_ID)) {
            // 未授权
            throw new YamiShopBindException("yami.attach.file.group.exception.unAuth");
        }
        attachFileGroupService.deleteById(attachFileGroupId);
        return ServerResponseEntity.success();
    }
}
