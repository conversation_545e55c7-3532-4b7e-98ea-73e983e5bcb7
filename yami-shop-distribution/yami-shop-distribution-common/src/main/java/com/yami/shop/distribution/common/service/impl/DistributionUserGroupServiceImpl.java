package com.yami.shop.distribution.common.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yami.shop.distribution.common.dao.DistributionUserGroupMapper;
import com.yami.shop.distribution.common.model.DistributionUserGroup;
import com.yami.shop.distribution.common.service.DistributionUserGroupService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> on 2019/04/01.
 */
@Service
@AllArgsConstructor
public class DistributionUserGroupServiceImpl extends ServiceImpl<DistributionUserGroupMapper, DistributionUserGroup> implements DistributionUserGroupService {

    private final DistributionUserGroupMapper distributionUserGroupMapper;

    @Override
    public IPage<DistributionUserGroup> distributionUserGroupsAndSysUserPage(Page page, DistributionUserGroup distributionUserGroup) {
        return distributionUserGroupMapper.distributionUserGroupsAndSysUserPage(page,distributionUserGroup);
    }

}
