package com.yami.shop.common.allinpay.member.req;

import java.io.Serial;
import java.io.Serializable;
/**
 * <AUTHOR>
 */
public class GetMemberInfo extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "getMemberInfo";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public GetMemberInfo() {
    }

    public GetMemberInfo(String bizUserId) {
        this.bizUserId = bizUserId;
    }
}
