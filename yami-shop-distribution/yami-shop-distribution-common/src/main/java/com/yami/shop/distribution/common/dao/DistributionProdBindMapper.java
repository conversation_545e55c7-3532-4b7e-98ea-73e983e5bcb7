package com.yami.shop.distribution.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yami.shop.distribution.common.model.DistributionProdBind;
import org.apache.ibatis.annotations.Param;

/**
 * 分销商品绑定表
 *
 * <AUTHOR>
 * @date 2019-04-22 10:01:44
 */
public interface DistributionProdBindMapper extends BaseMapper<DistributionProdBind> {

    /**
     * 根据分销员id修改状态（将商品分享记录设为失效）
     * @param distributionUserId 分销员id
     * @param state 状态
     */
    void updateStateByDistributionUserId(@Param("distributionUserId") Long distributionUserId,@Param("state") Integer state);
}
