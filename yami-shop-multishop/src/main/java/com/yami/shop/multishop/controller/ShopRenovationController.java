package com.yami.shop.multishop.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.enums.RenovationType;
import com.yami.shop.bean.model.ShopRenovation;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.ShopRenovationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;


/**
 * 店铺装修信息
 *
 * <AUTHOR>
 * @date 2021-01-05 11:03:38
 */
@RestController
@AllArgsConstructor
@RequestMapping("/shop/shopRenovation" )
@Tag(name = "店铺装修页面接口")
public class ShopRenovationController {

    private final ShopRenovationService shopRenovationService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param shopRenovation 店铺装修信息
     * @return 分页数据
     */
    @GetMapping("/pagePC" )
    @PreAuthorize("@pms.hasPermission('shop:shopRenovation:pagePC')")
    @Operation(summary = "PC端分页查询店铺装修页面信息" , description = "PC端分页查询店铺装修页面信息")
    public ServerResponseEntity<IPage<ShopRenovation>> getShopRenovationPagePc(PageParam<ShopRenovation> page, ShopRenovation shopRenovation) {
        return ServerResponseEntity.success(shopRenovationService.page(page, new LambdaQueryWrapper<ShopRenovation>()
                .eq(ShopRenovation::getShopId,SecurityUtils.getShopUser().getShopId())
                .eq(Objects.nonNull(shopRenovation.getRenovationType()), ShopRenovation::getRenovationType, shopRenovation.getRenovationType())
                .like(Objects.nonNull(shopRenovation.getName()), ShopRenovation::getName, shopRenovation.getName())
                .orderByDesc(ShopRenovation::getHomeStatus)
                .orderByDesc(ShopRenovation::getCreateTime)
        ));
    }

    /**
     * 分页查询
     * @param page 分页对象
     * @param shopRenovation 店铺装修信息
     * @return 分页数据
     */
    @GetMapping("/pageMove" )
    @PreAuthorize("@pms.hasPermission('shop:shopRenovation:pageMove')")
    @Operation(summary = "移动端分页查询店铺装修页面信息" , description = "移动端分页查询店铺装修页面信息")
    public ServerResponseEntity<IPage<ShopRenovation>> getShopRenovationPageMove(PageParam<ShopRenovation> page, ShopRenovation shopRenovation) {
        return ServerResponseEntity.success(shopRenovationService.page(page, new LambdaQueryWrapper<ShopRenovation>()
                .eq(ShopRenovation::getShopId,SecurityUtils.getShopUser().getShopId())
                .eq(Objects.nonNull(shopRenovation.getRenovationType()), ShopRenovation::getRenovationType, shopRenovation.getRenovationType())
                .like(Objects.nonNull(shopRenovation.getName()), ShopRenovation::getName, shopRenovation.getName())
                .orderByDesc(ShopRenovation::getHomeStatus)
                .orderByDesc(ShopRenovation::getCreateTime)
        ));
    }

    /**
     * 通过id查询店铺装修信息
     * @param renovationId id
     * @return 单个数据
     */
    @GetMapping("/info/{renovationId}" )
    @Operation(summary = "通过id查询店铺装修页面信息" , description = "通过id查询店铺装修页面信息")
    public ServerResponseEntity<ShopRenovation> getById(@PathVariable("renovationId") Long renovationId) {
        ShopRenovation shopRenovation = shopRenovationService.getById(renovationId);
        if(!Objects.equals(shopRenovation.getShopId() ,SecurityUtils.getShopUser().getShopId())){
            // 没有权限进行操作
            throw new YamiShopBindException("yami.no.auth");
        }
        return ServerResponseEntity.success(shopRenovation);
    }



    /**
     * 新增店铺装修信息
     * @param shopRenovation 店铺装修信息
     * @return 是否新增成功
     */
    @PostMapping("/savePC")
    @PreAuthorize("@pms.hasPermission('shop:shopRenovation:savePC')")
    @Operation(summary = "PC端新增店铺装修页面信息" , description = "PC端新增店铺装修页面信息")
    public ServerResponseEntity<Long> savePc(@RequestBody @Valid ShopRenovation shopRenovation) {
        shopRenovation.setShopId(SecurityUtils.getShopUser().getShopId());
        if (Objects.isNull(shopRenovation.getRenovationType())) {
            shopRenovation.setRenovationType(RenovationType.PC.value());
        }
        shopRenovationService.save(shopRenovation);
        return ServerResponseEntity.success(shopRenovation.getRenovationId());
    }

    /**
     * 新增店铺装修信息
     * @param shopRenovation 店铺装修信息
     * @return 是否新增成功
     */
    @PostMapping("/saveMove")
    @PreAuthorize("@pms.hasPermission('shop:shopRenovation:saveMove')")
    @Operation(summary = "移动端新增店铺装修页面信息" , description = "移动端新增店铺装修页面信息")
    public ServerResponseEntity<Long> saveMove(@RequestBody @Valid ShopRenovation shopRenovation) {
        shopRenovation.setShopId(SecurityUtils.getShopUser().getShopId());
        if (Objects.isNull(shopRenovation.getRenovationType())) {
            shopRenovation.setRenovationType(RenovationType.H5.value());
        }
        shopRenovationService.save(shopRenovation);
        return ServerResponseEntity.success(shopRenovation.getRenovationId());
    }

}
