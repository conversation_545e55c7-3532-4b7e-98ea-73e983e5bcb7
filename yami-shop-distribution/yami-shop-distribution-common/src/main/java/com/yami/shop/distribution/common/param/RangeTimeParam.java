package com.yami.shop.distribution.common.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 时间范围参数
 * <AUTHOR>
 */
@Data
public class RangeTimeParam {

    public RangeTimeParam(){}

    public RangeTimeParam(Date startTime, Date endTime){
        this.startTime=startTime;
        this.endTime=endTime;
    }


    /**
     * 起始时间
     */
    @Schema(description = "起始时间" ,requiredMode = Schema.RequiredMode.REQUIRED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间" ,requiredMode = Schema.RequiredMode.REQUIRED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @Override
    public String toString() {
        return "RangeTimeParam{" +
                "startTime=" + startTime.toString() +
                ", endTime=" + endTime.toString() +
                '}';
    }
}
