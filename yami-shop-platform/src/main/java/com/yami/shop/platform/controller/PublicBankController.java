package com.yami.shop.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.dto.PublicBankDTO;
import com.yami.shop.bean.model.PublicBank;
import com.yami.shop.bean.vo.PublicBankVO;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.PublicBankService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-29
 */
@Tag(name = "对公户银行")
@RestController
@RequestMapping("/publicBank")
@AllArgsConstructor
public class PublicBankController {

    private final PublicBankService publicBankService;

    @GetMapping("/list")
    @Operation(summary = "获取对公户银行列表", description = "获取对公户银行列表")
    @PreAuthorize("@pms.hasPermission('publicBank:list')")
    public ServerResponseEntity<List<PublicBankVO>> listPublicBank() {
        List<PublicBankVO> publicBankVOList = publicBankService.listPublicBank();
        return ServerResponseEntity.success(publicBankVOList);
    }

    @GetMapping("/page")
    @Operation(summary = "分页获取对公户银行列表", description = "分页获取对公户银行列表")
    @PreAuthorize("@pms.hasPermission('publicBank:page')")
    public ServerResponseEntity<IPage<PublicBankVO>> pagePublicBank(PageParam<PublicBank> pageParam,
                                                                    PublicBankDTO publicBankDTO) {
        IPage<PublicBankVO> pageVO = publicBankService.pagePublicBank(pageParam, publicBankDTO);
        return ServerResponseEntity.success(pageVO);
    }

    @PostMapping("/save")
    @Operation(summary = "保存", description = "保存")
    @PreAuthorize("@pms.hasPermission('publicBank:save')")
    public ServerResponseEntity<Void> save(@RequestBody PublicBankDTO publicBankDTO) {
        publicBankService.save(publicBankDTO);
        return ServerResponseEntity.success();
    }

    @PutMapping("/update")
    @Operation(summary = "更新", description = "更新")
    @PreAuthorize("@pms.hasPermission('publicBank:update')")
    public ServerResponseEntity<Void> update(@RequestBody PublicBankDTO publicBankDTO) {
        publicBankService.update(publicBankDTO);
        return ServerResponseEntity.success();
    }

    @DeleteMapping("/deleteByIds")
    @Operation(summary = "批量删除", description = "批量删除")
    @PreAuthorize("@pms.hasPermission('publicBank:delete')")
    public ServerResponseEntity<Void> deleteByIds(List<Long> bankIds) {
        publicBankService.deleteByIds(bankIds);
        return ServerResponseEntity.success();
    }
}
