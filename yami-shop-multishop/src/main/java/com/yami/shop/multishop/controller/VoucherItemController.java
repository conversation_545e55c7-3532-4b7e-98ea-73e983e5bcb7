package com.yami.shop.multishop.controller;


import com.yami.shop.bean.enums.VoucherItemStatus;
import com.yami.shop.bean.model.Voucher;
import com.yami.shop.bean.model.VoucherItem;
import com.yami.shop.bean.param.VoucherItemParam;
import com.yami.shop.bean.vo.VoucherItemImportVO;
import com.yami.shop.bean.vo.VoucherItemVO;
import com.yami.shop.bean.vo.VoucherStockBillVO;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.service.VoucherItemService;
import com.yami.shop.service.VoucherService;
import com.yami.shop.sys.common.service.ShopEmployeeService;
import jakarta.servlet.http.HttpServletResponse;

import com.yami.shop.common.response.ServerResponseEntity;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yami.shop.common.util.PageParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;


/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/multishop/voucherItem")
@Tag(name = "电子卡券详情")
@AllArgsConstructor
public class VoucherItemController {

    private final VoucherItemService voucherItemService;
    private final VoucherService voucherService;
    private final ShopEmployeeService shopEmployeeService;

    @GetMapping("/page")
    @Operation(summary = "获取列表", description = "分页获取列表")
    public ServerResponseEntity<IPage<VoucherItemVO>> getVoucherItemPage(PageParam<VoucherItemVO> page, VoucherItemParam param) {
        checkVoucherShop(param.getVoucherId());
        IPage<VoucherItemVO> iPage = voucherItemService.pageByParam(page, param);
        return ServerResponseEntity.success(iPage);
    }

    @GetMapping("/info/{voucherItemId}")
    @Operation(summary = "获取", description = "根据voucherItemId获取")
    @Parameter(name = "voucherItemId", description = "", required = true)
    public ServerResponseEntity<VoucherItem> getById(@PathVariable("voucherItemId") Long voucherItemId) {
        return ServerResponseEntity.success(voucherItemService.getById(voucherItemId));
    }

    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('multishop:voucherItem:delete')")
    @Operation(summary = "删除", description = "根据id删除")
    @Parameter(name = "voucherItemId", description = "", required = true)
    public ServerResponseEntity<Boolean> removeByIds(@RequestBody List<Long> ids) {
        List<VoucherItem> voucherItemList = voucherItemService.list(new LambdaQueryWrapper<>(VoucherItem.class).in(VoucherItem::getVoucherItemId, ids).ne(VoucherItem::getStatus, VoucherItemStatus.DELETE.value()));
        if (Objects.isNull(voucherItemList)) {
            return ServerResponseEntity.success();
        }
        checkVoucherShop(voucherItemList.get(0).getVoucherId());

        VoucherStockBillVO voucherStockBillVO = new VoucherStockBillVO();
        voucherStockBillVO.setShopId(SecurityUtils.getShopUser().getShopId());
        voucherStockBillVO.setEmployeeId(SecurityUtils.getShopUser().getEmployeeId());
        voucherStockBillVO.setEmployeeMobile(shopEmployeeService.getShopEmployeeById(voucherStockBillVO.getEmployeeId()).getMobile());

        voucherItemService.remove(voucherItemList, voucherStockBillVO);
        return ServerResponseEntity.success();
    }

    @PostMapping("/importExcel")
    @Operation(summary = "批量导入电子卡券" , description = "批量导入电子卡券")
    @PreAuthorize("@pms.hasPermission('multishop:voucherItem:import')")
    public ServerResponseEntity<VoucherItemImportVO> importExcel(@RequestParam("excelFile") MultipartFile excelFile, @RequestParam("voucherId") Long voucherId) throws Exception {
        if (Objects.isNull(excelFile)) {
            throw new YamiShopBindException("yami.network.busy");
        }

        VoucherStockBillVO voucherStockBillVO = new VoucherStockBillVO();
        voucherStockBillVO.setShopId(SecurityUtils.getShopUser().getShopId());
        voucherStockBillVO.setEmployeeId(SecurityUtils.getShopUser().getEmployeeId());
        voucherStockBillVO.setEmployeeMobile(shopEmployeeService.getShopEmployeeById(voucherStockBillVO.getEmployeeId()).getMobile());

        VoucherItemImportVO voucherItemImportVO = voucherItemService.parseFile(excelFile, voucherId, voucherStockBillVO);
        return ServerResponseEntity.success(voucherItemImportVO);
    }

    @GetMapping("/downLoadModel")
    @Operation(summary = "下载导入模板" , description = "下载电子卡券导入模板")
    public void downLoadModel(HttpServletResponse response) {
        voucherItemService.downLoadModel(response);
    }

    @GetMapping("/exportExcel")
    @Operation(summary = "导出卡券明细", description = "导出卡券明细")
    @PreAuthorize("@pms.hasPermission('multishop:voucherItem:export')")
    public void prodExport(HttpServletResponse response, VoucherItemParam param) {
        voucherItemService.export(response, param);
    }

    private void checkVoucherShop(Long voucherId) {
        Long shopId = AuthUserContext.getShopId();
        Voucher voucher = voucherService.getOne(new LambdaQueryWrapper<>(Voucher.class).ne(Voucher::getStatus, -1).eq(Voucher::getVoucherId, voucherId));
        if (Objects.isNull(voucher) || !Objects.equals(shopId, voucher.getShopId())) {
            throw new YamiShopBindException("yami.voucherItem.throw.tip.noPermission");
        }
    }
}
