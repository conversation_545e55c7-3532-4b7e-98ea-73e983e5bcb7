package com.yami.shop.bean.vo.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
@Data
public class MemberDealVO {

    @Schema(description = "成交会员数" )
    private Integer payMemberNum;

    @Schema(description = "成交会员数占比" )
    private Double payMemberNumRate;

    @Schema(description = "支付订单数" )
    private Integer payOrderNum;

    @Schema(description = "客单价" )
    private Double pricePerMember;

    @Schema(description = "支付金额" )
    private Double payAmount;

    @Schema(description = "支付金额占比" )
    private Double payAmountRate;

    @Schema(description = "会员类型(0:老会员, 1:新会员)")
    private Integer memberType;

    @Schema(description = "用户id")
    private String userId;
}
