package com.yami.shop.distribution.platform.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yami.shop.bean.enums.EnterpriseApplyType;
import com.yami.shop.bean.event.EnterprisePayFailEvent;
import com.yami.shop.bean.event.EnterprisePaySuccessEvent;
import com.yami.shop.bean.model.EnterprisePay;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.util.Arith;
import com.yami.shop.distribution.common.constants.DistributionWithdrawCashStateEnum;
import com.yami.shop.distribution.common.model.DistributionUser;
import com.yami.shop.distribution.common.model.DistributionUserWallet;
import com.yami.shop.distribution.common.model.DistributionUserWalletBill;
import com.yami.shop.distribution.common.model.DistributionWithdrawCash;
import com.yami.shop.distribution.common.service.DistributionUserService;
import com.yami.shop.distribution.common.service.DistributionUserWalletBillService;
import com.yami.shop.distribution.common.service.DistributionUserWalletService;
import com.yami.shop.distribution.common.service.DistributionWithdrawCashService;
import lombok.AllArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * 商家转账事件监听
 * <AUTHOR>
 */
@Component("distributionWithdrawEnterprisePayListener")
@AllArgsConstructor
public class EnterprisePayListener {

    private final DistributionWithdrawCashService distributionWithdrawCashService;
    private final DistributionUserWalletService distributionUserWalletService;
    private final DistributionUserService distributionUserService;
    private final DistributionUserWalletBillService distributionUserWalletBillService;


    @EventListener(EnterprisePaySuccessEvent.class)
    public void distributionWithdrawEventHandle(EnterprisePaySuccessEvent event) {
        EnterprisePay enterprisePay = event.getEnterprisePay();
        if (!Objects.equals(enterprisePay.getType(), EnterpriseApplyType.DISTRIBUTION_WITHDRAW.value())) {
            return;
        }
        // 更新提现状态
        DistributionWithdrawCash distributionWithdrawCash = distributionWithdrawCashService.getById(enterprisePay.getBizId());
        distributionWithdrawCash.setState(DistributionWithdrawCashStateEnum.CASH_SUCCESS.getValue());
        distributionWithdrawCash.setUpdateTime(new Date());
        distributionWithdrawCashService.updateById(distributionWithdrawCash);
    }


    @EventListener(EnterprisePayFailEvent.class)
    public void shopWithdrawFailEventHandle(EnterprisePayFailEvent event) {
        EnterprisePay enterprisePay = event.getEnterprisePay();
        //商户提现失败
        if (!Objects.equals(EnterpriseApplyType.DISTRIBUTION_WITHDRAW.value(), enterprisePay.getType())) {
            return;
        }
        DistributionWithdrawCash distributionWithdrawCash = distributionWithdrawCashService.getById(enterprisePay.getBizId());
        DistributionUser distributionUser = distributionUserService.getByUserIdAndShopId(enterprisePay.getUserId(), Constant.PLATFORM_SHOP_ID);
        DistributionUserWallet distributionUserWallet = distributionUserWalletService.getOne(new LambdaQueryWrapper<DistributionUserWallet>()
                .eq(DistributionUserWallet::getDistributionUserId, distributionUser.getDistributionUserId()));
        // 更新状态并将金额回退到分销员钱包中
        distributionWithdrawCash.setState(DistributionWithdrawCashStateEnum.CASH_FAIL.getValue());
        // 增加用户可提现金额
        distributionUserWallet.setSettledAmount(Arith.add(distributionUserWallet.getSettledAmount(), distributionWithdrawCash.getAmount()));

        //增加钱包流水记录
        distributionUserWalletBillService.save(new DistributionUserWalletBill(distributionUserWallet, "提现失败","Failed to withdraw cash", 0.0, distributionWithdrawCash.getAmount(), 0.0, 0.0, 0));
        distributionUserWalletService.updateById(distributionUserWallet);
    }

}
