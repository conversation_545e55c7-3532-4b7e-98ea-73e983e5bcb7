package com.yami.shop.common.allinpay.constant;

/**
 * 通联店铺状态 0未开通 1营业中 2平台下线 3上线待审核 4开店待审核
 * <AUTHOR>
 */
public enum AllinpayShopStatus {

    /**
     * 未开通
     */
    UN_AVAILABLE(0),

    /**
     * 营业中
     */
    OPEN(1),

    /**
     * 平台下线
     */
    OFFLINE(2),

    /**
     * 上线待审核
     */
    WAIT_AUDIT(3),

    /**
     * 开店待审核
     */
    OPEN_WAIT_AUDIT(4);

    private final Integer num;

    public Integer value() {
        return num;
    }

    AllinpayShopStatus(Integer num) {
        this.num = num;
    }
}
