package com.yami.shop.bean.vo.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 频道订阅者返回
 * <AUTHOR>
 */
public class SubscriberVO {

    @Schema(description = "平台/商家订阅者 uid")
    private String uid;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "商家id")
    private Long shopId;

    @Schema(description = "平台/商家管理员昵称")
    private String nickName;

    @Schema(description = "头像")
    private String pic;

    @Schema(description = "是否为白名单用户 1是0否")
    private Integer isWhiteListUser;

    @Schema(description = "是否在线 1是0否")
    private Integer isOnline;

    @Schema(description = "用户类型 0用户 1商家 2平台 -1机器人")
    private Integer sendUserType;

    @Schema(description = "用户状态 0禁用1正常")
    private Integer status;

    public SubscriberVO() {
    }

    public SubscriberVO(String uid, String nickName, String pic) {
        this.uid = uid;
        this.nickName = nickName;
        this.pic = pic;
    }

    public SubscriberVO(String uid, String nickName, String pic, Integer isWhiteListUser, Integer isOnline, Integer sendUserType) {
        this.uid = uid;
        this.nickName = nickName;
        this.pic = pic;
        this.isWhiteListUser = isWhiteListUser;
        this.isOnline = isOnline;
        this.sendUserType = sendUserType;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public Integer getIsWhiteListUser() {
        return isWhiteListUser;
    }

    public void setIsWhiteListUser(Integer isWhiteListUser) {
        this.isWhiteListUser = isWhiteListUser;
    }

    public Integer getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(Integer isOnline) {
        this.isOnline = isOnline;
    }

    public Integer getSendUserType() {
        return sendUserType;
    }

    public void setSendUserType(Integer sendUserType) {
        this.sendUserType = sendUserType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "SubscriberVO{" +
                "uid='" + uid + '\'' +
                ", userId='" + userId + '\'' +
                ", shopId=" + shopId +
                ", nickName='" + nickName + '\'' +
                ", pic='" + pic + '\'' +
                ", isWhiteListUser=" + isWhiteListUser +
                ", isOnline=" + isOnline +
                ", sendUserType=" + sendUserType +
                ", status=" + status +
                '}';
    }
}
