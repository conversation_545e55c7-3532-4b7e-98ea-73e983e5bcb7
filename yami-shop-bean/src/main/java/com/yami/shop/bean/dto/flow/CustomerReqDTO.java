package com.yami.shop.bean.dto.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CustomerReqDTO {
    /**
     * 时间类型 1今日实时 2 近7天 3 近30天 4自然日 5自然月
     */
    @Schema(description = "时间类型 1今日实时 2 近7天 3 近30天 4自然日 5自然月" )
    private Integer dateType;
    /**
     * 开始时间
     */
    @Schema(description = "开始时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 开始时间
     */
    @Schema(description = "开始时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dayStartTime;
    /**
     * 结束时间
     */
    @Schema(description = "结束时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 时间
     */
    @Schema(description = "不传字段" )
    private Date dateTime;

    /**
     * 店铺id
     */
    @Schema(description = "不传字段" )
    private Long shopId;

    /**
     * 第三方系统id 1：微信小程序
     */
    @Schema(description = "不传字段" )
    private Integer appId;

    /**
     * 粉丝=付费会员
     */
    private Integer member;
}
