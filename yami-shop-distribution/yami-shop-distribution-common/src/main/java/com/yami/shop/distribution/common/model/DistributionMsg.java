package com.yami.shop.distribution.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yami.shop.bean.vo.SysUserVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName( "tz_distribution_msg")
public class DistributionMsg {

    @TableId
    @Schema(description = "通知表id" )
    private Long msgId;

    @Schema(description = "通知级别(0 系统通知 1商家通知 2团队通知)" )
    private Integer level;

    @Schema(description = "通知标题" )
    private String msgTitle;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "指定上线时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "指定下线时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @Schema(description = "是否置顶(0 不置顶 1 置顶)" )
    private Integer isTop;

    @Schema(description = "公告内容" )
    private String content;

    @Schema(description = "公告封面图" )
    private String pic;

    @Schema(description = "通知类型(0:紧急通知， 1:活动通知，2:一般通知)" )
    private Integer msgType;

    @Schema(description = "通知状态(0 下线 1上线)" )
    private Integer state;

    @Schema(description = "修改时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "修改人" )
    private Long modifier;

    @Schema(description = "关联管理员" )
    @TableField(exist = false)
    private SysUserVO sysUser;
}
