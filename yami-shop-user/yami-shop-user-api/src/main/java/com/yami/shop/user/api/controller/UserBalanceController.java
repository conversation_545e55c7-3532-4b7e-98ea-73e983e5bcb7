package com.yami.shop.user.api.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yami.shop.bean.model.UserExtension;
import com.yami.shop.common.allinpay.constant.PaySysType;
import com.yami.shop.common.bean.PaySettlementConfig;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.security.api.util.SecurityUtils;
import com.yami.shop.service.SysConfigService;
import com.yami.shop.service.UserExtensionService;
import com.yami.shop.user.common.dto.UserBalanceDto;
import com.yami.shop.user.common.model.UserBalance;
import com.yami.shop.user.common.service.UserBalanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;


/**
 * 余额充值级别表
 *
 * <AUTHOR>
 * @date 2020-09-08 10:42:39
 */
@RestController
@AllArgsConstructor
@Tag(name = "会员充值信息接口")
@RequestMapping("/p/userBalance" )
public class UserBalanceController {

    private final SysConfigService sysConfigService;
    private final UserBalanceService userBalanceService;
    private final UserExtensionService userExtensionService;


    /**
     * 查询充值数据列表
     * @return 列表数据
     */
    @GetMapping("/getList" )
    @Operation(summary = "获取充值数据列表" , description = "获取充值数据列表")
    public ServerResponseEntity<List<UserBalanceDto>> getUserBalancePage() {
        List<UserBalance> balanceList = userBalanceService.getBalanceList();
        List<UserBalanceDto> userBalanceDtoList = BeanUtil.mapAsList(balanceList, UserBalanceDto.class);
        return ServerResponseEntity.success(userBalanceDtoList);
    }

    /**
     * 查询用户余额
     * @return 用户余额
     */
    @GetMapping("/getBalanceInfo" )
    @Operation(summary = "查询用户余额" , description = "查询用户余额")
    public ServerResponseEntity<Double> getBalanceInfo() {
        UserExtension userExtension = userExtensionService.getOne(new LambdaQueryWrapper<UserExtension>().eq(UserExtension::getUserId, SecurityUtils.getUser().getUserId()));
        // 原生支付，取默认余额；通联支付，取通联余额
        PaySettlementConfig config = sysConfigService.getSysConfigObject(Constant.PAY_SETTLEMENT_CONFIG, PaySettlementConfig.class);
        Double balance = Objects.equals(config.getPaySettlementType(), PaySysType.DEFAULT.value()) ? userExtension.getTotalBalance() : userExtension.getAllinpayTotalBalance();
        if (Objects.isNull(balance)){
            balance = 0.0;
        }
        return ServerResponseEntity.success(balance);
    }
}
