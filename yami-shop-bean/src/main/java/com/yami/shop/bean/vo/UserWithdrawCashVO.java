package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-05-09
 */
@Data
public class UserWithdrawCashVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "提现记录id")
    private Long cashId;

    @Schema(description = "提现订单号")
    private String orderNo;

    @Schema(description = "提现金额")
    private Double amount;

    @Schema(description = "提现状态(0:待支付, 1:申请成功, 2:提现成功, 3:提现失败)")
    private Integer status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
