package com.yami.shop.groupbuy.common.utils;

import com.yami.shop.groupbuy.common.enums.ActivityStatusEnum;
import com.yami.shop.groupbuy.common.enums.GroupActivityStatusEnum;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/9/6 11:17
 */
public class GroupActivityUtil {

    /**
     * 判断活动状态
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param status    状态
     * @return 活动状态
     */
    public static Integer getActivityStatus(Date startTime, Date endTime, Integer status) {
        long now = System.currentTimeMillis();
        if (Objects.equals(status, GroupActivityStatusEnum.OFFLINE.value())) {
            return ActivityStatusEnum.OFFLINE.value();
        }
        if (Objects.equals(status, GroupActivityStatusEnum.WAIT_AUDIT.value())) {
            return ActivityStatusEnum.WAIT_AUDIT.value();
        }
        if (Objects.equals(status, GroupActivityStatusEnum.INVALID.value()) || Objects.equals(status, GroupActivityStatusEnum.DELETE.value())) {
            return ActivityStatusEnum.EXPIRED.value();
        }
        if (Objects.equals(status, GroupActivityStatusEnum.DISABLE.value()) || now < startTime.getTime()) {
            return ActivityStatusEnum.NOT_STARTED.value();
        }
        if (now >= startTime.getTime() && now <= endTime.getTime()) {
            return ActivityStatusEnum.UNDER_WAY.value();
        }
        return ActivityStatusEnum.FINISHED.value();
    }
}
