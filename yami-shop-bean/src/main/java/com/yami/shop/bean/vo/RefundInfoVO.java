package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 退款信息VO
 *
 * <AUTHOR>
 * @date 2021-03-15 15:26:03
 */
@Data
public class RefundInfoVO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "序号" )
    private Long index;

    @Schema(description = "退款单号" )
    private Long refundId;

    @Schema(description = "关联的支付订单id" )
    private Long orderId;

    @Schema(description = "关联的支付单id" )
    private Long payId;

    @Schema(description = "退款状态" )
    private Integer refundStatus;

    @Schema(description = "退款金额" )
    private Long refundAmount;

    @Schema(description = "支付方式" )
    private Integer payType;

    @Schema(description = "回调内容" )
    private String callbackContent;

    @Schema(description = "回调时间" )
    private Date callbackTime;

}
