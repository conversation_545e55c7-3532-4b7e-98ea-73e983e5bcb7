package com.yami.shop.groupbuy.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/05/17
 */
@Data
public class GroupOrderDTO {

    @Schema(description = "拼团订单id" )
    private Long groupOrderId;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "拼团团队id" )
    private Long groupTeamId;

    @Schema(description = "拼团活动id" )
    private Long groupActivityId;

    @Schema(description = "user_id(当user_id为0时标识为机器人)" )
    private String userId;

    @Schema(description = "身份标识(0:成员  1:团长)" )
    private Integer identityType;

    @Schema(description = "销售价" )
    private Double activityProdPrice;

    @Schema(description = "拼团价" )
    private Double groupPrice;

    @Schema(description = "实付金额" )
    private Double payPrice;

    @Schema(description = "订单编号" )
    private String orderNumber;

    @Schema(description = "创建时间" )
    private Date createTime;

    @Schema(description = "状态 0:待支付、1:支付成功、-1:失效" )
    private Integer status;
}
