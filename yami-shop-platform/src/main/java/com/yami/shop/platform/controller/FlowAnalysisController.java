package com.yami.shop.platform.controller;

import com.yami.shop.bean.dto.FlowAnalysisDto;
import com.yami.shop.bean.dto.SystemDto;
import com.yami.shop.bean.param.FlowAnalysisParam;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.FlowLogService;
import com.yami.shop.service.FlowUserAnalysisExcelService;
import com.yami.shop.service.FlowUserAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR> on 2018/11/26.
 */
@RestController
@RequestMapping("/platform/flowAnalysis")
@Tag(name = "流量概况")
@AllArgsConstructor
public class FlowAnalysisController {

    private final FlowUserAnalysisService flowUserAnalysisService;
    private final FlowUserAnalysisExcelService flowUserAnalysisExcelService;
    private final FlowLogService flowLogService;

    @GetMapping("/getAnalysisData")
    @Operation(summary = "流量总览" , description = "流量总览")
    @PreAuthorize("@pms.hasPermission('platform:flowAnalysis:info')")
    public ServerResponseEntity<FlowAnalysisDto> getAnalysisData(FlowAnalysisParam flowAnalysisParam) {
        //获取开始和结束时间
        flowAnalysisParam.setTime(1);
        FlowAnalysisDto flowAnalysisDto = flowUserAnalysisService.getFlowAnalysisData(flowAnalysisParam);
        return ServerResponseEntity.success(flowAnalysisDto);
    }

    @GetMapping("/analysisDataExport")
    @PreAuthorize("@pms.hasPermission('flow:data:export')")
    @Operation(summary = "导出流量总览" , description = "导出流量总览")
    public void analysisDataExport(FlowAnalysisParam flowAnalysisParam, HttpServletResponse response) {
        //获取开始和结束时间
        flowAnalysisParam.setTime(1);
        flowUserAnalysisExcelService.analysisDataExport(flowAnalysisParam, response);
    }

    @GetMapping("/flowTrend")
    @Operation(summary = "流量趋势" , description = "流量趋势")
    @PreAuthorize("@pms.hasPermission('platform:flowAnalysis:info')")
    public ServerResponseEntity<List<FlowAnalysisDto>> flowTrend(FlowAnalysisParam flowAnalysisParam) {
        // 获取开始和结束时间
        flowAnalysisParam.setTime(2);
        List<FlowAnalysisDto> flowAnalysisDtoList = flowUserAnalysisService.flowTrend(flowAnalysisParam);
        return ServerResponseEntity.success(flowAnalysisDtoList);
    }


    @GetMapping("/flowTrendExport")
    @PreAuthorize("@pms.hasPermission('flow:trend:export')")
    @Operation(summary = "导出流量趋势" , description = "导出流量趋势")
    public void flowTrendExport(FlowAnalysisParam flowAnalysisParam, HttpServletResponse response) {
        //获取开始和结束时间
        flowAnalysisParam.setTime(2);
        flowUserAnalysisExcelService.flowTrendExport(flowAnalysisParam, response);
    }

    @GetMapping("/flowSour")
    @Operation(summary = "成交转换" , description = "成交转换")
    @PreAuthorize("@pms.hasPermission('platform:flowAnalysis:info')")
    public ServerResponseEntity<List<FlowAnalysisDto>> flowSour(FlowAnalysisParam flowAnalysisParam) {
        flowAnalysisParam.setTime(1);
        List<FlowAnalysisDto> flowAnalysisDtoList = flowUserAnalysisService.flowSour(flowAnalysisParam);
        return ServerResponseEntity.success(flowAnalysisDtoList);
    }


    @GetMapping("/flowSourExport")
    @PreAuthorize("@pms.hasPermission('flow:sour:export')")
    @Operation(summary = "导出成交转换" , description = "导出成交转换")
    public void flowSourExport(FlowAnalysisParam flowAnalysisParam, HttpServletResponse response) {
        //获取开始和结束时间
        flowAnalysisParam.setTime(1);
        flowUserAnalysisExcelService.flowSour(flowAnalysisParam, response);
    }

    @GetMapping("/systemTypeNums")
    @Operation(summary = "系统访客数量" , description = "系统访客数量")
    @PreAuthorize("@pms.hasPermission('platform:flowAnalysis:info')")
    public ServerResponseEntity<SystemDto> systemTypeNums(FlowAnalysisParam flowAnalysisParam) {
        flowAnalysisParam.setTime(1);
        SystemDto systemDto = flowUserAnalysisService.systemTypeNums(flowAnalysisParam);
        return ServerResponseEntity.success(systemDto);
    }

    /**
     * 实时统计刷新数据
     */
    @GetMapping("/refresh")
    @Operation(summary = "实时统计刷新数据", description = "实时统计刷新数据")
    public ServerResponseEntity refresh() {
        flowLogService.insertBatch();
        return ServerResponseEntity.success();
    }
}
