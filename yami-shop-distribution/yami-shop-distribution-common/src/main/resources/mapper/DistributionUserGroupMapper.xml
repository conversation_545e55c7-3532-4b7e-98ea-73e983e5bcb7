<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.distribution.common.dao.DistributionUserGroupMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.distribution.common.model.DistributionUserGroup">
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
      <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
  </resultMap>

  <resultMap id="DistributionUserGroups_SysUser" type="com.yami.shop.distribution.common.model.DistributionUserGroup">
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
      <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />

    <association property="sysUser" javaType="com.yami.shop.bean.vo.SysUserVO">
      <id column="user_id" jdbcType="BIGINT" property="userId" />
      <result column="username" jdbcType="VARCHAR" property="username" />
    </association>
  </resultMap>

  <select id="distributionUserGroupsAndSysUserPage" resultMap="DistributionUserGroups_SysUser">
      select
      dug.*,
      su.user_id,su.username
      from
      tz_distribution_user_group dug
      left join tz_sys_user su on dug.modifier=su.user_id
      <where>
          <if test="distributionUserGroup.shopId!= null ">
              and dug.shop_id=#{distributionUserGroup.shopId}
          </if>
          <if test="distributionUserGroup.groupName!= null and distributionUserGroup.groupName!='' ">
              and group_name LIKE CONCAT('%',#{distributionUserGroup.groupName},'%')
          </if>
      </where>
  </select>

</mapper>
