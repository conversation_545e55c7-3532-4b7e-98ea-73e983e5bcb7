package com.yami.shop.security.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yami.shop.security.common.model.AppConnect;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface AppConnectMapper extends BaseMapper<AppConnect> {

    /**
     * 根据第三方userId和第三方系统id获取用户关联第三方信息
     *
     * @param bizUserId 第三方用户id
     * @param appId     第三方系统id
     * @return 用户关联第三方信息
     */
    AppConnect getByBizUserId(@Param("bizUserId") String bizUserId, @Param("appId") Integer appId);

    /**
     * 获取根据尝试社交登录时，保存的临时的uid获取社交
     *
     * @param tempUid tempUid
     * @return 用户社交账号信息
     */
    AppConnect getByTempUid(@Param("tempUid") String tempUid);

    /**
     * 绑定社交账号，通过tempuid
     *
     * @param userId  userId
     * @param tempUid tempUid
     */
    void bindUserIdByTempUid(@Param("userId") String userId, @Param("tempUid") String tempUid);

    /**
     * 解除用户绑定
     *
     * @param bizUserId  openid
     * @param socialType 社交账号类型
     */
    void unBindUser(@Param("bizUserId") String bizUserId, @Param("socialType") Integer socialType);

    /**
     * 解除用户绑定
     *
     * @param userId 用户id
     */
    void unBindUserByUserId(@Param("userId") String userId);
}
