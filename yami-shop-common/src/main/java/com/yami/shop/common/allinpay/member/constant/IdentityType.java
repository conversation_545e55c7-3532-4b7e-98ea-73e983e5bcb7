package com.yami.shop.common.allinpay.member.constant;

/**
 * <AUTHOR>
 */

public enum IdentityType {

    /**
     * 身份证
     */
    ID_CARD(1),
    /**
     * 护照
     */
    PASSPORT(2),
    /**
     * 军官证
     */
    OFFICER_CERTIFICATE(3),
    /**
     * 回乡证
     */
    REENTRY_PERMIT(4),
    /**
     * 台胞证
     */
    MTP(5),
    /**
     * 警官证
     */
    POLICE_CERTIFICATE(6),
    /**
     * 士兵证
     */
    SOLDIER(7),
    /**
     * 户口簿
     */
    RESIDENCE_BOOKLET(8),
    /**
     * 港澳居民来往内地通行证
     */
    HOME_RETURN_PERMIT(9),
    /**
     * 临时身份证
     */
    INTERIM_IDENTITY_CARD(10),
    /**
     * 外国人居留证
     */
    RESIDENCE_PERMIT_FOR_FOREIGNERS(11),
    /**
     * 港澳台居民居住证
     */
    RESIDENCE_PERMIT(12),
    /**
     * 其他证件
     */
    OTHER(99);

    private final Integer num;

    public Integer value() {
        return num;
    }

    IdentityType(Integer num) {
        this.num = num;
    }
}
