package com.yami.shop.common.allinpay.constant;

/**
 * 通联提现状态 0.待支付 1.申请成功(还没确认)  2.提现成功 3.提现失败
 * <AUTHOR>
 */
public enum AllinpayWithdrawCashStatusType {

    /**
     * 待支付
     * 仅针对收银宝快捷短信验证码输入错误场景，允许商户继续调用确认支付
     */
    WAIT_PAY(0),
    /**
     * 申请成功(还没确认)
     */
    APPLY_SUCCESS(1),

    /**
     * 提现成功
     */
    CASH_SUCCESS(2),

    /**
     * 提现失败
     */
    CASH_FAIL(3),
    /**
     * 提现中
     */
    WITHDRAWING(4);

    private final Integer value;

    public Integer value() {
        return value;
    }

    AllinpayWithdrawCashStatusType(Integer value) {
        this.value = value;
    }
}
