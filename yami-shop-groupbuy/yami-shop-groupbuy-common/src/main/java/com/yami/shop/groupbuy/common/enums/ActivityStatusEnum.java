package com.yami.shop.groupbuy.common.enums;


import lombok.Getter;

/**
 * 拼团活动状态类型(用户查询之后对活动活动的封装)
 *
 * <AUTHOR>
 */
@Getter
public enum ActivityStatusEnum {

    /**
     * 未开始
     */
    NOT_STARTED(1, "未开始"),

    /**
     * 进行中
     */
    UNDER_WAY(2, "进行中"),

    /**
     * 已结束
     */
    FINISHED(3, "已结束"),

    /**
     * 已失效
     */
    EXPIRED(4, "已失效"),

    /**
     * 违规下架
     */
    OFFLINE(5, "违规下架"),

    /**
     * 等待审核
     */
    WAIT_AUDIT(6, "等待审核");


    private final Integer code;

    private final String title;

    public Integer value() {
        return code;
    }

    ActivityStatusEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }
}
