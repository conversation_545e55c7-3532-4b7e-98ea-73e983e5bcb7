package com.yami.shop.multishop.controller;

import cn.hutool.core.util.PhoneUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yami.shop.allinpay.service.AllinpayCompanyService;
import com.yami.shop.bean.enums.AuditStatus;
import com.yami.shop.bean.model.CompanyAuditing;
import com.yami.shop.bean.model.ShopCompany;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ResponseEnum;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.CompanyAuditingService;
import com.yami.shop.service.ShopDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/9/21 10:22
 */

@RestController
@RequestMapping("/shop/companyAuditing")
@Tag(name = "变更工商信息接口")
@AllArgsConstructor
public class CompanyAuditingController {

    private final CompanyAuditingService companyAuditingService;
    private final AllinpayCompanyService allinpayCompanyService;
    private final ShopDetailService shopDetailService;

    @PostMapping("/applyChangeCompanyInfo")
    @Operation(summary = "申请变更工商信息", description = "申请变更工商信息")
    @PreAuthorize("@pms.hasPermission('shop:shopCompany:applyChange')")
    public ServerResponseEntity<Void> applyChangeCompanyInfo(@RequestBody @Valid ShopCompany shopCompany) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        String userId = SecurityUtils.getShopUser().getUserId();
        // 校验是否已经存在申请中记录
        CompanyAuditing latestCompanyAuditing = companyAuditingService.getOne(new LambdaQueryWrapper<CompanyAuditing>().eq(CompanyAuditing::getShopId, shopId).orderByDesc(CompanyAuditing::getCreateTime), false);
        if (Objects.nonNull(latestCompanyAuditing) && Objects.equals(latestCompanyAuditing.getStatus(), AuditStatus.WAITAUDIT.value())) {
            throw new YamiShopBindException("yami.shop.company.repeat.application");
        }
        shopCompany.setShopCompanyId(null);
        shopCompany.setShopId(shopId);
        companyAuditingService.applyChangeCompanyInfo(shopCompany, userId);
        shopDetailService.removeShopDetailCacheByShopId(shopId);
        return ServerResponseEntity.success();
    }

    @PutMapping("/revoke")
    @Operation(summary = "撤销申请", description = "撤销申请")
    @PreAuthorize("@pms.hasPermission('shop:shopCompany:revoke')")
    public ServerResponseEntity<Void> revoke(@RequestBody Long companyAuditingId) {
        if (allinpayCompanyService.getIsAllinpay()) {
            // 当前无法撤销申请
            throw new YamiShopBindException("yami.company.auditing.exception.cannotCancel");
        }
        CompanyAuditing companyAuditing = companyAuditingService.getById(companyAuditingId);
        if (Objects.isNull(companyAuditing)) {
            return ServerResponseEntity.success();
        }
        if (!Objects.equals(companyAuditing.getShopId(), SecurityUtils.getShopUser().getShopId())) {
            return ServerResponseEntity.fail(ResponseEnum.UNAUTHORIZED);
        }
        companyAuditingService.revoke(companyAuditing);
        return ServerResponseEntity.success();
    }


    @GetMapping("/auditInfo")
    @Operation(summary = "查看申请审核情况", description = "查看申请审核情况")
    @PreAuthorize("@pms.hasPermission('shop:companyAuditing:info')")
    public ServerResponseEntity<CompanyAuditing> auditInfo() {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        CompanyAuditing auditInfo = companyAuditingService.getAuditInfo(shopId);
        if (Objects.nonNull(auditInfo) && Objects.nonNull(auditInfo.getShopCompany())) {
            if (Objects.nonNull(auditInfo.getShopCompany().getLegalPhone())) {
                auditInfo.getShopCompany().setLegalPhone(PhoneUtil.hideBetween(auditInfo.getShopCompany().getLegalPhone()).toString());
            }
        }
        return ServerResponseEntity.success(auditInfo);
    }
}
