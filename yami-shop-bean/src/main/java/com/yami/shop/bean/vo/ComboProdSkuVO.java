package com.yami.shop.bean.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 套装商品sku项
 *
 * <AUTHOR>
 * @date 2021-11-05 09:23:32
 */
@Data
@TableName("tz_combo_prod_sku")
public class ComboProdSkuVO implements Serializable{
    private static final long serialVersionUID = 1L;

    @TableId
    @Schema(description = "套餐商品sku项id" )
    private Long comboProdId;

    @Schema(description = "skuId" )
    @NotNull(message = "skuId不能为空")
    private Long skuId;

    @Schema(description = "搭配价格" )
    @NotNull(message = "搭配价格不能为空")
    private Double matchingPrice;

    @Schema(description = "sku名称" )
    @TableField(exist = false)
    private String skuName;

    @Schema(description = "sku图片" )
    @TableField(exist = false)
    private String pic;

    @Schema(description = "sku价格" )
    @TableField(exist = false)
    private Double price;

    @Schema(description = "sku库存" )
    @TableField(exist = false)
    private Integer stocks;

    @Schema(description = "sku状态 0 禁用 1 启用 -1 删除" )
    @TableField(exist = false)
    private Integer skuStatus;

    @Schema(description = "销售属性组合字符串 格式是p1:v1;p2:v2" )
    @TableField(exist = false)
    private String properties;

    /**
     * 商品是否必选：1：是 0：否
     */
    @TableField(exist = false)
    private Integer prodRequired;

}
