spring:
  application:
    name: @artifactId@
  # 环境 dev|prod|docker
  profiles:
    active: dev
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  #邮箱配置
  mail:
    port: 465
    default-encoding: utf-8
    protocol: smtps
# mybaits-plus配置
mybatis-plus:
  # MyBatis Mapper所对应的XML文件位置
  mapper-locations: classpath*:/mapper/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      # 主键类型 0:数据库ID自增 1.未定义 2.用户输入 3 id_worker 4.uuid 5.id_worker字符串表示
      id-type: AUTO
      #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
      field-strategy: NOT_NULL
      # 默认数据库表下划线命名
      table-underline: true

springdoc:
  # 默认是false，需要设置为true
  default-flat-param-object: true

sa-token:
  # token名称 (同时也是cookie名称)
  token-name: authorization
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token(不共用，避免登出时导致其他用户也登出)
  is-share: false
  # token风格(默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik)
  token-style: uuid
  # 是否输出操作日志
  is-log: false

# 用于美团leaf生成id -20
application:
  workerId: ${DATACENTER_ID:}

