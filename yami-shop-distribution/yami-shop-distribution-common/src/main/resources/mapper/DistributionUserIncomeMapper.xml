<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.distribution.common.dao.DistributionUserIncomeMapper">
    <resultMap id="BaseResultMap" type="com.yami.shop.distribution.common.model.DistributionUserIncome">
        <id column="income_id" jdbcType="BIGINT" property="incomeId"/>
        <result column="wallet_id" jdbcType="BIGINT" property="walletId"/>
        <result column="income_type" jdbcType="VARCHAR" property="incomeType"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="income_amount" jdbcType="DECIMAL" property="incomeAmount"/>
        <result column="order_item_id" jdbcType="BIGINT" property="orderItemId"/>
        <result column="merchant_order_id" jdbcType="VARCHAR" property="merchantOrderId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>

    </resultMap>

    <!--推广效果-->
    <resultMap id="DistributionUserIncome_DistributionUser"
               type="com.yami.shop.distribution.common.model.DistributionUserIncome">
        <id column="income_id" jdbcType="BIGINT" property="incomeId"/>
        <result column="wallet_id" jdbcType="BIGINT" property="walletId"/>
        <result column="income_type" jdbcType="VARCHAR" property="incomeType"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="income_amount" jdbcType="DECIMAL" property="incomeAmount"/>
        <result column="order_item_id" jdbcType="BIGINT" property="orderItemId"/>
        <result column="merchant_order_id" jdbcType="VARCHAR" property="merchantOrderId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <association property="distributionUser" javaType="com.yami.shop.distribution.common.vo.DistributionUserVO">
            <id column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId"/>
            <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
            <result column="user_mobile" jdbcType="VARCHAR" property="userMobile"/>
        </association>
        <association property="order" javaType="com.yami.shop.bean.model.Order">
            <result column="status" jdbcType="TINYINT" property="status"/>
        </association>
    </resultMap>
    <select id="incomeAndDistributionUserPage" resultMap="DistributionUserIncome_DistributionUser">
        select
        dui.*,
        du.nick_name,du.user_mobile,
        o.`status`
        from
        tz_distribution_user_income dui
        left join tz_distribution_user_wallet duw on duw.wallet_id = dui.wallet_id
        left join tz_distribution_user du on du.distribution_user_id=duw.distribution_user_id
        left join tz_order o on o.order_number = dui.order_number
        WHERE
            dui.income_type != 4
            AND dui.pay_sys_type = #{paySysType}
            <if test="shopId !=null">
                and du.shop_id=#{shopId}
            </if>
            <if test="rangeTimeParam.startTime !=null">
                and dui.create_time &gt;= #{rangeTimeParam.startTime}
            </if>
            <if test="state !=null">
                and dui.state = #{state}
            </if>
            <if test="rangeTimeParam.endTime !=null">
                and dui.create_time &lt;=#{rangeTimeParam.endTime}
            </if>
            <if test="distributionParam.userMobile != null and distributionParam.userMobile != ''">
                and du.user_mobile like concat( '%', #{distributionParam.userMobile}, '%' )
            </if>
            <if test="distributionParam.nickName != null and distributionParam.nickName != ''">
                AND du.nick_name LIKE CONCAT( '%', #{distributionParam.nickName}, '%' )
            </if>
            <if test="orderNumber != null and orderNumber != ''">
                and dui.order_number like concat('%',#{orderNumber},'%')
            </if>
        GROUP BY dui.income_id
        <if test="distributionUserIncome.sortParam != 0 and distributionUserIncome.sortType != 0">
            ORDER BY
            <choose>
                <when test="distributionUserIncome.sortParam == 1">
                    dui.income_amount
                </when>
                <when test="distributionUserIncome.sortParam == 2">
                    dui.create_time
                </when>
                <when test="distributionUserIncome.sortParam == 3">
                    dui.update_time
                </when>
                <otherwise>
                    dui.income_id
                </otherwise>
            </choose>
            <choose>
                <when test="distributionUserIncome.sortType == 1">
                    ASC
                </when>
                <when test="distributionUserIncome.sortType == 2">
                    DESC
                </when>
            </choose>
        </if>
    </select>
    <!--分销推广订单 -->
    <resultMap id="DistributionOrderDtoMap" type="com.yami.shop.distribution.common.dto.DistributionOrderDto">
        <result column="income_amount" property="incomeAmount"/>
        <result column="state" property="state"/>
        <result column="create_time" property="createTime"/>
        <result column="prod_id" property="prodId"/>
        <result column="prod_name" property="prodName"/>
        <result column="pic" property="pic"/>
    </resultMap>
    <select id="getDistributionOrderDtoByDistributionUserId" resultMap="DistributionOrderDtoMap">
            select
            dui.income_amount,
            dui.state,
            dui.create_time,
            o.pic,
            o.prod_id,
            o.prod_name
            from
            tz_distribution_user_income dui
            left join tz_order_item o on o.order_item_id=dui.order_item_id
            where dui.distribution_user_id=#{distributionUserId}
            AND dui.pay_sys_type = #{paySysType}
    </select>


    <resultMap id="DistributionUserWallet" type="com.yami.shop.distribution.common.model.DistributionUserWallet">
        <id column="wallet_id" jdbcType="BIGINT" property="walletId"/>
        <result column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId"/>
        <result column="unsettled_amount" jdbcType="DECIMAL" property="unsettledAmount"/>
        <result column="settled_amount" jdbcType="DECIMAL" property="settledAmount"/>
        <result column="addup_amount" jdbcType="DECIMAL" property="addupAmount"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
    </resultMap>

    <resultMap id="DistributionAmountAndWalletVO" type="com.yami.shop.distribution.common.vo.DistributionAmountAndWalletVO">
        <result column="wallet_id" jdbcType="BIGINT" property="distributionUserWalletId"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="parent_wallet_id" jdbcType="DECIMAL" property="parentDistributionUserWalletId"/>
        <result column="parent_amount" jdbcType="DECIMAL" property="parentAmount"/>
    </resultMap>


    <update id="updateStateByDistributionUserId">
        update tz_distribution_user_income
        set state = #{state}
        where
        distribution_user_id = #{distributionUserId}
    </update>

    <resultMap id="DistributionSettlementDto" type="com.yami.shop.distribution.common.dto.DistributionSettlementDto">
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="sum_amount" jdbcType="DECIMAL" property="sumAmount"/>
    </resultMap>

    <select id="statisticsDisUserIncome" resultType="double">
        SELECT
            COALESCE(SUM(income_amount),0)
        FROM
            tz_distribution_user_income dui
        WHERE
            dui.`distribution_user_id`= #{distributionUserId}
            AND dui.`state` = 2
            AND dui.pay_sys_type = #{paySysType}
            <if test="startTime != null and endTime != null">
                AND dui.`update_time` BETWEEN #{startTime} AND #{endTime}
            </if>
    </select>

    <select id="getDistributionUserIncomePage" resultType="com.yami.shop.distribution.common.dto.DistributionUserIncomeDto">
        SELECT dui.`income_amount`,dui.`income_type`,dui.`update_time`,dui.state
        FROM tz_distribution_user_income dui
        WHERE dui.income_amount != 0 AND dui.`distribution_user_id` = #{distributionUserId}
        AND dui.pay_sys_type = #{paySysType}
        ORDER BY dui.`update_time` DESC
    </select>
    <select id="getMyPromotionOrderByState" resultType="com.yami.shop.distribution.common.vo.DistributionOrdersVO">
        SELECT oi.order_number, oi.actual_total AS total, oi.prod_count, oi.pic, oi.price, oi.prod_name,
        oi.sku_name, dui.`update_time`, oi.order_item_id,dui.reson,dui.income_amount as distribution_amount,
        if(dui.reson > 0, -1, dui.state) as state
        FROM
        tz_distribution_user_income dui
        LEFT JOIN tz_order_item oi ON dui.`order_item_id` = oi.`order_item_id`
        LEFT JOIN tz_order o ON o.`order_number` = oi.`order_number`
        WHERE dui.`distribution_user_id` = #{distributionUserId} and dui.income_type != 4
            AND dui.pay_sys_type = #{paySysType}
        <choose>
            <when test="state != null and state == -1">
                AND dui.reson > 0
            </when>
            <when test="state != null and state != -1">
                AND dui.`state` = #{state}
            </when>
        </choose>
        GROUP BY dui.`income_id`
        ORDER BY dui.`update_time` DESC, dui.`income_id` ASC
    </select>

    <select id="listWaitCommissionSettlement" resultType="com.yami.shop.distribution.common.model.DistributionUserIncome">
        SELECT dui.*,oi.*,o.*, tor.return_money_sts FROM tz_distribution_user_income dui
        JOIN tz_order_item oi ON dui.`order_item_id` = oi.`order_item_id`
        JOIN tz_order  o ON o.`order_number` = dui.`order_number`
            left join tz_order_refund tor on tor.order_item_id = oi.order_item_id
        WHERE dui.`state` = 1 -- 未结算收入
<!--        AND dui.pay_sys_type = 1-->
        AND o.`status` = 5  -- 已确认收货
        AND oi.`distribution_card_no` != '' and oi.`distribution_card_no` is not null-- 分销订单项
        AND o.order_number in
        <foreach collection="orderNumbers" item="orderNumber" separator="," open="(" close=")">
            #{orderNumber}
        </foreach>
    </select>
    <update id="updateDistributionUserIncomesBy">
        update tz_distribution_user_income SET state=#{state}
        WHERE distribution_user_id=#{id}  AND income_type=#{type}
          AND order_number in
            <foreach collection="list" open="(" index="index" item="item" separator="," close=")">
                #{item}
            </foreach>
    </update>
    <select id="selectIncomeInfo" resultType="com.yami.shop.distribution.common.vo.DistributionUserInfoVO">
        SELECT tu.`user_id`, tu.pic, tu.`nick_name`,dui.distribution_user_id,
        SUM(IF(dui.`state` = 2, dui.`income_amount`, 0)) AS money,
        COUNT(DISTINCT IF(dui.`state` = 2, dui.`order_number`, NULL)) AS order_number
        FROM tz_distribution_user_bind ub
        LEFT JOIN tz_user tu ON ub.user_id = tu.user_id

        LEFT JOIN tz_order o ON o.user_id = tu.user_id
        LEFT JOIN `tz_distribution_user_income` dui ON o.order_number = dui.order_number
        AND dui.`pay_sys_type` = #{paySysType} AND dui.`income_type` = #{type}
        WHERE
        ub.`distribution_user_id` = #{distributionUserId} AND ub.`state` = 1

        GROUP BY tu.`user_id`
        ORDER BY money DESC
    </select>
    <select id="getFriendInfo" resultType="com.yami.shop.distribution.common.vo.DistributionUserInfoVO">
        SELECT
            tu.`user_id`,
            tu.pic,
            tu.`nick_name`,
            IFNULL(dui.money,0) AS money,
            IFNULL(dui.order_number, 0) AS order_number,
            du.distribution_user_id,
            du.state,
            du.bind_time
        FROM tz_distribution_user AS du
        LEFT JOIN (
            SELECT oi.distribution_card_no card_no,SUM(IFNULL(dui.`income_amount`, 0)) AS money,COUNT(IFNULL(dui.`order_number`, 0)) AS order_number
            FROM tz_distribution_user_income dui
            JOIN tz_order_item oi ON oi.distribution_card_no IN
            <foreach collection="friends" item="friend" open="(" separator="," close=")">
                #{friend.cardNo}
            </foreach>AND dui.order_item_id = oi.order_item_id
            JOIN tz_order o ON oi.order_number = o.order_number AND 1 &lt; o.`status` AND o.`status` &lt; 6
            WHERE dui.`state` = 2 AND dui.`income_type` = #{type} AND dui.distribution_user_id = #{distributionUser.distributionUserId}
                AND dui.pay_sys_type = #{paySysType}
            GROUP BY oi.distribution_card_no
        ) dui ON dui.card_no = du.card_no
        JOIN `tz_user` tu ON tu.`user_id` = du.`user_id`
        WHERE du.parent_id = #{distributionUser.distributionUserId} and du.state = 1
        ORDER BY money DESC, du.bind_time
    </select>

    <select id="listDistributionUserIncomeByOrderNumber" resultType="com.yami.shop.distribution.common.model.DistributionUserIncome">
        select ui.*,du.user_id from tz_distribution_user_income ui
        left join tz_distribution_user du on du.distribution_user_id = ui.distribution_user_id
        where ui.`state` = 1 and ui.order_number = #{orderNumber} AND ui.pay_sys_type = #{paySysType}
    </select>
</mapper>
