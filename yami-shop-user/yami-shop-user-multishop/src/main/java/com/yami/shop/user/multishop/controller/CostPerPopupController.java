package com.yami.shop.user.multishop.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.user.common.dto.CostPerPopupDTO;
import com.yami.shop.user.common.enums.PopupPageTypeEnum;
import com.yami.shop.user.common.enums.PopupStatusEnum;
import com.yami.shop.user.common.enums.PopupUserTypeEnum;
import com.yami.shop.user.common.enums.PushFrequencyEnum;
import com.yami.shop.user.common.model.CostPerPopup;
import com.yami.shop.user.common.service.CostPerPopupService;
import com.yami.shop.user.common.vo.CostPerPopupVO;
import com.yami.shop.user.common.vo.PopupRelateVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 弹窗广告
 * <AUTHOR>
 */
@RestController("multishopCostPerPopupController")
@RequestMapping("/costPerPopup")
@Tag(name = "multishop-弹窗广告")
@AllArgsConstructor
public class CostPerPopupController {

    private final CostPerPopupService costPerPopupService;

    @GetMapping("/page")
    @Operation(summary = "获取弹窗广告列表", description = "分页获取弹窗广告列表")
    @PreAuthorize("@pms.hasPermission('shop:popupAd:list')")
    public ServerResponseEntity<IPage<CostPerPopupVO>> page(PageParam<CostPerPopupVO> pageDTO, CostPerPopupDTO costPerPopupDTO) {
        costPerPopupDTO.setShopId(AuthUserContext.get().getShopId());
        IPage<CostPerPopupVO> costPerPopupPage = costPerPopupService.page(pageDTO, costPerPopupDTO);
        return ServerResponseEntity.success(costPerPopupPage);
    }

    @GetMapping
    @Operation(summary = "获取弹窗广告", description = "根据popupId获取弹窗广告")
    @PreAuthorize("@pms.hasPermission('shop:popupAd:view')")
    public ServerResponseEntity<CostPerPopupVO> getByPopupId(@RequestParam Long popupId) {
        CostPerPopupVO popup = costPerPopupService.getByPopupId(popupId);
        costPerPopupService.handleInfo(popup);
        return ServerResponseEntity.success(popup);
    }

    @PostMapping
    @Operation(summary = "保存弹窗广告", description = "保存弹窗广告")
    @PreAuthorize("@pms.hasPermission('shop:popupAd:save')")
    public ServerResponseEntity<Void> save(@Valid @RequestBody CostPerPopupDTO costPerPopupDTO) {
        CostPerPopup costPerPopup = BeanUtil.map(costPerPopupDTO, CostPerPopup.class);
        costPerPopup.setPopupId(null);
        costPerPopup.setShopId(AuthUserContext.get().getShopId());
        costPerPopup.setStatus(PopupStatusEnum.NOT_START.value());
        checkInfo(costPerPopup);
        costPerPopupService.insert(costPerPopup);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @Operation(summary = "更新弹窗广告", description = "更新弹窗广告")
    @PreAuthorize("@pms.hasPermission('shop:popupAd:update')")
    public ServerResponseEntity<Void> update(@Valid @RequestBody CostPerPopupDTO costPerPopupDTO) {
        CostPerPopup costPerPopup = BeanUtil.map(costPerPopupDTO, CostPerPopup.class);
        checkInfo(costPerPopup);
        costPerPopupService.update(costPerPopup);
        return ServerResponseEntity.success();
    }

    @DeleteMapping
    @Operation(summary = "删除弹窗广告", description = "根据弹窗广告id删除弹窗广告")
    @PreAuthorize("@pms.hasPermission('shop:popupAd:delete')")
    public ServerResponseEntity<Void> delete(@RequestParam Long popupId) {
        CostPerPopupVO perPopupVO = costPerPopupService.getByPopupId(popupId);
        if (Objects.isNull(perPopupVO)) {
            return ServerResponseEntity.success();
        }
        costPerPopupService.deleteById(popupId, perPopupVO.getShopId());
        return ServerResponseEntity.success();
    }

    @PutMapping("/invalid")
    @Operation(summary = "失效弹窗广告", description = "根据弹窗广告id失效弹窗广告")
    @PreAuthorize("@pms.hasPermission('shop:popupAd:invalid')")
    public ServerResponseEntity<Void> invalid(@RequestParam Long popupId) {
        CostPerPopupVO perPopupVO = costPerPopupService.getByPopupId(popupId);
        if (Objects.isNull(perPopupVO)) {
            return ServerResponseEntity.success();
        }
        costPerPopupService.invalid(popupId, perPopupVO.getShopId());
        return ServerResponseEntity.success();
    }

    private void checkInfo(CostPerPopup costPerPopup) {
        if (!PopupPageTypeEnum.isShop(costPerPopup.getPageType())) {
            // 触发页面类型错误
            throw new YamiShopBindException("yami.cost.per.popup.exception.pageTypeError");
        }
        if (!PopupUserTypeEnum.isShop(costPerPopup.getUserType())) {
            // 推送用户类型错误
            throw new YamiShopBindException("yami.cost.per.popup.exception.userTypeError");
        }
        if (Objects.equals(costPerPopup.getPushFrequency(), PushFrequencyEnum.CUSTOM.value())) {
            if (Objects.nonNull(costPerPopup.getDayFrequency()) && StrUtil.isNotBlank(costPerPopup.getWeekFrequency())) {
                // 推送天数/周不能同时存在
                throw new YamiShopBindException("yami.cost.per.popup.exception.dayWeekBothExist");
            }
            if (Objects.isNull(costPerPopup.getDayFrequency()) && StrUtil.isBlank(costPerPopup.getWeekFrequency())) {
                // 推送天数/周不能为空
                throw new YamiShopBindException("yami.cost.per.popup.exception.dayWeekNull");
            }
        }
        // 校验重复
        List<CostPerPopupVO> dbList = costPerPopupService.listByParam(costPerPopup);
        if (CollUtil.isEmpty(dbList)) {
            return;
        }
        boolean checkFrequency = Objects.equals(costPerPopup.getPushFrequency(), PushFrequencyEnum.CUSTOM.value()) && StrUtil.isNotBlank(costPerPopup.getWeekFrequency());
        if (!checkFrequency) {
            for (CostPerPopupVO costPerPopupVO : dbList) {
                checkLevel(costPerPopup, costPerPopupVO);
            }
            return;
        }
        if (checkFrequency) {
            // 只有按周推送需要再校验一下周数重复
            for (CostPerPopupVO costPerPopupVO : dbList) {
                if (!Objects.equals(costPerPopupVO.getPushFrequency(), PushFrequencyEnum.CUSTOM.value()) || StrUtil.isBlank(costPerPopupVO.getWeekFrequency())) {
                    checkLevel(costPerPopup, costPerPopupVO);
                } else {
                    // 按周推送的检查是否有重复周
                    List<String> duplicateList = costPerPopupService.getDuplicateList(costPerPopup, costPerPopupVO);
                    if (CollUtil.isNotEmpty(duplicateList)) {
                        checkLevel(costPerPopup, costPerPopupVO);
                    }
                }
            }
        }
    }

    private static void checkLevel(CostPerPopup costPerPopup, CostPerPopupVO costPerPopupVO) {
        if (!Objects.equals(costPerPopupVO.getUserType(), costPerPopup.getUserType())) {
            // 用户类型不同，但是页面相同，可能存在一定用户重复
            throw new YamiShopBindException("yami.cost.per.popup.exception.repeatPopup");
        }
        if (Objects.equals(costPerPopup.getUserType(), costPerPopupVO.getUserType()) && Objects.equals(costPerPopupVO.getUserType(), PopupUserTypeEnum.ALL_USER.value())) {
            // 已有重复弹窗广告
            throw new YamiShopBindException("yami.cost.per.popup.exception.repeatPopup");
        }
        List<Long> dbUserLevelIds = costPerPopupVO.getPopupRelateList().stream().map(PopupRelateVO::getUserLevelId).filter(userLevelId -> Objects.nonNull(userLevelId)).toList();
        if (CollUtil.isNotEmpty(dbUserLevelIds)) {
            List<Long> duplicateList = dbUserLevelIds.stream().filter(costPerPopup.getUserLevelIds()::contains).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(duplicateList)) {
                // 已有重复弹窗广告
                throw new YamiShopBindException("yami.cost.per.popup.exception.repeatPopup");
            }
        }
    }
}
