package com.yami.shop.bean.vo.search;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class EsProductSearchVO {


    /**
     * 暂时没用到，便于功能扩展
     */
    @Schema(description = "店铺信息")
    private ShopSearchVO shopInfo;

    @Schema(description = "门店信息")
    private StationSearchVO stationInfo;

    /**
     * 暂时没用到，便于功能扩展
     */
    @Schema(description = "品牌列表信息")
    private List<BrandSearchVO> brands;

    /**
     * 暂时没用到，便于功能扩展
     */
    @Schema(description = "分类列表信息")
    private List<CategorySearchVO> categories;

    @Schema(description = "spu列表信息")
    private List<ProductSearchVO> products;

}
