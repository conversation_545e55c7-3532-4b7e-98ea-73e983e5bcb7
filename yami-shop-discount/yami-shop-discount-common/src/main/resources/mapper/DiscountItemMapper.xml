<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.discount.common.dao.DiscountItemMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.discount.common.model.DiscountItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="discount_item_id" jdbcType="BIGINT" property="discountItemId" />
    <result column="discount_id" jdbcType="BIGINT" property="discountId" />
    <result column="need_amount" jdbcType="DECIMAL" property="needAmount" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
  </resultMap>

  <insert id="insertDiscountItems">
    insert into tz_discount_item (discount_id,need_amount,discount) values
    <foreach collection="discountItems" item="discountItem" separator=",">
    (#{discountItem.discountId},#{discountItem.needAmount},#{discountItem.discount})
    </foreach>
  </insert>

  <select id="listDiscountByProdIds" resultType="com.yami.shop.discount.common.model.DiscountProd">
    SELECT dp.`prod_id`,d.`discount_id` FROM tz_discount  d
    LEFT JOIN tz_discount_prod dp ON d.`discount_id` = dp.`discount_id`
    WHERE  d.`suitable_prod_type` = 1 AND dp.`prod_id` IN (
    <foreach collection="prodIds" separator="," item="prodId">
      #{prodId}
    </foreach>
    )
  </select>
</mapper>
