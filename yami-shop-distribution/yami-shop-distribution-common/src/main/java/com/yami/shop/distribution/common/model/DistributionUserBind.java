package com.yami.shop.distribution.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yami.shop.bean.vo.UserVO;
import com.yami.shop.distribution.common.vo.DistributionUserVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * <AUTHOR>
 */
@Data
@TableName( "tz_distribution_user_bind")
public class DistributionUserBind implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    @Schema(description = "用户关系表id" )
    private Long bindId;

    @Schema(description = "分销员id" )
    private Long distributionUserId;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "用户id" )
    private String userId;

    @Schema(description = "当前绑定关系（-1失效 0 预绑定 1生效）" )
    private Integer state;

    @Schema(description = "失效原因(0 超过有效期 1 管理员更改 3.暂时封禁 4.用户注销)" )
    private Integer invalidReason;

    @Schema(description = "变动时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "绑定时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bindTime;

    @Schema(description = "失效时间" )
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date invalidTime;

    @Schema(description = "关联用户" )
    @TableField(exist = false)
    private UserVO user;

    @Schema(description = "关联分销员" )
    @TableField(exist = false)
    private DistributionUserVO distributionUser;
}
