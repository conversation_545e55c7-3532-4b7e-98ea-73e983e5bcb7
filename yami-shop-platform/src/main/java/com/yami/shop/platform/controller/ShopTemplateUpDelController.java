package com.yami.shop.platform.controller;

import cn.hutool.core.util.BooleanUtil;
import com.yami.shop.bean.model.ShopTemplate;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.ShopTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Objects;


/**
 *
 *
 * <AUTHOR>
 * @date 2022-07-29 16:54:02
 */
@RestController
@RequestMapping("/platform/shopTemplate")
@Tag(name = "店铺模板-修改、删除、设为主页")
@RequiredArgsConstructor
public class ShopTemplateUpDelController {

    @Value("${yami.expose.operation.auth:}")
    private Boolean permission;

    private final ShopTemplateService shopTemplateService;

    @SysLog("修改店铺模板信息" )
    @PutMapping("/updatePC")
    @PreAuthorize("@pms.hasPermission('platform:shopTemplate:updatePC')")
    @Operation(summary = "PC端修改店铺模板信息" , description = "PC端修改店铺模板信息")
    public ServerResponseEntity<Boolean> updatePcById(@RequestBody @Valid ShopTemplate shopTemplate) {
        ShopTemplate shopTemplateDb = shopTemplateService.getById(shopTemplate.getTemplateId());
        if(BooleanUtil.isFalse(permission) || !Objects.equals(shopTemplateDb.getShopId() , Constant.PLATFORM_SHOP_ID)){
            // 没有权限进行操作
            throw new YamiShopBindException("yami.no.auth");
        }
        shopTemplate.setUpdateTime(new Date());
        return ServerResponseEntity.success(shopTemplateService.updateById(shopTemplate));
    }

    @SysLog("修改店铺模板信息" )
    @PutMapping("/updateMove")
    @PreAuthorize("@pms.hasPermission('platform:shopTemplate:updateMove')")
    @Operation(summary = "移动端修改店铺模板信息" , description = "移动端修改店铺模板信息")
    public ServerResponseEntity<Boolean> updateMoveById(@RequestBody @Valid ShopTemplate shopTemplate) {
        ShopTemplate shopTemplateDb = shopTemplateService.getById(shopTemplate.getTemplateId());
        if(BooleanUtil.isFalse(permission) || !Objects.equals(shopTemplateDb.getShopId() , Constant.PLATFORM_SHOP_ID)){
            // 没有权限进行操作
            throw new YamiShopBindException("yami.no.auth");
        }
        shopTemplate.setUpdateTime(new Date());
        return ServerResponseEntity.success(shopTemplateService.updateById(shopTemplate));
    }

    @SysLog("删除店铺模板信息" )
    @DeleteMapping("/deletePC/{templateId}")
    @PreAuthorize("@pms.hasPermission('platform:shopTemplate:deletePC')")
    @Operation(summary = "PC端通过id删除店铺模板信息" , description = "PC端通过id删除店铺模板信息")
    @Parameter(name = "templateId", description = "店铺模板id" )
    public ServerResponseEntity<Boolean> removePcById(@PathVariable Long templateId) {
        ShopTemplate shopTemplate = shopTemplateService.getById(templateId);
        if(BooleanUtil.isFalse(permission) || !Objects.equals(shopTemplate.getShopId() ,Constant.PLATFORM_SHOP_ID)){
            // 没有权限进行操作
            throw new YamiShopBindException("yami.no.auth");
        }
        return ServerResponseEntity.success(shopTemplateService.removeById(templateId));
    }

    @SysLog("删除店铺模板信息" )
    @DeleteMapping("/deleteMove/{templateId}")
    @PreAuthorize("@pms.hasPermission('platform:shopTemplate:deleteMove')")
    @Operation(summary = "移动端通过id删除店铺模板信息" , description = "移动端通过id删除店铺模板信息")
    @Parameter(name = "templateId", description = "店铺模板id" )
    public ServerResponseEntity<Boolean> removeMoveById(@PathVariable Long templateId) {
        ShopTemplate shopTemplate = shopTemplateService.getById(templateId);
        if(BooleanUtil.isFalse(permission) || !Objects.equals(shopTemplate.getShopId() ,Constant.PLATFORM_SHOP_ID)){
            // 没有权限进行操作
            throw new YamiShopBindException("yami.no.auth");
        }
        return ServerResponseEntity.success(shopTemplateService.removeById(templateId));
    }
}
