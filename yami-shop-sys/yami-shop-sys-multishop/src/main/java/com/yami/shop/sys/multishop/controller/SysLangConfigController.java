package com.yami.shop.sys.multishop.controller;


import com.yami.shop.common.bean.LangConfig;
import com.yami.shop.common.bean.LangItemConfig;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.manager.impl.LangManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 系统配置信息
 * <AUTHOR>
 */
@Tag(name = "系统语言配置信息")
@RestController
@RequestMapping("/sys/lang")
@AllArgsConstructor
public class SysLangConfigController {
    private final LangManager langManager;


    @GetMapping
    @Operation(summary = "语言配置信息", description = "语言配置信息")
    public ServerResponseEntity<LangConfig> getLangConfig(){
        LangConfig langConfig = langManager.getLangConfig();
        List<LangItemConfig> langItemConfigList = langConfig.getLangItemList().stream().filter(langItemConfig -> Objects.equals(langItemConfig.getLang(), langConfig.getLang())).collect(Collectors.toList());
        langConfig.getLangItemList().removeAll(langItemConfigList);
        langItemConfigList.addAll(langConfig.getLangItemList());
        langConfig.setLangItemList(langItemConfigList);
        return ServerResponseEntity.success(langConfig);
    }
}
