package com.yami.shop.common.allinpay.constant;

/**
 * <AUTHOR>
 */

public enum CardType {

    /**
     * 借记卡
     */
    DEBIT(1, "借记卡"),

    /**
     * 信用卡
     */
    CREDIT(2, "信用卡");

    private final Integer code;

    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByCode(Integer code) {
        if (DEBIT.getCode().equals(code)) {
            return DEBIT.getDesc();
        } else if (CREDIT.getCode().equals(code)) {
            return CREDIT.getDesc();
        }
        return "";
    }

    CardType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
