package com.yami.shop.discount.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("tz_discount_prod")
public class DiscountProd implements Serializable {
    private static final long serialVersionUID = -5694537556987462303L;
    /**
     * 满减 商品 联合id
     */
    @TableId
    private Long discountProdId;

    /**
     * 满减id
     */

    private Long discountId;

    /**
     * 商品id
     */

    private Long prodId;

    /**
     * 商品主图
     */
    @TableField(exist = false)
    private String pic;

    /**
     * 商品图片列表
     */
    @TableField(exist = false)
    private String imgs;

    /**
     * 商品名称
     */
    @TableField(exist = false)
    private String prodName;

    /**
     * 商品状态
     */
    @TableField(exist = false)
    private Integer prodStatus;
}
