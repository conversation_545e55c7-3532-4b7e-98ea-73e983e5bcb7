package com.yami.shop.distribution.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yami.shop.bean.vo.SysUserVO;
import com.yami.shop.distribution.common.vo.DistributionUserVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 分销员钱包流水
 *
 * <AUTHOR>
 * @date 2019-04-29 16:39:13
 */
@Data
@TableName("tz_distribution_user_wallet_bill")
@EqualsAndHashCode
public class DistributionUserWalletBill implements Serializable{
    private static final long serialVersionUID = 1L;

    public DistributionUserWalletBill(){

    }


    /**
     *
     * @param distributionUserWallet 变更后的钱包对象
     * @param remarks
     */
    public DistributionUserWalletBill(DistributionUserWallet distributionUserWallet,
                                      String remarks,
                                      String remarksEn,
                                      Double unsettledAmount,
                                      Double settledAmount,
                                      Double invalidAmount,
                                      Double addupAmount,
                                      Integer type) {
        this.remarks = remarks;
        this.remarksEn = remarksEn;
        this.unsettledAmount = unsettledAmount;
        this.settledAmount = settledAmount;
        this.invalidAmount = invalidAmount;
        this.addupAmount = addupAmount;
        this.type = type;
        this.createTime = new Date();
        this.walletId = distributionUserWallet.getWalletId();
        this.unsettledAmountAfter = distributionUserWallet.getUnsettledAmount();
        this.settledAmountAfter = distributionUserWallet.getSettledAmount();
        this.invalidAmountAfter = distributionUserWallet.getInvalidAmount();
        this.addupAmountAfter = distributionUserWallet.getAddupAmount();
    }

    @TableId
    @Schema(description = "钱包流水记录id" )
    private Long id;

    @Schema(description = "钱包id" )
    private Long walletId;

    @Schema(description = "待结算金额变更数额" )
    private Double unsettledAmount;

    @Schema(description = "可提现金额变更数额" )
    private Double settledAmount;

    @Schema(description = "失效金额变更数额" )
    private Double invalidAmount;

    @Schema(description = "积累收益变更数额" )
    private Double addupAmount;

    @Schema(description = "创建时间" )
    private Date createTime;

    @Schema(description = "备注" )
    private String remarks;

    @Schema(description = "英文备注" )
    private String remarksEn;

    @Schema(description = "变更后待结算金额" )
    private Double unsettledAmountAfter;

    @Schema(description = "变更后可提现金额" )
    private Double settledAmountAfter;

    @Schema(description = "变更后失效金额" )
    private Double invalidAmountAfter;

    @Schema(description = "变更后积累收益" )
    private Double addupAmountAfter;

    @Schema(description = "类型(0 系统修改 1人工修改)" )
    private Integer type;

    @Schema(description = "操作人id(空为系统)" )
    private Long modifier;

    @Schema(description = "关联分销员" )
    @TableField(exist=false)
    private DistributionUserVO distributionUser;

    @Schema(description = "关联操作人" )
    @TableField(exist=false)
    private SysUserVO sysUser;
}
