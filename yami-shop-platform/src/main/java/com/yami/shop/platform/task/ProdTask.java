package com.yami.shop.platform.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yami.shop.bean.app.vo.ProductVO;
import com.yami.shop.bean.bo.ProductBO;
import com.yami.shop.bean.enums.EsOperationType;
import com.yami.shop.bean.enums.ProdMoldEnum;
import com.yami.shop.bean.enums.ProdStatusEnums;
import com.yami.shop.bean.enums.ProdType;
import com.yami.shop.bean.event.EsProductUpdateEvent;
import com.yami.shop.bean.model.Product;
import com.yami.shop.bean.model.Sku;
import com.yami.shop.bean.model.Station;
import com.yami.shop.bean.model.StationProd;
import com.yami.shop.bean.param.EsProductParam;
import com.yami.shop.bean.vo.SkuStockVO;
import com.yami.shop.bean.vo.search.ProductSearchVO;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.constants.ProductCacheNames;
import com.yami.shop.common.enums.StatusEnum;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.properties.EsProperties;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.common.util.Json;
import com.yami.shop.common.util.RedisUtil;
import com.yami.shop.manager.impl.StockManager;
import com.yami.shop.search.common.service.EsProductService;
import com.yami.shop.search.common.service.SearchProductService;
import com.yami.shop.search.common.util.EsSearchUtil;
import com.yami.shop.service.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.common.geo.GeoPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ProdTask {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final ProductService productService;
    private final ApplicationContext applicationContext;
    private final SearchProductService searchProductService;
    private final SkuStockService skuStockService;
    private final WarehouseService warehouseService;
    private final StockBillLogService stockBillLogService;
    private final PurchaseOrderService purchaseOrderService;
    private final SkuService skuService;
    private final StockManager stockManager;
    private final StationService stationService;
    private final EsProperties esProperties;
    private final StationProdService stationProdService;
    private final EsProductService esProductService;

    private final VoucherItemService voucherItemService;


    @XxlJob("recoveryPreSaleProd")
    public void recoveryPreSaleProd(){
        logger.info("过了预售时间的商品，恢复成普通商品状态。。。");
        // 获取30分钟之前未支付的订单
        List<Product> products = productService.recoveryPreSaleProd();
        if(CollectionUtils.isEmpty(products)){
            return;
        }
        for (Product product : products) {
            //清除缓存
            productService.removeProdCacheByProdId(product.getProdId());
        }
    }

    @XxlJob("offlineExpireVirtualProd")
    public void offlineExpireVirtualProd(){
        logger.info("过了核销时间的虚拟商品，进行下架操作。");
        // 获取30分钟之前未支付的订单
        List<Product> products = productService.handleExpireVirtualProd();
        if(CollectionUtils.isEmpty(products)){
            return;
        }
        List<Long> ids = new ArrayList<>();
        for (Product product : products) {
            //清除缓存
            productService.removeProdCacheByProdId(product.getProdId());
            ids.add(product.getProdId());
        }
        applicationContext.publishEvent(new EsProductUpdateEvent(null, ids, EsOperationType.UPDATE_BATCH));
    }

    /**
     * 校验商品数量是否完整
     * 商品数据是否为最新的：根据商品更新时间判断
     */
    @XxlJob("verifyProdStockAndSold")
    public void verifySpuStockAndSold(){
        // 从redis中获取上次执行的时间，如果没有则默认为统计所有数据
        Date data = RedisUtil.get(ProductCacheNames.SKU_CHANGE_STATISTICS_TIME);
        // 当前时间
        Date currentTime = new Date();
        // 查询出入库记录，返回发生过库存变化的商品id
        if (Objects.nonNull(data)) {
            data = DateUtil.offsetSecond(data, -10);
        }
        List<Long> stockProdIds = stockBillLogService.listSpuIdOfStockChange(data, currentTime);
        // 采购订单记录
        List<Long> purchaseProdIds = purchaseOrderService.listProdIdOfStockChange(data, currentTime);
        if (CollUtil.isEmpty(stockProdIds) && CollUtil.isEmpty(purchaseProdIds)) {
            return;
        }
        Set<Long> prodIdsSet = new HashSet<>(Constant.INITIAL_CAPACITY);
        prodIdsSet.addAll(stockProdIds);
        prodIdsSet.addAll(purchaseProdIds);
        List<Product> productDBList = productService.getProductListBySpuIds(prodIdsSet);

        // 通过商品id查询商品下的skuId列表
        List<Sku> skuList = skuService.listSkuAndLangByProdIds(new ArrayList<>(prodIdsSet));
        List<Long> skuIds = skuList.stream().map(Sku::getSkuId).collect(Collectors.toList());
        Map<Long, List<Sku>> prodMap = skuList.stream().collect(Collectors.groupingBy(Sku::getProdId));
        // 获取sku总库存
        Map<Long, SkuStockVO> skuStockMap = stockManager.mapSkuTotalStock(skuIds);
        List<Product> productList = new ArrayList<>(prodIdsSet.size());
        // 根据spuId获取商品sku的总库存和总销量
        for (Product productDb : productDBList) {
            List<Sku> skuVOList = prodMap.get(productDb.getProdId());
            if (CollUtil.isEmpty(skuVOList)) {
                continue;
            }
            Product product = new Product();
            int saleNum = 0;
            int stock = 0;
            for (Sku sku : skuVOList) {
                if (!skuStockMap.containsKey(sku.getSkuId())) {
                    continue;
                }
                SkuStockVO skuStockVO = skuStockMap.get(sku.getSkuId());
                // 统计es商品的总销量和总库存
                saleNum += skuStockVO.getSale();
                stock += skuStockVO.getStock();
                // 用于库存预警
                sku.setStatus(skuStockVO.getStock());
                if (sku.getStockWarning() >= skuStockVO.getStock() && Objects.equals(sku.getStockWarning(), Constant.ZERO)) {
                    sku.setStockWarning(1);
                }
            }
            product.setProdId(productDb.getProdId());
            product.setSoldNum(saleNum);
            product.setTotalStocks(stock);
            productList.add(product);
        }
        // 更新es中的商品库存、销量数据
        if (CollUtil.isNotEmpty(productList)) {
            searchProductService.batchUpdateStockAndSaleNum(productList);
        }
        if(CollectionUtil.isNotEmpty(skuList)) {
            // 查询sku预警数量，并更新sku预警信息
            skuService.batchUpdateSkuWarning(skuList);
        }
        // 更新redis中的最后一次执行时间
        RedisUtil.set(ProductCacheNames.SKU_CHANGE_STATISTICS_TIME, currentTime, Constant.DAY_SECOND);
    }


    @XxlJob("restoreStock")
    public void restoreStock() {
        try {
            // ===================== 恢复商品库存 =====================
            productService.intiStockPointSKu();
            skuStockService.restoreStock();
        } catch (Exception e) {
            logger.error("恢复库存：", e);
            throw new YamiShopBindException(e.getMessage());
        }
    }


    @XxlJob("intiStock")
    public void intiStock() {
        try {
            // ===================== 初始化商品库存 =====================
            warehouseService.intiWarehouse();
            productService.intiStockPointSKu();
            String initStockStr = XxlJobHelper.getJobParam();
            Integer initStock = StrUtil.isNotBlank(initStockStr) ? Integer.parseInt(initStockStr) : 0;
            skuStockService.initStock(initStock);
            // ===================== 初始化商品库存记录 =====================
        } catch (Exception e) {
            throw new YamiShopBindException(e.getMessage());
        }
    }

    /**
     * 初始化库存预警状态，只执行一次即可
     */
    @XxlJob("intiStockWrningStatus")
    public void intiStockWrningStatus() {
        try {
            // ===================== 初始化商品库存预警状态 =====================
            List<Product> products = productService.list(new LambdaQueryWrapper<>(Product.class).ne(Product::getStatus, ProdStatusEnums.DELETE)
                    .ne(Product::getProdType, ProdType.PROD_TYPE_SCORE.value())
                    .ne(Product::getMold, ProdMoldEnum.COMBO.value()));
            List<Long> prodIds = products.stream().map(Product::getProdId).collect(Collectors.toList());
            List<Sku> skuList = skuService.listSkuAndLangByProdIds(prodIds);
            skuService.batchUpdateSkuWarning(skuList);
        } catch (Exception e) {
            throw new YamiShopBindException(e.getMessage());
        }
    }

    /**
     * 将店铺支持自提或者同城配送的商品。保存一份门店关联商品数据到es和mysql
     * 执行一次即可
     */
    @XxlJob("synchStationSpuSaveEs")
    public void synchStationSpuSaveEs() {
        logger.info("将店铺支持自提或者同城配送的商品。保存一份门店关联商品数据到es和mysql");
        List<ProductVO> prodList = productService.getPickAndSameCityProdList(null);
        List<Long> prodIdList = prodList.stream().map(ProductVO::getProdId).toList();
        List<ProductBO> productList = esProductService.loadStationProduct(prodIdList);
        Map<Long, List<ProductBO>> prodByShopIdMap = productList.stream().collect(Collectors.groupingBy(ProductBO::getShopId));
        List<Station> stationList = stationService.list(new LambdaQueryWrapper<Station>().eq(Station::getStatus, StatusEnum.ENABLE.value()));
        Map<Long, List<Station>> stationByShopIdMap = stationList.stream().collect(Collectors.groupingBy(Station::getShopId));
        Map<String, ProductBO> saveProductMap = new HashMap<>(Constant.INITIAL_CAPACITY);
        List<StationProd> stationProdList = new ArrayList<>(Constant.INITIAL_CAPACITY);
        for (Long shopId : prodByShopIdMap.keySet()) {
            List<ProductBO> productBOList= prodByShopIdMap.get(shopId);
            List<Station> stations = stationByShopIdMap.get(shopId);
            if (CollUtil.isEmpty(stations) || CollUtil.isEmpty(productBOList)) {
                continue;
            }
            for (Station station : stations) {
                for (ProductBO productBO : productBOList) {
                    ProductBO saveProd = BeanUtil.map(productBO, ProductBO.class);
                    Product.DeliveryModeVO deliveryModeVO = Json.parseObject(productBO.getDeliveryMode(), Product.DeliveryModeVO.class);
                    saveProd.setStationId(station.getStationId());
                    saveProd.setStationLocation(new GeoPoint(station.getLat(), station.getLng()));
                    saveProd.setStationSaleNum(0);
                    saveProd.setHasUserPickUp(deliveryModeVO.getHasUserPickUp());
                    saveProd.setHasCityDelivery(deliveryModeVO.getHasCityDelivery());
                    saveProd.setNearby(true);
                    saveProd.setAppDisplay(true);
                    saveProductMap.put(productBO.getProdId() + Constant.COLON + station.getStationId(), saveProd);
                    StationProd stationProd = new StationProd();
                    stationProd.setStationId(station.getStationId());
                    stationProd.setProdId(productBO.getProdId());
                    stationProd.setLng(station.getLng());
                    stationProd.setLat(station.getLat());
                    stationProd.setStatus(station.getStatus());
                    stationProdList.add(stationProd);
                }
            }
        }

        if (esProperties.getEnable()) {
            EsSearchUtil.saveBatchStationProduct(saveProductMap);
        }
        stationProdService.saveBatch(stationProdList);

    }

    /**
     * 过期卡券商品，减少库存
     */
    @XxlJob("overdueVoucher")
    public void overdueVoucher(){
        logger.info("失效到期的卡券商品，并减少对应商品库存");
        voucherItemService.overdueVoucher();
    }

}
