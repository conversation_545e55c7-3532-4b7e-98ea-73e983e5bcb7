package com.yami.shop.platform.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.enums.RemindType;
import com.yami.shop.bean.enums.SendType;
import com.yami.shop.bean.model.NotifyTemplate;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.NotifyTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 *
 *
 * <AUTHOR>
 * @date 2020-07-01 16:13:08
 */
@RestController
@AllArgsConstructor
@RequestMapping("/platform/notifyTemplate")
@Tag(name = "消息模板")
public class NotifyTemplateController {

    private final NotifyTemplateService notifyTemplateService;

    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('platform:notifyTemplate:page')" )
    @Operation(summary = "分页查询" , description = "分页查询")
    public ServerResponseEntity<IPage<NotifyTemplate>> getNotifyTemplatePage(PageParam<NotifyTemplate> page, NotifyTemplate notifyTemplate) {

        IPage<NotifyTemplate> templatePage = notifyTemplateService.page(page, new LambdaQueryWrapper<NotifyTemplate>()
                .eq(Objects.nonNull(notifyTemplate.getSendType()),NotifyTemplate::getSendType,notifyTemplate.getSendType())
                .eq(Objects.nonNull(notifyTemplate.getMsgType()), NotifyTemplate::getMsgType, notifyTemplate.getMsgType())
                .ne(NotifyTemplate::getSendType, SendType.CUSTOMIZE.getValue())
                .orderByDesc(NotifyTemplate::getStatus).orderByDesc(NotifyTemplate::getCreateTime));
        if(CollectionUtils.isEmpty(templatePage.getRecords())){
            return ServerResponseEntity.success();
        }
        for (NotifyTemplate  template: templatePage.getRecords()) {
            List<Integer> templateList = getTemplateList(template.getTemplateTypes());
            for (Integer type : templateList) {
                template.setSms(Objects.equals(type, RemindType.SMS.value()));
                template.setSub(Objects.equals(type, RemindType.MP.value()));
                template.setApp(Objects.equals(type, RemindType.MINI.value()));
            }
            template.setTemplateTypeList(templateList);
        }
        return ServerResponseEntity.success(templatePage);
    }

    private List<Integer> getTemplateList(String templateTypes) {
        String[] templateTypeList = templateTypes.split(StrUtil.COMMA);
        List<Integer> templates = new ArrayList<>();
        for (String templateStr : templateTypeList) {
            if (StrUtil.isBlank(templateStr)) {
                continue;
            }
            templates.add(Integer.valueOf(templateStr));
        }
        return templates;
    }

    @GetMapping("/info/{templateId}" )
    @Operation(summary = "查询模板信息" , description = "查询模板信息")
    @Parameter(name = "templateId", description = "模板id" )
    @PreAuthorize("@pms.hasPermission('platform:notifyTemplate:info')" )
    public ServerResponseEntity<NotifyTemplate> getById(@PathVariable("templateId") Long templateId) {
        NotifyTemplate template = notifyTemplateService.getById(templateId);
        template.setTemplateTypeList(getTemplateList(template.getTemplateTypes()));
        return ServerResponseEntity.success(template);
    }
    @PutMapping
    @PreAuthorize("@pms.hasPermission('platform:notifyTemplate:update')" )
    @Operation(summary = "修改模板" , description = "修改模板")
    public ServerResponseEntity<Boolean> updateById(@RequestBody @Valid NotifyTemplate notifyTemplate) {
        List<NotifyTemplate> notifyTemplates = notifyTemplateService.list(new LambdaQueryWrapper<NotifyTemplate>()
                .eq(NotifyTemplate::getSendType, notifyTemplate.getSendType()).ne(NotifyTemplate::getTemplateId,notifyTemplate.getTemplateId()));
        if(CollectionUtils.isNotEmpty(notifyTemplates)){
            // 已经存在当前消息类型的短信，请去进行修改操作
            throw new YamiShopBindException("yami.select.notify.type.check");
        }
        if (CollUtil.isNotEmpty(notifyTemplate.getTemplateTypeList())) {
            notifyTemplate.setTemplateTypes(arrayChangeList(notifyTemplate.getTemplateTypeList()));
        }
        notifyTemplate.setMsgType(null);
        notifyTemplate.setSendType(null);
        return ServerResponseEntity.success(notifyTemplateService.updateById(notifyTemplate));
    }

    @DeleteMapping("/{templateId}" )
    @PreAuthorize("@pms.hasPermission('platform:notifyTemplate:updateSts')" )
    @Operation(summary = "状态变更" , description = "状态变更")
    @Parameter(name = "templateId", description = "模板id" )
    public ServerResponseEntity<Boolean> removeById(@PathVariable Long templateId) {
        NotifyTemplate template = notifyTemplateService.getById(templateId);
        template.setStatus(template.getStatus() == 1? 0:1);
        return ServerResponseEntity.success(notifyTemplateService.updateById(template));
    }

    @GetMapping("/getNoMatchSendTypes" )
    @Operation(summary = "获取没有匹配的公众号消息模板列表" , description = "获取没有匹配的公众号消息模板列表")
    public ServerResponseEntity<List<Long>> getNoMatchSendTypes() {
        return ServerResponseEntity.success(Constant.NO_MATCH_SEND_TYPES);
    }

    private String getRepeatValue(List<Integer> templateTypeList,List<Integer> shopTemplateTypeList){
        ArrayList<Integer> repeatValue = new ArrayList<>();
        for (Integer integer : templateTypeList) {
            if (shopTemplateTypeList.contains(integer)){
                repeatValue.add(integer);
            }
        }
        String s = repeatValue.toString().replace(" ","");
        return s.substring(1,s.length()-1);
    }

    private String arrayChangeList(List<Integer> templateTypeList) {
        StringBuilder templateTypes = new StringBuilder();
        for (Integer templateType : templateTypeList) {
            templateTypes.append(templateType);
            templateTypes.append(StrUtil.COMMA);
        }
        templateTypes.deleteCharAt(templateTypes.length() - 1);
        return templateTypes.toString();
    }

}
