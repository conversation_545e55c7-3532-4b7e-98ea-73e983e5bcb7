package com.yami.shop.bean.app.param;

import com.yami.shop.bean.model.OrderInvoice;
import com.yami.shop.bean.vo.VirtualRemarkVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OrderShopParam {

    /** 店铺ID **/
    @Schema(description = "店铺id" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Long shopId;

    /**
     * 订单备注信息
     */
    @Schema(description = "订单备注信息" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String remarks;

    @Schema(description = "每次订单提交时的uuid" )
    private String uuid;

    @Schema(description = "订单发票信息" )
    private OrderInvoice orderInvoice;

    @Schema(description = "虚拟商品留言备注" )
    private List<VirtualRemarkVO> virtualRemarkList;

    @Schema(description = "用户操作参数")
    private OrderFlowLogParam orderFlowLogParam;
}
