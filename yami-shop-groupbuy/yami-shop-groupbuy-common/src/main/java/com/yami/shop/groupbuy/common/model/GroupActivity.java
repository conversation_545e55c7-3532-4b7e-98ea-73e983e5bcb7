package com.yami.shop.groupbuy.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupSkuDto;
import com.yami.shop.groupbuy.common.utils.GroupActivityUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
@Data
@TableName("tz_group_activity")
@Schema(description = "拼团活动表")
public class GroupActivity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    @Schema(description = "拼团活动id" )
    private Long groupActivityId;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "活动名称" )
    private String activityName;

    @Schema(description = "商品id" )
    private Long prodId;

    @Schema(description = "拼团状态(1:启用、2:未启用、0:已失效、-1:删除、3:违规下架、4:平台审核 5:已结束)" )
    private Integer status;

    @Schema(description = "活动开始时间" )
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "活动结束时间" )
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Schema(description = "成团有效期时间" )
    private Integer groupValidTime;

    @Schema(description = "成团人数" )
    private Integer groupNumber;

    @Schema(description = "商品是否限购" )
    private Integer hasMaxNum;

    @Schema(description = "限购数量" )
    private Integer maxNum;

    @Schema(description = "是否模拟成团" )
    private Integer hasRobot;

    @Schema(description = "活动是否预热" )
    private Integer isPreheat;

    @Schema(description = "是否开启凑团模式" )
    private Long hasGroupTip;

    @Schema(description = "拼团价格" )
    private Double price;

    @Schema(description = "创建时间" )
    private Date createTime;

    @Schema(description = "更新时间" )
    private Date updateTime;

    @TableField(exist = false)
    @Schema(description = "活动状态（根据活动时间生成）" )
    private Integer activityStatus;

    @TableField(exist = false)
    @Schema(description = "商品图片" )
    private String prodPic;

    @TableField(exist = false)
    @Schema(description = "商品名称" )
    private String prodName;

    @TableField(exist = false)
    @Schema(description = "商品名称" )
    private List<ApiGroupSkuDto> groupSkuList;

    @Schema(description = "获取活动状态（活动状态：1:未开始、2:进行中、3:已结束、4:已失效、5:违规下架、6:待审核）" )
    public Integer getActivityStatus(){
        return GroupActivityUtil.getActivityStatus(startTime, endTime, status);
    }
}
