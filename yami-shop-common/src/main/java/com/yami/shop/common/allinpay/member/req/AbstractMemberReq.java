package com.yami.shop.common.allinpay.member.req;


import com.yami.shop.common.allinpay.AbstractReq;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public abstract class AbstractMemberReq extends AbstractReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String SERVICE_NAME = "memberService";

    @Override
    public String getService() {
        return SERVICE_NAME;
    }
}
