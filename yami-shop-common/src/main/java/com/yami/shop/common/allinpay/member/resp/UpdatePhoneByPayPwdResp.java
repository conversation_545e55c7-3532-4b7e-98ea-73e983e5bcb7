package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;
/**
 * <AUTHOR>
 * @date 2023-03-23
 */
public class UpdatePhoneByPayPwdResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 商户userId
     */
    private String bizUserId;

    /**
     * 状态(OK:成功, 失败:error)
     */
    private String status;

    /**
     * 新手机号
     */
    private String newPhone;

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getNewPhone() {
        return newPhone;
    }

    public void setNewPhone(String newPhone) {
        this.newPhone = newPhone;
    }

    @Override
    public String toString() {
        return "UpdatePhoneByPayPwdResp{" +
                "bizUserId='" + bizUserId + '\'' +
                ", status='" + status + '\'' +
                ", newPhone='" + newPhone + '\'' +
                '}';
    }
}
