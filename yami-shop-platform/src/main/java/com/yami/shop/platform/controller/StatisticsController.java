package com.yami.shop.platform.controller;

import com.yami.shop.bean.vo.statistics.HotStatisticsVO;
import com.yami.shop.bean.vo.statistics.PlatformStatisticsVO;
import com.yami.shop.bean.vo.statistics.TrendStatisticsVO;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.StatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/5/16 15:34
 */
@RestController
@RequestMapping("/platform/statistics")
@AllArgsConstructor
@Tag(name = "平台统计信息")
public class StatisticsController {

    private final StatisticsService statisticsService;

    @GetMapping("/getPlatformStatistics")
    @Operation(summary = "获取主页基本信息、今日待办")
    public ServerResponseEntity<PlatformStatisticsVO> getPlatformStatistics() {
        return ServerResponseEntity.success(statisticsService.getPlatformStatistics());
    }

    @GetMapping("/platformRealTimeOverview")
    @Operation(summary = "获取主页实时概括")
    @Parameter(name = "startTime", description = "开始时间" )
    public ServerResponseEntity<PlatformStatisticsVO> platformRealTimeOverview(@DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam("startTime") Date startTime) {
        return ServerResponseEntity.success(statisticsService.platformRealTimeOverview(startTime));
    }

    @GetMapping("/prod")
    @Operation(summary = "获取某段时间内的商品销量排行")
    public ServerResponseEntity<List<HotStatisticsVO>> getHotProds(@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam("startTime") Date startTime,
                                                             @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam("endTime") Date endTime) {
        return ServerResponseEntity.success(statisticsService.loadHotProdByDate(startTime, endTime));
    }


    @GetMapping("/shop")
    @Operation(summary = "获取某段时间内的店铺销量排行")
    public ServerResponseEntity<List<HotStatisticsVO>> getHotShops(@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam("startTime") Date startTime,
                                                             @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") @RequestParam("endTime") Date endTime) {
        return ServerResponseEntity.success(statisticsService.loadHotShopByDate(startTime, endTime));
    }

    @GetMapping("/trendData")
    @Operation(summary = "获取某段时间内的交易数据")
    public ServerResponseEntity<List<TrendStatisticsVO>> getTrendData(@DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam("startTime") Date startTime,
                                                                @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam("endTime") Date endTime) {
        return ServerResponseEntity.success(statisticsService.loadDataTrend(startTime, endTime));
    }


}
