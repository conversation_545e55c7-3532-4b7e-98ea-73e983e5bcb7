package com.yami.shop.distribution.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.distribution.common.model.DistributionUserBan;
import com.yami.shop.distribution.common.param.DistributionParam;

/**
 *
 * <AUTHOR> on 2019/04/01.
 */
public interface DistributionUserBanService extends IService<DistributionUserBan> {
    /**
     * 分销员封禁列表
     * @param page 分页参数
     * @param shopId 店铺id
     * @param distributionParam 分销查询参数
     * @param distributionUserBan 查询参数
     * @return 封禁列表
     */
    IPage<DistributionUserBan> distributionUserBanPage(Page page, Long shopId, DistributionParam distributionParam, DistributionUserBan distributionUserBan);


}
