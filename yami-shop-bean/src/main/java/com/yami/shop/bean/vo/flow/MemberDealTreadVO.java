package com.yami.shop.bean.vo.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
@Data
public class MemberDealTreadVO {

    private Long currentDay;

    @Schema(description = "新成交会员数" )
    private Integer newPayMemberNum;
    @Schema(description = "老成交会员数" )
    private Integer oldPayMemberNum;

    @Schema(description = "新支付订单数" )
    private Integer newPayOrderNum;
    @Schema(description = "老支付订单数" )
    private Integer oldPayOrderNum;

    @Schema(description = "新客单价" )
    private Double newPricePerMember;
    @Schema(description = "老客单价" )
    private Double oldPricePerMember;

    @Schema(description = "新支付金额" )
    private Double newPayAmount;
    @Schema(description = "老支付金额" )
    private Double oldPayAmount;
}
