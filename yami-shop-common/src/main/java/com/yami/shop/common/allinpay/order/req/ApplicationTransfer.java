package com.yami.shop.common.allinpay.order.req;

/**
 * 平台转账
 * 该接口一般用于平台向用户发红包、各类代金券、体验金、购物返利等营销活动。
 * 注：
 * 1. 目前只支持从平台保证金账户、营销专用账户和自定义 A 帐户转账到用户托管账户余额。
 * 2. 源帐户集：平台保证金账户、营销专用账户和自定义 A 帐户所属的账户集。
 * 3. 目标账户集：云账户分配给业务端的托管专用账户集。
 * 4. 平台转账无需支付确认。
 *
 * <AUTHOR>
 */

public class ApplicationTransfer extends AbstractOrderReq {
    /**
     * 商户系统转账编号商户系统唯一
     */
    private String bizTransferNo;
    /**
     * 源账户集编号
     */
    private String sourceAccountSetNo;
    /**
     * 云账户编号
     */
    private String targetBizUserId;
    /**
     * 标账户集编号
     */
    private String targetAccountSetNo;
    /**
     * 本次退款总金额
     */
    private Long amount;
    /**
     * 商户批次号
     */
    private String remark;

    private static final String METHOD_NAME = "applicationTransfer";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public ApplicationTransfer() {
    }

    public ApplicationTransfer(String bizTransferNo, String sourceAccountSetNo,
                               String targetBizUserId, String targetAccountSetNo, Long amount,
                               String remark) {
        this.bizTransferNo = bizTransferNo;
        this.sourceAccountSetNo = sourceAccountSetNo;
        this.targetBizUserId = targetBizUserId;
        this.targetAccountSetNo = targetAccountSetNo;
        this.amount = amount;
        this.remark = remark;
    }

    public ApplicationTransfer(String bizTransferNo, String sourceAccountSetNo,
                               String targetBizUserId, String targetAccountSetNo, Long amount) {
        this.bizTransferNo = bizTransferNo;
        this.sourceAccountSetNo = sourceAccountSetNo;
        this.targetBizUserId = targetBizUserId;
        this.targetAccountSetNo = targetAccountSetNo;
        this.amount = amount;
    }

    public String getBizTransferNo() {
        return bizTransferNo;
    }

    public void setBizTransferNo(String bizTransferNo) {
        this.bizTransferNo = bizTransferNo;
    }

    public String getSourceAccountSetNo() {
        return sourceAccountSetNo;
    }

    public void setSourceAccountSetNo(String sourceAccountSetNo) {
        this.sourceAccountSetNo = sourceAccountSetNo;
    }

    public String getTargetBizUserId() {
        return targetBizUserId;
    }

    public void setTargetBizUserId(String targetBizUserId) {
        this.targetBizUserId = targetBizUserId;
    }

    public String getTargetAccountSetNo() {
        return targetAccountSetNo;
    }

    public void setTargetAccountSetNo(String targetAccountSetNo) {
        this.targetAccountSetNo = targetAccountSetNo;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }


}
