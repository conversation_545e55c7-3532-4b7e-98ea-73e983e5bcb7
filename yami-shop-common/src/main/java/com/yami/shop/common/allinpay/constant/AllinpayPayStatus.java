package com.yami.shop.common.allinpay.constant;

/**
 * 通联支付返回状态
 * 1、成功：success
 * 2、进行中：pending
 * 3、失败：fail
 * 4.未支付：unpay
 *
 * <AUTHOR>
 */
public enum AllinpayPayStatus {

    /**
     * 成功
     */
    SUCCESS("success"),
    /**
     * 进行中
     */
    PENDING("pending"),
    /**
     * 失败
     */
    FAIL("fail"),
    /**
     * 未支付
     */
    UNPAY("unpay"),

    /**
     * 提现和充值回调的成功
     */
    OK("OK"),

    /**
     * 提现回调的失败
     */
    ERROR("error");

    private final String value;

    public String value() {
        return value;
    }

    AllinpayPayStatus(String value) {
        this.value = value;
    }
}
