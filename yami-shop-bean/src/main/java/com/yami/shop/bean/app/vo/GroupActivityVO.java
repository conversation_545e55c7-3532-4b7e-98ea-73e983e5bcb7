package com.yami.shop.bean.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/30 9:59
 */
@Data
public class GroupActivityVO {

    @Schema(description = "拼团活动id" )
    private Long groupActivityId;

    @Schema(description = "商品id" )
    private Long prodId;

    @Schema(description = "拼团状态(1:启用、2:未启用、0:已失效、-1:删除、3:违规下架、4:平台审核 5:已结束)" )
    private Integer status;

    @Schema(description = "活动开始时间" )
    private Date startTime;

    @Schema(description = "活动结束时间" )
    private Date endTime;

    @Schema(description = "服务器当前时间" )
    private Date nowTime;

    @Schema(description = "成团人数" )
    private Integer groupNumber;

    @Schema(description = "成功成团数")
    private Integer successNum;

    @Schema(description = "商品是否限购（1:限购、0:不限购）" )
    private Integer hasMaxNum;

    @Schema(description = "限购数量" )
    private Integer maxNum;

    @Schema(description = "已购买数量" )
    private Integer prodCount;

    @Schema(description = "当前用户参团的团队ID（null表示还没有参加该活动）" )
    private Long joinGroupTeamId;

    @Schema(description = "成团有效时间" )
    private Integer groupValidTime;

    @Schema(description = "开启模拟成团后，拼团有效期内人数未满的团，系统将会模拟“匿名买家”凑满人数，使该团成团。 你只需要对已付款参团的真实买家发货。建议合理开启，以提高成团率。" )
    private Integer hasGroupTip;

    @Schema(description = "开启后，商品详情页展示未开始的拼团活动，但活动开始前用户无法拼团购买" )
    private Integer isPreheat;

    @Schema(description = "活动状态（活动状态：1:未开始、2:进行中、3:已结束、4:已失效、5:违规下架、6：等待审核）" )
    private Integer activityStatus;

    @Schema(description = "商品活动价格（最低价）" )
    private Double actPrice;

    @Schema(description = "sku列表" )
    private List<GroupSkuVO> groupSkuList;
}
