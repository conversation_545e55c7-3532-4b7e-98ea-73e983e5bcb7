package com.yami.shop.common.allinpay.member.req;

import java.io.Serial;
import java.io.Serializable;
/**
 * 绑定手机
 *
 * <AUTHOR>
 */
public class BindPhone extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    public static final String METHOD_NAME = "bindPhone";

    private String phone;

    /**
     * 验证码
     */
    private String verificationCode;


    public BindPhone() {
    }

    public BindPhone(String bizUserId, String phone, String verificationCode) {
        this.phone = phone;
        this.bizUserId = bizUserId;
        this.verificationCode = verificationCode;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }


}
