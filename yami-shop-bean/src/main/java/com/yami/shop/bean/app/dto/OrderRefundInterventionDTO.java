package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 订单退款介入记录信息DTO
 *
 * <AUTHOR>
 * @date 2023-08-25 10:45:35
 */
@Data
public class OrderRefundInterventionDTO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "退款id")
    private Long refundId;

    @Schema(description = "退款编号")
    private String refundSn;

    @Schema(description = "商家/供应商/用户id")
    private Long bizId;

    @Schema(description = "商家/供应商/用户名称")
    private String bizName;

    @Schema(description = "系统类型， 0.普通用户 1.商家端 2.平台端 3.供应商端")
    private Integer sysType;

    @Schema(description = "凭证说明")
    private String voucherDesc;

    @Schema(description = "文件凭证(逗号隔开)")
    private String imgUrls;

    @Schema(description = "退款金额")
    private Double refundAmount;

    /**
     * 退款原因
     */
    @Schema(description = "退款原因")
    private String rejectMessage;

    @Schema(description = "平台介入退款方式 1.商家承担 2.商家供应商承担（供应商发货订单） 3.不同意退款")
    private Integer platformInterventionRefundType;

    /**
     * 平台留言
     */
    @Schema(description = "平台留言")
    private String platformMessage;
    @Schema(description = "支付完成回跳地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String returnUrl;
    /**
     * 商家备注
     */
    private String sellerMsg;

    @Schema(description = "订单编号")
    private String orderNumber;
}
