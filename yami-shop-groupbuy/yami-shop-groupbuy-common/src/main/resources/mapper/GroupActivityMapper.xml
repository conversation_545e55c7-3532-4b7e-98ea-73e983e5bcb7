<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yami.shop.groupbuy.common.dao.GroupActivityMapper">
    <resultMap id="groupActivityMap" type="com.yami.shop.groupbuy.common.model.GroupActivity">
        <id column="group_activity_id" property="groupActivityId"/>
        <result column="shop_id" property="shopId"/>
        <result column="activity_name" property="activityName"/>
        <result column="status" property="status"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="group_valid_time" property="groupValidTime"/>
        <result column="group_number" property="groupNumber"/>
        <result column="has_max_num" property="hasMaxNum"/>
        <result column="max_num" property="maxNum"/>
        <result column="prod_id" property="prodId"/>
        <result column="price" property="price"/>
        <result column="has_robot" property="hasRobot"/>
        <result column="is_preheat" property="isPreheat"/>
        <result column="has_group_tip" property="hasGroupTip"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <resultMap id="apiGroupActivityMap" type="com.yami.shop.groupbuy.common.api.dto.ApiGroupActivityDto" >
        <id column="group_activity_id" property="groupActivityId"/>
        <result column="shop_id" property="shopId"/>
        <result column="activity_name" property="activityName"/>
        <result column="status" property="status"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="group_valid_time" property="groupValidTime"/>
        <result column="group_number" property="groupNumber"/>
        <result column="has_max_num" property="hasMaxNum"/>
        <result column="max_num" property="maxNum"/>
        <result column="has_robot" property="hasRobot"/>
        <result column="is_preheat" property="isPreheat"/>
        <result column="has_group_tip" property="hasGroupTip"/>
        <result column="price" property="actPrice"/>
        <collection property="groupSkuList" ofType="com.yami.shop.groupbuy.common.api.dto.ApiGroupSkuDto">
            <result column="group_sku_id" property="groupSkuId"/>
            <result column="sku_id" property="skuId"/>
            <result column="act_price" property="actPrice"/>
            <result column="sell_num" property="sellNum"/>
        </collection>
    </resultMap>
    <select id="getByProdId" resultMap="apiGroupActivityMap">
        SELECT ga.*,
               gs.group_sku_id,gs.sku_id,gs.act_price,gs.sell_num
        FROM tz_group_activity ga
          LEFT JOIN tz_group_sku gs ON ga.group_activity_id = gs.group_activity_id
        WHERE ga.status in (1,2) AND ga.prod_id = #{prodId}
        order by gs.group_sku_id
    </select>
    <update id="updateToDelete">
        UPDATE tz_group_activity SET status = -1 WHERE group_activity_id = #{groupActivityId}
    </update>

    <select id="getGroupActivityPage" resultType="com.yami.shop.groupbuy.common.dto.GroupActivityDto">
        SELECT
            ga.*,
            (
                SELECT COUNT(DISTINCT tgo.group_team_id) FROM tz_group_order tgo
                LEFT JOIN tz_group_team tgt ON tgt.`group_team_id` = tgo.`group_team_id`
                WHERE ga.group_activity_id = tgo.group_activity_id AND tgo.status = 1 AND tgt.`status` = 2
            ) AS group_order_count,
            sd.shop_name,tp.prod_name,tp.pic,tp.prod_id
        FROM tz_group_activity ga
        LEFT JOIN tz_shop_detail sd ON ga.shop_id = sd.shop_id
        JOIN tz_prod tp ON tp.`prod_id` = ga.`prod_id`
        <if test="groupActivityDto.prodName != null and groupActivityDto.prodName != ''">
            LEFT JOIN tz_prod_lang tpl ON tpl.prod_id = tp.prod_id AND tpl.lang = #{groupActivityDto.lang}
        </if>
        <where>
            ga.`status` != -1
            <if test="groupActivityDto.activityName != null">
                and trim(replace(ga.activity_name,' ','')) like trim(replace(concat('%',#{groupActivityDto.activityName},'%'),' ',''))
            </if>
            <if test="groupActivityDto.shopName != null">
                and trim(replace(sd.shop_name,' ','')) like trim(replace(concat('%',#{groupActivityDto.shopName},'%'),' ',''))
            </if>
            <if test="groupActivityDto.shopId != null">
               AND ga.shop_id = #{groupActivityDto.shopId}
            </if>
            <if test="groupActivityDto.status != null">
                AND ga.`status` = #{groupActivityDto.status}
            </if>
            <if test="groupActivityDto.prodName != null and groupActivityDto.prodName != ''">
                and ifnull(tpl.prod_name,tp.prod_name) like concat('%',#{groupActivityDto.prodName},'%')
            </if>
        </where>
        ORDER BY ga.create_time DESC
    </select>

    <select id="getApiGroupActivityInfo" resultType="com.yami.shop.groupbuy.common.api.dto.ApiGroupActivityDto">
        SELECT
          ga.*
        FROM tz_group_activity ga
        WHERE ga.group_activity_id = #{groupActivityId} AND ga.prod_id = #{prodId}
    </select>

    <select id="getActivityFinishProduct" resultType="com.yami.shop.bean.model.Product">
        SELECT p.* FROM tz_group_activity ga
        INNER JOIN tz_prod p ON ga.group_activity_id = p.activity_id
        WHERE ga.`status` = 1 AND ga.end_time <![CDATA[ < ]]> #{newDate}
    </select>

    <update id="updateStatus">
        UPDATE tz_group_activity ga SET ga.`status` =#{status}  WHERE ga.`group_activity_id` = #{activityId}
    </update>
    <update id="batchUpdateGroupActivityStatus">
        UPDATE tz_group_activity SET `status` = #{status} WHERE group_activity_id IN
        <foreach collection="groupActivityIds" item="groupActivityId" separator="," open="(" close=")">
            #{groupActivityId}
        </foreach>
    </update>

    <select id="getUnInvalidGroupActivityByProdId" resultMap="groupActivityMap">
<!--    根据商品id获取团购信息，不需要失效、删除和已结束状态的活动-->
        select  * from tz_group_activity where prod_id = #{prodId} and `status` in (1, 2, 3, 4)
    </select>

    <update id="updateGroupActivityStatus">
        UPDATE tz_group_activity SET `status` = #{status} WHERE group_activity_id = #{groupActivityId}
    </update>

    <select id="listActivityPrice" resultMap="groupActivityMap">
        SELECT prod_id,price FROM tz_group_activity WHERE prod_id IN
        <foreach collection="prodIds" item="prodId" open="(" close=")" separator=",">
            #{prodId}
        </foreach>
         AND `status` IN (1,2,3,4)
    </select>
</mapper>
