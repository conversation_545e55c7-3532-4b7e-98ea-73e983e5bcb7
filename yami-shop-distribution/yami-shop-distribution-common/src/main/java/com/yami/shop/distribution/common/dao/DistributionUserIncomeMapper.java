package com.yami.shop.distribution.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.dto.DistributionOrderDto;
import com.yami.shop.distribution.common.dto.DistributionUserIncomeDto;
import com.yami.shop.distribution.common.model.DistributionUser;
import com.yami.shop.distribution.common.model.DistributionUserIncome;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.param.RangeTimeParam;
import com.yami.shop.distribution.common.vo.DistributionOrdersVO;
import com.yami.shop.distribution.common.vo.DistributionUserInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface DistributionUserIncomeMapper extends BaseMapper<DistributionUserIncome> {

    /**
     * 获取分销员的收入记录
     * @param page 分页参数
     * @param shopId 店铺id
     * @param rangeTimeParam 时间范围
     * @param distributionParam 分销查询参数
     * @param orderNumber 订单号
     * @param state 收入状态
     * @param distributionUserIncome 分销员收入信息
     * @return 分销员的收入记录
     */
    IPage<DistributionUserIncome> incomeAndDistributionUserPage(Page page,
                                                                @Param("shopId") Long shopId,
                                                                @Param("rangeTimeParam") RangeTimeParam rangeTimeParam,
                                                                @Param("distributionParam") DistributionParam distributionParam,
                                                                @Param("orderNumber") String orderNumber,
                                                                @Param("state") Integer state,
                                                                @Param("distributionUserIncome") DistributionUserIncome distributionUserIncome,
                                                                @Param("paySysType") Integer paySysType);

    /**
     * 分销员推广订单信息
     * @param page 分页参数
     * @param distributionUserId 分销员id
     * @return 分销员推广订单信息
     */
    IPage<DistributionOrderDto> getDistributionOrderDtoByDistributionUserId(Page page,
                                                                            @Param("distributionUserId") Long distributionUserId,
                                                                            @Param("paySysType") Integer paySysType);

    /**
     * 根据分销员id更新收入状态
     * @param distributionUserId 分销员id
     * @param state 收入状态
     */
    void updateStateByDistributionUserId(@Param("distributionUserId") Long distributionUserId, @Param("state") Integer state);


    /**
     * 统计分销员某时间段收入
     * @param distributionUserId 分销员id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 收入金额
     */
    Double statisticsDisUserIncome(@Param("distributionUserId") Long distributionUserId,
                                   @Param("startTime") Date startTime,
                                   @Param("endTime") Date endTime,
                                   @Param("paySysType") Integer paySysType);

    /**
     * 查询分销员收入记录
     * @param page 分页参数
     * @param distributionUserId 分销员id
     * @return 收入记录
     */
    IPage<DistributionUserIncomeDto> getDistributionUserIncomePage(@Param("page") PageParam<DistributionUserIncome> page,
                                                                   @Param("distributionUserId") Long distributionUserId,
                                                                   @Param("paySysType") Integer paySysType);

    /**
     * 通过状态获取我的推广订单(0:待支付 1:待结算 2:已结算 -1:订单失效)
     *
     * @param page 分页参数
     * @param distributionUserId 分销员id
     * @param state 收入状态 0:待支付 1:待结算 2:已结算 -1:订单失效
     * @return 我的推广订单
     */
    IPage<DistributionOrdersVO> getMyPromotionOrderByState(@Param("page") PageParam<DistributionOrdersVO> page,
                                                           @Param("distributionUserId") Long distributionUserId,
                                                           @Param("state") Integer state,
                                                           @Param("paySysType") Integer paySysType);

    /**
     * 未结算的收益记录
     *
     * @param orderNumbers 符合结算条件的订单列表
     * @param paySysType
     * @return 收益记录
     */
    List<DistributionUserIncome> listWaitCommissionSettlement(@Param("orderNumbers") List<String> orderNumbers, @Param("paySysType") Integer paySysType);

    /**
     * 根据分销员id和订单流水号统计佣金和订单数量
     * @param type  收益类型 收入类型(1一代奖励、2二代奖励 3邀请奖励 等 )
     * @param distributionUserId
     * @param paySysType
     * @return 用户对应佣金数额
     */
    List<DistributionUserInfoVO> selectIncomeInfo(@Param("type")Integer type,
                                                  @Param("distributionUserId")Long distributionUserId,
                                                  @Param("paySysType") Integer paySysType);

    /**
     * 根据订单号和分销员id修改收入状态
     * @param orderNumber
     * @param distributionUserId
     * @param state
     * @param type
     */
    void updateDistributionUserIncomesBy(@Param("list")List<String> orderNumber, @Param("id")Long distributionUserId, @Param("state")Integer state, @Param("type")Integer type);

    /**
     * 获取我的好友信息
     * @param distributionUser 当前用户
     * @param friends 该用户的好友
     * @param type 收益类型
     * @return
     */
    List<DistributionUserInfoVO> getFriendInfo(@Param("distributionUser")DistributionUser distributionUser,
                                               @Param("friends")List<DistributionUser> friends,
                                               @Param("type")Integer type,
                                               @Param("paySysType") Integer paySysType);

    /**
     * 根据订单号获取收入明细
     *
     * @param orderNumber
     * @param paySysType
     * @return
     */
    List<DistributionUserIncome> listDistributionUserIncomeByOrderNumber(@Param("orderNumber") String orderNumber, @Param("paySysType") Integer paySysType);
}
