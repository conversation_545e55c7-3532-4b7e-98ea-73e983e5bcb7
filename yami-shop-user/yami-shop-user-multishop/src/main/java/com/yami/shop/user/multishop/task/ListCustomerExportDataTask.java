package com.yami.shop.user.multishop.task;

import com.yami.shop.bean.dto.ShopCustomerDTO;
import com.yami.shop.bean.vo.ShopCustomerVO;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.ShopCustomerService;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.concurrent.Callable;

/**
 * @author: zsm
 * @date: 2023/1/14 10:42
 */
@AllArgsConstructor
public class ListCustomerExportDataTask implements Callable<List<ShopCustomerVO>> {
    private ShopCustomerService shopCustomerService;
    private ShopCustomerDTO param;
    private PageParam<ShopCustomerVO> pageParam;

    @Override
    public List<ShopCustomerVO> call() throws Exception {
        return shopCustomerService.pageShopCustomerByParam(pageParam, param).getRecords();
    }
}

