package com.yami.shop.distribution.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.distribution.common.dto.DistributionAuditingDto;
import com.yami.shop.distribution.common.model.DistributionAuditing;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.param.RangeTimeParam;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface DistributionAuditingMapper extends BaseMapper<DistributionAuditing> {

    /**
     * 获取分销员审核信息
     * @param page 分页信息
     * @param distributionAuditing 审核信息
     * @param rangeTimeParam 申请时间
     * @param startExpenseNumber 订单数量
     * @param endExpenseNumber 订单数量
     * @param startPayAmount 支付金额范围
     * @param endPayAmount 支付金额范围
     * @param distributionParam 分销查询参数
     * @return 分销员审核信息
     */
    Page<DistributionAuditingDto> distributionAuditingPage(Page page, @Param("distributionAuditing") DistributionAuditing distributionAuditing, @Param("rangeTimeParam") RangeTimeParam rangeTimeParam, @Param("startExpenseNumber") Integer startExpenseNumber, @Param("endExpenseNumber")Integer endExpenseNumber, @Param("startPayAmount") Double startPayAmount, @Param("endPayAmount") Double endPayAmount, @Param("distributionParam")DistributionParam distributionParam);
}
