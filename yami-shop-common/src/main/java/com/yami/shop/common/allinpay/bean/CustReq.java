
package com.yami.shop.common.allinpay.bean;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class CustReq implements Serializable {
    private static final long serialVersionUID = 1L;
    @JSONField(
            name = "PAYEE_ACCT_NO"
    )
    private String payeeAcctNo;
    @J<PERSON>NField(
            name = "PAYEE_ACCT_NAME"
    )
    private String payeeAcctName;
    @J<PERSON><PERSON>ield(
            name = "AMOUNT"
    )
    private String amount;
    @J<PERSON><PERSON>ield(
            name = "SUMMARY"
    )
    private String summary;

    public String getPayeeAcctNo() {
        return this.payeeAcctNo;
    }

    public void setPayeeAcctNo(String payeeAcctNo) {
        this.payeeAcctNo = payeeAcctNo;
    }

    public String getPayeeAcctName() {
        return this.payeeAcctName;
    }

    public void setPayeeAcctName(String payeeAcctName) {
        this.payeeAcctName = payeeAcctName;
    }

    public String getAmount() {
        return this.amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getSummary() {
        return this.summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this, SerializerFeature.SortField);
    }
}
