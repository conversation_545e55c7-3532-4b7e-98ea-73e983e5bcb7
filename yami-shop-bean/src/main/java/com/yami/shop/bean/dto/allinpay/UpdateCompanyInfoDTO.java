package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class UpdateCompanyInfoDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "商户唯一号")
    private String bizUserId;

    @Schema(description = "企业名称")
    private String companyName;

    @Schema(description = "企业地址")
    private String companyAddress;

    @Schema(description = "法人姓名")
    private String legalName;

    @Schema(description = "法人证件类型")
    private Integer identityType;

    @Schema(description = "法人证件号码")
    private String legalIds;

    @Schema(description = "法人手机号")
    private String legalPhone;

    public UpdateCompanyInfoDTO() {
    }

    public UpdateCompanyInfoDTO(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    public Integer getIdentityType() {
        return identityType;
    }

    public void setIdentityType(Integer identityType) {
        this.identityType = identityType;
    }

    public String getLegalIds() {
        return legalIds;
    }

    public void setLegalIds(String legalIds) {
        this.legalIds = legalIds;
    }

    public String getLegalPhone() {
        return legalPhone;
    }

    public void setLegalPhone(String legalPhone) {
        this.legalPhone = legalPhone;
    }

    @Override
    public String toString() {
        return "UpdateCompanyInfoDTO{" +
                "bizUserId='" + bizUserId + '\'' +
                ", companyName='" + companyName + '\'' +
                ", companyAddress='" + companyAddress + '\'' +
                ", legalName='" + legalName + '\'' +
                ", identityType=" + identityType +
                ", legalIds='" + legalIds + '\'' +
                ", legalPhone='" + legalPhone + '\'' +
                '}';
    }
}
