package com.yami.shop.bean.vo.search;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class GroupActivitySearchVO {

    @Schema(description = "店铺类型1自营店 2普通店" )
    private Long groupActivityId;

    @Schema(description = "成团人数" )
    private Integer groupNumber;

    @Schema(description = "开始时间" )
    private Date startTime;

    @Schema(description = "结束时间" )
    private Date endTime;

    @Schema(description = "活动创建时间" )
    private Date createTime;

    @Schema(description = "拼团价格" )
    private Double price;
}
