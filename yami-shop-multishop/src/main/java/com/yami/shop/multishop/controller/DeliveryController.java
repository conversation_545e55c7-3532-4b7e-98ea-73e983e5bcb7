package com.yami.shop.multishop.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yami.shop.bean.model.Delivery;
import com.yami.shop.common.enums.StatusEnum;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.DeliveryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * <AUTHOR> on 2018/11/26.
 */
@RestController
@RequestMapping("/admin/delivery")
@Tag(name = "物流公司接口")
@AllArgsConstructor
public class DeliveryController {

    private final DeliveryService deliveryService;

    @GetMapping("/list")
    @Operation(summary = "获取物流公司列表" , description = "获取物流公司列表")
    @PreAuthorize("@pms.hasPermission('admin:delivery:list')")
    public ServerResponseEntity<List<Delivery>> list(){
        List<Delivery> list = deliveryService.list(Wrappers.lambdaQuery(Delivery.class)
                .eq(Delivery::getStatus, StatusEnum.ENABLE.value())
        );
        return ServerResponseEntity.success(list);
    }


    @GetMapping("/page")
    @Operation(summary = "分页获取物流公司列表" , description = "分页获取物流公司列表")
    public ServerResponseEntity<IPage<Delivery>> page(PageParam<Delivery> page, Delivery delivery){
        IPage<Delivery> deliveryPage = deliveryService.page(page, new LambdaQueryWrapper<Delivery>()
                .eq(Delivery::getStatus, StatusEnum.ENABLE.value())
                .eq(!ObjectUtils.isEmpty(delivery.getDvyId()), Delivery::getDvyId, delivery.getDvyId())
                .like(!ObjectUtils.isEmpty(delivery.getDvyName()), Delivery::getDvyName, delivery.getDvyName())
                .like(!ObjectUtils.isEmpty(delivery.getDvyNo()), Delivery::getDvyNo, delivery.getDvyNo())
                .like(!ObjectUtils.isEmpty(delivery.getAliNo()), Delivery::getAliNo, delivery.getAliNo())
                .like(!ObjectUtils.isEmpty(delivery.getDvyNoHd()), Delivery::getDvyNoHd, delivery.getDvyNoHd())
        );
        return ServerResponseEntity.success(deliveryPage);
    }
}
