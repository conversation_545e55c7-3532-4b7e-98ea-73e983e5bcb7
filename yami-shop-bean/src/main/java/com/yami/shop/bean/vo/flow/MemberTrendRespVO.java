package com.yami.shop.bean.vo.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MemberTrendRespVO {

    @Schema(description = "时间，格式例如：20200721" )
    private Long currentDay;

    @Schema(description = "注册会员数量" )
    private Integer memberNum;

    @Schema(description = "注册会员数在统计时间类的所有注册会员人数的占比" )
    private Double memberNumRate;
}
