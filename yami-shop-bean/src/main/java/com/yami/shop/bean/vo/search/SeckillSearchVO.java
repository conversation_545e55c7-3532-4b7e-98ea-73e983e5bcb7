package com.yami.shop.bean.vo.search;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SeckillSearchVO {

    @Schema(description = "秒杀活动id" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long seckillId;

    @Schema(description = "活动名称" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String seckillName;

    @Schema(description = "开始时间" , requiredMode = Schema.RequiredMode.REQUIRED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "结束时间" , requiredMode = Schema.RequiredMode.REQUIRED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @Schema(description = "秒杀活动剩余总库存" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer seckillTotalStocks;

    @Schema(description = "秒杀活动原始库存" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer seckillOriginStocks;

    @Schema(description = "秒杀活动最低价" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Double seckillPrice;
}
