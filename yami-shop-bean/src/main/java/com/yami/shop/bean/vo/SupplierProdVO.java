package com.yami.shop.bean.vo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SupplierProdVO {

    @Schema(description = "供应商商品id" )
    private Long supplierProdId;

    @Schema(description = "供应商id" )
    private Long supplierId;

    @Schema(description = "商品id" )
    private Long prodId;

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "skuId" )
    private Long skuId;

    @Schema(description = "sku名称" )
    private String skuName;

    @Schema(description = "最小订货量" )
    private Integer minOrderQuantity;

    @Schema(description = "采购价" )
    private Double purchasePrice;

    @Schema(description = "商品编码" )
    private String partyCode;

    @Schema(description = "sku图片" )
    private String pic;

    @Schema(description = "sku库存" )
    private Integer stocks;

    @Schema(description = "sku列表" )
    private List<SupplierSkuVO> skuList;

    @Schema(description = "商品状态" )
    private Integer status;

}
