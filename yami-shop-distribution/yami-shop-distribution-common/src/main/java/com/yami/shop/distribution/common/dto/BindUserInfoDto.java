package com.yami.shop.distribution.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class BindUserInfoDto  {

    @Schema(description = "0 失效 1生效" )
    private Integer state;

    @Schema(description = "0 超过有效期 1 管理员更改 2抢客" )
    private Integer invalidReason;

    @Schema(description = "变动时间" )
    private Date updateTime;

    @Schema(description = "绑定时间" )
    private Date bindTime;

    @Schema(description = "失效时间" )
    private Date invalidTime;

    @Schema(description = "用户昵称" )
    private String nickName;

    @Schema(description = "用户手机号" )
    private String userMobile;

    @Schema(description = "用户头像" )
    private String pic;


}
