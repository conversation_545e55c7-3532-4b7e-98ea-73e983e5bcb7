package com.yami.shop.bean.vo.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 获取某频道消息
 * <AUTHOR>
 */
public class MessageSyncVO {

    @Schema(description = "对接客服id")
    private Long employeeId;

    @Schema(description = "查询的start_message_seq")
    private Integer startMessageSeq;

    @Schema(description = "查询的end_message_seq")
    private Integer endMessageSeq;

    @Schema(description = "是否有更多  0.无 1.有")
    private Integer more;

    @Schema(description = "消息")
    private List<MessageVO> messages;

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public Integer getStartMessageSeq() {
        return startMessageSeq;
    }

    public void setStartMessageSeq(Integer startMessageSeq) {
        this.startMessageSeq = startMessageSeq;
    }

    public Integer getEndMessageSeq() {
        return endMessageSeq;
    }

    public void setEndMessageSeq(Integer endMessageSeq) {
        this.endMessageSeq = endMessageSeq;
    }

    public Integer getMore() {
        return more;
    }

    public void setMore(Integer more) {
        this.more = more;
    }

    public List<MessageVO> getMessages() {
        return messages;
    }

    public void setMessages(List<MessageVO> messages) {
        this.messages = messages;
    }

    @Override
    public String toString() {
        return "MessageSyncVO{" +
                "employeeId=" + employeeId +
                ", start_message_seq=" + startMessageSeq +
                ", end_message_seq=" + endMessageSeq +
                ", more=" + more +
                ", messages=" + messages +
                '}';
    }
}
