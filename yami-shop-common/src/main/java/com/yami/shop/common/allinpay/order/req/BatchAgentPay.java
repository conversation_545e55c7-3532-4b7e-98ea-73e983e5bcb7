package com.yami.shop.common.allinpay.order.req;

import java.util.List;

/**
 * 批量托管代付
 *
 * <AUTHOR>
 */
public class BatchAgentPay extends AbstractOrderReq {
    /**
     * 商户批次号
     */
    private String bizBatchNo;
    /**
     * 批量代付列表
     */
    private List<AgentPay> batchPayList;
    /**
     * 业务码,代收消费金[3001] + 代付购买金[4001]
     */
    private String tradeCode;

    /**
     * 方法名称
     */
    public static final String METHOD_NAME = "batchAgentPay";


    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public BatchAgentPay() {
    }

    public BatchAgentPay(String bizBatchNo, List<AgentPay> agentPay, String tradeCode) {
        this.bizBatchNo = bizBatchNo;
        this.batchPayList = agentPay;
        this.tradeCode = tradeCode;
    }

    public String getBizBatchNo() {
        return bizBatchNo;
    }

    public void setBizBatchNo(String bizBatchNo) {
        this.bizBatchNo = bizBatchNo;
    }

    public List<AgentPay> getBatchPayList() {
        return batchPayList;
    }

    public void setBatchPayList(List<AgentPay> agentPay) {
        this.batchPayList = agentPay;
    }

    public String getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }

    @Override
    public String toString() {
        return "BatchAgentPay{" +
                ", bizBatchNo='" + bizBatchNo + '\'' +
                ", batchPayList=" + batchPayList +
                ", tradeCode='" + tradeCode + '\'' +
                '}';
    }
}
