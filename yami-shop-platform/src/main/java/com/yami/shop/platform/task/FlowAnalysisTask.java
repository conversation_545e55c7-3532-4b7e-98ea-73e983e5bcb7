package com.yami.shop.platform.task;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.util.RedisUtil;
import com.yami.shop.service.FlowLogService;
import com.yami.shop.service.FlowService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class FlowAnalysisTask {


    private final FlowLogService flowLogService;
    private final FlowService flowService;


    /**
     * 根据设置的时间，将缓存中的记录插入到数据库
     */
    @XxlJob("insertFlowAnalysisLog")
    public void insertFlowAnalysisLog(){
        if (RedisUtil.hasKey(Constant.FLOW_ANALYSIS_LOG)){
            flowLogService.insertBatch();
        }
    }

    /**
     * 统计记录数据，储存到对应的数据表中
     */
    @XxlJob("statisticalFlowData")
    public void statisticalFlowData(){
        //更新数据再统计
        insertFlowAnalysisLog();
        flowService.statisticalData();
    }
}
