package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;
/**
 * cardBin 信息
 *
 * <AUTHOR>
 */
public class CardBinInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 卡bin
     */
    private String cardBin;

    /**
     * 卡种
     */
    private Integer cardType;

    /**
     * 发卡行代码
     */
    private String bankCode;

    /**
     * 发卡行名称
     */
    private String bankName;

    /**
     * 发卡行名称
     */
    private String cardName;

    /**
     * 卡片长度
     */
    private String cardLenth;

    /**
     * 状态（1：有效；0：无效）
     */
    private Integer cardState;

    /**
     * 卡种名称
     */
    private String cardTypeLabel;

    public String getCardBin() {
        return cardBin;
    }

    public void setCardBin(String cardBin) {
        this.cardBin = cardBin;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getCardLenth() {
        return cardLenth;
    }

    public void setCardLenth(String cardLenth) {
        this.cardLenth = cardLenth;
    }

    public Integer getCardState() {
        return cardState;
    }

    public void setCardState(Integer cardState) {
        this.cardState = cardState;
    }

    public String getCardTypeLabel() {
        return cardTypeLabel;
    }

    public void setCardTypeLabel(String cardTypeLabel) {
        this.cardTypeLabel = cardTypeLabel;
    }


}
