package com.yami.shop.multishop.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.enums.TemplateType;
import com.yami.shop.bean.model.ShopTemplate;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.ShopTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Objects;


/**
 *
 *
 * <AUTHOR>
 * @date 2022-07-29 16:54:02
 */
@RestController
@RequestMapping("/shop/shopTemplate")
@Tag(name = "店铺装修模板接口")
@AllArgsConstructor
public class ShopTemplateController {

    private final ShopTemplateService shopTemplateService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param shopTemplate 店铺装修模板信息
     * @return 分页数据
     */
    @GetMapping("/pagePC")
    @PreAuthorize("@pms.hasPermission('shop:shopTemplate:pagePC')")
    @Operation(summary = "PC端分页查询店铺模板信息" , description = "PC端分页查询店铺模板信息")
    public ServerResponseEntity<IPage<ShopTemplate>> getShopTemplatePagePc(PageParam<ShopTemplate> page, ShopTemplate shopTemplate) {
        return ServerResponseEntity.success(shopTemplateService.page(page, new LambdaQueryWrapper<ShopTemplate>()
                .eq(ShopTemplate::getShopId, SecurityUtils.getShopUser().getShopId())
                .eq(Objects.nonNull(shopTemplate.getType()), ShopTemplate::getType, shopTemplate.getType())
                .orderByDesc(ShopTemplate::getUpdateTime)
        ));
    }

    /**
     * 分页查询
     * @param page 分页对象
     * @param shopTemplate 店铺装修模板信息
     * @return 分页数据
     */
    @GetMapping("/pageMove")
    @PreAuthorize("@pms.hasPermission('shop:shopTemplate:pageMove')")
    @Operation(summary = "移动端分页查询店铺模板信息" , description = "移动端分页查询店铺模板信息")
    public ServerResponseEntity<IPage<ShopTemplate>> getShopTemplatePageMove(PageParam<ShopTemplate> page, ShopTemplate shopTemplate) {
        return ServerResponseEntity.success(shopTemplateService.page(page, new LambdaQueryWrapper<ShopTemplate>()
                .eq(ShopTemplate::getShopId, SecurityUtils.getShopUser().getShopId())
                .eq(Objects.nonNull(shopTemplate.getType()), ShopTemplate::getType, shopTemplate.getType())
                .orderByDesc(ShopTemplate::getUpdateTime)
        ));
    }

    /**
     * 通过id查询店铺模板信息
     * @param templateId id
     * @return 单个数据
     */
    @GetMapping("/info/{templateId}" )
    @Operation(summary = "通过id查询店铺装修模板信息" , description = "通过id查询店铺装修模板信息")
    public ServerResponseEntity<ShopTemplate> getById(@PathVariable("templateId") Long templateId) {
        ShopTemplate shopTemplate = shopTemplateService.getById(templateId);
        if(!Objects.equals(shopTemplate.getShopId() ,SecurityUtils.getShopUser().getShopId())){
            // 没有权限进行操作
            throw new YamiShopBindException("yami.no.auth");
        }
        return ServerResponseEntity.success(shopTemplate);
    }


    /**
     * 新增店铺模板信息
     * @param shopTemplate 店铺模板信息
     * @return 是否新增成功
     */
    @PostMapping("/savePC")
    @PreAuthorize("@pms.hasPermission('shop:shopTemplate:savePC')")
    @Operation(summary = "PC端新增店铺模板信息" , description = "PC端新增店铺模板信息")
    public ServerResponseEntity<Long> savePc(@RequestBody @Valid ShopTemplate shopTemplate) {
        shopTemplate.setShopId(SecurityUtils.getShopUser().getShopId());
        if (Objects.isNull(shopTemplate.getType())) {
            shopTemplate.setType(TemplateType.H5.value());
        }
        shopTemplateService.save(shopTemplate);
        return ServerResponseEntity.success(shopTemplate.getTemplateId());
    }

    /**
     * 新增店铺模板信息
     * @param shopTemplate 店铺模板信息
     * @return 是否新增成功
     */
    @PostMapping("/saveMove")
    @PreAuthorize("@pms.hasPermission('shop:shopTemplate:saveMove')")
    @Operation(summary = "移动端新增店铺模板信息" , description = "移动端新增店铺模板信息")
    public ServerResponseEntity<Long> saveMove(@RequestBody @Valid ShopTemplate shopTemplate) {
        shopTemplate.setShopId(SecurityUtils.getShopUser().getShopId());
        if (Objects.isNull(shopTemplate.getType())) {
            shopTemplate.setType(TemplateType.H5.value());
        }
        shopTemplateService.save(shopTemplate);
        return ServerResponseEntity.success(shopTemplate.getTemplateId());
    }



    @PostMapping("/copyPC/{templateId}")
    @PreAuthorize("@pms.hasPermission('shop:shopTemplate:copyPC')")
    @Operation(summary = "PC端复制模板信息" , description = "PC端复制模板信息")
    public ServerResponseEntity<Long> copyTemplatePc(@PathVariable Long templateId){
        ShopTemplate shopTemplate = shopTemplateService.getById(templateId);
        shopTemplate.setTemplateId(null);
        shopTemplate.setName(shopTemplate.getName()+"副本");
        shopTemplate.setCreateTime(new Date());
        shopTemplate.setUpdateTime(new Date());
        shopTemplateService.save(shopTemplate);
        return ServerResponseEntity.success(shopTemplate.getTemplateId());
    }

    @PostMapping("/copyMove/{templateId}")
    @PreAuthorize("@pms.hasPermission('shop:shopTemplate:copyMove')")
    @Operation(summary = "移动端复制模板信息" , description = "移动端复制模板信息")
    public ServerResponseEntity<Long> copyTemplateMove(@PathVariable Long templateId){
        ShopTemplate shopTemplate = shopTemplateService.getById(templateId);
        shopTemplate.setTemplateId(null);
        shopTemplate.setName(shopTemplate.getName()+"副本");
        shopTemplate.setCreateTime(new Date());
        shopTemplate.setUpdateTime(new Date());
        shopTemplateService.save(shopTemplate);
        return ServerResponseEntity.success(shopTemplate.getTemplateId());
    }

}
