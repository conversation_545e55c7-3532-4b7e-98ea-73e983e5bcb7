package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class EsProdUpdateVO implements Serializable {

    private static final long serialVersionUID = 6457261945829470666L;

    @Schema(description = "产品ID" )
    private Long prodId;

    @Schema(description = "商品数量" )
    private Integer count;

    @Schema(description = "类型 0：减少 1：增加" )
    private Integer type;

    @Schema(description = "门店ID" )
    private Long stationId;
}
