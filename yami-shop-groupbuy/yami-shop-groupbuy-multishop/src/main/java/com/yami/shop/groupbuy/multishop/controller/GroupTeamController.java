package com.yami.shop.groupbuy.multishop.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.groupbuy.common.dto.GroupOrderDTO;
import com.yami.shop.groupbuy.common.dto.GroupTeamDto;
import com.yami.shop.groupbuy.common.model.GroupOrder;
import com.yami.shop.groupbuy.common.model.GroupTeam;
import com.yami.shop.groupbuy.common.service.GroupTeamService;
import com.yami.shop.security.multishop.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 拼团团队表
 *
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
@RestController
@AllArgsConstructor
@RequestMapping("/group/team")
@Tag(name = "商家端拼团团队接口")
public class GroupTeamController {

    private final GroupTeamService groupTeamService;

    @GetMapping("/page")
    @Operation(summary = "分页查找拼团团队列表")
    @PreAuthorize("@pms.hasPermission('group:team:page')")
    public ServerResponseEntity<IPage<GroupTeamDto>> getGroupTeamPage(PageParam<GroupTeam> page, GroupTeamDto groupTeamDto) {
        groupTeamDto.setShopId(SecurityUtils.getShopUser().getShopId());
        IPage<GroupTeamDto> pageList = groupTeamService.getPage(page, groupTeamDto);
        return ServerResponseEntity.success(pageList);
    }

    @GetMapping("/info")
    @Operation(summary = "查看同团订单列表")
    @PreAuthorize("@pms.hasPermission('group:team:info')")
    public ServerResponseEntity<IPage<GroupOrderDTO>> getById(PageParam<GroupOrder> page, GroupOrder groupOrder) {
        IPage<GroupOrderDTO> orderPage = groupTeamService.getGroupOrderPage(page, groupOrder);
        return ServerResponseEntity.success(orderPage);
    }
}
