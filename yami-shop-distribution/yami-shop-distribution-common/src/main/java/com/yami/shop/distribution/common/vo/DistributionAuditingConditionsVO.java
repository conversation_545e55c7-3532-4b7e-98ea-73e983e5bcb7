package com.yami.shop.distribution.common.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yami.shop.bean.model.Product;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 成为分销员的条件设置数据
 * <AUTHOR>
 */
@Data
public class DistributionAuditingConditionsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 是否需要关注公众号
     */
    private Boolean isFocus;

    /**
     * 用户id列表
     */
    private List<Long> userList;

    /**
     * 商品id列
     */
    private List<Long> prodList;

    /**
     * 消费笔数大于等于
     */
    private Double expenseNumber;

    /**
     * 消费金额大于等于
     */
    private Double expenseAmount;

    /**
     * 商品列表
     */
    @TableField(exist = false)
    private List<Product> productDtoList;
}
