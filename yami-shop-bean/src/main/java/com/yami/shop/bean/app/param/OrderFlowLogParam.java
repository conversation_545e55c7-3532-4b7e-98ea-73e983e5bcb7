package com.yami.shop.bean.app.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用户流量记录参数
 * @since 2023/8/7 9:09
 */
@Data
public class OrderFlowLogParam implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "uuid")
    private String uuid;

    @Schema(description = "会话uuid")
    private String uuidSession;

    /**
     * 枚举类：FlowSystemTypeEnum
     */
    @Schema(description = "系统类型 1:pc  2:h5  3:小程序 4:安卓  5:ios ")
    private Integer systemType;

    @Schema(description = "访问类型 1:页面访问  2:分享访问  3:页面点击  4:加入购物车")
    private Integer visitType;

    @Schema(description = "用户操作步骤数")
    private Integer step;

    /**
     * 用户登陆ip
     */
    private String ip;
}
