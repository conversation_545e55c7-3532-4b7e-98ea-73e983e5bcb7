package com.yami.shop.groupbuy.common.api.param;

import com.yami.shop.bean.app.param.OrderParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 开团/参团需要的参数
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "拼团下单参数")
public class GroupOrderParam extends OrderParam {

    @Schema(description = "拼团团队id，（如果用户为参团则需要填写对应的拼团团队Id(groupTeamId)，如果为用户为开团,拼团团队Id(groupTeamId)为0）" )
    @NotNull(message = "拼团团队id不能为空")
    private Long groupTeamId;

    @NotNull(message = "活动商品规格Id不能为空")
    @Schema(description = "拼团商品规格Id" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long groupSkuId;
}
