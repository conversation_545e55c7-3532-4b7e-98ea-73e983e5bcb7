package com.yami.shop.groupbuy.common.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 拼团活动用户信息
 *
 * <AUTHOR>
 * @date 2019/9/2 17:11
 */
@Data
public class ApiGroupUserDto {

    /**
     * 等于value ,用于对字段的说明
     */
    @Schema(description = "用户ID（当ID为0时标识为机器人）" )
    private String userId;

    @Schema(description = "用户昵称" )
    private String nickName;

    @Schema(description = "用户头像" )
    private String pic;

    @Schema(description = "订单号" )
    private String orderNumber;

    @Schema(description = "用户类型(0:成员  1:团长)" )
    private Integer identityType;

    @Schema(description = "创建时间" )
    private Date createTime;

}
