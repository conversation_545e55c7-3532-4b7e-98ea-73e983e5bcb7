package com.yami.shop.common.allinpay.member.req;

import com.yami.shop.common.allinpay.member.constant.PayAcctType;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class ApplyBindAcct extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "applyBindAcct";

    /**
     * set:绑定, query:查询
     */
    private String operationType = "set";

    /**
     * 支付账户类型
     *
     * @see PayAcctType
     */
    private String acctType;

    /**
     * 支付账户用户标识
     */
    private String acct;

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getAcctType() {
        return acctType;
    }

    public void setAcctType(String acctType) {
        this.acctType = acctType;
    }

    public String getAcct() {
        return acct;
    }

    public void setAcct(String acct) {
        this.acct = acct;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    @Override
    public String toString() {
        return "ApplyBindAcct{" +
                "operationType='" + operationType + '\'' +
                ", acctType='" + acctType + '\'' +
                ", acct='" + acct + '\'' +
                ", bizUserId='" + bizUserId + '\'' +
                '}';
    }
}
