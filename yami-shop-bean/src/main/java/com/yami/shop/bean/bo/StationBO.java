package com.yami.shop.bean.bo;

import com.yami.shop.bean.vo.SameCityVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.elasticsearch.common.geo.GeoPoint;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自提门店
 * @since 2024/1/19 16:08
 */
@Data
public class StationBO implements Serializable {
    /**
     * 门店自提点id
     */
    private Long stationId;

    /**
     * 关联店铺id
     */
    private Long shopId;

    /**
     * 门店自提点名称
     */
    private String stationName;

    /**
     * 门店地址(省+市+区+详细地址拼接)
     */
    private String stationAddr;


    /**
     * 门店自提点主图片
     */
    private String pic;

    /**
     * 门店自提点图片,多张用逗号隔开
     */
    private String imgUrls;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 电话区号
     */
    private String phonePrefix;

    /**
     * 手机/电话号码
     */
    private String phone;

    /**
     * 店铺所在经纬度
     */
    private GeoPoint stationLocation;


    /**
     * 省ID
     */
    private Long provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区ID
     */
    private Long areaId;

    /**
     * 区
     */
    private String area;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 地址
     */
    private String addr;

    /**
     * 时间数据
     */
    private String timeDate;

    /**
     * -1:已删除 0:关闭 1:营业 2:强制关闭 3:审核中 4:审核失败
     */
    private Integer status;

    /**
     * 账号名
     */
    private String account;

    /**
     * 库存模式
     */
    private Integer stockMode;

    /**
     * 自提门店用途是否支持自提（0：不支持，1：支持）
     */
    private Integer selfPickup;

    /**
     * 自提门店用途是否支持同城配送（0：不支持，1：支持）
     */
    private Integer sameCityDelivery;

    /**
     * 自提门店评分
     */
    private Double stationScore;

    /**
     * 同城配送信息
     */
    private SameCityVO sameCityVO;

    /**
     * 商品创建时间
     */
    private Long createTime;

    /**
     * 商品更新时间
     */
    private Long updateTime;

}
