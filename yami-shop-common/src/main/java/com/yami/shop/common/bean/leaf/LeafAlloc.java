package com.yami.shop.common.bean.leaf;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("tz_leaf_alloc")
public class LeafAlloc {
    /**
     * 分类
     */
    private String bizTag;
    /**
     * 最大id值
     */
    private long maxId;
    /**
     * 步长
     */
    private int step;
    /**
     * 当前的id值
     */
    private Long value;
    /**
     * 更新时间
     */
    private String updateTime;
}
