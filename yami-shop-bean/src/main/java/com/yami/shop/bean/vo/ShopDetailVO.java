package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/8/3 14:33
 */
@Data
public class ShopDetailVO {
    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "店铺类型1自营店 2普通店")
    private Integer type;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "店铺简介")
    private String intro;

    @Schema(description = "接收短信号码")
    private String noticeMobile;

    @Schema(description = "店铺logo(可修改)")
    private String shopLogo;

    @Schema(description = "店铺状态(-1:已删除 0: 停业中 1:营业中 2:平台下线 3:开店申请待审核 4:店铺申请中 5:上线申请待审核)")
    private Integer shopStatus;

    @Schema(description = "店铺评分")
    private Double shopScore;

    @Schema(description = "是否优选好店 1.是 0.不是")
    private Integer isPreferred;

    @Schema(description = "店铺收藏数量")
    private Long collectionNum;

    @Schema(description = "移动端背景图")
    private String mobileBackgroundPic;

    @Schema(description = "pc背景图")
    private String pcBackgroundPic;

    @Schema(description = "联系人姓名")
    private String contactName;

    @Schema(description = "联系方式")
    private String contactPhone;

    @Schema(description = "商家账号")
    private String merchantAccount;

    @Schema(description = "账号状态， 1:启用 0:禁用 -1:删除")
    private Integer accountStatus;

    @Schema(description = "商家名称")
    private String merchantName;

    @Schema(description = "签约起始时间")
    private Date contractStartTime;

    @Schema(description = "签约终止时间")
    private Date contractEndTime;

    @Schema(description = "是否在通联创建了企业会员 0否1是")
    private Integer isCreateMember;

    @Schema(description = "是否绑定了手机 0否1是")
    private Integer isBindPhone;

    @Schema(description = "企业信息审核状态 0.未提交 1.待审核 2.审核成功 3.审核失败")
    private Integer companyInfoProcessStatus;

    @Schema(description = "影印件采集审核状态  0.未上传 1.待审核 2.只有工商认证通过 3.只有法人信息通过 4.均审核通过")
    private Integer idCardCollectProcessStatus;

    @Schema(description = "通联店铺状态 0未开通 1营业中 2平台下线 3上线待审核 4开店待审核")
    private Integer allinpayShopStatus;

    @Schema(description = "企业账户提现协议编号")
    private String companyAcctProtocolNo;

    @Schema(description = "法人账户提现协议编号")
    private String legalAcctProtocolNo;
}
