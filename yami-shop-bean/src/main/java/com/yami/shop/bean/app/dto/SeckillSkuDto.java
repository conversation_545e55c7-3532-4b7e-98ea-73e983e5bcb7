package com.yami.shop.bean.app.dto;

import com.yami.shop.bean.model.SeckillStockPointSku;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SeckillSkuDto {

    @Schema(description = "秒杀sku拥有的库存" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer seckillStocks;

    @Schema(description = "sku价格" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Double price;

    @Schema(description = "秒杀价格" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Double seckillPrice;

    @Schema(description = "sku图片" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String pic;
    @Schema(description = "销售属性组合字符串,格式是p1:v1;p2:v2" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String properties;

    /**
     * 秒杀活动单个skuid
     */
    @Schema(description = "秒杀活动单个skuid" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long seckillSkuId;
    /**
     * skuId
     */
    @Schema(description = "skuId" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long skuId;

    @Schema(description = "库存关联列表")
    private List<SeckillStockPointSku> seckillStockPointList;

    @Schema(description = "是否有库存" )
    private Boolean isHasStock = true;
}
