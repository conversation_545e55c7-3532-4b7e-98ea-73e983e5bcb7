package com.yami.shop.common.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import org.elasticsearch.common.geo.GeoPoint;

import java.io.IOException;

/**
 * <AUTHOR>
 */
public class GeoPointSerializer extends StdSerializer<GeoPoint> {
    private static final long serialVersionUID = -1262685290185555664L;

    GeoPointSerializer() {
        this(null);
    }

    GeoPointSerializer(Class<GeoPoint> t) {
        super(t);
    }

    @Override
    public void serialize(GeoPoint value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeStartObject();
        gen.writeNumberField("lat", value.getLat());
        gen.writeNumberField("lon", value.getLon());
        gen.writeEndObject();
    }
}

