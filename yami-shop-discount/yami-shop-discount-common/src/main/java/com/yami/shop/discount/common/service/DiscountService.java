package com.yami.shop.discount.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.bean.app.dto.DiscountDto;
import com.yami.shop.bean.app.dto.ProductDto;
import com.yami.shop.bean.model.Product;
import com.yami.shop.bean.param.OfflineHandleEventAuditParam;
import com.yami.shop.bean.param.ProductParam;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.discount.common.model.Discount;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <AUTHOR> on 2018/11/21.
 */
public interface DiscountService extends IService<Discount> {

    /**
     * 插入满减满折优惠信息与满减满折优惠商品
     * @param discount 满减满折优惠信息
     */
    void insertDiscountAndItemAndProd(@Valid Discount discount);

    /**
     * 更新满减满折优惠信息与满减满折优惠商品
     * @param discount 满减满折优惠信息
     */
    void updateDiscountAndItemAndProd(@Valid Discount discount);

    /**
     * 删除满减满折优惠信息与满减满折优惠商品
     * @param discountId 满减满折优惠ID
     * @param shopId 店铺ID
     */
    void deleteDiscountsAndItemsAndProds(Long discountId, Long shopId);

    /**
     * 通过ID获取折扣和折扣商品
     * @param discountId 满减满折优惠ID
     * @return 满减满折优惠信息
     */
    Discount getDiscountAndItemAndProdById(Long discountId);

    /**
     * 通过ID获取折扣和折扣商品
     * @param discountId 满减满折优惠ID
     * @return 满减满折优惠信息
     */
    Discount getDiscounttAndItemAndProdByDiscountId(Long discountId);

    /**
     * 根据ID删除满减满折优惠信息和满减满折优惠商品的缓存
     * @param discountId 满减满折优惠ID
     */
    void removeDiscountAndItemAndProdCacheById(Long discountId);

    /**
     * 根据店铺ID批量获取折扣和折扣商品
     * @param shopId 店铺ID
     * @return 满减满折优惠列表
     */
    List<Discount> listDiscountsAndItemsByShopId(Long shopId);

    /**
     * 通过商店ID删除折扣和折扣商品缓存
     * @param shopId 店铺ID
     */
    void removeDiscountsAndItemsCacheByShopId(Long shopId);

    /**
     * 根据商品ID批量获取折扣信息
     * @param prodId 商品ID
     * @param shopId 店铺ID
     * @return 满减满折优惠列表
     */
    List<DiscountDto> listByProdId(Long prodId, Long shopId);

    /**
     * 根据折扣ID分页获取折扣商品列表
     * @param page 分页参数
     * @param discountId 满减满折优惠ID
     * @return 商品列表
     */
    IPage<ProductDto> discountProdList(PageParam<ProductDto> page, Long discountId);

    /**
     * 分页获取平台满减数据
     * @param page 分页参数
     * @param discount 满减满折优惠
     * @return 满减满折优惠列表
     */
    IPage<Discount> getPlatformDiscountPage(PageParam<Discount> page, Discount discount);

    /**
     * 平台 --下线活动
     * @param discount 满减满折优惠信息
     * @param offlineReason 下线原因
     * @param sysUserId 系统用户ID
     */
    void offline(Discount discount, String offlineReason, Long sysUserId);

    /**
     * 商家 --申请活动
     * @param eventId 事件ID
     * @param discountId 满减满折优惠ID
     * @param reapplyReason 申请原因
     */
    void auditApply(Long eventId, Long discountId, String reapplyReason);

    /**
     * 平台 --审核活动
     * @param offlineHandleEventAuditParam
     * @param sysUserId 系统用户ID
     */
    void auditDiscount(OfflineHandleEventAuditParam offlineHandleEventAuditParam, Long sysUserId);

    /**
     * 分页获取折扣信息
     * @param page 分页参数
     * @return 折扣信息列表
     */
    IPage<DiscountDto> getDiscountList(PageParam<Discount> page);

    /**
     * 根据ID获取折扣信息
     * @param discountId 折扣信息ID
     * @return 折扣信息
     */
    DiscountDto getDiscountByDiscountId(Long discountId);

    /**
     * 通过更改状态关闭折扣
     */
    void closeDiscountBySetStatus();

    /**
     * 根据店铺ID获取店铺参与类型为所有商品参与的折扣活动数量
     * @param shopId 店铺ID
     * @param prodId 商品ID
     * @return
     */
    Integer countProdIsSeckillAll(Long shopId, Long prodId);

    /**
     * 根据店铺ID与商品ID获取店铺参与类型为指定商品参与的折扣活动数量
     * @param shopId 店铺ID
     * @param prodId 商品ID
     * @return 数量
     */
    Integer countProdIsSeckillSome(Long shopId, Long prodId);

    /**
     * 根据店铺ID与商品ID获取店铺参与类型为指定商品不参与的折扣活动数量
     * @param shopId 店铺ID
     * @param prodId 商品ID
     * @return 数量
     */
    Integer countProdIsSeckillExcept(Long shopId, Long prodId);

    /**
     * 批量获取店铺下不是折扣商品的商品ID列表
     * @param shopId 店铺ID
     * @return 商品ID列表
     */
    List<Long> getProdIsSeckillExcept(Long shopId);

    /**
     * 根据折扣分页获取商品列表
     * @param page 分页参数
     * @param product 商品参数
     * @param discount 满减满折优惠信息
     * @return 商品列表
     */
    IPage<Product> pageProdByDiscount(PageParam<Product> page, ProductParam product, Discount discount);

    /**
     * 修改满减活动的可用商品列表
     * @param prodIds 商品ID列表
     * @return 可以商品ID列表
     */
    List<Long> updateDiscountProdByIds(List<Long> prodIds);

    /**
     * 获取要被定时任务关闭的满减活动的店铺id
     * @return
     */
    List<Long> listShopIdsWhichWillClose();

    /**
     * 根据店铺id与活动状态获取活动id集合
     * @param shopId
     * @param status
     * @return
     */
    List<Long> listIdByShopIdAndStatus(Long shopId, Integer status);
}
