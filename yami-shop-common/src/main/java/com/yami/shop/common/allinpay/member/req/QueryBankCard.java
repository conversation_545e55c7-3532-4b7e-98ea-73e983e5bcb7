package com.yami.shop.common.allinpay.member.req;

import java.io.Serial;
import java.io.Serializable;
/**
 * 查询绑定银行卡
 *
 * <AUTHOR>
 */
public class QueryBankCard extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "queryBankCard";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public QueryBankCard(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public QueryBankCard() {
    }

    /**
     * 卡号 如果不传就查全部
     */
    private String cardNo;

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }


}
