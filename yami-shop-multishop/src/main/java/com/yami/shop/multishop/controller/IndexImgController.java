package com.yami.shop.multishop.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.IndexImg;
import com.yami.shop.bean.model.Product;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.IndexImgService;
import com.yami.shop.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> on 2018/11/26.
 */
@RestController
@RequestMapping("/admin/indexImg")
@Tag(name = "轮播图接口")
@AllArgsConstructor
public class IndexImgController {

    private final IndexImgService indexImgService;

    private final ProductService productService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('admin:indexImg:page')")
    @Operation(summary = "分页获取轮播图列表" , description = "分页获取轮播图列表")
    public ServerResponseEntity<IPage<IndexImg>> page(IndexImg indexImg, PageParam<IndexImg> page) {
        IPage<IndexImg> indexImgPage = indexImgService.page(page,
                new LambdaQueryWrapper<IndexImg>()
                        .eq(indexImg.getStatus() != null, IndexImg::getStatus, indexImg.getStatus())
                        .eq(IndexImg::getShopId, SecurityUtils.getShopUser().getShopId())
                        .eq(!Objects.isNull(indexImg.getImgType()),IndexImg::getImgType, indexImg.getImgType())
                        .orderByDesc(IndexImg::getSeq)
                        .orderByDesc(IndexImg::getStatus)
                        .orderByAsc(IndexImg::getImgType)
                        );
        return ServerResponseEntity.success(indexImgPage);
    }

    @GetMapping("/info/{imgId}")
    @Operation(summary = "根据轮播图Id获取轮播图" , description = "根据轮播图Id获取轮播图")
    @PreAuthorize("@pms.hasPermission('admin:indexImg:info')")
    public ServerResponseEntity<IndexImg> info(@PathVariable("imgId") Long imgId) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        IndexImg indexImg = indexImgService.getOne(new LambdaQueryWrapper<IndexImg>().eq(IndexImg::getShopId, shopId).eq(IndexImg::getImgId, imgId));
        if (Objects.nonNull(indexImg.getRelation())) {
            Product product = productService.getProductAndLang(indexImg.getRelation());
            if (product !=null) {
                indexImg.setPic(product.getPic());
                indexImg.setProdName(product.getProdName());
            }
        }
        return ServerResponseEntity.success(indexImg);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('admin:indexImg:save')")
    @Operation(summary = "新增轮播图" , description = "新增轮播图")
    public ServerResponseEntity<Void> save(@RequestBody @Valid IndexImg indexImg) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        indexImg.setShopId(shopId);
        indexImg.setUploadTime(new Date());
        long count = indexImgService.count(new LambdaQueryWrapper<IndexImg>()
                .eq(IndexImg::getImgType, indexImg.getImgType())
                .eq(IndexImg::getShopId, shopId)
        );
        if (count >= Constant.MAX_INDEX_IMG_NUM) {
            // 该平台的轮播图已达到最大数量，不能再进行新增操作
            throw new YamiShopBindException("yami.index.img.reached.limit");
        }
        checkProdStatus(indexImg);
        indexImgService.save(indexImg);
        indexImgService.removeIndexImgCacheByShopId(shopId);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('admin:indexImg:update')")
    @Operation(summary = "更新轮播图" , description = "更新轮播图")
    public ServerResponseEntity<Void> update(@RequestBody @Valid IndexImg indexImg) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        indexImg.setShopId(shopId);
        long count = indexImgService.count(new LambdaQueryWrapper<IndexImg>()
                .eq(IndexImg::getImgType, indexImg.getImgType())
                .eq(IndexImg::getShopId, shopId)
        );
        if (count >= Constant.MAX_INDEX_IMG_NUM) {
            // 该店铺的轮播图已达到最大数量，不能再进行新增操作
            throw new YamiShopBindException("yami.index.img.reached.limit");
        }
        checkProdStatus(indexImg);
        indexImgService.saveOrUpdate(indexImg);
        // 移除缓存
        indexImgService.removeIndexImgCacheByShopId(shopId);
        return ServerResponseEntity.success();
    }

    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('admin:indexImg:delete')")
    @Operation(summary = "根据轮播图id删除轮播图" , description = "根据轮播图id删除轮播图")
    public ServerResponseEntity<Void> delete(@RequestBody Long[] ids) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        indexImgService.deleteIndexImgsByIds(ids,shopId);
        indexImgService.removeIndexImgCacheByShopId(shopId);
        return ServerResponseEntity.success();
    }

    private void checkProdStatus(IndexImg indexImg) {
        if (!Objects.equals(indexImg.getType(), 0)) {
            return;
        }
        if (Objects.isNull(indexImg.getRelation())) {
            throw new YamiShopBindException("yami.score.select.num");
        }
        Product product = productService.getById(indexImg.getRelation());
        if (Objects.isNull(product)) {
            throw new YamiShopBindException("yami.product.not.exist");
        }
        if (!Objects.equals(product.getStatus(), 1)) {
            //该商品未上架，请选择别的商品
            throw new YamiShopBindException("yami.product.not.shelf");
        }
    }

}
