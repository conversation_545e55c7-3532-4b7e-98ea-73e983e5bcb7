package com.yami.shop.security.common.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "用户名和密码参数")
public class UsernameAndPasswordDto {

    @NotBlank(message="用户名不能为空")
    @Schema(description = "用户名" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String username;

    @NotBlank(message="密码不能为空")
    @Schema(description = "密码" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String password;

    @NotBlank(message="验证码不能为空")
    @Schema(description = "验证码" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;
}
