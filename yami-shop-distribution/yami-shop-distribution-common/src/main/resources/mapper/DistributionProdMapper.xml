<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.distribution.common.dao.DistributionProdMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.distribution.common.model.DistributionProd">
    <id column="distribution_prod_id" jdbcType="BIGINT" property="distributionProdId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="prod_id" jdbcType="BIGINT" property="prodId" />
    <result column="award_id" jdbcType="DOUBLE" property="awardId" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="default_reward" jdbcType="TINYINT" property="defaultReward" />
  </resultMap>

  <resultMap id="DistributionProd_ProdCut_DistributionAward" type="com.yami.shop.distribution.common.model.DistributionProd">
    <id column="distribution_prod_id" jdbcType="BIGINT" property="distributionProdId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="prod_id" jdbcType="BIGINT" property="prodId" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="default_reward" jdbcType="TINYINT" property="defaultReward" />
    <result column="award_proportion" jdbcType="TINYINT" property="awardProportion" />
    <result column="award_number_set" jdbcType="TINYINT" property="awardNumberSet" />
    <result column="award_numbers" jdbcType="VARCHAR" property="awardNumbers" />
    <result column="parent_award_numbers" jdbcType="VARCHAR" property="parentAwardNumbers" />
    <result column="parent_award_set" jdbcType="TINYINT" property="parentAwardSet" />
    <association javaType="com.yami.shop.bean.model.Product" property="product">
      <id column="prod_id" jdbcType="BIGINT" property="prodId" />
      <result column="prod_name" jdbcType="VARCHAR" property="prodName" />
      <result column="pic" jdbcType="VARCHAR" property="pic" />
      <result column="shop_name" property="shopName"/>
    </association>
  </resultMap>

    <select id="distributionProdsPage" resultMap="DistributionProd_ProdCut_DistributionAward">
        select
            p.prod_id,p.pic,tsd.shop_name,
            dp.distribution_prod_id,
            dp.shop_id,
            dp.award_id,
            dp.state,
            dp.update_time,
            dp.modifier,
            dp.default_reward,
            dp.award_proportion,
            dp.award_number_set,
            dp.award_numbers,
            dp.parent_award_numbers,
            dp.parent_award_set
        from
        tz_distribution_prod dp
        join tz_prod p on p.prod_id=dp.prod_id and p.`status` != -1
        JOIN tz_shop_detail tsd ON p.shop_id = tsd.shop_id
        <if test="prodName!= null and prodName!='' ">
            LEFT JOIN tz_prod_lang pl on pl.prod_id = p.prod_id
        </if>
        <where>
          <if test="distributionProd.shopId != null ">
           and  dp.shop_id=#{distributionProd.shopId}
          </if>
          <if test="distributionProd.state != null ">
            and  dp.state=#{distributionProd.state}
          </if>
          <if test="prodName!= null and prodName!='' ">
            and trim(replace(pl.prod_name,' ','')) like trim(replace(concat('%',#{prodName},'%'),' ',''))
          </if>
        </where>
        ORDER BY dp.update_time DESC
    </select>



    <!--分销商品接口-->
    <resultMap id="DistributionProdPO" type="com.yami.shop.distribution.common.po.DistributionProdPO">
        <result column="shop_id" property="shopId"/>
        <result column="prod_name" property="prodName"/>
        <result column="prod_id" property="prodId"/>
        <result column="prod_type" property="prodType"/>
        <result column="pic" property="pic"/>
        <result column="price" property="price"/>
        <result column="award_proportion" jdbcType="TINYINT" property="awardProportion" />
        <result column="award_number_set" jdbcType="TINYINT" property="awardNumberSet" />
        <result column="award_numbers" jdbcType="VARCHAR" property="awardNumbers" />
        <result column="parent_award_numbers" jdbcType="VARCHAR" property="parentAwardNumbers" />
        <result column="parent_award_set" jdbcType="TINYINT" property="parentAwardSet" />
    </resultMap>

    <select id="distributionProdPoPage" resultMap="DistributionProdPO">
            select
                p.pic,
                p.price,
                dp.default_reward,
                dp.prod_id,
                dp.shop_id,
                dp.award_proportion,
                dp.award_number_set,
                dp.award_numbers,
                dp.award_id,
                dp.parent_award_numbers,
                dp.parent_award_set,
                p.prod_type
            from
            tz_distribution_prod dp
            join tz_prod p on p.prod_id=dp.prod_id
            join tz_prod_lang pl on p.prod_id=pl.prod_id
            join tz_shop_detail sd on dp.shop_id = sd.shop_id
            <if test="sort == 2">
                LEFT JOIN tz_prod_extension pe ON pe.prod_id=p.prod_id
            </if>
            <where>
                dp.state = 1 and p.`status` = 1 and sd.shop_status = 1 and pl.lang = #{lang}
                <if test="prodName != null and prodName!=''">
                    and pl.prod_name like concat('%',#{prodName} ,'%')
                </if>
                <if test="sort == 1">
                    order by p.putaway_time
                </if>
                <if test="sort == 2">
                    order by pe.sold_num
                </if>
                <if test="sort == 3">
                    order by p.price
                </if>
                <if test="orderBy == 0">
                    asc
                </if>
                <if test="orderBy == 1">
                    desc
                </if>
            </where>

    </select>

    <select id="getDistributionProdPoByProdId" resultMap="DistributionProdPO">
        select
            dp.default_reward,
            dp.prod_id,
            dp.shop_id,
            dp.award_proportion,
            dp.award_number_set,
            dp.award_numbers,
            dp.award_id,
            dp.parent_award_numbers,
            dp.parent_award_set
        from
        tz_distribution_prod dp
        where dp.prod_id=#{prodId}
    </select>

    <select id="listByOrderItems" resultType="com.yami.shop.distribution.common.model.DistributionProd">
        select
            dp.default_reward,
            dp.prod_id,
            dp.shop_id,
            dp.award_proportion,
            dp.award_number_set,
            dp.award_numbers,
            dp.award_id,
            dp.parent_award_numbers,
            dp.parent_award_set
        from
        tz_distribution_prod dp
        where dp.state = 1 and dp.prod_id in (
        <foreach collection="orderItems" separator="," item="orderItem">
          #{orderItem.prodId}
        </foreach>
        )
    </select>

    <update id="updateState">
        UPDATE tz_distribution_prod dp SET dp.`state` = #{state} WHERE dp.`distribution_prod_id`= #{distributionProdId}
    </update>
    <select id="getDistributionProdLogPage"
            resultType="com.yami.shop.distribution.common.model.DistributionProdLog">
        SELECT res.*,res.prod_name FROM (
        SELECT dui.income_id,dui.income_amount,dui.create_time,dui.order_number,dui.state,
                dui.income_type, oi.prod_name,
               oi.pic,oi.shop_id,du.nick_name,du.user_mobile,dui.order_item_id,o.create_time as place_time,
                dui.reson
          <if test="distributionProdLog.shopId == null">
              ,sd.shop_name
          </if>
        FROM  tz_distribution_user_income dui
        JOIN tz_order_item oi ON dui.order_item_id = oi.order_item_id
        JOIN tz_order o ON o.order_number = oi.order_number
        JOIN tz_distribution_user du ON du.distribution_user_id = dui.distribution_user_id
        <if test="distributionProdLog.shopId == null">
            JOIN tz_shop_detail sd ON sd.shop_id = oi.shop_id
        </if>
        WHERE dui.income_type IN (1,2)
            AND dui.pay_sys_type = #{paySysType}
        <if test="distributionProdLog.shopId != null">
            AND oi.shop_id = #{distributionProdLog.shopId}
        </if>
        <if test="distributionProdLog.orderNumber != null">
            and trim(replace(oi.order_number,' ','')) like trim(replace(concat('%',#{distributionProdLog.orderNumber},'%'),' ',''))
        </if>
        <if test="distributionProdLog.state != null">
            AND dui.state = #{distributionProdLog.state}
        </if>
        <if test="distributionProdLog.prodName != null and distributionProdLog.prodName != ''">
            and trim(replace(oi.prod_name,' ','')) like trim(replace(concat('%',#{distributionProdLog.prodName},'%'),' ',''))
        </if>
        <if test="distributionProdLog.shopName != null and distributionProdLog.shopName != ''">
            and trim(replace(sd.shop_name,' ','')) like trim(replace(concat('%',#{distributionProdLog.shopName},'%'),' ',''))
        </if>
        <if test="distributionProdLog.nickName != null and distributionProdLog.nickName != ''">
            and trim(replace(du.nick_name,' ','')) like trim(replace(concat('%',#{distributionProdLog.nickName},'%'),' ',''))
        </if>
        <if test="distributionProdLog.userMobile != null and distributionProdLog.userMobile != ''">
            AND du.user_mobile LIKE CONCAT('%',#{distributionProdLog.userMobile},'%')
        </if>

        ) res
        ORDER BY res.create_time DESC, res.order_number
    </select>

</mapper>
