package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;
/**
 * 文件上传返回参数
 * <AUTHOR>
 */
public class FileUploadResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private String code;

    private String msg;

    private String subCode;

    private String subMsg;

    private TokenResp data;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getSubCode() {
        return subCode;
    }

    public void setSubCode(String subCode) {
        this.subCode = subCode;
    }

    public String getSubMsg() {
        return subMsg;
    }

    public void setSubMsg(String subMsg) {
        this.subMsg = subMsg;
    }

    public TokenResp getData() {
        return data;
    }

    public void setData(TokenResp data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "FileUploadResp{" +
                "code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                ", subCode='" + subCode + '\'' +
                ", subMsg='" + subMsg + '\'' +
                ", data=" + data +
                '}';
    }
}
