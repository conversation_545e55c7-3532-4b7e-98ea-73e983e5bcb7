<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yami.shop.groupbuy.common.dao.GroupTeamMapper">

    <resultMap id="groupTeamMap" type="com.yami.shop.groupbuy.common.model.GroupTeam">
        <id column="group_team_id" property="groupTeamId"/>
        <result column="shop_id" property="shopId"/>
        <result column="group_activity_id" property="groupActivityId"/>
        <result column="prod_id" property="prodId"/>
        <result column="join_num" property="joinNum"/>
        <result column="status" property="status"/>
        <result column="total_price" property="totalPrice"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="create_time" property="createTime"/>
        <result column="share_user_id" property="shareUserId"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <resultMap id="ApiGroupTeamDtoMap" type="com.yami.shop.groupbuy.common.api.dto.ApiGroupTeamDto">
        <id column="group_team_id" property="groupTeamId"/>
        <result column="shop_id" property="shopId"/>
        <result column="group_activity_id" property="groupActivityId"/>
        <result column="prod_id" property="groupProdId"/>
        <result column="join_num" property="joinNum"/>
        <result column="group_number" property="groupNumber"/>
        <result column="end_time" property="endTime"/>
        <result column="status" property="status"/>
        <collection property="groupUserDtoList" javaType="list" ofType="com.yami.shop.groupbuy.common.api.dto.ApiGroupUserDto">
            <id column="user_id" jdbcType="VARCHAR" property="userId"/>
            <result column="pic" jdbcType="VARCHAR" property="pic"/>
            <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
            <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
            <result column="identity_type" jdbcType="INTEGER" property="identityType"/>
            <result column="create_time" property="createTime"/>
        </collection>
    </resultMap>
    <resultMap type="com.yami.shop.bean.model.Order" id="orderAndOrderItemMap">
        <id column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="prod_name" jdbcType="VARCHAR" property="prodName"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="total" jdbcType="DECIMAL" property="total"/>
        <result column="actual_total" jdbcType="DECIMAL" property="actualTotal"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="dvy_type" jdbcType="VARCHAR" property="dvyType"/>
        <result column="dvy_id" jdbcType="BIGINT" property="dvyId"/>
        <result column="dvy_flow_id" jdbcType="VARCHAR" property="dvyFlowId"/>
        <result column="freight_amount" jdbcType="DECIMAL" property="freightAmount"/>
        <result column="addr_order_id" jdbcType="BIGINT" property="addrOrderId"/>
        <result column="product_nums" jdbcType="INTEGER" property="productNums"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="dvy_time" jdbcType="TIMESTAMP" property="dvyTime"/>
        <result column="finally_time" jdbcType="TIMESTAMP" property="finallyTime"/>
        <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime"/>
        <result column="is_payed" jdbcType="BIT" property="isPayed"/>
        <result column="delete_status" jdbcType="INTEGER" property="deleteStatus"/>
        <result column="refund_status" jdbcType="INTEGER" property="refundStatus"/>
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="close_type" jdbcType="INTEGER" property="closeType"/>
        <result column="order_mold" jdbcType="INTEGER" property="orderMold"/>
        <result column="is_bind_voucher" jdbcType="INTEGER" property="isBindVoucher"/>
        <collection property="orderItems" ofType="com.yami.shop.bean.model.OrderItem">
            <id column="order_item_id" jdbcType="BIGINT" property="orderItemId"/>
            <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
            <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
            <result column="prod_id" jdbcType="BIGINT" property="prodId"/>
            <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
            <result column="prod_count" jdbcType="INTEGER" property="prodCount"/>
            <result column="prod_name" jdbcType="VARCHAR" property="prodName"/>
            <result column="pic" jdbcType="VARCHAR" property="pic"/>
            <result column="price" jdbcType="DECIMAL" property="price"/>
            <result column="user_id" jdbcType="VARCHAR" property="userId"/>
            <result column="product_total_amount" jdbcType="DECIMAL" property="productTotalAmount"/>
            <result column="rec_time" jdbcType="TIMESTAMP" property="recTime"/>
            <result column="comm_sts" jdbcType="INTEGER" property="commSts"/>
            <result column="distribution_card_no" property="distributionCardNo"/>
            <result column="basket_date" property="basketDate"/>
            <result column="share_reduce" property="shareReduce"/>
            <result column="distribution_amount" property="distributionAmount"/>
            <result column="distribution_parent_amount" property="distributionParentAmount"/>
            <result column="return_money_sts" property="returnMoneySts"/>
            <result column="stock_point_id" property="stockPointId"/>
        </collection>
    </resultMap>
    <select id="getPage" resultType="com.yami.shop.groupbuy.common.dto.GroupTeamDto">
        SELECT gt.group_team_id,gt.join_num,gt.status,gt.group_activity_id,gt.total_price,ga.activity_name,ga.start_time,ga.group_number,ga.shop_id,tp.pic,o.prod_name
        FROM tz_group_team gt
        LEFT JOIN tz_group_activity ga ON gt.group_activity_id = ga.group_activity_id
        LEFT JOIN tz_prod tp ON tp.prod_id = gt.prod_id
        LEFT JOIN tz_group_order go ON go.group_team_id = gt.group_team_id
        LEFT JOIN tz_order o ON o.order_number = go.order_number
        <where>
            <if test="groupTeam.activityName != null and groupTeam.activityName != ''">
                AND ga.activity_name like concat('%',#{groupTeam.activityName},'%')
            </if>
            <if test="groupTeam.shopId != null">
                AND gt.shop_id = #{groupTeam.shopId}
            </if>
        </where>
        GROUP BY gt.group_team_id
        ORDER BY gt.create_time DESC
    </select>

    <select id="getGroupOrderPage" resultType="com.yami.shop.groupbuy.common.dto.GroupOrderDTO">
        SELECT
            gs.act_price AS groupPrice,
            o.actual_total AS pay_price,
            tgo.*
        FROM
            `tz_group_order` tgo
            LEFT JOIN `tz_order` o ON o.`order_number` = tgo.`order_number`
            LEFT JOIN `tz_order_item` oi ON oi.order_number = tgo.order_number
            LEFT JOIN `tz_group_sku` gs ON gs.group_activity_id = tgo.group_activity_id AND gs.sku_id = oi.sku_id
        WHERE
            tgo.`group_team_id` = #{groupOrder.groupTeamId}
    </select>

    <select id="listByJoinGroupAndNowDate" resultMap="ApiGroupTeamDtoMap">
        SELECT
            gt.*,
            ga.group_number AS group_number,
            u.nick_name as nick_name ,
            u.user_id as user_id,
            u.pic as pic,
            go.order_number,
            go.create_time as create_time,
            if(gt.share_user_id = u.user_id, 1, 0) as identity_type
        FROM tz_group_team gt
        INNER JOIN tz_group_activity ga ON gt.group_activity_id = ga.group_activity_id
        join tz_group_order go ON gt.group_team_id = go.group_team_id  AND go.`status` = 1
        INNER JOIN tz_user u ON go.user_id = u.user_id
        WHERE gt.group_activity_id = #{groupActivityId} AND gt.`status` = 1
        AND gt.start_time &lt;= #{nowTime} AND gt.end_time &gt;= #{nowTime}
        ORDER BY (ga.group_number - gt.join_num) ASC, gt.end_time ASC, gt.start_time DESC, go.create_time ASC
        <if test="showSize != null and showSize != 0">
            LIMIT #{showSize}
        </if>
    </select>

    <select id="getApiGroupTeamDtoByTeamId" resultType="com.yami.shop.groupbuy.common.api.dto.ApiGroupTeamDto">
        SELECT
            gt.prod_id as group_prod_id,
            gt.*,
            ga.group_number AS group_number
        FROM tz_group_team gt
        LEFT JOIN tz_group_activity ga ON gt.group_activity_id = ga.group_activity_id
        WHERE gt.group_team_id = #{groupTeamId}
    </select>

    <select id="getJoinGroupTeamHasMaxNum" resultType="com.yami.shop.groupbuy.common.api.dto.ApiJoinGroupTeamDto">
        SELECT
        gt.group_team_id AS join_group_team_id
        FROM tz_group_team gt
        LEFT JOIN tz_group_order go ON gt.group_team_id = go.group_team_id
        WHERE gt.`status` != 3 AND gt.group_activity_id = #{groupActivityId}
        ORDER BY gt.start_time DESC LIMIT 1
    </select>

    <select id="getNotGroupTeamByNowDate" resultType="com.yami.shop.groupbuy.common.model.GroupTeam">
        SELECT
        gt.*,
        ga.has_robot AS has_robot,
        ga.group_number AS group_number
        FROM tz_group_team gt
        LEFT JOIN tz_group_activity ga ON gt.group_activity_id = ga.group_activity_id
        -- 活动结束 待成团、拼团中
        WHERE (gt.`status` IN (0,1) AND ga.`status` <![CDATA[ <> ]]>1 AND ga.end_time &lt; #{nowDate})
        -- 活动正常 团队结束 拼团中、待成团
        OR    (gt.`status` IN (0,1) AND ga.`status` = 1 AND ga.end_time &gt;= #{nowDate} AND gt.end_time &lt; #{nowDate})
    </select>

    <select id="getJoinNotGroupTeamOrder" resultType="com.yami.shop.groupbuy.common.dto.GroupRefundOrder">
        SELECT
        o.*,
        os.pay_no AS order_pay_no,
        os.settlement_id AS settlement_id,
            os.pay_status as pay_status, go.group_order_id as group_order_id
        FROM tz_group_order go
        INNER JOIN tz_order o ON go.order_number = o.order_number
        INNER JOIN tz_order_settlement os ON o.order_number = os.order_number
        WHERE go.group_team_id = #{groupTeamId}
    </select>

    <select id="getOrders" resultMap="orderAndOrderItemMap">
        SELECT o.*,oi.* FROM tz_group_team gt
        JOIN tz_group_order go ON gt.group_team_id = go.group_team_id
        JOIN tz_order o ON go.order_number = o.order_number
        join tz_order_item oi on o.order_number = oi.order_number
        WHERE gt.group_team_id = #{groupTeamId}
        <if test="status != null">
            and go.status = #{status}
        </if>
    </select>

    <select id="getNotGroupTeamsByActivityId" resultType="com.yami.shop.groupbuy.common.model.GroupTeam">
        SELECT
        gt.*,
        ga.has_robot AS has_robot,
        ga.group_number AS group_number
        FROM tz_group_team gt
        LEFT JOIN tz_group_activity ga ON gt.group_activity_id = ga.group_activity_id
        WHERE gt.`status` = 0 OR gt.`status` = 1 AND ga.`status` = 1
        AND  ga.group_activity_id = #{groupActivityId}
    </select>
    <select id="getNotGroupTeamByProdId" resultType="com.yami.shop.groupbuy.common.model.GroupTeam"
            parameterType="java.lang.Long">
        SELECT
        gt.*,
        ga.has_robot AS has_robot,
        ga.group_number AS group_number
        FROM tz_group_team gt
        LEFT JOIN tz_group_activity ga ON gt.group_activity_id = ga.group_activity_id
        WHERE gt.`status` = 0 OR gt.`status` = 1 AND ga.`status` = 1
        AND  gt.prod_id = #{prodId}
    </select>
    <update id="updateGroupTeam">
        update tz_group_team set
        update_time = #{groupTeam.updateTime},
        join_num = #{groupTeam.joinNum},
        total_price = #{groupTeam.totalPrice},
        status = #{groupTeam.status}
        where group_team_id = #{groupTeam.groupTeamId}
        and status = 1 and join_num <![CDATA[ <= ]]>#{groupNumber}
    </update>
</mapper>
