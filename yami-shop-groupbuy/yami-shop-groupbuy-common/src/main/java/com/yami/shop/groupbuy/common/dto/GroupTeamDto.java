/*
 * Copyright (c) 2018-2999 四川众聚易达科技有限公司 All rights reserved.
 *
 * https://www.mall4j.com/
 *
 * 未经允许，不可做商业用途！
 *
 * 版权所有，侵权必究！
*/package com.yami.shop.groupbuy.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 拼团活动状态类型
 *
 * <AUTHOR>
 * @date 2019/8/28 17:43
*/
@Data
public class GroupTeamDto {

    @Schema(description = "拼团团队ID" )
    private Long groupTeamId;

    @Schema(description = "活动id" )
    private Long groupActivityId;

    @Schema(description = "活动名称" )
    private String activityName;

    @Schema(description = "开团时间" )
    private Date startTime;

    @Schema(description = "成团人数" )
    private Integer groupNumber;

    @Schema(description = "已参团人数" )
    private Integer joinNum;

    @Schema(description = "订单总金额" )
    private Double totalPrice;

    @Schema(description = "拼团状态(0:待成团，1:拼团中，2:拼团成功，3:拼团失败)" )
    private Integer status;

    @Schema(description = "店铺Id" )
    private Long shopId;

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "商品图片" )
    private String pic;
}
