package com.yami.shop.distribution.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分销收入类型
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistributionUserIncomeTypeEnum {
    /**
     * 其他奖励
     */
    OTHER(0, "其他"),
    /**
     * 直推奖励
     */
    AWARD_ONE(1, "直推奖励"),
    /**
     * 间推奖励
     */
    AWARD_TWO(2, "间推奖励"),

    /**
     * 邀请奖励
     */
    INVITATION(3, "邀请奖励"),

    /**
     * 平台修改
     */
    PLATFORM(4,"平台修改");

    private final int value;
    private final String desc;
}
