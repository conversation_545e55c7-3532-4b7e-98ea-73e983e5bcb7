package com.yami.shop.common.config;

import lombok.Data;

/**
 * payPal支付 网络钩子常量
 * <AUTHOR>
 */
@Data
public class WebhookConstant {

    public static final String ALL_EVENTS  = "ALL.EVENTS";
    public static final String BILLING_PLAN_ACTIVATED  = "BILLING.PLAN.ACTIVATED";
    public static final String BILLING_PLAN_CREATED  = "BILLING.PLAN.CREATED";
    public static final String BILLING_PLAN_DEACTIVATED  = "BILLING.PLAN.DEACTIVATED";
    public static final String BILLING_PLAN_PRICING_CHANGE_ACTIVATED  = "BILLING.PLAN.PRICING-CHANGE.ACTIVATED";
    public static final String BILLING_PLAN_PRICING_CHANGE_INPROGRESS  = "BILLING.PLAN.PRICING-CHANGE.INPROGRESS";
    public static final String BILLING_PLAN_UPDATED  = "BILLING.PLAN.UPDATED";
    public static final String BILLING_SUBSCRIPTION_ACTIVATED  = "BILLING.SUBSCRIPTION.ACTIVATED";
    public static final String BILLING_SUBSCRIPTION_CANCELLED  = "BILLING.SUBSCRIPTION.CANCELLED";
    public static final String BILLING_SUBSCRIPTION_CREATED = "BILLING.SUBSCRIPTION.CREATED";
    public static final String BILLING_SUBSCRIPTION_EXPIRED = "BILLING.SUBSCRIPTION.EXPIRED";
    public static final String BILLING_SUBSCRIPTION_PAYMENT_FAILED = "BILLING.SUBSCRIPTION.PAYMENT.FAILED";
    public static final String BILLING_SUBSCRIPTION_RE_ACTIVATED = "BILLING.SUBSCRIPTION.RE-ACTIVATED";
    public static final String BILLING_SUBSCRIPTION_RENEWED = "BILLING.SUBSCRIPTION.RENEWED";
    public static final String BILLING_SUBSCRIPTION_SUSPENDED = "BILLING.SUBSCRIPTION.SUSPENDED";
    public static final String BILLING_SUBSCRIPTION_UPDATED = "BILLING.SUBSCRIPTION.UPDATED";
    public static final String CATALOG_PRODUCT_CREATED = "CATALOG.PRODUCT.CREATED";
    public static final String CATALOG_PRODUCT_UPDATED = "CATALOG.PRODUCT.UPDATED";
    public static final String CHECKOUT_CHECKOUT_BUYER_APPROVED = "CHECKOUT.CHECKOUT.BUYER-APPROVED";
    public static final String CHECKOUT_ORDER_APPROVED = "CHECKOUT.ORDER.APPROVED";
    public static final String CHECKOUT_ORDER_COMPLETED = "CHECKOUT.ORDER.COMPLETED";
    public static final String CHECKOUT_ORDER_SAVED = "CHECKOUT.ORDER.SAVED";
    public static final String CHECKOUT_ORDER_VOIDED = "CHECKOUT.ORDER.VOIDED";
    public static final String COMPLIANCE_PROCESS_AGENT_ACTION_INITIATED = "COMPLIANCE.PROCESS.AGENT-ACTION-INITIATED";
    public static final String COMPLIANCE_PROCESS_COMPLETED = "COMPLIANCE.PROCESS.COMPLETED";
    public static final String COMPLIANCE_PROCESS_END_USER_ACTION_REQUIRED = "COMPLIANCE.PROCESS.END-USER-ACTION-REQUIRED";
    public static final String COMPLIANCE_PROCESS_EXEMPTED = "COMPLIANCE.PROCESS.EXEMPTED";
    public static final String COMPLIANCE_PROCESS_FAILED = "COMPLIANCE.PROCESS.FAILED";
    public static final String COMPLIANCE_PROCESS_NOT_APPLIED = "COMPLIANCE.PROCESS.NOT-APPLIED";
    public static final String COMPLIANCE_PROCESS_SYSTEM_ACTION_INITIATED = "COMPLIANCE.PROCESS.SYSTEM-ACTION-INITIATED";
    public static final String DISPUTES_CREATED = "DISPUTES.CREATED";
    public static final String DISPUTES_RESOLVED = "DISPUTES.RESOLVED";
    public static final String DISPUTES_UPDATED = "DISPUTES.UPDATED";
    public static final String CUSTOMER_MANAGED_ACCOUNT_CREATED = "CUSTOMER.MANAGED-ACCOUNT.CREATED";
    public static final String CUSTOMER_MANAGED_ACCOUNT_RISK_ASSESSED = "CUSTOMER.MANAGED-ACCOUNT.RISK-ASSESSED";
    public static final String CUSTOMER_MANAGED_ACCOUNT_UPDATED = "CUSTOMER.MANAGED-ACCOUNT.UPDATED";
    public static final String CUSTOMER_MERCHANT_INTEGRATION_CAPABILITY_UPDATED = "CUSTOMER.MERCHANT-INTEGRATION.CAPABILITY-UPDATED";
    public static final String CUSTOMER_MERCHANT_INTEGRATION_PRODUCT_SUBSCRIPTION_UPDATED = "CUSTOMER.MERCHANT-INTEGRATION.PRODUCT-SUBSCRIPTION-UPDATED";
    public static final String CUSTOMER_MERCHANT_INTEGRATION_SELLER_ALREADY_INTEGRATED = "CUSTOMER.MERCHANT-INTEGRATION.SELLER-ALREADY-INTEGRATED";
    public static final String CUSTOMER_MERCHANT_INTEGRATION_SELLER_CONSENT_GRANTED = "CUSTOMER.MERCHANT-INTEGRATION.SELLER-CONSENT-GRANTED";
    public static final String CUSTOMER_MERCHANT_INTEGRATION_SELLER_EMAIL_CONFIRMED = "CUSTOMER.MERCHANT-INTEGRATION.SELLER-EMAIL-CONFIRMED";
    public static final String CUSTOMER_MERCHANT_INTEGRATION_SELLER_ERROR_BAD_REQUEST = "CUSTOMER.MERCHANT-INTEGRATION.SELLER-ERROR-BAD-REQUEST";
    public static final String CUSTOMER_MERCHANT_INTEGRATION_SELLER_ERROR_INTERNAL_SERVER_ERROR = "CUSTOMER.MERCHANT-INTEGRATION.SELLER-ERROR-INTERNAL-SERVER-ERROR";
    public static final String CUSTOMER_MERCHANT_INTEGRATION_SELLER_ONBOARDING_INITIATED = "CUSTOMER.MERCHANT-INTEGRATION.SELLER-ONBOARDING-INITIATED";
    public static final String CUSTOMER_MERCHANT_INTEGRATION_SELLER_ONBOARDING_STARTED = "CUSTOMER.MERCHANT-INTEGRATION.SELLER-ONBOARDING-STARTED";
    public static final String CUSTOMER_PAYOUT_COMPLETED = "CUSTOMER.PAYOUT.COMPLETED";
    public static final String CUSTOMER_PAYOUT_FAILED = "CUSTOMER.PAYOUT.FAILED";
    public static final String CUSTOMER_USER_PROFILE_PASSWORD_CHANGED = "CUSTOMER.USER-PROFILE.PASSWORD-CHANGED";
    public static final String IDENTITY_AUTHORIZATION_CONSENT_GRANTED = "IDENTITY.AUTHORIZATION-CONSENT.GRANTED";
    public static final String IDENTITY_AUTHORIZATION_CONSENT_REVOKED = "IDENTITY.AUTHORIZATION-CONSENT.REVOKED";
    public static final String INVOICING_INVOICE_CANCELLED = "INVOICING.INVOICE.CANCELLED";
    public static final String INVOICING_INVOICE_CREATED = "INVOICING.INVOICE.CREATED";
    public static final String INVOICING_INVOICE_PAID = "INVOICING.INVOICE.PAID";
    public static final String INVOICING_INVOICE_REFUNDED = "INVOICING.INVOICE.REFUNDED";
    public static final String INVOICING_INVOICE_SCHEDULED = "INVOICING.INVOICE.SCHEDULED";
    public static final String INVOICING_INVOICE_UPDATED = "INVOICING.INVOICE.UPDATED";
    public static final String MERCHANT_ONBOARDING_COMPLETED = "MERCHANT.ONBOARDING.COMPLETED";
    public static final String MERCHANT_PARTNER_CONSENT_REVOKED = "MERCHANT.PARTNER-CONSENT.REVOKED";
    public static final String PAYMENT_NETWORKS_ALTERNATIVE_PAYMENT_COMPLETED = "PAYMENT-NETWORKS.ALTERNATIVE-PAYMENT.COMPLETED";
    public static final String PAYMENT_AUTHORIZATION_CREATED = "PAYMENT.AUTHORIZATION.CREATED";
    public static final String PAYMENT_AUTHORIZATION_VOIDED = "PAYMENT.AUTHORIZATION.VOIDED";
    public static final String PAYMENT_CAPTURE_COMPLETED = "PAYMENT.CAPTURE.COMPLETED";
    public static final String PAYMENT_CAPTURE_DENIED = "PAYMENT.CAPTURE.DENIED";
    public static final String PAYMENT_CAPTURE_PENDING = "PAYMENT.CAPTURE.PENDING";
    public static final String PAYMENT_CAPTURE_REFUNDED = "PAYMENT.CAPTURE.REFUNDED";
    public static final String PAYMENT_CAPTURE_REVERSED = "PAYMENT.CAPTURE.REVERSED";
    public static final String PAYMENT_ORDER_CANCELLED = "PAYMENT.ORDER.CANCELLED";
    public static final String PAYMENT_ORDER_CREATED = "PAYMENT.ORDER.CREATED";
    public static final String PAYMENT_PAYOUTS_ITEM_BLOCKED = "PAYMENT.PAYOUTS-ITEM.BLOCKED";
    public static final String PAYMENT_PAYOUTS_ITEM_CANCELED = "PAYMENT.PAYOUTS-ITEM.CANCELED";
    public static final String PAYMENT_PAYOUTS_ITEM_DENIED = "PAYMENT.PAYOUTS-ITEM.DENIED";
    public static final String PAYMENT_PAYOUTS_ITEM_FAILED = "PAYMENT.PAYOUTS-ITEM.FAILED";
    public static final String PAYMENT_PAYOUTS_ITEM_HELD = "PAYMENT.PAYOUTS-ITEM.HELD";
    public static final String PAYMENT_PAYOUTS_ITEM_REFUNDED = "PAYMENT.PAYOUTS-ITEM.REFUNDED";
    public static final String PAYMENT_PAYOUTS_ITEM_RETURNED = "PAYMENT.PAYOUTS-ITEM.RETURNED";
    public static final String PAYMENT_PAYOUTS_ITEM_SUCCEEDED = "PAYMENT.PAYOUTS-ITEM.SUCCEEDED";
    public static final String PAYMENT_PAYOUTS_ITEM_UNCLAIMED = "PAYMENT.PAYOUTS-ITEM.UNCLAIMED";
    public static final String PAYMENT_PAYOUTSBATCH_DENIED = "PAYMENT.PAYOUTSBATCH.DENIED";
    public static final String PAYMENT_PAYOUTSBATCH_PROCESSING = "PAYMENT.PAYOUTSBATCH.PROCESSING";
    public static final String PAYMENT_PAYOUTSBATCH_SUCCESS = "PAYMENT.PAYOUTSBATCH.SUCCESS";
    public static final String PAYMENT_REFUND_COMPLETED = "PAYMENT.REFUND.COMPLETED";
    public static final String PAYMENT_SALE_COMPLETED = "PAYMENT.SALE.COMPLETED";
    public static final String PAYMENT_SALE_DENIED = "PAYMENT.SALE.DENIED";
    public static final String PAYMENT_SALE_PENDING = "PAYMENT.SALE.PENDING";
    public static final String PAYMENT_SALE_REFUNDED = "PAYMENT.SALE.REFUNDED";
    public static final String PAYMENT_SALE_REVERSED = "PAYMENT.SALE.REVERSED";
    public static final String PAYMENTS_PAYMENT_CREATED = "PAYMENTS.PAYMENT.CREATED";
    public static final String PAYMENT_NETWORKS_INSTRUMENT_LINKED_ACCOUNT_UPDATED = "PAYMENT.NETWORKS.INSTRUMENT.LINKED-ACCOUNT-UPDATED";
    public static final String RISK_DISPUTE_CREATED = "RISK.DISPUTE.CREATED";
    public static final String VAULT_CREDIT_CARD_CREATED = "VAULT.CREDIT-CARD.CREATED";
    public static final String VAULT_CREDIT_CARD_DELETED = "VAULT.CREDIT-CARD.DELETED";
    public static final String VAULT_CREDIT_CARD_UPDATED = "VAULT.CREDIT-CARD.UPDATED";
    public static final String VAULT_PAYMENT_TOKEN_CREATED = "VAULT.PAYMENT-TOKEN.CREATED";
    public static final String VAULT_PAYMENT_TOKEN_DELETED = "VAULT.PAYMENT-TOKEN.DELETED";
    public static final String VAULT_PAYMENT_TOKEN_DELETION_INITIATED = "VAULT.PAYMENT-TOKEN.DELETION-INITIATED";

}
