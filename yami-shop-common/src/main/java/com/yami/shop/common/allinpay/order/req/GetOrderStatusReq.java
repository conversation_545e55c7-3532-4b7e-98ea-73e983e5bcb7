package com.yami.shop.common.allinpay.order.req;

/**
 * 获取订单信息
 *
 * <AUTHOR>
 */
public class GetOrderStatusReq extends AbstractOrderReq {

    public static final String METHOD_NAME = "getOrderStatus";

    /**
     * 商户订单号（支付订单）
     */
    private String bizOrderNo;

    public GetOrderStatusReq() {
    }

    public GetOrderStatusReq(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    @Override
    public String getBizUserId() {
        return bizUserId;
    }

    @Override
    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    @Override
    public String toString() {
        return "GetOrderStatus{" +
                "bizOrderNo='" + bizOrderNo + '\'' +
                '}';
    }
}
