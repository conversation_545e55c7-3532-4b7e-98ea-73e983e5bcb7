package com.yami.shop.common.allinpay.member.req;

import com.yami.shop.common.allinpay.member.constant.IdentityType;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class ResetPayPwd extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "resetPayPwd";

    /**
     * 跳转页面类型
     */
    private Long jumpPageType;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 姓名
     */
    private String name;
    /**
     * 证件类型（目前只支持身份证）
     */
    private Integer identityType = IdentityType.ID_CARD.value();
    /**
     * 身份证号
     */
    private String identityNo;
    /**
     * 跳转返回地址
     */
    private String jumpUrl;
    /**
     * 后台通知地址
     */
    private String backUrl;
    /**
     * 失败跳转地址
     */
    private String errorJumpUrl;

    public Long getJumpPageType() {
        return jumpPageType;
    }

    public void setJumpPageType(Long jumpPageType) {
        this.jumpPageType = jumpPageType;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getIdentityType() {
        return identityType;
    }

    public void setIdentityType(Integer identityType) {
        this.identityType = identityType;
    }

    public String getIdentityNo() {
        return identityNo;
    }

    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public String getErrorJumpUrl() {
        return errorJumpUrl;
    }

    public void setErrorJumpUrl(String errorJumpUrl) {
        this.errorJumpUrl = errorJumpUrl;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    @Override
    public String toString() {
        return "ResetPayPwd{" +
                "jumpPageType='" + jumpPageType + '\'' +
                ", phone='" + phone + '\'' +
                ", name='" + name + '\'' +
                ", identityType=" + identityType +
                ", identityNo='" + identityNo + '\'' +
                ", jumpUrl='" + jumpUrl + '\'' +
                ", backUrl='" + backUrl + '\'' +
                ", errorJumpUrl='" + errorJumpUrl + '\'' +
                ", bizUserId='" + bizUserId + '\'' +
                '}';
    }
}
