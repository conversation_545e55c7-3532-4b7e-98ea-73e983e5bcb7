//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//
package com.yami.shop.common.allinpay.util;

import com.yami.shop.common.config.Constant;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
public class Base64 {
    private static final char[] BASE_64_ENCODE_CHARS = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '+', '/'};
    private static final byte[] BASE_64_DECODE_CHARS = {-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 62, -1, -1, -1, 63, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, -1, -1, -1, -1, -1, -1, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, -1, -1, -1, -1, -1, -1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, -1, -1, -1, -1, -1};

    public static String encode(byte[] data) {
        StringBuilder sb = new StringBuilder(Constant.INITIAL_CAPACITY);
        int len = data.length;
        int i = 0;

        while (i < len) {
            int b1 = data[i++] & 255;
            if (i == len) {
                sb.append(BASE_64_ENCODE_CHARS[b1 >>> 2]);
                sb.append(BASE_64_ENCODE_CHARS[(b1 & 3) << 4]);
                sb.append("==");
                break;
            }

            int b2 = data[i++] & 255;
            int i1 = (b1 & 3) << 4 | (b2 & 240) >>> 4;
            if (i == len) {
                sb.append(BASE_64_ENCODE_CHARS[b1 >>> 2]);
                sb.append(BASE_64_ENCODE_CHARS[i1]);
                sb.append(BASE_64_ENCODE_CHARS[(b2 & 15) << 2]);
                sb.append("=");
                break;
            }

            int b3 = data[i++] & 255;
            sb.append(BASE_64_ENCODE_CHARS[b1 >>> 2]);
            sb.append(BASE_64_ENCODE_CHARS[i1]);
            sb.append(BASE_64_ENCODE_CHARS[(b2 & 15) << 2 | (b3 & 192) >>> 6]);
            sb.append(BASE_64_ENCODE_CHARS[b3 & 63]);
        }

        return sb.toString();
    }

    @SuppressWarnings("DuplicatedCode")
    public static byte[] decode(String str) {
        byte[] data = str.getBytes(StandardCharsets.UTF_8);
        int len = data.length;
        ByteArrayOutputStream buf = new ByteArrayOutputStream(len);
        int i = 0;

        while (i < len) {
            byte b1;
            do {
                b1 = BASE_64_DECODE_CHARS[data[i++]];
            } while (i < len && b1 == -1);

            if (b1 == -1) {
                break;
            }

            byte b2;
            do {
                b2 = BASE_64_DECODE_CHARS[data[i++]];
            } while (i < len && b2 == -1);

            if (b2 == -1) {
                break;
            }

            buf.write(b1 << 2 | (b2 & 48) >>> 4);

            byte b3;
            do {
                b3 = data[i++];
                if (b3 == 61) {
                    return buf.toByteArray();
                }

                b3 = BASE_64_DECODE_CHARS[b3];
            } while (i < len && b3 == -1);

            if (b3 == -1) {
                break;
            }

            buf.write((b2 & 15) << 4 | (b3 & 60) >>> 2);

            byte b4;
            do {
                b4 = data[i++];
                if (b4 == 61) {
                    return buf.toByteArray();
                }

                b4 = BASE_64_DECODE_CHARS[b4];
            } while (i < len && b4 == -1);

            if (b4 == -1) {
                break;
            }

            buf.write((b3 & 3) << 6 | b4);
        }

        return buf.toByteArray();
    }
}
