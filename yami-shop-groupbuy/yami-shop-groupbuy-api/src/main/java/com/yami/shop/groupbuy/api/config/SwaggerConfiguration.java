package com.yami.shop.groupbuy.api.config;


import lombok.AllArgsConstructor;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration("groupbuySwaggerConfiguration")
@AllArgsConstructor
public class SwaggerConfiguration {

    @Bean
    public GroupedOpenApi groupBuyRestApi() {
        return GroupedOpenApi.builder()
                .group("团购活动接口")
                .packagesToScan("com.yami.shop.groupbuy.api.controller")
                .pathsToMatch("/**")
                .build();
    }

}
