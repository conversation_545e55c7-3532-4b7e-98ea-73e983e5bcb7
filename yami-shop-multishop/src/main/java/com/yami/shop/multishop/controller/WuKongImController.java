package com.yami.shop.multishop.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.dto.wukong.*;
import com.yami.shop.bean.enums.SendUserTypeEnum;
import com.yami.shop.bean.model.ImChannel;
import com.yami.shop.bean.model.ShopDetail;
import com.yami.shop.bean.model.User;
import com.yami.shop.bean.vo.wukong.*;
import com.yami.shop.common.enums.SysTypeEnum;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ResponseEnum;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.common.wukongim.constant.WuKongConstant;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.service.ImChannelService;
import com.yami.shop.service.ShopDetailService;
import com.yami.shop.service.UserService;
import com.yami.shop.sys.common.model.ShopEmployee;
import com.yami.shop.sys.common.service.ShopEmployeeService;
import com.yami.shop.wukongim.service.WuKongImService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/shop/wuKongIm")
@Tag(name = "店铺客服接口")
public class WuKongImController {

    private final WuKongImService wuKongImService;
    private final ShopEmployeeService shopEmployeeService;
    private final ShopDetailService shopDetailService;
    private final ImChannelService imChannelService;
    private final UserService userService;

    @PostMapping("/registerOrLogin")
    @Operation(summary = "店铺员工注册或登录到im")
    public ServerResponseEntity<RegisterOrLoginVO> registerOrLogin() {
        Long employeeId = AuthUserContext.getEmployeeId();
        List<String> list = StpUtil.getTokenValueListByLoginId(AuthUserContext.getUid());
        // 使用最开始登录的token
        String token = list.get(0);
        Long shopId = AuthUserContext.getShopId();
        String uid = WuKongConstant.getShopUid(employeeId, shopId);
        // 商家客服支持多开消息盒子窗口
        RegisterOrLoginDTO registerOrLoginDTO = new RegisterOrLoginDTO(uid, token, 1, 0);
        wuKongImService.registerOrLogin(registerOrLoginDTO);

        // 添加订阅到商家的频道，用于窗口多开时，账号下线操作的过程中通知其他使用同一个token窗口的客服都下线
        imChannelService.registerOrUpdateAdminChannel(shopId, employeeId);
        return ServerResponseEntity.success(new RegisterOrLoginVO(uid, token));
    }

    @PostMapping("/createChannel")
    @Operation(summary = "用户与商家创建频道", description = "商家首次发消息时调用")
    @Parameter(name = "userId", description = "用户id", required = true)
    public ServerResponseEntity<ChannelVO> createChannel(@RequestParam("userId") String userId) {
        Long shopId = AuthUserContext.getShopId();
        Long employeeId = AuthUserContext.getEmployeeId();
        // 频道id，用户id+店铺id
        String channelId = WuKongConstant.getChannelId(userId, shopId);
        List<String> subscribers = new ArrayList<>();
        subscribers.add(WuKongConstant.USER + userId);
        subscribers.add(WuKongConstant.getShopUid(employeeId, shopId));
        ChannelDTO channelDTO = new ChannelDTO(channelId, WuKongConstant.GROUP_CHAT, 0, subscribers);
        // 组装结果
        ChannelVO channelVO = getChannelVO(userId, channelId);
        // 创建频道
        ImChannel imChannel = new ImChannel(channelId, shopId, userId, employeeId);
        imChannelService.createChannel(imChannel, channelDTO);
        return ServerResponseEntity.success(channelVO);
    }

    private ChannelVO getChannelVO(String userId, String channelId) {
        ChannelVO channelVO = new ChannelVO(channelId);
        channelVO.setUid(WuKongConstant.USER + userId);
        User user = userService.getUserByUserId(userId);
        if (Objects.isNull(user)) {
            // 用户已注销
            throw new YamiShopBindException("yami.wukong.im.exception.userLogout");
        }
        channelVO.setNickName(user.getNickName());
        channelVO.setPic(user.getPic());
        return channelVO;
    }

    @GetMapping("/listTransfer")
    @Operation(summary = "获取可转接客服列表")
    @PreAuthorize("@pms.hasPermission('shop:im:listTransfer')")
    public ServerResponseEntity<List<SubscriberVO>> listTransfer() {
        Long employeeId = AuthUserContext.getEmployeeId();
        Long shopId = AuthUserContext.getShopId();
        List<ShopEmployee> shopEmployeeList = shopEmployeeService.listEnableEmployee(shopId);
        List<Long> shopEmployeeIds = shopEmployeeList.stream().map(ShopEmployee::getEmployeeId).collect(Collectors.toList());
        shopEmployeeIds.remove(employeeId);
        if (CollUtil.isEmpty(shopEmployeeIds)) {
            return ServerResponseEntity.success(new ArrayList<>());
        }
        List<Long> onlineShopEmployeeIds = wuKongImService.listOnlineManagerIds(shopEmployeeIds, shopId);
        if (CollUtil.isEmpty(onlineShopEmployeeIds)) {
            return ServerResponseEntity.success(new ArrayList<>());
        }
        Map<Long, String> employeeMap = shopEmployeeList.stream().collect(Collectors.toMap(ShopEmployee::getEmployeeId, ShopEmployee::getNickname));
        ShopDetail shopDetail = shopDetailService.getShopDetailByShopId(shopId);
        List<SubscriberVO> res = new ArrayList<>(onlineShopEmployeeIds.size());
        for (Long onlineShopEmployeeId : onlineShopEmployeeIds) {
            String nickName = employeeMap.get(onlineShopEmployeeId);
            SubscriberVO subscriberVO = new SubscriberVO(WuKongConstant.getShopUid(onlineShopEmployeeId, shopId), nickName, shopDetail.getShopLogo());
            res.add(subscriberVO);
        }
        return ServerResponseEntity.success(res);
    }

    @PostMapping("/transfer")
    @Operation(summary = "转接客服")
    @Parameters({@Parameter(name = "uid", description = "转接客服的uid", required = true),
            @Parameter(name = "channelId", description = "频道id", required = true)})
    @PreAuthorize("@pms.hasPermission('shop:im:transfer')")
    public ServerResponseEntity<Void> transfer(@RequestParam("uid") String uid, @RequestParam("channelId") String channelId) {
        Long employeeId = WuKongConstant.getEmployeeId(uid);
        Long shopId = AuthUserContext.getShopId();
        if (!Objects.equals(shopId, WuKongConstant.getShopId(channelId))) {
            throw new YamiShopBindException(ResponseEnum.UNAUTHORIZED);
        }
        ShopEmployee shopEmployee = checkAndGetEmployee(employeeId, shopId);
        List<Long> onlineManagerIds = wuKongImService.listOnlineManagerIds(Collections.singletonList(employeeId), shopId);
        if (CollUtil.isEmpty(onlineManagerIds)) {
            // 客服当前不在线，请刷新页面重试
            throw new YamiShopBindException("yami.wukong.im.exception.serviceOffline");
        }
        ImChannel imChannel = imChannelService.getByChannelId(channelId);
        imChannel.setEmployeeId(employeeId);
        imChannelService.updateChannel(imChannel, uid, shopEmployee.getNickname());
        return ServerResponseEntity.success();
    }

    @GetMapping("/conversation/sync/page")
    @Operation(summary = "分页获取用户最近会话列表")
    @PreAuthorize("@pms.hasPermission('shop:im:listConversation')")
    public ServerResponseEntity<IPage<ConversationSyncVO>> conversationSyncPage(PageParam<ConversationSyncVO> pageParam, ConversationSyncDTO conversationSyncDTO) {
        String uid = WuKongConstant.getShopUid(AuthUserContext.getEmployeeId(), AuthUserContext.getShopId());
        conversationSyncDTO.setUid(uid);
        conversationSyncDTO.setMsg_count(1);
        List<ConversationSyncVO> conversationSyncList = wuKongImService.conversationSync(conversationSyncDTO);
        IPage<ConversationSyncVO> page = new PageParam<>();
        page.setTotal(conversationSyncList.size());
        page.setSize(pageParam.getSize());
        page.setCurrent(pageParam.getCurrent());
        long totalPages = page.getTotal() % pageParam.getSize() == 0 ?
                page.getTotal() / pageParam.getSize() : (page.getTotal() / pageParam.getSize() + 1);
        page.setPages(totalPages);
        page.setRecords(new ArrayList<>());
        if (CollUtil.isNotEmpty(conversationSyncList)) {
            int start = Math.min((int)(pageParam.getSize() * (pageParam.getCurrent() - 1)), conversationSyncList.size());
            int end = Math.min(start + (int)pageParam.getSize(), conversationSyncList.size());
            List<ConversationSyncVO> subList = new ArrayList<>();
            if (start < end) {
                subList = conversationSyncList.subList(start, end);
            }
            if (CollUtil.isNotEmpty(subList)) {
                Set<String> userIds = subList.stream().map(con -> WuKongConstant.getUserId(con.getChannelId())).collect(Collectors.toSet());
                List<User> userList = userService.getUserByUserIds(new ArrayList<>(userIds));
                Map<String, User> userMap = userList.stream().collect(Collectors.toMap(User::getUserId, u -> u));
                for (ConversationSyncVO conversationSyncVO : subList) {
                    String userId = WuKongConstant.getUserId(conversationSyncVO.getChannelId());
                    conversationSyncVO.setUserId(userId);
                    conversationSyncVO.setIsDestroy(0);
                    User user = userMap.get(userId);
                    if (Objects.isNull(user)) {
                        conversationSyncVO.setNickName("用户已注销");
                        conversationSyncVO.setIsDestroy(1);
                        continue;
                    }
                    conversationSyncVO.setNickName(user.getNickName());
                    conversationSyncVO.setPic(user.getPic());
                }
            }
            page.setRecords(subList);
        }
        return ServerResponseEntity.success(page);
    }

    @PostMapping("/conversations/setUnread")
    @Operation(summary = "标记频道为已读", description = "点进频道时请求")
    public ServerResponseEntity<Void> setUnread(@RequestBody @Valid SetUnreadDTO setUnreadDTO) {
        if (!Objects.equals(AuthUserContext.getShopId(), WuKongConstant.getShopId(setUnreadDTO.getChannel_id()))) {
            throw new YamiShopBindException(ResponseEnum.UNAUTHORIZED);
        }
        if (Objects.isNull(setUnreadDTO.getMaxSeq())) {
            // 已读的最大消息序号不能为空
            throw new YamiShopBindException("yami.wukong.im.exception.maxSeqNull");
        }
        String uid = WuKongConstant.getShopUid(AuthUserContext.getEmployeeId(), AuthUserContext.getShopId());
        setUnreadDTO.setUid(uid);
        setUnreadDTO.setUnread(0);
        setUnreadDTO.setChannel_type(WuKongConstant.GROUP_CHAT);
        imChannelService.setUnread(setUnreadDTO, SysTypeEnum.MULTISHOP.value());
        return ServerResponseEntity.success();
    }

    @PostMapping("/updateAuto")
    @Operation(summary = "更新自动回复")
    public ServerResponseEntity<Void> updateAuto(@RequestParam("channelId") String channelId) {
        if (!Objects.equals(AuthUserContext.getShopId(), WuKongConstant.getShopId(channelId))) {
            throw new YamiShopBindException(ResponseEnum.UNAUTHORIZED);
        }
        imChannelService.updateAuto(channelId, 0, null, null);
        return ServerResponseEntity.success();
    }

    @PostMapping("/channel/messageSync")
    @Operation(summary = "获取用户频道消息列表")
    @PreAuthorize("@pms.hasPermission('shop:im:listMessage')")
    public ServerResponseEntity<MessageSyncVO> messageSync(@RequestBody @Valid MessageSyncDTO messageSyncDTO) {
        if (!Objects.equals(AuthUserContext.getShopId(), WuKongConstant.getShopId(messageSyncDTO.getChannel_id()))) {
            throw new YamiShopBindException(ResponseEnum.UNAUTHORIZED);
        }
        String uid = WuKongConstant.getShopUid(AuthUserContext.getEmployeeId(), AuthUserContext.getShopId());
        messageSyncDTO.setLogin_uid(uid);
        messageSyncDTO.setChannel_type(WuKongConstant.GROUP_CHAT);
        MessageSyncVO messageSyncVO = wuKongImService.messageSync(messageSyncDTO);
        return ServerResponseEntity.success(messageSyncVO);
    }

    @GetMapping("/listChannelSubcribers")
    @Operation(summary = "获取用户频道订阅者信息")
    public ServerResponseEntity<ImChannelVO> listChannelSubcribers(@RequestParam("channelId") String channelId) {
        Long shopId = WuKongConstant.getShopId(channelId);
        if (!Objects.equals(AuthUserContext.getShopId(), shopId)) {
            throw new YamiShopBindException(ResponseEnum.UNAUTHORIZED);
        }
        ImChannel imChannel = imChannelService.getByChannelId(channelId);
        if (Objects.isNull(imChannel)) {
            // 频道不存在
            throw new YamiShopBindException("yami.wukong.im.exception.channelNotExist");
        }
        String userId = imChannel.getUserId();
        if (!Objects.equals(imChannel.getShopId(), shopId)) {
            // 当前频道不属于你
            throw new YamiShopBindException("yami.wukong.im.exception.notBelongYou");
        }
        ImChannelVO imChannelVO = new ImChannelVO();
        imChannelVO.setChannelId(imChannel.getChannelId());
        imChannelVO.setShopId(shopId);
        List<Long> subscribers = Arrays.stream(imChannel.getSubscribers().split(StrUtil.COMMA))
                .map(Long::parseLong).sorted().collect(Collectors.toList());
        List<SubscriberVO> result = new ArrayList<>(subscribers.size());
        List<Long> listOnlineManagerIds = wuKongImService.listOnlineManagerIds(subscribers, shopId);
        ShopDetail shopDetail = shopDetailService.getShopDetailByShopId(shopId);
        imChannelVO.setShopName(shopDetail.getShopName());
        for (Long subscriber : subscribers) {
            ShopEmployee shopEmployee = shopEmployeeService.getShopEmployeeById(subscriber);
            if (Objects.isNull(shopEmployee)) {
                continue;
            }
            SubscriberVO subscriberVO = new SubscriberVO();
            subscriberVO.setShopId(shopId);
            subscriberVO.setUid(WuKongConstant.getShopUid(subscriber, shopId));
            subscriberVO.setPic(shopDetail.getShopLogo());
            subscriberVO.setIsWhiteListUser(Objects.equals(imChannel.getEmployeeId(), subscriber) ? 1 : 0);
            subscriberVO.setIsOnline(listOnlineManagerIds.contains(subscriber) ? 1 : 0);
            subscriberVO.setSendUserType(SendUserTypeEnum.MULTISHOP.value());
            subscriberVO.setNickName(shopEmployee.getNickname());
            subscriberVO.setStatus(shopEmployee.getStatus());
            result.add(subscriberVO);
        }
        // 组装用户信息
        User user = userService.getUserByUserId(userId);
        if (Objects.isNull(user)) {
            user = new User();
            user.setNickName("用户已注销");
        }
        String uid = WuKongConstant.USER + userId;
        Integer userOnline = wuKongImService.getUserOnline(uid) ? 1 : 0;
        SubscriberVO subscriberVO = new SubscriberVO(uid, user.getNickName(), user.getPic(), 1, userOnline, SendUserTypeEnum.ORDINARY.value());
        subscriberVO.setUserId(userId);
        result.add(subscriberVO);
        imChannelVO.setSubscribers(result);
        return ServerResponseEntity.success(imChannelVO);
    }

    @PostMapping("/getShopChannelInfo")
    @Operation(summary = "获取用户与商家的频道信息")
    @PreAuthorize("@pms.hasPermission('shop:im:getInfo')")
    @Parameter(name = "userId", description = "用户id", required = true)
    public ServerResponseEntity<ChannelVO> getShopChannelInfo(@RequestParam(value = "userId") String userId) {
        Long shopId = AuthUserContext.getShopId();
        ImChannel imChannel = imChannelService.getByChannelId(WuKongConstant.getChannelId(userId, shopId));
        if (Objects.isNull(imChannel)) {
            return ServerResponseEntity.success();
        }
        ChannelVO channelVO = new ChannelVO();
        channelVO.setIsWhiteUser(Objects.equals(imChannel.getEmployeeId(), AuthUserContext.getEmployeeId())? 1 : 0);
        ShopDetail shopDetail = shopDetailService.getShopDetailByShopId(shopId);
        channelVO.setShopLogo(shopDetail.getShopLogo());
        channelVO.setShopName(shopDetail.getShopName());
        ShopEmployee shopEmployee = shopEmployeeService.getShopEmployeeById(imChannel.getEmployeeId());
        channelVO.setEmployeeName(shopEmployee.getNickname());
        User user = userService.getUserByUserId(userId);
        if (Objects.isNull(user)) {
            channelVO.setNickName("用户已注销");
            channelVO.setIsDestroy(1);
            channelVO.setUserIsOnline(0);
        } else {
            channelVO.setNickName(user.getNickName());
            channelVO.setPic(user.getPic());
            channelVO.setIsDestroy(0);
            Boolean userOnline = wuKongImService.getUserOnline(WuKongConstant.USER + userId);
            channelVO.setUserIsOnline(userOnline ? 1 : 0);
        }
        channelVO.setImChannel(imChannel);
        return ServerResponseEntity.success(channelVO);
    }

    private ShopEmployee checkAndGetEmployee(Long employeeId, Long shopId) {
        ShopEmployee shopEmployee = shopEmployeeService.getShopEmployeeById(employeeId);
        if (Objects.isNull(shopEmployee)) {
            // 该账号不存在
            throw new YamiShopBindException("yami.phone.number.not.exists");
        }
        if (!Objects.equals(shopEmployee.getShopId(), shopId)) {
            throw new YamiShopBindException(ResponseEnum.UNAUTHORIZED);
        }
        if (Objects.equals(shopEmployee.getStatus(), 0)) {
            // 用户已禁用
            throw new YamiShopBindException("yami.wukong.im.exception.userForbid");
        }
        return shopEmployee;
    }

}
