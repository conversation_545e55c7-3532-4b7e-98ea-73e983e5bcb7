package com.yami.shop.platform.controller;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.enums.DeliveryType;
import com.yami.shop.bean.model.Order;
import com.yami.shop.bean.model.OrderItem;
import com.yami.shop.bean.model.Station;
import com.yami.shop.bean.model.UserAddrOrder;
import com.yami.shop.bean.param.OrderParam;
import com.yami.shop.bean.param.OrderPayParam;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2018/09/15.
 */
@RestController
@RequestMapping("/platform/order")
@AllArgsConstructor
@Tag(name = "订单")
public class OrderController {

    private final OrderService orderService;
    private final OrderItemService orderItemService;
    private final UserAddrOrderService userAddrOrderService;
    private final StatisticsOrderService statisticsOrderService;
    private final OrderExcelService orderExcelService;
    private final StationService stationService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('platform:order:page')")
    @Operation(summary = "分页获取" , description = "分页获取")
    public ServerResponseEntity<IPage<Order>> page(OrderParam orderParam, PageParam<Order> page) {
        IPage<Order> orderPage = orderService.pageOrdersDetailByOrderParam(page, orderParam);
        if (CollUtil.isNotEmpty(orderPage.getRecords())) {
            Set<Long> stationSet = orderPage.getRecords().stream().filter(order -> DeliveryType.isStation(order.getDvyType())).map(Order::getDvyId).collect(Collectors.toSet());
            if (CollUtil.isEmpty(stationSet)) {
                return ServerResponseEntity.success(orderPage);
            }
            List<Station> stations = stationService.listByIds(stationSet);
            Map<Long, String> stationMap = stations.stream()
                    .collect(Collectors.toMap(Station::getStationId, Station::getStationName));
            for (Order order : orderPage.getRecords()) {
                if (DeliveryType.isStation(order.getDvyType())) {
                    order.setStationName(stationMap.get(order.getDvyId()));
                }
            }
        }
        return ServerResponseEntity.success(orderPage);
    }

    @GetMapping("/orderPayByShopId")
    @Operation(summary = "根据商家id获取支付信息" , description = "根据商家id获取支付信息")
    @Parameters(value = {
            @Parameter(name = "startTime", description = "开始时间" ),
            @Parameter(name = "endTime", description = "结束时间" )
    })
    @PreAuthorize("@pms.hasPermission('platform:order:payInfo')")
    public ServerResponseEntity<OrderPayParam> orderPayByShopId(@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")@RequestParam("startTime") Date startTime,
                                                          @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")@RequestParam("endTime") Date endTime) {
        OrderPayParam actualTotal = statisticsOrderService.getPayUserCountByshopId(null,startTime,endTime);
        return ServerResponseEntity.success(actualTotal);
    }

    @GetMapping("/orderInfo/{orderNumber}")
    @PreAuthorize("@pms.hasPermission('platform:order:info')")
    @Operation(summary = "获取信息" , description = "获取信息")
    @Parameter(name = "orderNumber", description = "订单编号" )
    public ServerResponseEntity<Order> info(@PathVariable("orderNumber") String orderNumber) {

        Order order = orderService.getOne(new LambdaUpdateWrapper<Order>().eq(Order::getOrderNumber, orderNumber));
        if (order == null) {
            // 未找到所在的订单
            throw new YamiShopBindException("yami.order.no.exist");
        }
        UserAddrOrder userAddrOrder = userAddrOrderService.getById(order.getAddrOrderId());
        order.setUserAddrOrder(userAddrOrder);

        List<OrderItem> orderItems = orderItemService.getOrderItemsByOrderNumber(orderNumber,true);
        order.setOrderItems(orderItems);
        return ServerResponseEntity.success(order);
    }

    @GetMapping("/soldExcel")
    @PreAuthorize("@pms.hasPermission('platform:order:exportExcel')")
    @Operation(summary = "导出已销售订单" , description = "导出已销售订单")
    public void soldExcel(OrderParam orderParam, HttpServletResponse response) {
        orderExcelService.soldExcel(orderParam,response);
    }

    @GetMapping("/getOrderByUserId")
    @Operation(summary = "分页获取用户订单列表" , description = "分页获取用户订单列表")
    @Parameter(name = "userId", description = "用户id" )
    @PreAuthorize("@pms.hasPermission('platform:order:pageByUserId')")
    public ServerResponseEntity<IPage<Order>> getOrderByUserId(PageParam<Order> page, String userId){
        IPage<Order> pages = orderService.pageByUserId(page,userId);
        return ServerResponseEntity.success(pages);
    }
}
