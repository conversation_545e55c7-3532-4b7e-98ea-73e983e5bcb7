package com.yami.shop.platform.config;

import cn.hutool.core.util.StrUtil;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import com.yami.shop.common.util.IpHelper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * xxl-job config
 *
 * <AUTHOR>
 * @date 2021/1/18
 */
@Configuration
@RequiredArgsConstructor
public class XxlJobConfig {
    private final Logger logger = LoggerFactory.getLogger(XxlJobConfig.class);

    @Value("${xxl-job.admin.addresses}")
    private String adminAddresses;

    @Value("${xxl-job.accessToken}")
    private String accessToken;

    @Value("${xxl-job.logPath}")
    private String logPath;

    @Value("${server.port}")
    private int port;

    @Value("${xxl-job.local.ip:}")
    private String localIp;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {

        logger.info(">>>>>>>>>>> xxl-job config init.");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppname("mall4j-bbc");
        if (StrUtil.isNotBlank(localIp)) {
            xxlJobSpringExecutor.setIp(localIp);
        } else {
            xxlJobSpringExecutor.setIp(IpHelper.getLocalIp());
        }
        xxlJobSpringExecutor.setPort(port + 1000);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setLogPath(logPath);
        xxlJobSpringExecutor.setLogRetentionDays(3);
        return xxlJobSpringExecutor;
    }
}
