package com.yami.shop.platform.controller;

import cn.hutool.core.util.PhoneUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.NotifyLog;
import com.yami.shop.bean.param.OrderParam;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.NotifyLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;


/**
 *
 *
 * <AUTHOR>
 * @date 2020-08-10 15:20:53
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/platform/notifyLog" )
@Tag(name = "消息记录")
public class NotifyLogController {

    private final NotifyLogService notifyLogService;



    @GetMapping("/page" )
    @Operation(summary = "分页查询" , description = "分页查询")
    @PreAuthorize("@pms.hasPermission('platform:notifyLog:page')")
    public ServerResponseEntity<IPage<NotifyLog>> getNotifyLogPage(PageParam<NotifyLog> page, OrderParam orderParam) {
        IPage<NotifyLog> notifyLogPage = notifyLogService.pageByParam(page, orderParam);
        for (NotifyLog record : notifyLogPage.getRecords()) {
            if (Objects.nonNull(record.getUserMobile())) {
                record.setUserMobile(PhoneUtil.hideBetween(record.getUserMobile()).toString());
            }
        }
        return ServerResponseEntity.success(notifyLogPage);
    }


}
