package com.yami.shop.user.multishop.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.param.CustomerManagerParam;
import com.yami.shop.bean.param.UserExcelParam;
import com.yami.shop.bean.param.UserManagerParam;
import com.yami.shop.bean.param.UserManagerReqParam;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.service.UserService;
import com.yami.shop.user.common.service.UserMemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user/user")
@Tag(name = "会员管理")
@AllArgsConstructor
public class UserMemberController {

    private final UserService userService;
    private final UserMemberService userMemberService;

    @GetMapping("/pageUser")
    @Operation(summary = "分页获取会员列表", description =  "分页获取会员列表")
    @PreAuthorize("@pms.hasPermission('user:userMember:pageUser')")
    public ServerResponseEntity<IPage<UserManagerParam>> pageUser(PageParam<UserManagerParam> page, UserManagerReqParam reqParam) {
        Long shopId = AuthUserContext.getShopId();
        reqParam.setShopId(shopId);
        IPage<UserManagerParam> userManagerPage = userService.pageUserByMultiShop(page, reqParam);
        return ServerResponseEntity.success(userManagerPage);
    }

    @GetMapping("/pageCustomer")
    @Operation(summary = "分页获取客户列表", description = "分页获取客户列表")
    @PreAuthorize("@pms.hasPermission('user:userMember:pageCustomer')")
    public ServerResponseEntity<IPage<CustomerManagerParam>> pageCustomer(PageParam<CustomerManagerParam> page, CustomerManagerParam reqParam) {
        Long shopId = AuthUserContext.getShopId();
        reqParam.setShopId(shopId);
        IPage<CustomerManagerParam> customerPage = userService.pageCustomerByMultiShop(page, reqParam);
        return ServerResponseEntity.success(customerPage);
    }

    @GetMapping("/exportUser")
    @Operation(summary = "导出用户信息", description =  "导出用户信息")
    @PreAuthorize("@pms.hasPermission('user:userMember:exportUser')")
    public void exportUser(UserManagerReqParam userReqParam, HttpServletResponse response) {
        Long shopId = AuthUserContext.getShopId();
        userReqParam.setShopId(shopId);
        userMemberService.exportUser(userReqParam, response);
    }

    @GetMapping("/exportCustomer")
    @Operation(summary = "导出客户信息", description = "导出客户信息")
    @PreAuthorize("@pms.hasPermission('user:userMember:exportCustomer')")
    public void exportCustomer(CustomerManagerParam customerParam, HttpServletResponse response) {
        Long brandId = AuthUserContext.getShopId();
        customerParam.setShopId(brandId);
        userMemberService.exportCustomer(customerParam, response);
    }

    @GetMapping("/downloadTemplate")
    @Operation(summary = "下载导入模板（0:客户, 1:会员）", description = "下载导入模板（0:客户, 1:会员）")
    @PreAuthorize("@pms.hasPermission('user:userMember:downloadTemplate')")
    public void downloadTemplate(Integer templateType, HttpServletResponse response) {
        userMemberService.downloadTemplate(templateType, response);
    }

    @PostMapping("/importUserExcel")
    @Operation(summary = "导入会员信息", description =  "导入会员信息")
    @PreAuthorize("@pms.hasPermission('user:userMember:importUser')")
    public ServerResponseEntity<UserExcelParam> importUserExcel(@RequestParam("excelFile") MultipartFile excelFile) throws Exception {
        if (Objects.isNull(excelFile)) {
            // 网络繁忙，请稍后重试
            throw new YamiShopBindException("yami.network.busy");
        }
        Long shopId = AuthUserContext.getShopId();
        UserExcelParam userExcelParam = userMemberService.importUser(shopId, excelFile);
        return ServerResponseEntity.success(userExcelParam);
    }

    @PostMapping("/importCustomerExcel")
    @Operation(summary = "导入客户信息", description = "导入客户信息")
    @PreAuthorize("@pms.hasPermission('user:userMember:importCustomer')")
    public ServerResponseEntity<UserExcelParam> importCustomerExcel(@RequestParam("excelFile") MultipartFile excelFile) throws Exception {
        if (Objects.isNull(excelFile)) {
            // 网络繁忙，请稍后重试
            throw new YamiShopBindException("yami.network.busy");
        }
        Long shopId = AuthUserContext.getShopId();
        UserExcelParam userExcelParam = userMemberService.importCustomer(shopId, excelFile);
        return ServerResponseEntity.success(userExcelParam);
    }


}
