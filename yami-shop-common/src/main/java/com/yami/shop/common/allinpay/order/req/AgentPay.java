package com.yami.shop.common.allinpay.order.req;

import java.util.List;

/**
 * 单笔托管代付列表
 *
 * <AUTHOR>
 */
public class AgentPay extends AbstractOrderReq {
    /**
     * 商户订单号（支付订单）
     * 全局唯一，不可重复
     * 不可包含“|”字符
     */
    private String bizOrderNo;
    /**
     * 源托管代收订单付款信息
     */
    private List<CollectPay> collectPayList;
    /**
     * 收款方的账户集编号
     */
    private String accountSetNo;
    /**
     * 后台通知地址
     */
    private String backUrl;
    /**
     * 金额，单位：分
     */
    private Long amount;
    /**
     * 手续费，单位：分；内扣，如果不存在，则填0；如amount为100，fee为2，实际到账金额为98
     * 到账到平台余额
     */
    private Long fee;
    /**
     * 分账规则
     * 内扣.
     * 支持分账到会员或者平台账户。
     * 分账规则：分账层级数<=3，分账总会员数<=10
     */
    private List<SplitRule> splitRuleList;
    /**
     * 单个代付需填写
     * 业务码,代收消费金[3001] + 代付购买金[4001]
     */
    private String tradeCode;
    /**
     * 单个代付需填写
     * 方法名称
     */
    public static final String METHOD_NAME = "signalAgentPay";


    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public AgentPay() {
    }

    public AgentPay(String bizOrderNo, List<CollectPay> collectPay, String bizUserId,
                    String accountSetNo, String backUrl, Long amount, Long fee, String tradeCode) {
        this.bizOrderNo = bizOrderNo;
        this.collectPayList = collectPay;
        this.bizUserId = bizUserId;
        this.accountSetNo = accountSetNo;
        this.backUrl = backUrl;
        this.amount = amount;
        this.fee = fee;
        this.tradeCode = tradeCode;
    }

    public List<SplitRule> getSplitRuleList() {
        return splitRuleList;
    }

    public void setSplitRuleList(List<SplitRule> splitRuleList) {
        this.splitRuleList = splitRuleList;
    }

    public String getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public List<CollectPay> getCollectPayList() {
        return collectPayList;
    }

    public void setCollectPayList(List<CollectPay> collectPay) {
        this.collectPayList = collectPay;
    }

    public String getAccountSetNo() {
        return accountSetNo;
    }

    public void setAccountSetNo(String accountSetNo) {
        this.accountSetNo = accountSetNo;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Long getFee() {
        return fee;
    }

    public void setFee(Long fee) {
        this.fee = fee;
    }

    /**
     * 源托管代收订单付款信息
     */
    public static class CollectPay {
        /**
         * 相关代收交易的“商户订单号”
         */
        private String bizOrderNo;
        /**
         * 相关代收订单,云商通订单号
         */
        private String orderNo;
        /**
         * 非必填，一年前的代收订单必须上送，yyyy-MM-dd 精确到天
         */
        private String bizOrderCreateDate;
        /**
         * 金额，单位：分；部分代付时，可以少于或等于托管代收订单金额
         */
        private Long amount;

        public String getBizOrderNo() {
            return bizOrderNo;
        }

        public void setBizOrderNo(String bizOrderNo) {
            this.bizOrderNo = bizOrderNo;
        }

        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public String getBizOrderCreateDate() {
            return bizOrderCreateDate;
        }

        public void setBizOrderCreateDate(String bizOrderCreateDate) {
            this.bizOrderCreateDate = bizOrderCreateDate;
        }

        public Long getAmount() {
            return amount;
        }

        public void setAmount(Long amount) {
            this.amount = amount;
        }

        @Override
        public String toString() {
            return "CollectPayList{" +
                    "bizOrderNo='" + bizOrderNo + '\'' +
                    ", orderNo='" + orderNo + '\'' +
                    ", bizOrderCreateDate='" + bizOrderCreateDate + '\'' +
                    ", amount='" + amount + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "AgentPay{" +
                "bizOrderNo='" + bizOrderNo + '\'' +
                ", collectPay=" + collectPayList +
                ", accountSetNo='" + accountSetNo + '\'' +
                ", backUrl='" + backUrl + '\'' +
                ", amount=" + amount +
                ", fee=" + fee +
                ", splitRuleList=" + splitRuleList +
                ", tradeCode='" + tradeCode + '\'' +
                '}';
    }
}
