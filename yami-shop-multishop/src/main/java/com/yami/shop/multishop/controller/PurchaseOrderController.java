package com.yami.shop.multishop.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.PurchaseOrder;
import com.yami.shop.bean.vo.ExcelInfoVO;
import com.yami.shop.bean.vo.PurchaseOrderVO;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.model.YamiShopUser;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.PurchaseOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Objects;

/**
 * 采购订单
 *
 * <AUTHOR>
 * @date 2021-09-08 10:42:01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/purchase/order" )
@Tag(name = "采购订单接口")
public class PurchaseOrderController {

    private final PurchaseOrderService purchaseOrderService;

    @GetMapping("/page" )
    @Operation(summary = "分页查询采购订单" , description = "分页查询采购订单")
    @PreAuthorize("@pms.hasPermission('purchase:purchaseOrder:page')" )
    public ServerResponseEntity<IPage<PurchaseOrderVO>> getPurchaseOrderPage(PageParam<PurchaseOrder> page, PurchaseOrder purchaseOrder) {
        purchaseOrder.setShopId(SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success(purchaseOrderService.pagePurchaseOrder(page, purchaseOrder));
    }

    @GetMapping("/info/{purchaseOrderId}")
    @Operation(summary = "根据id查询采购订单" , description = "根据id查询采购订单")
    @PreAuthorize("@pms.hasPermission('purchase:purchaseOrder:info')" )
    public ServerResponseEntity<PurchaseOrder> getById(@PathVariable("purchaseOrderId") Long purchaseOrderId) {
        PurchaseOrder purchaseOrder = purchaseOrderService.info(purchaseOrderId);
        if (!Objects.equals(SecurityUtils.getShopUser().getShopId(), purchaseOrder.getShopId())) {
            throw new YamiShopBindException("yami.no.auth");
        }
        return ServerResponseEntity.success(purchaseOrder);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('purchase:purchaseOrder:save')" )
    @Operation(summary = "新增采购订单" , description = "新增采购订单")
    public ServerResponseEntity<Void> save(@RequestBody @Valid PurchaseOrder purchaseOrder) {
        YamiShopUser shopUser = SecurityUtils.getShopUser();
        purchaseOrder.setShopId(shopUser.getShopId());
        purchaseOrder.setEmployeeId(shopUser.getEmployeeId());
        purchaseOrderService.savePurchaseOrder(purchaseOrder);
        return ServerResponseEntity.success();
    }

    @DeleteMapping("/{purchaseOrderId}" )
    @Operation(summary = "根据id删除采购订单" , description = "根据id删除采购订单")
    @PreAuthorize("@pms.hasPermission('purchase:purchaseOrder:delete')" )
    public ServerResponseEntity<Boolean> removeById(@PathVariable Long purchaseOrderId) {
        return ServerResponseEntity.success(purchaseOrderService.removeById(purchaseOrderId));
    }

    @PutMapping("/inbound")
    @PreAuthorize("@pms.hasPermission('purchase:purchaseOrder:inbound')" )
    @Operation(summary = "入库采购订单" , description = "入库采购订单")
    public ServerResponseEntity<Void> inbound(@RequestBody PurchaseOrder purchaseOrder) {
        purchaseOrder.setShopId(SecurityUtils.getShopUser().getShopId());
        purchaseOrderService.inbound(purchaseOrder);
        return ServerResponseEntity.success();
    }

    @DeleteMapping("/nullify/{purchaseOrderId}" )
    @PreAuthorize("@pms.hasPermission('purchase:purchaseOrder:nullify')" )
    @Operation(summary = "作废采购订单" , description = "作废采购订单")
    public ServerResponseEntity<Void> nullify(@PathVariable Long purchaseOrderId) {
        purchaseOrderService.nullify(SecurityUtils.getShopUser().getShopId(), purchaseOrderId);
        return ServerResponseEntity.success();
    }

    @PutMapping("/complete" )
    @PreAuthorize("@pms.hasPermission('purchase:purchaseOrder:complete')" )
    @Operation(summary = "完成采购订单" , description = "完成采购订单")
    public ServerResponseEntity<Void> finish(@RequestBody PurchaseOrder purchaseOrder) {
        purchaseOrder.setShopId(SecurityUtils.getShopUser().getShopId());
        purchaseOrderService.complete(SecurityUtils.getShopUser().getShopId(), purchaseOrder.getPurchaseOrderId());
        return ServerResponseEntity.success();
    }

    @GetMapping(value = "/downloadModel")
    @Operation(summary = "下载导入采购商品模板" , description = "下载导入采购商品模板")
    @PreAuthorize("@pms.hasPermission('purchase:purchaseOrder:downloadModel')" )
    public void downloadModel(HttpServletResponse response) {
        purchaseOrderService.downloadModel(response);
    }

    @PostMapping("/exportExcel/{supplierId}")
    @Operation(summary = "导入采购商品" , description = "导入采购商品")
    @PreAuthorize("@pms.hasPermission('purchase:purchaseOrder:import')" )
    public ServerResponseEntity<ExcelInfoVO> exportExcel(@RequestPart MultipartFile excelFile,
                                                         @PathVariable Long supplierId,
                                                         Long warehouseId) throws Exception {
        if (excelFile == null) {
            // 网络繁忙，请稍后重试
            throw new YamiShopBindException("yami.network.busy");
        }
        Long shopId = SecurityUtils.getShopUser().getShopId();
        ExcelInfoVO excelInfoVO = purchaseOrderService.parseFile(excelFile, shopId, supplierId, warehouseId);
        return ServerResponseEntity.success(excelInfoVO);
    }

    @GetMapping("/inbound/export")
    @Schema(description = "导出入库商品" )
    @PreAuthorize("@pms.hasPermission('purchase:purchaseOrder:export')" )
    public void export(PurchaseOrder purchaseOrder, HttpServletResponse response) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        purchaseOrder.setShopId(shopId);
        purchaseOrderService.export(purchaseOrder,response);
    }

    @GetMapping(value = "/inbound/downloadModel")
    @Schema(description = "下载入库模板" )
    @PreAuthorize("@pms.hasPermission('purchase:purchaseOrder:downloadModel')" )
    public void inboundDownloadModel(HttpServletResponse response) {
        purchaseOrderService.inboundDownloadModel(response);
    }

    @PostMapping("/inbound/exportExcel/{purchaseOrderId}")
    @Schema(description = "导入入库文件" )
    @PreAuthorize("@pms.hasPermission('purchase:purchaseOrder:import')" )
    public ServerResponseEntity<ExcelInfoVO> inboundExportExcel(@RequestParam("excelFile") MultipartFile excelFile, @PathVariable Long purchaseOrderId) throws Exception {
        if (excelFile == null) {
            // 网络繁忙，请稍后重试
            throw new YamiShopBindException("yami.network.busy");
        }
        Long shopId = SecurityUtils.getShopUser().getShopId();
        ExcelInfoVO excelInfoVO = purchaseOrderService.inboundParseFile(excelFile, shopId, purchaseOrderId);
        return ServerResponseEntity.success(excelInfoVO);
    }

    @Operation(summary = "自动采购入库")
    @PostMapping("/autoPurchase")
    public ServerResponseEntity<Void> autoPurchase(@RequestBody @Valid PurchaseOrder purchaseOrder) {
        YamiShopUser shopUser = SecurityUtils.getShopUser();
        purchaseOrder.setShopId(shopUser.getShopId());
        purchaseOrder.setEmployeeId(shopUser.getEmployeeId());
        purchaseOrderService.autoPurchase(purchaseOrder);
        return ServerResponseEntity.success();
    }
}
