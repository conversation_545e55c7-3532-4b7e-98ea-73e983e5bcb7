package com.yami.shop.bean.dto.allinpay;

import java.io.Serial;
import java.io.Serializable;
/**
 * <AUTHOR>
 * @date 2023-06-01
 */
public class AllinpayBalanceDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 总额
     */
    private Long allAmount;
    /**
     * 冻结额
     */
    private Long freezenAmount;

    public Long getAllAmount() {
        return allAmount;
    }

    public void setAllAmount(Long allAmount) {
        this.allAmount = allAmount;
    }

    public Long getFreezenAmount() {
        return freezenAmount;
    }

    public void setFreezenAmount(Long freezenAmount) {
        this.freezenAmount = freezenAmount;
    }

    @Override
    public String toString() {
        return "AllinpayBalanceDTO{" +
                "allAmount=" + allAmount +
                ", freezenAmount=" + freezenAmount +
                '}';
    }
}
