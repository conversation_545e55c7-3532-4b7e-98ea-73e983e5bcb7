package com.yami.shop.platform.task;

import cn.hutool.core.util.StrUtil;
import com.github.binarywang.wxpay.bean.merchanttransfer.DetailsQueryResult;
import com.github.binarywang.wxpay.bean.merchanttransfer.MerchantDetailsQueryRequest;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.yami.shop.bean.model.EnterprisePay;
import com.yami.shop.config.WxConfig;
import com.yami.shop.service.EnterprisePayService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;


/**
 * 商家转账定时任务
 * <AUTHOR>
 * 使用商家转账的需要将下面的注释打开，注意！使用商家转账功能要经过测试之后才能上线，否则自行承担对应的后果！！！
 */
@Slf4j
@Component("enterprisePay")
@AllArgsConstructor
public class EnterprisePayTask {

    private final EnterprisePayService enterprisePayService;

    private final WxConfig wxConfig;
    /**
     * 商家转账
     * 注意！使用这段代码要经过测试之后才能上线，否则自行承担对应的后果！！！
     * @XxlJob("sendEnterprisePay")
     */
    public void sendEnterprisePay() {
        log.info("开始执行发送商家转账任务》》》》》》》》》》》》》》》》》》》》》");

        List<EnterprisePay> enterprisePayList = enterprisePayService.listApplyEnterprisePay();
        enterprisePayService.sendEnterprisePay(enterprisePayList);

        log.info("结束执行发送商家转账任务》》》》》》》》》》》》》》》》》》》》》");
    }

    /**
     * 查询支付情况
     * 注意！使用这段代码要经过测试之后才能上线，否则自行承担对应的后果！！！
     * @XxlJob("queryAndUpdateEntPay")
     */
    public void queryAndUpdateEntPay() {
        log.info("开始查询商家转账任务》》》》》》》》》》》》》》》》》》》》》》》");

        List<EnterprisePay> enterprisePayList = enterprisePayService.listEnterprisePay();
        if (CollectionUtils.isNotEmpty(enterprisePayList)) {
            for (EnterprisePay enterprisePay : enterprisePayList) {
                try {
                    MerchantDetailsQueryRequest request = new MerchantDetailsQueryRequest();
                    request.setOutBatchNo(enterprisePay.getOutBatchNo());
                    request.setOutDetailNo(String.valueOf(enterprisePay.getEntPayOrderNo()));
                    DetailsQueryResult detailsQueryResult = wxConfig.getMerchantTransferService().queryMerchantDetails(request);
                    // SYSTEM_ERROR：系统错误，不要更换商家批次单号，使用原商家批次单号重试
                    if (StrUtil.equalsIgnoreCase(detailsQueryResult.getDetailStatus(), "SYSTEM_ERROR")) {
                        enterprisePayService.sendEnterprisePay(Collections.singletonList(enterprisePay));
                    }
                    // PROCESSING：处理中
                    if (StrUtil.equalsIgnoreCase(detailsQueryResult.getDetailStatus(), "PROCESSING")) {
                    }
                    // SUCCESS: 转账成功
                    if (StrUtil.equalsIgnoreCase(detailsQueryResult.getDetailStatus(), "SUCCESS")) {
                        enterprisePayService.paySuccess(enterprisePay);
                    }
                    // FAIL: 转账失败
                    if (StrUtil.equalsIgnoreCase(detailsQueryResult.getDetailStatus(), "FAIL")) {
                        enterprisePayService.payFailed(enterprisePay);
                        log.error(detailsQueryResult.getFailReason());
                    }
                } catch (WxPayException e) {
                    log.error("WxPayException:", e);
                }
            }
        }
        log.info("结束查询商家转账任务》》》》》》》》》》》》》》》》》》》》》》》");
    }

}
