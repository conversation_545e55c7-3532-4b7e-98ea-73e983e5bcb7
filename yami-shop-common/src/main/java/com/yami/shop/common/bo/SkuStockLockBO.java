package com.yami.shop.common.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class SkuStockLockBO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @NotNull(message = "skuId不能为空")
    @Schema(description = "skuId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long skuId;

    @NotNull(message = "orderId不能为空")
    @Schema(description = "orderId", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long orderId;

    @NotNull(message = "商品数量不能为空")
    @Min(value = 1, message = "商品数量不能为空")
    @Schema(description = "商品数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer stockCount;

    @Schema(description = "库存仓库id")
    private Long stockPointId;

    @Schema(description = "默认仓库id,如果不为空,在回退库存时回退到这里")
    private Long defaultStockPointId;

    public SkuStockLockBO() {
    }

    public Long getDefaultStockPointId() {
        return defaultStockPointId;
    }

    public void setDefaultStockPointId(Long defaultStockPointId) {
        this.defaultStockPointId = defaultStockPointId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getStockCount() {
        return stockCount;
    }

    public void setStockCount(Integer stockCount) {
        this.stockCount = stockCount;
    }

    public Long getStockPointId() {
        return stockPointId;
    }

    public void setStockPointId(Long stockPointId) {
        this.stockPointId = stockPointId;
    }

    @Override
    public String toString() {
        return "SkuStockLockBO{" +
                "skuId=" + skuId +
                ", orderId=" + orderId +
                ", stockCount=" + stockCount +
                ", stockPointId=" + stockPointId +
                ", defaultStockPointId=" + defaultStockPointId +
                '}';
    }
}
