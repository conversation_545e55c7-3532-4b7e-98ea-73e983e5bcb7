package com.yami.shop.common.constants;


/**
 * lua脚本操作
 * <AUTHOR>
 */
public enum LuaOperateEnum {
    /**
     * ======================  sku操作  -  1-100  ======================
     */

    /**
     * 插入库存
     */
    SKU_INSERT(1),

    /**
     * 增加库存
     */
    SKU_ADD(2),
    /**
     * 减少库存
     */
    SKU_SUB(3),
    /**
     * 替换库存(覆盖原有库存)
     */
    SKU_REPLACE(4),
    /**
     * sku总库存状态变更 0:商品或sku下架、禁用、删除 1:上架状态 提交订单时需要校验此状态
     */
    SKU_STATUS(5),
    /**
     * sku区域库存状态变更 0:下架此库存，并扣除sku总库存 1:上架此区域库存，并增加sku总库存数量
     */
    SKU_POINT_STATUS(6),

    /**
     * ======================  订单操作    100-200  ======================
     */

    /**
     * 提交订单 - 扣除sku可售库存，增加对应数量的sku锁定库存
     */
    ORDER_SUBMIT(100),
    /**
     * 订单支付成功 - 扣除sku锁定库存，增加对应数量的sku销量
     */
    ORDER_SUCCESS(101),
    /**
     * 取消订单 - 扣除sku锁定库存，增加对应数量的sku可售库存
     */
    ORDER_CANCEL(102),

    /**
     * ======================  退款操作    200-300    ======================
     */


    /**
     * ======================  采购订单操作    300-400  ======================
     */


    ;

    private final Integer value;

    public Integer value() {
        return value;
    }

    LuaOperateEnum(Integer value) {
        this.value = value;
    }

    public static LuaOperateEnum instance(Integer value) {
        LuaOperateEnum[] enums = values();
        for (LuaOperateEnum stateEnum : enums) {
            if (stateEnum.value().equals(value)) {
                return stateEnum;
            }
        }
        return null;
    }
}
