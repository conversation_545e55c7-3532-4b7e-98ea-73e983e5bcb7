package com.yami.shop.groupbuy.common.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.bean.model.Order;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupTeamDto;
import com.yami.shop.groupbuy.common.api.dto.ApiJoinGroupTeamDto;
import com.yami.shop.groupbuy.common.dto.GroupOrderDTO;
import com.yami.shop.groupbuy.common.dto.GroupRefundOrder;
import com.yami.shop.groupbuy.common.dto.GroupTeamDto;
import com.yami.shop.groupbuy.common.model.GroupOrder;
import com.yami.shop.groupbuy.common.model.GroupTeam;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 拼团团队表
 *
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
public interface GroupTeamMapper extends BaseMapper<GroupTeam> {

    /**
     * 获取分页列表
     *
     * @param page 分页信息
     * @param groupTeamDto 团购团队信息
     * @return 团购团队列表
     */
    IPage<GroupTeamDto> getPage(Page page, @Param("groupTeam") GroupTeamDto groupTeamDto);

    /**
     * 根据团队id分页获取拼团订单信息
     * @param page
     * @param groupOrder
     * @return
     */
    IPage<GroupOrderDTO> getGroupOrderPage(PageParam<GroupOrder> page, @Param("groupOrder") GroupOrder groupOrder);

    /**
     * 可加入的团列表
     *
     * @param groupActivityId 拼团活动ID
     * @param nowTime         当前时间
     * @param showSize        显示数量（默认10）
     * @return 团列表
     */
    List<ApiGroupTeamDto> listByJoinGroupAndNowDate(@Param("groupActivityId") Long groupActivityId, @Param("nowTime") Date nowTime, @Param("showSize") Integer showSize);

    /**
     * 获取拼团单（拼团团队）信息
     *
     * @param groupTeamId 团单ID
     * @return ApiGroupTeamDto对象
     */
    ApiGroupTeamDto getApiGroupTeamDtoByTeamId(@Param("groupTeamId") Long groupTeamId);

    /**
     * 获取拼团团队信息
     *
     * @param groupActivityId 活动ID
     * @return 拼团团队信息
     */
    ApiJoinGroupTeamDto getJoinGroupTeamHasMaxNum(@Param("groupActivityId") Long groupActivityId);

    /**
     * 获取活动结束或团单结束未成团的拼团团队
     *
     * @param nowDate 当前时间
     * @return GroupTeam列表
     */
    List<GroupTeam> getNotGroupTeamByNowDate(@Param("nowDate") Date nowDate);

    /**
     * 获取未成团的订单ID列表
     *
     * @param groupTeamId 拼团团队ID
     * @return 团购退款订单列表
     */
    List<GroupRefundOrder> getJoinNotGroupTeamOrder(@Param("groupTeamId") Long groupTeamId);

    /**
     * 通过活动id获取未成团的订单
     *
     * @param groupActivityId 团购活动id
     * @return 团购团队列表
     */
    List<GroupTeam> getNotGroupTeamsByActivityId(@Param("groupActivityId") Long groupActivityId);

    /**
     * 获取参团的订单列表
     * @param groupTeamId 拼团团队ID
     * @param status 支付状态
     * @return 订单列表
     */
    List<Order> getOrders(@Param("groupTeamId") Long groupTeamId, @Param("status") Integer status);

    /**
     * 通过活动商品id获取未成团的订单
     *
     * @param prodId 商品id
     * @return 团购团队列表
     */
    List<GroupTeam> getNotGroupTeamByProdId(@Param("prodId") Long prodId);

    /**
     * 更新拼团(参团/满员判断)
     * @param groupTeam
     * @param groupNumber 成团人数
     * @return
     */
    int updateGroupTeam(@Param("groupTeam") GroupTeam groupTeam, @Param("groupNumber") Integer groupNumber);
}
