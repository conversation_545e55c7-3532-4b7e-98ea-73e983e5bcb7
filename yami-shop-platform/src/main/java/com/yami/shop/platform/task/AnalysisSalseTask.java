package com.yami.shop.platform.task;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.bean.model.User;
import com.yami.shop.bean.param.UserManagerParam;
import com.yami.shop.bean.param.UserManagerReqParam;
import com.yami.shop.service.UserService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 */
@Slf4j
public class AnalysisSalseTask implements Callable<List<UserManagerParam>> {

    private final UserService userService;
    private final UserManagerReqParam user;
    private final Page<User> pages;
    private final Integer lang;

    public AnalysisSalseTask(UserService userService, UserManagerReqParam user, Page<User> pages, Integer lang) {
        this.userService = userService;
        this.user = user;
        this.pages = pages;
        this.lang = lang;
    }

    @Override
    public List<UserManagerParam> call() {
        IPage<UserManagerParam> userPage =  userService.getUserInfoPage(pages, user, lang);
        return userPage.getRecords();
    }
}
