package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-03-28
 */
public class QueryInExpDetailDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "账户集编号")
    private String accountSetNo;

    @Schema(description = "订单编号")
    private String bizOrderNo;

    @NotNull(message = "dateStart不能为null或空")
    @Schema(description = "开始日期(时间格式yyyy-MM-dd HH:mm:ss)")
    private String dateStart;

    @NotNull(message = "dateEnd不能为null或空")
    @Schema(description = "结束日期(与开始日期间隔不能超过31天)")
    private String dateEnd;

    @Schema(description = "交易类型(1:充值, 2:转账, 3:提现, 4:退款, 5:应收账款确认, 9:收银宝退款资金调拨, 10:应收账款手续费确认, 12:退票)")
    private Integer tradeType;

    public String getAccountSetNo() {
        return accountSetNo;
    }

    public void setAccountSetNo(String accountSetNo) {
        this.accountSetNo = accountSetNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getDateStart() {
        return dateStart;
    }

    public void setDateStart(String dateStart) {
        this.dateStart = dateStart;
    }

    public String getDateEnd() {
        return dateEnd;
    }

    public void setDateEnd(String dateEnd) {
        this.dateEnd = dateEnd;
    }

    public Integer getTradeType() {
        return tradeType;
    }

    public void setTradeType(Integer tradeType) {
        this.tradeType = tradeType;
    }

    @Override
    public String toString() {
        return "QueryInExpDetailDTO{" +
                "accountSetNo='" + accountSetNo + '\'' +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                ", dateStart='" + dateStart + '\'' +
                ", dateEnd='" + dateEnd + '\'' +
                ", tradeType=" + tradeType +
                '}';
    }
}
