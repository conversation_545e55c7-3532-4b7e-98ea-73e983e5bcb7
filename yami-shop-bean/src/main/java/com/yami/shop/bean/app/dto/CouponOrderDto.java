package com.yami.shop.bean.app.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CouponOrderDto implements Serializable {

    @Schema(description = "优惠券id" )
    private Long couponId;

    @Schema(description = "店铺ID" )
    private Long shopId;

    @Schema(description = "优惠券名称" )
    private String couponName;

    @Schema(description = "订单号" )
    private String orderNumber;

    @Schema(description = "副标题" )
    private String subTitle;

    @Schema(description = "优惠类型 1:代金券 2:折扣券 3:兑换券" )
    private Integer couponType;

    @Schema(description = "使用条件" )
    private Double cashCondition;

    @Schema(description = "减免金额" )
    private Double reduceAmount;

    @Schema(description = "折扣" )
    private Double couponDiscount;

    @Schema(description = "开始时间" )
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @Schema(description = "结束时间" )
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @Schema(description = "适用商品类型 0全部商品参与 1指定商品参与 2指定商品不参与" )
    private Integer suitableProdType;

    @Schema(description = "指定的商品id" )
    private List<Long> prodIds;

    @Schema(description = "是否选中" )
    private boolean isChoose;

    @Schema(description = "是否可用" )
    private boolean canUse;

    @Schema(description = "用户优惠券id" )
    private Long couponUserId;

    @Schema(description = "生效类型 1:固定时间 2:领取后生效" )
    private Integer validTimeType;

    @Schema(description = "领券后X天起生效" )
    private Integer afterReceiveDays;

    @Schema(description = "有效天数" )
    private Integer validDays;

    @Schema(description = "领券时间" )
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date receiveTime;

    /**
     * 当前可用优惠券的商品实际金额（商品实际金额 - 商品分摊金额）
     */
    private double prodCanUseCouponActualTotal;
}
