package com.yami.shop.bean.bo;

import com.yami.shop.bean.vo.allinpay.SplitRuleVO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/03/13
 */
@Data
public class AllinpaySettlementBO {

    /**
     * 用户支付单号
     */
    private String payId;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 商家收入明细
     */
    private SplitRuleVO shopSplitRuleVO;
    /**
     * 平台收入明细
     */
    private SplitRuleVO platformSplitRuleVO;
    /**
     * 分销员收入明细
     */
    private List<SplitRuleVO> distributionUserSplitRuleList;
}
