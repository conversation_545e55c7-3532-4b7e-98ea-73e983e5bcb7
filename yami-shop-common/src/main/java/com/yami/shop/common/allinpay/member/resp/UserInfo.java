package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class UserInfo extends UserBaseInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 用户状态
     */
    private Integer userState;

    /**
     * 会员类型
     */
    private Integer memberType;

    /**
     * 云账户用户id
     */
    private String userId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 身份证号码
     */
    private String identityCardNo;

    /**
     * 是否绑定手机
     */
    private Boolean isPhoneChecked;

    /**
     * 创建时间
     */
    private String registerTime;

    /**
     * 创建ip
     */
    private String registerIp;

    /**
     * 支付失败次数
     */
    private Long payFailAmount;

    /**
     * 是否进行实名认证
     */
    private Boolean isIdentityChecked;

    /**
     * 实名认证时间
     */
    private String realNameTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 访问终端类型
     */
    private String source;

    /**
     * 是否设置支付密码
     */
    private Boolean isSetPayPwd;

    /**
     * 账户提现签约协议时间
     */
    private String signAcctProtocolTime;

    /**
     * 账户提现协议编号
     */
    private String acctProtocolNo;

    /**
     * 是否绑定用户支付标识
     */
    public Boolean isBindPayAcct;

    public Integer getUserState() {
        return userState;
    }

    public void setUserState(Integer userState) {
        this.userState = userState;
    }

    public Integer getMemberType() {
        return memberType;
    }

    public void setMemberType(Integer memberType) {
        this.memberType = memberType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getIdentityCardNo() {
        return identityCardNo;
    }

    public void setIdentityCardNo(String identityCardNo) {
        this.identityCardNo = identityCardNo;
    }

    public Boolean getIsPhoneChecked() {
        return isPhoneChecked;
    }

    public void setIsPhoneChecked(Boolean isPhoneChecked) {
        this.isPhoneChecked = isPhoneChecked;
    }

    public String getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(String registerTime) {
        this.registerTime = registerTime;
    }

    public String getRegisterIp() {
        return registerIp;
    }

    public void setRegisterIp(String registerIp) {
        this.registerIp = registerIp;
    }

    public Long getPayFailAmount() {
        return payFailAmount;
    }

    public void setPayFailAmount(Long payFailAmount) {
        this.payFailAmount = payFailAmount;
    }

    public Boolean getIsIdentityChecked() {
        return isIdentityChecked;
    }

    public void setIsIdentityChecked(Boolean isIdentityChecked) {
        this.isIdentityChecked = isIdentityChecked;
    }

    public String getRealNameTime() {
        return realNameTime;
    }

    public void setRealNameTime(String realNameTime) {
        this.realNameTime = realNameTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Boolean getIsSetPayPwd() {
        return isSetPayPwd;
    }

    public void setIsSetPayPwd(Boolean isSetPayPwd) {
        this.isSetPayPwd = isSetPayPwd;
    }

    public Boolean getPhoneChecked() {
        return isPhoneChecked;
    }

    public void setPhoneChecked(Boolean phoneChecked) {
        isPhoneChecked = phoneChecked;
    }

    public Boolean getIdentityChecked() {
        return isIdentityChecked;
    }

    public void setIdentityChecked(Boolean identityChecked) {
        isIdentityChecked = identityChecked;
    }

    public Boolean getSetPayPwd() {
        return isSetPayPwd;
    }

    public void setSetPayPwd(Boolean setPayPwd) {
        isSetPayPwd = setPayPwd;
    }

    public String getSignAcctProtocolTime() {
        return signAcctProtocolTime;
    }

    public void setSignAcctProtocolTime(String signAcctProtocolTime) {
        this.signAcctProtocolTime = signAcctProtocolTime;
    }

    public String getAcctProtocolNo() {
        return acctProtocolNo;
    }

    public void setAcctProtocolNo(String acctProtocolNo) {
        this.acctProtocolNo = acctProtocolNo;
    }

    public Boolean getIsBindPayAcct() {
        return isBindPayAcct;
    }

    public void setIsBindPayAcct(Boolean bindPayAcct) {
        isBindPayAcct = bindPayAcct;
    }
}
