package com.yami.shop.multishop.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.TakeStock;
import com.yami.shop.bean.param.TakeStockParam;
import com.yami.shop.bean.vo.TakeStockVO;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.model.YamiShopUser;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.TakeStockExcelService;
import com.yami.shop.service.TakeStockService;
import com.yami.shop.sys.common.service.ShopEmployeeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2021-09-15 11:18:33
 */
@RestController
@RequestMapping("/stock/takeStock")
@Tag(name = "库存盘点接口")
@RequiredArgsConstructor
public class TakeStockController {

    private final TakeStockService takeStockService;
    private final ShopEmployeeService shopEmployeeService;
    private final TakeStockExcelService takeStockExcelService;

    @GetMapping("/page")
    @Operation(summary = "分页查询库存盘点信息" , description = "分页查询库存盘点信息")
    @PreAuthorize("@pms.hasPermission('stock:takeStock:page')" )
    public ServerResponseEntity<IPage<TakeStockVO>> getTakeStockPage(PageParam<TakeStock> page, TakeStockParam takeStock) {
        takeStock.setShopId(SecurityUtils.getShopUser().getShopId());
        IPage<TakeStockVO> takeStockVoPage = takeStockService.pageByParams(page, takeStock);
        for(TakeStockVO takeStockVO : takeStockVoPage.getRecords()){
            if (StrUtil.isNotBlank(takeStockVO.getMakerMobile())) {
                takeStockVO.setMakerMobile(PhoneUtil.hideBetween(takeStockVO.getMakerMobile()).toString());
            }
        }
        return ServerResponseEntity.success(takeStockVoPage);
    }

    @GetMapping("/info/{takeStockId}")
    @Operation(summary = "根据id查询库存盘点信息" , description = "根据id查询库存盘点信息")
    @PreAuthorize("@pms.hasPermission('stock:takeStock:info')" )
    public ServerResponseEntity<TakeStockVO> getById(@PathVariable("takeStockId") Long takeStockId) {
        TakeStockVO takeStockVO = takeStockService.getInfo(takeStockId);
        // 隐藏手机号码
        if (StrUtil.isNotBlank(takeStockVO.getMakerMobile())) {
            takeStockVO.setMakerMobile(PhoneUtil.hideBetween(takeStockVO.getMakerMobile()).toString());
        }
        return ServerResponseEntity.success(takeStockVO);
    }

    @PostMapping
    @Operation(summary = "新增盘点" , description = "新增盘点")
    @PreAuthorize("@pms.hasPermission('multishop:takeStock:save')" )
    public ServerResponseEntity<Long> save(@RequestBody @Valid TakeStock takeStock) {
        takeStock.setShopId(SecurityUtils.getShopUser().getShopId());
        takeStock.setMaker(SecurityUtils.getShopUser().getEmployeeId());
        takeStock.setMakerMobile(shopEmployeeService.getShopEmployeeById(takeStock.getMaker()).getMobile());
        Long takeStockId = takeStockService.saveTakeStock(takeStock);
        return ServerResponseEntity.success(takeStockId);
    }

    @PutMapping
    @Operation(summary = "保存草稿" , description = "保存草稿")
    @PreAuthorize("@pms.hasPermission('multishop:takeStockProd:update')" )
    public ServerResponseEntity<Boolean> updateById(@RequestBody @Valid TakeStock takeStock) {
        takeStock.setShopId(SecurityUtils.getShopUser().getShopId());
        if (CollectionUtil.isEmpty(takeStock.getTakeStockProdList())) {
            //盘点商品不能为空
            throw new YamiShopBindException("yami.take.stock.prod.null");
        }
        return ServerResponseEntity.success(takeStockService.updateTakeStock(takeStock));
    }

    @PutMapping("/finishInventory")
    @Operation(summary = "完成盘点" , description = "完成盘点")
    @PreAuthorize("@pms.hasPermission('multishop:takeStockProd:finish')" )
    public ServerResponseEntity<Boolean> finishInventory(@RequestBody @Valid TakeStock takeStock) {
        YamiShopUser shopUser = SecurityUtils.getShopUser();
        takeStock.setShopId(SecurityUtils.getShopUser().getShopId());
        if (CollectionUtil.isEmpty(takeStock.getTakeStockProdList())) {
            //盘点商品不能为空
            throw new YamiShopBindException("yami.take.stock.prod.null");
        }
        return ServerResponseEntity.success(takeStockService.finishTakeStock(takeStock));
    }

    @PutMapping("/voidedInventory/{takeStockId}")
    @Operation(summary = "作废盘点" , description = "作废盘点")
    @PreAuthorize("@pms.hasPermission('multishop:takeStock:voided')" )
    public ServerResponseEntity<Boolean> voidedInventory(@PathVariable("takeStockId") Long takeStockId) {
        TakeStock takeStock = takeStockService.getById(takeStockId);
        return ServerResponseEntity.success(takeStockService.voidedTakeStock(takeStock));
    }

    @GetMapping("/exportTakeStock")
    @Schema(description = "导出盘点信息" )
    @PreAuthorize("@pms.hasPermission('multishop:takeStock:export')" )
    public void exportTakeStock(TakeStockParam takeStock, HttpServletResponse response) {
        takeStock.setShopId(SecurityUtils.getShopUser().getShopId());
        List<TakeStockVO> takeStocks = takeStockService.listByParams(takeStock);
        takeStockExcelService.createExcel(takeStocks, false, response);
    }
}
