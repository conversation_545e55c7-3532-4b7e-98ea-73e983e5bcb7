package com.yami.shop.bean.app.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "设置用户信息")
public class UserPwdUpdateParam {

    @Schema(description = "密码" )
    private String password;

    @Schema(description = "手机号" )
    private String mobile;

    @Schema(description = "校验登陆注册验证码成功的标识" )
    private String checkRegisterSmsFlag;


}
