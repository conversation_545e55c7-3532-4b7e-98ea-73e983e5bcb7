package com.yami.shop.distribution.common.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.distribution.common.dto.DistributionUserWalletBillDto;
import com.yami.shop.distribution.common.model.DistributionUserWalletBill;
import com.yami.shop.distribution.common.param.DistributionParam;
import org.apache.ibatis.annotations.Param;

/**
 * 分销员钱包流水
 *
 * <AUTHOR>
 * @date 2019-04-29 16:39:13
 */
public interface DistributionUserWalletBillMapper extends BaseMapper<DistributionUserWalletBill> {

    /**
     * 获取分销员钱包流水记录
     * @param page 分页参数
     * @param distributionParam 分销查询参数
     * @return 分销员钱包流水记录
     */
    Page<DistributionUserWalletBill> getDistributionUserWalletBillAndUserPage(Page page,
                                                                              @Param("distributionParam")DistributionParam distributionParam);

    /**
     * 获取分销员钱包流水记录
     * @param page 分页参数
     * @param distributionUserId 分销员用户id
     * @param orderBy 排序方式
     * @return 分销员钱包流水记录
     */
    Page<DistributionUserWalletBillDto> getDistributionUserWalletBillDtoPage(Page page, @Param("distributionUserId") Long distributionUserId,@Param("orderBy") Integer orderBy);

    /**
     * 根据钱包id获取已提现金额
     * @param walletId
     * @return
     */
    Double getHaveWithdrawalSum(@Param("walletId") Long walletId);

    /**
     * 初始化分销钱包
     */
    void initWalletBill();
}
