package com.yami.shop.platform.controller;

import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.CategoryShopService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2021/8/23 9:44
 */
@RestController
@RequestMapping("/platform/categoryShop")
@Tag(name = "签约分类相关接口")
@AllArgsConstructor
public class CategoryShopController {

    private final CategoryShopService categoryShopService;

    @DeleteMapping
    @Operation(summary = "删除签约分类" , description = "删除签约分类")
    @Parameters(value = {
            @Parameter(name = "shopId", description = "店铺id" ),
            @Parameter(name = "categoryId", description = "分类id" )
    })
    @PreAuthorize("@pms.hasPermission('platform:categoryShop:delete')")
    public ServerResponseEntity<Void> delete(@RequestParam("shopId") Long shopId, @RequestParam("categoryId") Long categoryId) {
        categoryShopService.delete(shopId, categoryId);
        return ServerResponseEntity.success();
    }

    @PutMapping("updateRate")
    @Operation(summary = "更新自定义扣率" , description = "更新自定义扣率")
    @Parameters(value = {
            @Parameter(name = "shopId", description = "店铺id" ),
            @Parameter(name = "rate", description = "扣率" )
    })
    @PreAuthorize("@pms.hasPermission('platform:categoryShop:update')")
    public ServerResponseEntity<Void> updateRate(@RequestParam("shopId") Long shopId, @RequestParam("categoryId") Long categoryId,
                                           @RequestParam(value = "rate", required = false) Double rate) {
        categoryShopService.updateRate(shopId, categoryId, rate);
        return ServerResponseEntity.success();
    }
}
