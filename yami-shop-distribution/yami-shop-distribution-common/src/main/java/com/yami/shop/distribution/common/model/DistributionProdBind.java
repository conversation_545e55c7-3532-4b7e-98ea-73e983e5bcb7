package com.yami.shop.distribution.common.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-04-22 10:01:44
 */
@Data
@TableName("tz_distribution_prod_bind")
@EqualsAndHashCode
@Schema(description = "分销商品绑定表")
public class DistributionProdBind implements Serializable{
    private static final long serialVersionUID = 1L;

    @TableId
    @Schema(description = "用户商品绑定表" )
    private Long id;

    @Schema(description = "分销员id" )
    private Long distributionUserId;

    @Schema(description = "用户id" )
    private String userId;

    @Schema(description = "绑定时间" )
    private Date bindTime;

    @Schema(description = "商品id" )
    private Long prodId;

    @Schema(description = "状态(0失效 1生效)" )
    private Integer state;
}
