package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;
/**
 * 确认绑定银行卡
 *
 * <AUTHOR>
 */
public class BindBankCardResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private String bizUserId;

    /**
     * 流水号
     */
    private String tranceNum;

    /**
     * 申请时间
     */
    private String transDate;

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getTranceNum() {
        return tranceNum;
    }

    public void setTranceNum(String tranceNum) {
        this.tranceNum = tranceNum;
    }

    public String getTransDate() {
        return transDate;
    }

    public void setTransDate(String transDate) {
        this.transDate = transDate;
    }


}
