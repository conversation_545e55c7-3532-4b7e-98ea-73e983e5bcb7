package com.yami.shop.common.allinpay.order.req;

/**
 * <AUTHOR>
 * @date 2023-04-27
 */
public class PayBySms extends AbstractOrderReq {

    @Override
    public String getMethod() {
        return "payBySMS";
    }

    /**
     * 商户订单号(*)
     */
    private String bizOrderNo;

    /**
     * 短信验证码(收银宝H5收银台不填)
     */
    private String verificationCode;

    /**
     * ip地址(*)
     */
    private String consumerIp;

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    public String getConsumerIp() {
        return consumerIp;
    }

    public void setConsumerIp(String consumerIp) {
        this.consumerIp = consumerIp;
    }

    @Override
    public String toString() {
        return "PayBySms{" +
                "bizOrderNo='" + bizOrderNo + '\'' +
                ", verificationCode='" + verificationCode + '\'' +
                ", consumerIp='" + consumerIp + '\'' +
                ", bizUserId='" + bizUserId + '\'' +
                '}';
    }
}
