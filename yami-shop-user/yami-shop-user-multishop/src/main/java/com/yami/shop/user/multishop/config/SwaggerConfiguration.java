package com.yami.shop.user.multishop.config;

import lombok.AllArgsConstructor;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration("userSwaggerConfiguration")
@AllArgsConstructor
public class SwaggerConfiguration {

    @Bean
    public GroupedOpenApi scoreMallRestApi() {
        return GroupedOpenApi.builder()
                .group("用户积分和等级接口")
                .packagesToScan("com.yami.shop.user.multishop.controller")
                .pathsToMatch("/**")
                .build();
    }

}