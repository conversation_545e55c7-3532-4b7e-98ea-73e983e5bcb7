package com.yami.shop.common.allinpay.member.req;

import java.io.Serial;
import java.io.Serializable;

/**
 * 查询卡bin
 *
 * <AUTHOR>
 */
public class GetBankCardBin extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "getBankCardBin";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    /**
     * 银行卡号
     */
    private String cardNo;

    public GetBankCardBin() {
    }

    public GetBankCardBin(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }


}
