package com.yami.shop.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.dto.PageAnalysisDto;
import com.yami.shop.bean.param.FlowAnalysisParam;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.FlowPageAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR> on 2018/11/26.
 */
@RestController
@RequestMapping("/platform/flowPageAnalysis")
@Tag(name = "流量页面分析")
@AllArgsConstructor
public class FlowPageAnalysisController {

    private final FlowPageAnalysisService flowPageAnalysisService;

    @GetMapping("/page")
    @Operation(summary = "分页获取页面统计数据" , description = "分页获取页面统计数据")
    @PreAuthorize("@pms.hasPermission('platform:flowPageAnalysis:page')")
    public ServerResponseEntity<IPage<PageAnalysisDto>> page(PageParam<PageAnalysisDto> page, FlowAnalysisParam flowAnalysisParam) {
        return ServerResponseEntity.success(flowPageAnalysisService.getPageOrProdAnalysis(page, flowAnalysisParam));
    }
}
