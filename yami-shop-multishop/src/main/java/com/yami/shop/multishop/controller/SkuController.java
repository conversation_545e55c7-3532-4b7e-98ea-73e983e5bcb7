package com.yami.shop.multishop.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.app.dto.ProductDto;
import com.yami.shop.bean.model.Product;
import com.yami.shop.bean.model.Sku;
import com.yami.shop.bean.param.ProductParam;
import com.yami.shop.common.enums.StatusEnum;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.SkuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sku")
@Tag(name = "sku规格接口")
@AllArgsConstructor
public class SkuController {

    private final SkuService skuService;

    @GetMapping("/getAllSkuList")
    @Operation(summary = "通过prodId获取全部的规格列表" , description = "通过prodId获取全部的规格列表")
    @Parameter(name = "prodId", description = "商品id" )
    @PreAuthorize("@pms.hasPermission('sku:list')")
    public ServerResponseEntity<List<Sku>> getSkuListByProdId(Long prodId) {
        List<Sku> skus = skuService.listSkuAndSkuStockForAdmin(prodId);
        return ServerResponseEntity.success(skus);
    }

    @GetMapping("/getEnableSkuList")
    @Operation(summary = "通过prodId获取启用的规格列表" , description = "通过prodId获取启用的规格列表")
    @Parameter(name = "prodId", description = "商品id" )
    @PreAuthorize("@pms.hasPermission('sku:enableList')")
    public ServerResponseEntity<List<Sku>> getEnableSkuList(Long prodId) {
        List<Sku> skus = skuService.listPutOnSkuAndSkuStock(prodId, null, null, true, null);
        skus = skus.stream().filter(item -> Objects.equals(item.getStatus(), StatusEnum.ENABLE.value())).collect(Collectors.toList());
        return ServerResponseEntity.success(skus);
    }

    @GetMapping("/pageSku")
    @Operation(summary = "分页查询sku信息" , description = "分页查询sku信息")
    @PreAuthorize("@pms.hasPermission('sku:page')")
    public ServerResponseEntity<IPage<Sku>> pageSku(PageParam<Sku> page, ProductParam product) {
        product.setLang(I18nMessage.getLang());
        product.setShopId(SecurityUtils.getShopUser().getShopId());
        IPage<Sku> skuPage = skuService.pageSku(page, product);
        return ServerResponseEntity.success(skuPage);
    }

    @GetMapping("/exportSkuList")
    @Operation(summary = "导出sku信息" , description = "导出sku信息")
    @PreAuthorize("@pms.hasPermission('sku:export')")
    public void exportSkuList(HttpServletResponse response, ProductParam product) {
        product.setShopId(SecurityUtils.getShopUser().getShopId());
        skuService.exportSkuList(product, response);
    }
    @GetMapping("/stockWarningCount")
    @Operation(summary = "获取库存预警数量" , description = "获取库存预警数量")
    public ServerResponseEntity<Long> stockWarningCount(){
        return ServerResponseEntity.success(skuService.stockWarningCount(SecurityUtils.getShopUser().getShopId()));
    }

    @PostMapping("/calculateStockBySkuIds")
    @Operation(summary = "计算当前商品sku列表的总共同仓库库存数量", description = "计算当前商品sku列表的总共同仓库库存数量")
    public ServerResponseEntity<Product> calculateStockBySkuIds(@RequestBody ProductDto productDto) {
        productDto.setShopId(AuthUserContext.getShopId());
        Product product = skuService.calculateStockBySkuIds(productDto);
        return ServerResponseEntity.success(product);
    }
}
