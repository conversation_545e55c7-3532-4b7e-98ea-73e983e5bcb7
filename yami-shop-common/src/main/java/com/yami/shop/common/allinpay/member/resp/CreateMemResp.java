package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;
/**
 * 创建会员 返回
 *
 * <AUTHOR>
 */
public class CreateMemResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 云商通用户唯一标识
     */
    private String userId;

    /**
     * 商户系统用户标识，商户系统中唯一编号。
     */
    private String bizUserId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

}
