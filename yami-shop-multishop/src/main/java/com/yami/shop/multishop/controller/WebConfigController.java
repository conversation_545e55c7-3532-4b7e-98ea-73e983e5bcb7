package com.yami.shop.multishop.controller;


import com.yami.shop.bean.enums.WebConfigTypeEnum;
import com.yami.shop.bean.vo.WebConfigVO;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.bean.SysServiceConfig;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.SysConfigService;
import com.yami.shop.service.WebConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021-02-20 09:44:42
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sys/webConfig")
@Tag(name = "系统配置接口")
public class WebConfigController {

    private final SysConfigService sysConfigService;

    private final WebConfigService webConfigService;


    /**
     * 获取当前激活的商家端网站配置
     *
     * @return
     */
    @GetMapping("/getActivity")
    @Operation(summary = "获取当前激活的商家端网站配置" , description = "获取当前激活的商家端网站配置")
    public ServerResponseEntity<WebConfigVO> getActivityWebConfig() {
        WebConfigVO webConfig = webConfigService.getActivityWebConfig(WebConfigTypeEnum.MULTISHOP.value());
        SysServiceConfig sysConfigObject = sysConfigService.getSysConfigObject(Constant.SERVICE_SWITCH_CONFIG, SysServiceConfig.class);
        webConfig.setMerchantRegisterProtocolSwitch(sysConfigObject.getMerchantRegisterProtocolSwitch());
        return ServerResponseEntity.success(webConfig);
    }

    @SysLog("获取配置信息")
    @GetMapping("/info/{key}")
    @Operation(summary = "获取配置信息" , description = "获取配置信息")
    @Parameter(name = "key", description = "参数名" )
    @PreAuthorize("@pms.hasPermission('sys:webConfig:info')")
    public ServerResponseEntity<String> info(@PathVariable("key") String key) {
        return ServerResponseEntity.success(sysConfigService.getValue(key));
    }
}
