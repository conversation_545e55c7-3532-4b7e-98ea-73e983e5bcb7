package com.yami.shop.discount.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yami.shop.discount.common.model.DiscountProd;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DiscountProdMapper extends BaseMapper<DiscountProd> {

    /**
     * 批量插入折扣商品信息
     * @param discountProds 满减满折商品关联列表
     */
    void insertDiscountProds(@Param("discountProds") List<DiscountProd> discountProds);

    /**
     * 根据折扣ID批量获取折扣商品列表
     * @param discountId 折扣优惠ID
     * @return 满减满折商品关联列表
     */
    List<DiscountProd> getDiscountProdByDiscountId(@Param("discountId") Long discountId);

}
