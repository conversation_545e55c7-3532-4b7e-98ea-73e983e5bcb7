package com.yami.shop.bean.app.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "添加评论信息")
public class ProdCommParam {

    @Schema(description = "订单项ID" )
    @NotNull(message = "订单项ID不能为空")
    private Long orderItemId;

    @Schema(description = "评分，0-5分" ,requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "评分不能为空")
    private Integer score;

    @Schema(description = "评论内容" ,requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "评论内容不能为空")
    @Size(max = 500, message = "评论内容长度应该小于{max}")
    private String content;

    @Schema(description = "评论图片, 用逗号分隔" )
    private String pics;

    @Schema(description = "是否匿名(1:是  0:否) 默认为否" )
    @NotNull(message = "是否匿名不能为空")
    private Integer isAnonymous;

    @Schema(description = "评价(0好评 1中评 2差评)" )
    @NotNull(message = "评价不能为空")
    private Integer evaluate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "评价记录时间" )
    @NotNull(message = "评价记录时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date recTime;

    }
