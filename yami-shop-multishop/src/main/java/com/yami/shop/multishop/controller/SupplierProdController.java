package com.yami.shop.multishop.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.dto.flow.SupplierProdDTO;
import com.yami.shop.bean.model.Supplier;
import com.yami.shop.bean.model.SupplierProd;
import com.yami.shop.bean.vo.SupplierProdVO;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.SupplierProdService;
import com.yami.shop.service.SupplierService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2021-09-07 10:12:32
 */
@RestController
@RequestMapping("/supplier/supplierProd")
@Tag(name = "供应商商品接口")
@AllArgsConstructor
public class SupplierProdController {

    private final SupplierProdService supplierProdService;
    private final SupplierService supplierService;

    @GetMapping("/listSupplierProd")
    @Operation(summary = "根据供应商id获取供应商商品列表" , description = "根据供应商id获取供应商商品列表")
    @PreAuthorize("@pms.hasPermission('supplier:supplierProd:list')")
    public ServerResponseEntity<List<SupplierProdVO>> listSupplierProd(@RequestParam("supplierId") Long supplierId) {
        Supplier supplier = supplierService.getById(supplierId);
        if (!Objects.equals(supplier.getShopId(), SecurityUtils.getShopUser().getShopId())) {
            // 无法查看非本店铺的商品
            throw new YamiShopBindException("yami.supplier.prod.not.shop");
        }
        List<SupplierProdVO> supplierProdList = supplierProdService.listSupplierProd(supplierId);
        return ServerResponseEntity.success(supplierProdList);
    }

    @GetMapping("/info/{supplierProdId}")
    @Operation(summary = "根据供应商商品id获取供应商商品信息" , description = "根据供应商商品id获取供应商商品信息")
    @PreAuthorize("@pms.hasPermission('supplier:supplierProd:info')")
    public ServerResponseEntity<SupplierProd> getById(@PathVariable("supplierProdId") Long supplierProdId) {
        return ServerResponseEntity.success(supplierProdService.getById(supplierProdId));
    }

    @PutMapping
    @Operation(summary = "修改供应商商品" , description = "修改供应商商品")
    @PreAuthorize("@pms.hasPermission('multishop:supplierProd:update')")
    public ServerResponseEntity<Boolean> updateBatch(@RequestBody List<SupplierProd> supplierProds,
                                               @RequestParam("supplierId") Long supplierId) {
        boolean update = supplierProdService.addOrUpdate(supplierProds, supplierId);
        supplierProdService.removeCacheBySupplierId(supplierId);
        return ServerResponseEntity.success(update);
    }

    @GetMapping("/page")
    @Operation(summary = "分页获取供应商商品列表" , description = "分页获取供应商商品列表")
    @PreAuthorize("@pms.hasPermission('supplier:supplierProd:page')")
    public ServerResponseEntity<IPage<SupplierProdVO>> page(PageParam<SupplierProdVO> pageParam,
                                                            SupplierProdDTO supplierProdDTO) {
        supplierProdDTO.setShopId(SecurityUtils.getShopUser().getShopId());
        IPage<SupplierProdVO> page = supplierProdService.supplierProdPage(pageParam, supplierProdDTO);
        return ServerResponseEntity.success(page);
    }

    @GetMapping("/downLoadModel")
    @Schema(description = "下载导入供应商商品模板" )
    @PreAuthorize("@pms.hasPermission('multishop:supplierProd:downLoad')")
    public void downLoadModel(HttpServletResponse response) {
        supplierProdService.downLoadModel(response);
    }

    @Operation(summary = "导入供应商商品" , description = "导入供应商商品")
    @PostMapping("/importExcel")
    @ResponseBody
    @PreAuthorize("@pms.hasPermission('multishop:supplierProd:import')")
    public ServerResponseEntity<Object> importExcel(@RequestParam("excelFile") MultipartFile excelFile, @RequestParam("supplierId") Long supplierId) throws Exception {
        if (Objects.isNull(excelFile)) {
            throw new YamiShopBindException("yami.network.busy");
        }
        if (Objects.isNull(supplierId)) {
            throw new YamiShopBindException("yami.supplier.not.null");
        }
        Object o = supplierProdService.parseFile(excelFile, SecurityUtils.getShopUser().getShopId(), supplierId);
        supplierProdService.removeCacheBySupplierId(supplierId);
        return ServerResponseEntity.success(o);
    }

    @GetMapping("/exportSupplierProd")
    @Schema(description = "导出供应商商品" )
    @PreAuthorize("@pms.hasPermission('multishop:supplierProd:export')")
    public void exportSupplierProd(@RequestParam("supplierId") Long supplierId, HttpServletResponse response) {
        supplierProdService.exportSupplierProd(supplierId, response);
    }
}
