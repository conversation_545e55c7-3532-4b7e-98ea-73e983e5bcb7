package com.yami.shop.distribution.common.service.impl;

import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yami.shop.bean.vo.UserVO;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.util.PrincipalUtil;
import com.yami.shop.distribution.common.dao.DistributionUserBindMapper;
import com.yami.shop.distribution.common.model.DistributionUserBind;
import com.yami.shop.distribution.common.param.RangeTimeParam;
import com.yami.shop.distribution.common.service.DistributionUserBindService;
import com.yami.shop.distribution.common.vo.DistributionUserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR> on 2019/04/01.
 */
@Service
@RequiredArgsConstructor
public class DistributionUserBindServiceImpl extends ServiceImpl<DistributionUserBindMapper, DistributionUserBind> implements DistributionUserBindService {

    private final DistributionUserBindMapper distributionUserBindMapper;

    @Override
    public IPage<DistributionUserBind> distributionMsgsAndUserPage(Page page, DistributionUserBind distributionUserBind, RangeTimeParam bindTime, RangeTimeParam invalidTime, String userName, String parentName, String cUserMobile, String dUserMobile, Integer sort, Integer orderBy) {
        IPage<DistributionUserBind> bindPage = distributionUserBindMapper.distributionMsgsAndUserPage(page, distributionUserBind, bindTime, invalidTime, userName, parentName, cUserMobile, dUserMobile, sort, orderBy);
        for (DistributionUserBind record : bindPage.getRecords()) {
            if (Objects.isNull(record.getDistributionUser().getNickName())) {
                record.getDistributionUser().setNickName(I18nMessage.getMessage("yami.user.off"));
            }
        }
        for (DistributionUserBind record : bindPage.getRecords()) {
            DistributionUserVO distributionUser = record.getDistributionUser();
            if (Objects.nonNull(distributionUser)) {
                if(StrUtil.isNotBlank(distributionUser.getUserMobile())){
                    distributionUser.setUserMobile(PhoneUtil.hideBetween(distributionUser.getUserMobile()).toString());
                }
                if (PrincipalUtil.isMobile(distributionUser.getNickName())) {
                    distributionUser.setNickName(PhoneUtil.hideBetween(distributionUser.getNickName()).toString());
                }
            }
            UserVO user = record.getUser();
            if (Objects.nonNull(user)) {
                user.setUserMobile(user.getUserMobile() != null ? PhoneUtil.hideBetween(user.getUserMobile()).toString() : null);
                if (PrincipalUtil.isMobile(user.getNickName())) {
                    user.setNickName(PhoneUtil.hideBetween(user.getNickName()).toString());
                }
            }
        }
        return bindPage;
    }
}
