package com.yami.shop.common.allinpay.order.resp;


import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-03-30
 */
public class DepositApplyResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 通联订单号
     */
    private String orderNo;

    /**
     * 商户订单号
     */
    private String bizOrderNo;

    /**
     * 渠道交易流水号
     */
    private String payInterfaceOutTradeNo;

    /**
     * 微信APP支付信息(JSON)
     */
    private String weChatAPPInfo;

    /**
     * 收银台支付宝小程序支付信息(JSON)
     */
    private String miniprogramPayInfo_VSP;

    /**
     * 交易验证方式(1:时，需要调用‘确认支付（后台+短信验证码确认）’功能)
     */
    private Integer validateType;

    /**
     * 扫码支付信息
     */
    private String payInfo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getPayInterfaceOutTradeNo() {
        return payInterfaceOutTradeNo;
    }

    public void setPayInterfaceOutTradeNo(String payInterfaceOutTradeNo) {
        this.payInterfaceOutTradeNo = payInterfaceOutTradeNo;
    }

    public String getWeChatAPPInfo() {
        return weChatAPPInfo;
    }

    public void setWeChatAPPInfo(String weChatAPPInfo) {
        this.weChatAPPInfo = weChatAPPInfo;
    }

    public Integer getValidateType() {
        return validateType;
    }

    public void setValidateType(Integer validateType) {
        this.validateType = validateType;
    }

    public String getPayInfo() {
        return payInfo;
    }

    public void setPayInfo(String payInfo) {
        this.payInfo = payInfo;
    }

    public String getMiniprogramPayInfo_VSP() {
        return miniprogramPayInfo_VSP;
    }

    public void setMiniprogramPayInfo_VSP(String miniprogramPayInfo_VSP) {
        this.miniprogramPayInfo_VSP = miniprogramPayInfo_VSP;
    }

    @Override
    public String toString() {
        return "DepositApplyResp{" +
                "orderNo='" + orderNo + '\'' +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                ", payInterfaceOutTradeNo='" + payInterfaceOutTradeNo + '\'' +
                ", weChatAPPInfo='" + weChatAPPInfo + '\'' +
                ", miniprogramPayInfo_VSP='" + miniprogramPayInfo_VSP + '\'' +
                ", validateType=" + validateType +
                ", payInfo='" + payInfo + '\'' +
                '}';
    }
}
