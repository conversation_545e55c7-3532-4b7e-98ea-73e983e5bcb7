package com.yami.shop.platform.controller;

import cn.hutool.core.date.DateUtil;
import com.yami.shop.bean.param.FlowUserAnalysisParam;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.platform.config.FlowUserAnalysisType;
import com.yami.shop.service.FlowUserAnalysisExcelService;
import com.yami.shop.service.FlowUserAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> on 2018/11/26.
 */
@RestController
@RequestMapping("/platform/flowUserAnalysis")
@Tag(name = "用户数据分析")
@AllArgsConstructor
public class FlowUserAnalysisController {

    private final FlowUserAnalysisService flowUserAnalysisService;
    private final FlowUserAnalysisExcelService flowUserAnalysisExcelService;


    @GetMapping("/getUserAnalysisData")
    @Operation(summary = "获取会员分析数据" , description = "获取会员分析数据")
    @PreAuthorize("@pms.hasPermission('platform:flowUserAnalysis:info')")
    public ServerResponseEntity<FlowUserAnalysisParam> getUserAnalysisData(FlowUserAnalysisParam flowUserAnalysisParam) {
        handleTime(flowUserAnalysisParam);
        flowUserAnalysisService.getUserAnalysisData(flowUserAnalysisParam);
        return ServerResponseEntity.success(flowUserAnalysisParam);
    }


    @GetMapping("/userAnalysisDataExport")
    @PreAuthorize("@pms.hasPermission('user:analysis:data:export')")
    @Operation(summary = "导出会员分析数据-地图" , description = "导出会员分析数据-地图")
    public void userAnalysisDataExport(FlowUserAnalysisParam flowUserAnalysisParam, HttpServletResponse response) {
        handleTime(flowUserAnalysisParam);
        flowUserAnalysisExcelService.userAnalysisDataExport(flowUserAnalysisParam, response);
    }

    private void handleTime(FlowUserAnalysisParam flowUserAnalysisParam) {
        Integer type = flowUserAnalysisParam.getType();
        if (!Objects.equals(type,FlowUserAnalysisType.CUSTOM.value())){
            int day = 0;
            if (type == 1){
                day = -7;
            }else {
                day = -30;
            }
            Date endTime = DateUtil.beginOfDay(new Date());
            flowUserAnalysisParam.setEndTime(endTime);
            flowUserAnalysisParam.setStartTime(DateUtil.offsetDay(endTime,day));
            flowUserAnalysisParam.setStart(flowUserAnalysisParam.getStartTime().getTime());
            flowUserAnalysisParam.setEnd(DateUtil.offsetDay(endTime,-1).getTime());
        }else {
            if (Objects.isNull(flowUserAnalysisParam.getEnd()) || Objects.isNull(flowUserAnalysisParam.getStart())){
                flowUserAnalysisParam.setEndTime(DateUtil.endOfDay(new Date()));
                flowUserAnalysisParam.setStartTime(DateUtil.beginOfDay(new Date()));
            }else {
                flowUserAnalysisParam.setEndTime(DateUtil.endOfDay(new Date(flowUserAnalysisParam.getEnd())));
                flowUserAnalysisParam.setStartTime(new Date(flowUserAnalysisParam.getStart()));
            }
        }
    }
}
