server:
  port: 8113
spring:
  datasource:
    url: *****************************************************************************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 0
      maximum-pool-size: 20
      idle-timeout: 25000
      auto-commit: true
      connection-test-query: SELECT 1
  data:
    redis:
      host: ***************
      port: 6379
  # es默认关闭，开启es的时候，请把spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration 这两行删除
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration
  elasticsearch:
    enable: false
    uris: http://***************:9200
  mail:
    # 企微邮箱为：smtp.exmail.qq.com，qq邮箱为：smtp.qq.com，163邮箱为：smtp.163.com，其他的请自行查找相关资料
    host: smtp.exmail.qq.com
    # 进行发送通知的邮箱账户名
    username:
    # 进行发送通知的邮箱密码，这个不是密码，而是自己邮箱-账户-开启POP3/SMTP时的客户端授权码
    password:

# 这个redis和上面的redis不一样，是用来存库存的
redis:
  aof:
    database: 0
    redis-addr: ***************:6389
    password:

logging:
  config: classpath:logback/logback-prod.xml
  # 接受异常通知的邮箱
  log-error-email:
  # 当前环境，比如测试环境，生产环境
  log-env:

wukongim:
  address: http://***************:5001
  token:

# 短信配置
sms:
  # 标注从yml读取配置
  config-type: yaml
  restricted: true
  account-max: 10
  minute-max: 1
  blends:
    local:
      supplier: alibaba #默认阿里云,参考详情见 SmsTypeEnum
      access-key-id: #必填 短信服务accessKeyId
      access-key-secret: #必填 短信accessKeySecret
      signature: #必填 短信签名signName
      request-url: #APP接入地址 (亿美软通)
      sender: #国内短信签名通道号 (华为云)
      status-call-back: #短信状态报告接收地 (华为云)
      url: #APP接入地址 (华为云)
      callback-url: #短信发送后将向这个地址推送(运营商返回的)发送报告 (云片短信)
      mch-id: #企业ID （联麓短信）
      app-key: #appKey （联麓短信）
      app-id: #appId （联麓短信）
      signature-id: #签名ID （七牛云短信）
