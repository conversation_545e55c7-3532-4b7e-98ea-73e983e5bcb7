package com.yami.shop.bean.dto.flow;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class MemberReqDTO extends CustomerReqDTO {

    @Schema(description = "0 全部会员 1免费会员(普通会员) 2付费会员" )
    private Integer memberType;

    @Schema(description = "领券会员数" )
    private Long receiverUserNum;

    @Schema(description = "几年前" )
    private Date beforeYear;

    @Hidden
    @Schema(description = "支付系统")
    private Integer paySysType;
}
