package com.yami.shop.security.platform.adapter;

import com.yami.shop.security.common.adapter.DefaultAuthConfigAdapter;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/16
 */
@Component
public class ResourceServerAdapter extends DefaultAuthConfigAdapter {

    public static final List<String> EXCLUDE_PATH = Arrays.asList(
            "/webjars/**",
            "/swagger/**",
            "/v3/api-docs/**",
            "/doc.html",
            "/swagger-ui.html",
            "/swagger-resources/**",
            "/captcha/**",
            "/sys/webConfig/getActivity",
            "/platformLogin",
            "/actuator/health/liveness",
            "/actuator/health/readiness",
            "/sys/public/config/**",
            "/sys/lang"
    );

    @Override
    public List<String> excludePathPatterns() {
        return EXCLUDE_PATH;
    }
}
