package com.yami.shop.bean.app.dto;

import com.yami.shop.bean.dto.DerivativeCategoryDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CategoryDto {

    @Schema(description = "分类id" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Long categoryId;

    @Schema(description = "分类父id" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Long parentId;

    @Schema(description = "分类名称" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String categoryName;

    @Schema(description = "分类图片" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String pic;

    @Schema(description = "分类图标" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String icon;

    @Schema(description = "子类分类列表" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DerivativeCategoryDto> categories;

}
