<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yami.shop.distribution.common.dao.DistributionProdBindMapper">
    <resultMap id="distributionProdBindMap" type="com.yami.shop.distribution.common.model.DistributionProdBind">
         <id property="id" column="id"/>
        <result property="distributionUserId" column="distribution_user_id"/>
        <result property="userId" column="user_id"/>
        <result property="bindTime" column="bind_time"/>
        <result property="prodId" column="prod_id"/>
        <result property="state" column="state"/>
    </resultMap>




    <!--根据分销员id修改状态-->
    <update id="updateStateByDistributionUserId">
        update tz_distribution_prod_bind
        set state = #{state}
        where
        distribution_user_id = #{distributionUserId}
    </update>




</mapper>
