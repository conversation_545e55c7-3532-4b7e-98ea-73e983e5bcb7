package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/11/26 14:36
 */
@Data
public class SkuAddrVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "属性id")
    private Long skuId;

    @Schema(description = "SPU id")
    private Long prodId;

    @Schema(description = "shopId")
    private Long shopId;

    @Schema(description = "商品重量")
    private BigDecimal weight;

    @Schema(description = "商品体积")
    private BigDecimal volume;

    @Schema(description = "运费模板id")
    private Long deliveryTemplateId;
    /**
     * 商品类别 0.实物商品 1. 虚拟商品 2.组合商品
     */
    private Integer mold;

}
