package com.yami.shop.common.allinpay.member.resp;


import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * 企业账户基本信息
 *
 * <AUTHOR>
 */
public class CompanyBasicInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "企业名称，如有括号，用中文格式（）")
    protected String companyName;

    @Schema(description = "企业地址")
    protected String companyAddress;

    @Schema(description = "认证类型 1.三证 2.一证 本系统采用2.一证")
    protected Integer authType;

    @Schema(description = "统一社会信用")
    protected String uniCredit;

    @Schema(description = "绑定手机 返回参数")
    protected String phone;

    @Schema(description = "联系电话")
    protected String telephone;

    @Schema(description = "法人姓名")
    protected String legalName;

    @Schema(description = "法人证件类型")
    protected Integer identityType;

    @Schema(description = "法人证件号")
    protected String legalIds;

    @Schema(description = "证件有效开始日期")
    protected String identityBeginDate;

    @Schema(description = "证件有效截止日期")
    protected String identityEndDate;

    @Schema(description = "法人手机号")
    protected String legalPhone;

    @Schema(description = "企业对公账户")
    protected String accountNo;

    @Schema(description = "开户银行名称")
    protected String parentBankName;

    @Schema(description = "开户行地区代码")
    protected String bankCityNo;

    @Schema(description = "开户支行名称")
    protected String bankName;

    @Schema(description = "支付行号 12位数字")
    protected String unionBank;

    @Schema(description = "开户行所在省")
    protected String province;

    @Schema(description = "开户行所在市")
    protected String city;

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public Integer getAuthType() {
        return authType;
    }

    public void setAuthType(Integer authType) {
        this.authType = authType;
    }

    public String getUniCredit() {
        return uniCredit;
    }

    public void setUniCredit(String uniCredit) {
        this.uniCredit = uniCredit;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    public Integer getIdentityType() {
        return identityType;
    }

    public void setIdentityType(Integer identityType) {
        this.identityType = identityType;
    }

    public String getLegalIds() {
        return legalIds;
    }

    public void setLegalIds(String legalIds) {
        this.legalIds = legalIds;
    }

    public String getIdentityBeginDate() {
        return identityBeginDate;
    }

    public void setIdentityBeginDate(String identityBeginDate) {
        this.identityBeginDate = identityBeginDate;
    }

    public String getIdentityEndDate() {
        return identityEndDate;
    }

    public void setIdentityEndDate(String identityEndDate) {
        this.identityEndDate = identityEndDate;
    }

    public String getLegalPhone() {
        return legalPhone;
    }

    public void setLegalPhone(String legalPhone) {
        this.legalPhone = legalPhone;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getParentBankName() {
        return parentBankName;
    }

    public void setParentBankName(String parentBankName) {
        this.parentBankName = parentBankName;
    }

    public String getBankCityNo() {
        return bankCityNo;
    }

    public void setBankCityNo(String bankCityNo) {
        this.bankCityNo = bankCityNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getUnionBank() {
        return unionBank;
    }

    public void setUnionBank(String unionBank) {
        this.unionBank = unionBank;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Override
    public String toString() {
        return "CompanyBasicInfo{" +
                "companyName='" + companyName + '\'' +
                ", companyAddress='" + companyAddress + '\'' +
                ", authType=" + authType +
                ", uniCredit='" + uniCredit + '\'' +
                ", phone='" + phone + '\'' +
                ", telephone='" + telephone + '\'' +
                ", legalName='" + legalName + '\'' +
                ", identityType=" + identityType +
                ", legalIds='" + legalIds + '\'' +
                ", identityBeginDate='" + identityBeginDate + '\'' +
                ", identityEndDate='" + identityEndDate + '\'' +
                ", legalPhone='" + legalPhone + '\'' +
                ", accountNo='" + accountNo + '\'' +
                ", parentBankName='" + parentBankName + '\'' +
                ", bankCityNo='" + bankCityNo + '\'' +
                ", bankName='" + bankName + '\'' +
                ", unionBank='" + unionBank + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                '}';
    }
}
