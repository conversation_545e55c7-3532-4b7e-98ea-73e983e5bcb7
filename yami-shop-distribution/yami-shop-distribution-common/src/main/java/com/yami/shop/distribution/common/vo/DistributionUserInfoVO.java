package com.yami.shop.distribution.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 我的用户信息
 * <AUTHOR>
 */
@Schema(description = "分销员推广用户信息")
@Data
public class DistributionUserInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户id" )
    private String userId;

    @Schema(description = "分销员id" )
    private Long distributionUserId;

    @Schema(description = "用户昵称" )
    private String nickName;

    @Schema(description = "头像" )
    private String pic;

    @Schema(description = "贡献收益" )
    private Double money;

    @Schema(description = "成交订单数" )
    private Integer orderNumber;

    @Schema(description = "用户状态 状态(-1永久封禁 0待审核状态 1正常 2暂时封禁 3 审核未通过)" )
    private Integer state;

    @Schema(description = "加入时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date bindTime;

    @Schema(description = "状态 0关 1开" )
    private Integer recruitState;
}
