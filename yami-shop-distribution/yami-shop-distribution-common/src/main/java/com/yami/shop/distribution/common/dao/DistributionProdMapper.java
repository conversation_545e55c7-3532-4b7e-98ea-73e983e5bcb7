package com.yami.shop.distribution.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.bean.model.OrderItem;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.model.DistributionProd;
import com.yami.shop.distribution.common.model.DistributionProdLog;
import com.yami.shop.distribution.common.po.DistributionProdPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DistributionProdMapper extends BaseMapper<DistributionProd> {

    /**
     * 查询分销商品列表
     * @param page 分页参数
     * @param distributionProd 分销商品
     * @param prodName 商品名称
     * @return 分销商品列表
     */
    IPage<DistributionProd> distributionProdsPage(Page page, @Param("distributionProd") DistributionProd distributionProd, @Param("prodName") String prodName);

    /**
     * 查询分销商品列表
     * @param page 分页参数
     * @param prodName 商品名称
     * @param sort 排序规则
     * @param orderBy 正序 / 倒序
     * @param lang 语言
     * @return 分销商品 分页
     */
    IPage<DistributionProdPO> distributionProdPoPage(Page page, @Param("prodName") String prodName,
                                                     @Param("sort") Integer sort, @Param("orderBy") Integer orderBy, @Param("lang") Integer lang);

    /**
     * 获取分销商品详情
     * @param prodId 商品id
     * @return 分销商品详情
     */
    DistributionProdPO getDistributionProdPoByProdId(@Param("prodId") Long prodId);

    /**
     * 更新分销商品状态
     * @param distributionProdId 分销商品id
     * @param state 状态
     * @return 是否更新成功
     */
    int updateState(@Param("distributionProdId") Long distributionProdId, @Param("state") int state);

    /**
     * 分销商品记录
     * @param page 分页参数
     * @param distributionProdLog 记录参数
     * @return 分销商品记录
     */
    IPage<DistributionProdLog> getDistributionProdLogPage(@Param("page") PageParam<DistributionProdLog> page,
                                                          @Param("distributionProdLog") DistributionProdLog distributionProdLog,
                                                          @Param("paySysType") Integer paySysType);

    /**
     * 获取订单项中的分销商品信息
     * @param orderItems 订单项信息
     * @return 分销商品信息
     */
    List<DistributionProd> listByOrderItems(@Param("orderItems") List<OrderItem> orderItems);
}
