package com.yami.shop.common.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yami.shop.common.wrapper.SensitiveWordRequestWrapper;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021/8/17 11:05
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SensitiveFilter implements Filter {

    private final ObjectMapper objectMapper;

    private static final String[] NOTIFY_URLS = {"/order/refund/result/**", "/notice/pay/**", "/notice/im/online", "/notice/allinpay/**"};

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        // 只处理PUT和POST请求
        if (!"PUT".equalsIgnoreCase(req.getMethod()) || !"POST".equalsIgnoreCase(req.getMethod())) {
            chain.doFilter(request, response);
            return;
        }
        // 回调消息不需要过滤敏感词
        AntPathMatcher antPathMatcher = new AntPathMatcher();
        for (String url : NOTIFY_URLS) {
            if (antPathMatcher.match(req.getRequestURI(), url)) {
                chain.doFilter(request, response);
                return;
            }
        }


        String contentType = req.getContentType();
        // 敏感词输入过滤
        if (StrUtil.isNotBlank(contentType) && contentType.contains(ContentType.JSON.getValue())) {
            HttpServletRequestWrapper wrappedRequest = new SensitiveWordRequestWrapper(req,objectMapper);
            chain.doFilter(wrappedRequest, response);
        } else {
            // 前端传过来的数据都是json格式的，并且请求头带有json，遇到非常特殊的情况，可以不过滤敏感词
            chain.doFilter(req, response);
        }
    }
}
