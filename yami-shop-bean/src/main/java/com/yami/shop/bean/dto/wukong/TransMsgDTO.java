package com.yami.shop.bean.dto.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
public class TransMsgDTO {
    /**
     * 本系统自定义的文字类型用户发送机器人消息
     * -1是自动回复消息
     * -2是在线已读消息
     * -3是离线已读消息
     * -4是在线状态消息
     * -5是客服转接消息
     */
    @Schema(description = "消息类型 1为文字类型")
    private Integer type;

    @Schema(description = "消息内容")
    private String msg;

    @Schema(description = "发送方")
    private Integer sender;

    public TransMsgDTO(Integer type, String content) {
        this.type = type;
        this.msg = content;
    }

    public TransMsgDTO(Integer type, String msg, Integer sender) {
        this.type = type;
        this.msg = msg;
        this.sender = sender;
    }

    public TransMsgDTO() {
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getSender() {
        return sender;
    }

    public void setSender(Integer sender) {
        this.sender = sender;
    }

    @Override
    public String toString() {
        return "TransMsgDTO{" +
                "type=" + type +
                ", msg='" + msg + '\'' +
                ", sender=" + sender +
                '}';
    }
}
