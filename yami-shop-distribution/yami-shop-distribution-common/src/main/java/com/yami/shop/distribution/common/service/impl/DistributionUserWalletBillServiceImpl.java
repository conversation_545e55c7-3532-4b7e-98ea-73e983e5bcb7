package com.yami.shop.distribution.common.service.impl;


import cn.hutool.core.util.PhoneUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.util.PrincipalUtil;
import com.yami.shop.distribution.common.dao.DistributionUserWalletBillMapper;
import com.yami.shop.distribution.common.dto.DistributionUserWalletBillDto;
import com.yami.shop.distribution.common.model.DistributionUserWalletBill;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.service.DistributionUserWalletBillService;
import com.yami.shop.distribution.common.vo.DistributionUserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分销员钱包流水
 * <AUTHOR>
 * @date 2019-04-29 16:39:13
 */
@Service
@RequiredArgsConstructor
public class DistributionUserWalletBillServiceImpl extends ServiceImpl<DistributionUserWalletBillMapper, DistributionUserWalletBill> implements DistributionUserWalletBillService {

    private final DistributionUserWalletBillMapper distributionUserWalletBillMapper;

    @Override
    public Page<DistributionUserWalletBill> getDistributionUserWalletBillAndUserPage(Page page, DistributionParam distributionParam) {
        Page<DistributionUserWalletBill> billPage = distributionUserWalletBillMapper.getDistributionUserWalletBillAndUserPage(page, distributionParam);
        List<DistributionUserWalletBill> billPageRecords = billPage.getRecords();
        for (DistributionUserWalletBill billPageRecord : billPageRecords) {
            DistributionUserVO distributionUser = billPageRecord.getDistributionUser();
            distributionUser.setUserMobile(PhoneUtil.hideBetween(distributionUser.getUserMobile()).toString());
            if (PrincipalUtil.isMobile(distributionUser.getNickName())) {
                distributionUser.setNickName(PhoneUtil.hideBetween(distributionUser.getNickName()).toString());
            }
        }
        if (I18nMessage.getLang() == 1) {
            for (DistributionUserWalletBill billPageRecord : billPageRecords) {
                if (billPageRecord.getRemarksEn() != null){
                    billPageRecord.setRemarks(billPageRecord.getRemarksEn());
                }
            }
        }
        return billPage;
    }

    @Override
    public Page<DistributionUserWalletBillDto> getDistributionUserWalletBillDtoPage(Page page, Long distributionUserId,Integer orderBy) {
        return distributionUserWalletBillMapper.getDistributionUserWalletBillDtoPage(page,distributionUserId,orderBy);
    }

    @Override
    public void initWalletBill() {
        distributionUserWalletBillMapper.initWalletBill();
    }
}
