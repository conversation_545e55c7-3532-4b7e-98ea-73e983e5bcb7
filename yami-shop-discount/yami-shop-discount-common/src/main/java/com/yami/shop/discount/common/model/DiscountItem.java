package com.yami.shop.discount.common.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("tz_discount_item")
public class DiscountItem implements Serializable {
    private static final long serialVersionUID = -35634815438365118L;
    /**
     * 满减满折优惠项id
     */
    @TableId

    private Long discountItemId;

    /**
     * 满减满折优惠id
     */

    private Long discountId;

    /**
     * 所需需要金额
     */

    private Double needAmount;

    /**
     * 优惠（元/折）
     */
    private Double discount;

}
