package com.yami.shop.bean.app.param;

import com.yami.shop.bean.model.OrderInvoice;
import com.yami.shop.bean.vo.VirtualRemarkVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "提交订单参数")
public class SubmitOrderParam {
    @Schema(description = "每个店铺提交的订单信息" , requiredMode = Schema.RequiredMode.REQUIRED)
    private List<OrderShopParam> orderShopParams;

    @Schema(description = "每次订单提交时的uuid" )
    private String uuid;

    @Schema(description = "发票信息" )
    private List<OrderInvoice> orderInvoiceList;

    @Schema(description = "虚拟商品留言备注" )
    private List<VirtualRemarkVO> virtualRemarkList;

    @Schema(description = "用户操作参数")
    private OrderFlowLogParam orderFlowLogParam;
}
