package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DiscountDto implements Serializable {

    @Schema(description = "满减满折优惠id" )
    private Long discountId;

    @Schema(description = "店铺ID" )
    private Long shopId;

    @Schema(description = "活动名称" )
    private String discountName;

    @Schema(description = "店铺名称" )
    private String shopName;

    @Schema(description = "店铺Logo" )
    private String shopLogo;

    @Schema(description = "手机端活动图片" )
    private String mobilePic;

    @Schema(description = "pc端活动列表图片" )
    private String pcPic;

    @Schema(description = "pc端活动背景图片" )
    private String pcBackgroundPic;

    @Schema(description = "优惠规则(0:满钱减钱 1:满件减钱 2:满钱打折 3:满件打折)" )
    private Integer discountRule;

    @Schema(description = "减免类型(0:按满足最高层级减一次 1:每满一次减一次)" )
    private Integer discountType;

    @Schema(description = "适用商品类型(0:通用券 1:商品券)" )
    private Integer suitableProdType;

    @Schema(description = "最多减多少" )
    private Double maxReduceAmount;

    @Schema(description = "开始时间" )
    private Date startTime;

    @Schema(description = "结束时间" )
    private Date endTime;

    private List<DiscountItemDto> discountItems;

}
