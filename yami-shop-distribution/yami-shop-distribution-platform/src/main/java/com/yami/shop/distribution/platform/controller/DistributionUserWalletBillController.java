package com.yami.shop.distribution.platform.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.model.DistributionUserWalletBill;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.service.DistributionUserWalletBillService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2019-04-29 16:39:13
 */
@RestController
@RequestMapping("/distribution/distributionUserWalletBill")
@Tag(name = "平台端分销员钱包流水接口")
@AllArgsConstructor
public class DistributionUserWalletBillController {

    private final DistributionUserWalletBillService distributionUserWalletBillService;

    @GetMapping("/page")
    @Operation(summary = "分页获取分销员钱包流水列表")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserWalletBill:page')")
    public ServerResponseEntity<IPage<DistributionUserWalletBill>> getDistributionUserWalletBillPage(PageParam<DistributionUserWalletBill> page,
                                                                                                     DistributionUserWalletBill distributionUserWalletBill) {
        return ServerResponseEntity.success(distributionUserWalletBillService.page(page, new LambdaQueryWrapper<DistributionUserWalletBill>()));
    }

    @GetMapping("/pageAndUser")
    @Operation(summary = "分页查询(携带UserVO)")
    @Parameter(name = "userMobile", description = "分销员手机号码" )
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserWalletBill:page')")
    public ServerResponseEntity<IPage<DistributionUserWalletBill>> getDistributionUserWalletBillAndUserPage(PageParam page, DistributionParam distributionParam) {
        return ServerResponseEntity.success(distributionUserWalletBillService.getDistributionUserWalletBillAndUserPage(page, distributionParam));
    }

    @GetMapping("/info/{id}")
    @Operation(summary = "根据id获取分销员钱包流水信息")
    @Parameter(name = "id", description = "分销员钱包流水id" )
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserWalletBill:info')")
    public ServerResponseEntity<DistributionUserWalletBill> getById(@PathVariable("id") Long id) {
        return ServerResponseEntity.success(distributionUserWalletBillService.getById(id));
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserWalletBill:save')")
    @Operation(summary = "新增分销员钱包流水")
    public ServerResponseEntity<Boolean> save(@RequestBody @Valid DistributionUserWalletBill distributionUserWalletBill) {
        return ServerResponseEntity.success(distributionUserWalletBillService.save(distributionUserWalletBill));
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserWalletBill:update')")
    @Operation(summary = "修改分销员钱包流水")
    public ServerResponseEntity<Boolean> updateById(@RequestBody @Valid DistributionUserWalletBill distributionUserWalletBill) {
        return ServerResponseEntity.success(distributionUserWalletBillService.updateById(distributionUserWalletBill));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserWalletBill:delete')")
    @Operation(summary = "根据id删除分销员钱包流水")
    @Parameter(name = "id", description = "分销员钱包流水id" )
    public ServerResponseEntity<Boolean> removeById(@PathVariable Long id) {
        return ServerResponseEntity.success(distributionUserWalletBillService.removeById(id));
    }
}
