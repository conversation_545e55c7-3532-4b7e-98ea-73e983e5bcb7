package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;
/**
 * <AUTHOR>
 */
public class SignAcctProtocolResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 商户系统用户id
     */
    private String bizUserId;

    /**
     * 账户提现协议编号（需要保存进数据库）
     */
    private String acctProtocolNo;

    /**
     * 签约户名
     */
    private String signAcctName;

    /**
     * 签订结果（成功:ok，失败:error）
     */
    private String result;

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getAcctProtocolNo() {
        return acctProtocolNo;
    }

    public void setAcctProtocolNo(String acctProtocolNo) {
        this.acctProtocolNo = acctProtocolNo;
    }

    public String getSignAcctName() {
        return signAcctName;
    }

    public void setSignAcctName(String signAcctName) {
        this.signAcctName = signAcctName;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "SignAcctProtocolResp{" +
                "bizUserId='" + bizUserId + '\'' +
                ", acctProtocolNo='" + acctProtocolNo + '\'' +
                ", signAcctName='" + signAcctName + '\'' +
                ", result='" + result + '\'' +
                '}';
    }
}
