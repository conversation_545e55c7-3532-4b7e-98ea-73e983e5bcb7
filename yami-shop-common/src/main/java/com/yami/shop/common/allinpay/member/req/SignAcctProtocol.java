package com.yami.shop.common.allinpay.member.req;

import com.yami.shop.common.allinpay.member.constant.PageType;
import com.yami.shop.common.allinpay.member.constant.VisitSourceType;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class SignAcctProtocol extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "signAcctProtocol";

    /**
     * 签约户名(个人会员：名称)(企业会员：[法人提现]法人姓名、[对公户提现]企业名称)
     */
    private String signAcctName;

    /**
     * 跳转页面类型
     *
     * @see PageType
     */
    private Long jumpPageType;

    /**
     * 成功后跳转地址
     */
    private String jumpUrl;

    /**
     * 失败/取消后跳转地址
     */
    private String noContractUrl;

    /**
     * 通知地址
     */
    private String backUrl;

    /**
     * 访问终端类型
     *
     * @see VisitSourceType
     */
    private Integer source;

    public String getSignAcctName() {
        return signAcctName;
    }

    public void setSignAcctName(String signAcctName) {
        this.signAcctName = signAcctName;
    }

    public Long getJumpPageType() {
        return jumpPageType;
    }

    public void setJumpPageType(Long jumpPageType) {
        this.jumpPageType = jumpPageType;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getNoContractUrl() {
        return noContractUrl;
    }

    public void setNoContractUrl(String noContractUrl) {
        this.noContractUrl = noContractUrl;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    @Override
    public String toString() {
        return "SignAcctProtocol{" +
                "signAcctName='" + signAcctName + '\'' +
                ", jumpPageType=" + jumpPageType +
                ", jumpUrl='" + jumpUrl + '\'' +
                ", noContractUrl='" + noContractUrl + '\'' +
                ", backUrl='" + backUrl + '\'' +
                ", source=" + source +
                ", bizUserId='" + bizUserId + '\'' +
                '}';
    }
}
