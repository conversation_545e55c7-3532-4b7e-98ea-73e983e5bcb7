package com.yami.shop.discount.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 满减满折活动状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DiscountStatusEnum {

    /**  关闭    */
    CLOSE(0, "关闭"),

    /**  启动    */
    RUN(1, "启动"),

    /**  违规下线    */
    OFFLINE(2, "违规下线"),

    /**  平台审核    */
    PLATFORM_AUDIT(3, "平台审核");

    private final Integer value;
    private final String desc;

}

