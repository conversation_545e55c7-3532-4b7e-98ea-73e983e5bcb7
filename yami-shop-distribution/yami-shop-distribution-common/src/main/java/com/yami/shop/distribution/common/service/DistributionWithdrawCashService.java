package com.yami.shop.distribution.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.distribution.common.dto.DistributionWithdrawCashDto;
import com.yami.shop.distribution.common.model.DistributionUser;
import com.yami.shop.distribution.common.model.DistributionUserWallet;
import com.yami.shop.distribution.common.model.DistributionWithdrawCash;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.param.RangeTimeParam;
import com.yami.shop.security.common.model.AppConnect;

/**
 * <AUTHOR> on 2019/04/01.
 */
public interface DistributionWithdrawCashService extends IService<DistributionWithdrawCash> {

    /**
     * 分页获取分销员提现申请记录
     * @param page 分页参数
     * @param shopId 店铺id
     * @param rangeTimeParam 申请时间
     * @param distributionParam 分销查询参数
     * @param distributionWithdrawCash 查询参数
     * @return 分销员提现申请记录
     */
    IPage<DistributionWithdrawCash> distributionWithdrawCashsPage(Page page, Long shopId, RangeTimeParam rangeTimeParam, DistributionParam distributionParam, DistributionWithdrawCash distributionWithdrawCash);

    /**
     * 分页获取分销员提现申请记录
     * @param page 分页参数
     * @param distributionUserId 分销员id
     * @return 分销员提现申请记录
     */
    IPage<DistributionWithdrawCashDto> distributionWithdrawCashDtoPageByUserId(Page page, Long distributionUserId);


    /**
     * 发起提现申请
     * @param amount 申请金额
     * @param distributionUser 分销员信息
     */
    void apply(Double amount, DistributionUser distributionUser);

    /**
     * 根据时间区间获取用户的提现次数
     *
     * @param rangeTimeParam 时间范围
     * @param distributionUserId 分销员id
     * @return 获取用户的提现次数
     */
    Integer getCountByRangeTimeAndDistributionUserId(RangeTimeParam rangeTimeParam, Long distributionUserId);

    /**
     * 查看分销员总提现金额
     * @param walletId 钱包id
     * @return 总提现金额
     */
    Double getUserTotalWithdrawCash(Long walletId);

    /**
     * 添加企业支付记录
     * @param distributionWithdrawCash  提现记录
     * @param userId     提现用户id
     * @param openId     提现用户openId
     * @param status     提现状态
     */
    void enterprisePay(DistributionWithdrawCash distributionWithdrawCash, String userId, String openId, Integer status);

    /**
     * 更新提现
     * @param distributionWithdrawCash
     * @param distributionUser
     * @param wallet
     */
    void updateWithDraw(DistributionWithdrawCash distributionWithdrawCash, DistributionUserWallet wallet, DistributionUser distributionUser);

}
