package com.yami.shop.common.util;


/**
 * 数字处理工具类
 *
 * <AUTHOR>
 * @version 1.0
 */
public class NumberUtil {

    private static final int[] RANGE_1 = {1, 7, 9, 2, 8, 6, 5, 0, 3, 4};

    private static final int[] RANGE_2 = {6, 2, 7, 0, 9, 5, 3, 1, 4, 8};

    private static final int[] RANGE_3 = {8, 9, 7, 4, 2, 0, 3, 1, 5, 6};

    private static final int[] RANGE_4 = {7, 4, 6, 0, 8, 5, 1, 9, 2, 3};

    private static final int[] RANGE_5 = {3, 2, 0, 8, 6, 4, 1, 7, 9, 5};

    private static final int[] RANGE_6 = {3, 7, 1, 5, 6, 9, 2, 0, 4, 8};

    private static final int[] RANGE_7 = {8, 7, 1, 4, 2, 9, 6, 0, 5, 3};

    private static final int[] RANGE_8 = {0, 1, 5, 4, 2, 3, 8, 9, 7, 6};

    private static final int[] RANGE_9 = {2, 6, 7, 3, 5, 9, 8, 4, 1, 0};

    private static final int[] RANGE_10 = {2, 7, 5, 1, 4, 8, 0, 3, 6, 9};

    private static final int[] RANGE_11 = {3, 6, 0, 1, 2, 5, 8, 9, 7, 4};

    private static final int[] RANGE_12 = {3, 6, 7, 5, 9, 2, 0, 8, 1, 4};

    private static final int[] RANGE_13 = {7, 5, 6, 0, 4, 2, 3, 1, 9, 8};

    private static final int[] RANGE_14 = {5, 8, 0, 7, 1, 3, 4, 6, 9, 2};

    private static final int[] RANGE_15 = {8, 2, 4, 9, 1, 7, 6, 3, 5, 0};

    private static final int[] RANGE_16 = {2, 6, 1, 0, 8, 7, 9, 5, 3, 4};

    private static final int[][] RANGE_ARRAY =
            {RANGE_1, RANGE_2, RANGE_3, RANGE_4, RANGE_5, RANGE_6, RANGE_7, RANGE_8, RANGE_9, RANGE_10, RANGE_11, RANGE_12, RANGE_13, RANGE_14, RANGE_15, RANGE_16};

    /**
     * 将有限的数字打乱如100会变成，RANGE_1的第一个数 + RANGE_2第0个数 + RANGE_3 第0个数，会变成768
     * @param id 原id
     * @param length id长度
     * @return 新的id
     */
    public static long genNo(long id, int length) {
        long result = 0;
        StringBuilder sb = new StringBuilder();
        sb.append(id);

        while (sb.length() < length) {
            sb.insert(0, "0");
        }
        for (int i = 0; i < length; i++) {
            // ASCII码 char转换为int要-48
            int indexId = sb.charAt(i) - 48;
            result += RANGE_ARRAY[i][indexId];
            if (i != length-1) {
                result *= 10;
            }
        }
        return result;
    }
}
