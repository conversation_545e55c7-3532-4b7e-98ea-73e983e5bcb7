package com.yami.shop.platform.controller;

import com.yami.shop.allinpay.service.AllinpayService;
import com.yami.shop.bean.dto.allinpay.LockMemberDTO;
import com.yami.shop.common.response.ServerResponseEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Tag(name = "通联支付-会员接口(平台端)")
@RestController("platformAllinpayMemberController")
@RequestMapping("/platform/allinpay/member")
@AllArgsConstructor
public class AllinpayMemberController {

    private final AllinpayService allinpayService;

    @Operation(summary = "锁定会员", description = "锁定会员")
    @PutMapping("/lock_member")
    @PreAuthorize("@pms.hasPermission('platform:allinpayMember:update')")
    public ServerResponseEntity<Void> lockMember(@RequestBody LockMemberDTO lockMemberDTO) {
        allinpayService.lockMember(lockMemberDTO);
        return ServerResponseEntity.success();
    }

    @Operation(summary = "解锁会员", description = "解锁会员")
    @PutMapping("/unlock_member")
    @PreAuthorize("@pms.hasPermission('platform:allinpayMember:update')")
    public ServerResponseEntity<Void> unlockMember(@RequestBody LockMemberDTO lockMemberDTO) {
        allinpayService.unlockMember(lockMemberDTO);
        return ServerResponseEntity.success();
    }
}
