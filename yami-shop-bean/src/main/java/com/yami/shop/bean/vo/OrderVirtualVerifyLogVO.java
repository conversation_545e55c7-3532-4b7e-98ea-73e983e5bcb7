package com.yami.shop.bean.vo;

import com.yami.shop.bean.model.OrderVirtualVerifyLog;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @author: zsm
 * @date: 2023/3/9 9:27
 */
@Schema(description = "虚拟订单核销记录信息")
@Data
public class OrderVirtualVerifyLogVO {

    @Schema(description = "订单编号" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNumber;



    @Schema(description = "券码核销记录" )
    private List<OrderVirtualVerifyLog> orderVirtualVerifyLogList;
}
