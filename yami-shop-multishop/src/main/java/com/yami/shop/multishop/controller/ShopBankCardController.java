package com.yami.shop.multishop.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yami.shop.allinpay.service.AllinpayCompanyService;
import com.yami.shop.bean.dto.allinpay.AllinpayShopBankCardDTO;
import com.yami.shop.bean.enums.AuditStatus;
import com.yami.shop.bean.model.ShopBankCard;
import com.yami.shop.bean.model.ShopCompany;
import com.yami.shop.bean.model.ShopDetail;
import com.yami.shop.bean.model.ShopWithdrawCash;
import com.yami.shop.bean.vo.ShopCompanyVO;
import com.yami.shop.common.allinpay.constant.CompanyInfoProcessStatus;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.ShopBankCardService;
import com.yami.shop.service.ShopCompanyService;
import com.yami.shop.service.ShopDetailService;
import com.yami.shop.service.ShopWithdrawCashService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;


/**
 * 商家提现申请信息
 *
 * <AUTHOR>
 * * @date 2020-04-07 14:22:08
 */
@RestController
@AllArgsConstructor
@RequestMapping("/shop/shopBankCard")
@Tag(name = "银行卡接口")
public class ShopBankCardController {
    private static final Logger logger = LoggerFactory.getLogger(ShopBankCardController.class);

    private final ShopBankCardService shopBankCardService;
    private final ShopWithdrawCashService shopWithdrawCashService;
    private final AllinpayCompanyService allinpayCompanyService;
    private final ShopCompanyService shopCompanyService;
    private final ShopDetailService shopDetailService;

    @GetMapping("/getShopBankCardList")
    @Operation(summary = "获取店铺下的银行卡列表", description = "获取店铺下的银行卡列表")
    public ServerResponseEntity<List<ShopBankCard>> getShopBankCardList() {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        List<ShopBankCard> list = shopBankCardService.list(new LambdaQueryWrapper<ShopBankCard>().eq(ShopBankCard::getShopId, shopId).eq(ShopBankCard::getStatus, 1));
        return ServerResponseEntity.success(list);
    }

    @GetMapping
    @Operation(summary = "根据银行卡id获取银行卡信息", description = "根据银行卡id获取银行卡信息")
    @PreAuthorize("@pms.hasPermission('shop:shopBankCard:info')")
    public ServerResponseEntity<ShopBankCard> getById(@RequestParam("shopBankCardId") Long shopBankCardId) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        ShopBankCard shopBankCard = shopBankCardService.getOne(Wrappers.lambdaQuery(ShopBankCard.class)
                .eq(ShopBankCard::getShopBankCardId, shopBankCardId)
                .eq(ShopBankCard::getShopId, shopId)
        );
        return ServerResponseEntity.success(shopBankCard);
    }

    @PutMapping
    @Operation(summary = "更新银行卡信息", description = "更新银行卡信息")
    @PreAuthorize("@pms.hasPermission('shop:shopBankCard:update')")
    public ServerResponseEntity<Void> update(@RequestBody @Valid ShopBankCard shopBankCard) {
        shopBankCardService.updateByShopId(shopBankCard, SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success();
    }

    @PostMapping
    @Operation(summary = "添加单个银行卡信息", description = "添加单个银行卡信息")
    @PreAuthorize("@pms.hasPermission('shop:shopBankCard:save')")
    public ServerResponseEntity<Long> save(@RequestBody @Valid ShopBankCard shopBankCard) {
        if (allinpayCompanyService.getIsAllinpay()) {
            // 银行卡接口错误，请刷新页面重试
            throw new YamiShopBindException("yami.shop.bank.exception.apiError");
        }
        Long shopId = SecurityUtils.getShopUser().getShopId();
        shopBankCard.setShopId(shopId);
        long count = shopBankCardService.count(new LambdaQueryWrapper<ShopBankCard>().eq(ShopBankCard::getShopId, shopId).eq(ShopBankCard::getStatus, 1));
        if (count >= Constant.MAX_SHOP_BANK_CARD) {
            throw new YamiShopBindException("yami.shop.max.card.num");
        }
        return ServerResponseEntity.success(shopBankCardService.insertSelective(shopBankCard));
    }

    @PutMapping("/setDefault")
    @Operation(summary = "设置为默认银行卡", description = "设置为默认银行卡")
    @PreAuthorize("@pms.hasPermission('shop:shopBankCard:default')")
    public ServerResponseEntity<Void> setDefault(@RequestBody @Valid ShopBankCard shopBankCard) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        shopBankCardService.setDefault(shopBankCard.getShopBankCardId(), shopId);
        return ServerResponseEntity.success();
    }

    @PutMapping("/setNotDefault")
    @Operation(summary = "取消默认银行卡", description = "取消默认银行卡")
    @PreAuthorize("@pms.hasPermission('shop:shopBankCard:noDefault')")
    public ServerResponseEntity<Void> setNotDefault(@RequestParam(value = "shopBankCardId") Long shopBankCardId) {
        shopBankCardService.setNotDefault(shopBankCardId, SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success();
    }

    @DeleteMapping("/{shopBankCardId}")
    @Operation(summary = "根据银行卡id删除银行卡信息", description = "根据银行卡id删除银行卡信息")
    @PreAuthorize("@pms.hasPermission('shop:shopBankCard:delete')")
    public ServerResponseEntity<Void> removeById(@PathVariable("shopBankCardId") Long shopBankCardId) {
        if (allinpayCompanyService.getIsAllinpay()) {
            // 银行卡接口错误，请刷新页面重试
            throw new YamiShopBindException("yami.shop.bank.exception.apiError");
        }
        Long shopId = SecurityUtils.getShopUser().getShopId();
        ShopBankCard shopBankCard = shopBankCardService.getById(shopBankCardId);
        if (Objects.equals(shopBankCard.getIsDefault(), 1)) {
            // 不能删除默认银行卡
            throw new YamiShopBindException("yami.shop.cannot.delete.card");
        }
        // 检查是否是最后一张银行卡
        if (shopBankCardService.count(Wrappers.lambdaQuery(ShopBankCard.class).eq(ShopBankCard::getShopId, shopId).eq(ShopBankCard::getStatus, 1)) == 1) {
            throw new YamiShopBindException("yami.shop.least.one.card");
        }
        long count = shopWithdrawCashService.count(new LambdaQueryWrapper<ShopWithdrawCash>()
                .eq(ShopWithdrawCash::getShopBankCardId, shopBankCardId).eq(ShopWithdrawCash::getStatus, 0));
        if (count > 0) {
            // 正在用于申请提现的银行卡不能删除
            throw new YamiShopBindException("yami.shop.cannot.delete.check");
        }
        // 进行逻辑删除
        shopBankCardService.update(new LambdaUpdateWrapper<ShopBankCard>().set(ShopBankCard::getStatus, -1).eq(ShopBankCard::getShopBankCardId, shopBankCardId).eq(ShopBankCard::getShopId, shopId));
        return ServerResponseEntity.success();
    }

    @PostMapping("/saveAndApplyShop")
    @Operation(summary = "批量保存店铺银行卡信息并提交店铺审核信息", description = "批量保存店铺银行卡信息并提交店铺审核信息")
    public ServerResponseEntity<Void> saveAndApplyShop(@Valid @RequestBody List<ShopBankCard> shopBankCards) {
        if (allinpayCompanyService.getIsAllinpay()) {
            throw new YamiShopBindException("yami.shop.bank.exception.apiError");
        }
        Long shopId = SecurityUtils.getShopUser().getShopId();
        Long employeeId = SecurityUtils.getShopUser().getEmployeeId();
        shopBankCardService.insertBatchAndSubmitApply(shopBankCards, shopId, employeeId);
        return ServerResponseEntity.success();
    }

    @PostMapping("/allinpaySaveAndApplyShop")
    @Operation(summary = "通联支付--批量保存店铺银行卡信息并提交店铺审核信息", description = "批量保存店铺银行卡信息并提交店铺审核信息")
    public ServerResponseEntity<Void> allinpaySaveAndApplyShop(@Valid @RequestBody AllinpayShopBankCardDTO allinpayShopBankCardDTO) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        // 查找工商信息
        ShopCompanyVO shopCompany = shopCompanyService.getShopCompanyByShopIdAndStatus(shopId, AuditStatus.WAITAUDIT.value());
        if (Objects.isNull(shopCompany)) {
            logger.info("店铺工商信息为空，请刷新页面重新填写");
            throw new YamiShopBindException("yami.shop.bank.exception.shopCompanyEmpty");
        }
        checkAllinpay(shopCompany);
        ShopDetail shopDetail = shopDetailService.getShopDetailByShopId(shopId);
        if (Objects.equals(shopDetail.getCompanyInfoProcessStatus(), CompanyInfoProcessStatus.SUCCESS.value())) {
            // 已经审核成功就不必要多次设置企业信息了
            throw new YamiShopBindException("yami.shop.bank.exception.passed");
        }

        shopBankCardService.insertAndSetCompanyInfo(allinpayShopBankCardDTO, shopId, BeanUtil.map(shopCompany, ShopCompany.class));
        shopDetailService.removeShopDetailCacheByShopId(shopId);
        return ServerResponseEntity.success();
    }

    private void checkAllinpay(ShopCompanyVO shopCompany) {
        boolean needInfo = Objects.isNull(shopCompany.getLegalIds()) || Objects.isNull(shopCompany.getLegalPhone());
        if (allinpayCompanyService.getIsAllinpay() && needInfo) {
            // 工商信息需要提交的内容有所变更，请重新填写
            throw new YamiShopBindException("yami.shop.bank.exception.shopCompanyChange");
        }
    }

    @PostMapping("/refresh")
    @Operation(summary = "刷新银行卡列表", description = "通联独有，防止有银行卡没有同步到数据库")
    public ServerResponseEntity<Void> refresh() {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        shopBankCardService.refresh(shopId);
        return ServerResponseEntity.success();
    }
}
