package com.yami.shop.discount.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@TableName("tz_discount")
public class Discount implements Serializable {
    private static final long serialVersionUID = -82534421471027766L;

    @TableId
    @Schema(description = "满减满折优惠id" )
    private Long discountId;

    @Schema(description = "活动名称" )
    private String discountName;

    @Schema(description = "枚举DiscountRule(0 满钱减钱 1满件减钱 2 满钱打折 3满件打折)" )
    private Integer discountRule;

    @Schema(description = "减免类型 0按满足最高层级减一次 1每满一次减一次" )
    private Integer discountType;

    @Schema(description = "适用商品类型 0全部商品参与 1指定商品参与 2指定商品不参与" )
    private Integer suitableProdType;

    @Schema(description = "最多减多少" )
    private Double maxReduceAmount;

    @Schema(description = "开始时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "结束时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Schema(description = "活动状态：1 开启 0 关闭" )
    private Integer status;

    @Schema(description = "手机端活动图片" )
    private String mobilePic;

    @Schema(description = "pc端活动列表图片" )
    private String pcPic;

    @Schema(description = "pc端活动背景图片" )
    private String pcBackgroundPic;

    @Schema(description = "店铺ID" )
    private Long shopId;

    @Schema(description = "店铺名称" )
    @TableField(exist = false)
    private String shopName;

    @Schema(description = "满减满折优惠项" )
    @TableField(exist = false)
    private List<DiscountItem> discountItems;

    @Schema(description = "满减满折商品" )
    @TableField(exist = false)
    private List<DiscountProd> discountProds;
}
