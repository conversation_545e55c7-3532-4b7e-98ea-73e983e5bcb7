package com.yami.shop.platform.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yami.shop.allinpay.service.AllinpayCompanyService;
import com.yami.shop.bean.app.param.ShopAuditingParam;
import com.yami.shop.bean.dto.ShopAuditingInfoDto;
import com.yami.shop.bean.enums.ShopStatus;
import com.yami.shop.bean.model.ShopAuditing;
import com.yami.shop.bean.model.ShopDetail;
import com.yami.shop.bean.param.AuditingInfoParam;
import com.yami.shop.bean.param.ShopTypeParam;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.common.util.PasswordUtil;
import com.yami.shop.common.util.RedisUtil;
import com.yami.shop.security.common.manager.PasswordManager;
import com.yami.shop.security.platform.util.SecurityUtils;
import com.yami.shop.service.ShopAuditingService;
import com.yami.shop.service.ShopDetailService;
import com.yami.shop.sys.common.model.ShopEmployee;
import com.yami.shop.sys.common.service.ShopEmployeeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;


/**
 * 商家审核信息
 *
 * <AUTHOR>
 * @date 2019-09-19 14:02:57
 */
@RestController
@AllArgsConstructor
@RequestMapping("/shop/shopAuditing")
@Tag(name = "店铺审核信息")
public class ShopAuditingController {

    private final ShopAuditingService shopAuditingService;
    private final ShopDetailService shopDetailService;
    private final ShopEmployeeService shopEmployeeService;
    private final PasswordEncoder passwordEncoder;
    private final PasswordManager passwordManager;
    private final AllinpayCompanyService allinpayCompanyService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('shop:shopAuditing:page')")
    @Operation(summary = "分页查询", description = "分页查询")
    public ServerResponseEntity<IPage<ShopAuditingInfoDto>> getShopAuditingPage(PageParam<ShopAuditingInfoDto> page, AuditingInfoParam auditingInfoParam) {
        return ServerResponseEntity.success(shopAuditingService.auditingInfoList(page, auditingInfoParam));
    }

    @GetMapping("/shopDetail/{shopId}")
    @PreAuthorize("@pms.hasPermission('shop:shopAuditing:info')")
    @Operation(summary = "查询详情信息", description = "查询详情信息")
    @Parameter(name = "shopId", description = "店铺id")
    public ServerResponseEntity<ShopDetail> auditingDetail(@PathVariable Long shopId) {
        ShopDetail shopDetail = shopDetailService.getShopDetailByShopId(shopId);
        return ServerResponseEntity.success(shopDetail);
    }

    @GetMapping("/{shopId}")
    @Operation(summary = "根据店铺id查询审核信息", description = "根据店铺id查询审核信息")
    @Parameter(name = "shopId", description = "店铺id")
    @PreAuthorize("@pms.hasPermission('shop:shopAuditing:info')")
    public ServerResponseEntity<ShopAuditing> getShopAuditing(@PathVariable Long shopId) {
        ShopAuditing shopAuditing = shopAuditingService.getOne(new LambdaQueryWrapper<ShopAuditing>().eq(ShopAuditing::getShopId, shopId));
        ShopDetail shopDetail = shopDetailService.getShopDetailByShopId(shopId);
        if (Objects.isNull(shopAuditing)) {
            shopAuditing = new ShopAuditing();
        }
        if (Objects.equals(shopDetail.getShopStatus(), ShopStatus.OFFLINE.value())) {
            shopAuditing.setStatus(2);
        } else if (Objects.equals(shopDetail.getShopStatus(), ShopStatus.OFFLINE_AUDIT.value())) {
            shopAuditing.setStatus(0);
        }
        return ServerResponseEntity.success(shopAuditing);
    }

    @SysLog("审核商家信息")
    @PutMapping("/audit")
    @PreAuthorize("@pms.hasPermission('shop:shopAuditing:audit')")
    @Operation(summary = "审核信息(审核商家)", description = "审核信息(审核商家)")
    public ServerResponseEntity<Void> audit(@Valid @RequestBody ShopAuditingParam shopAuditingParam) {
        if (allinpayCompanyService.getIsAllinpay()) {
            // 开启通联后，平台无需审核商家
            throw new YamiShopBindException("yami.shop.auditing.exception.notNeedAudit");
        }
        String limitKey = "AUDIT_" + shopAuditingParam.getShopId();
        Long incr = RedisUtil.incr(limitKey, 1);
        if (incr == 1) {
            RedisUtil.expire(limitKey, 1);
        } else {
            // 已提交审核请求
            throw new YamiShopBindException("yami.shop.auditing.exception.applied");
        }
        shopAuditingParam.setAuditorId(SecurityUtils.getSysUser().getUserId());
        shopDetailService.audit(shopAuditingParam);
        shopDetailService.removeShopDetailCacheByShopId(shopAuditingParam.getShopId());

        return ServerResponseEntity.success();
    }

    @PostMapping
    @Operation(summary = "新建店铺", description = "新建店铺")
    public ServerResponseEntity<ShopDetail> insertDetail(@RequestBody ShopDetail shopDetail) {
        // 判断是账号是否已存在
        long count = shopEmployeeService.checkUserName(shopDetail.getShopId(), shopDetail.getMobile());
        String decryptPassword = passwordManager.decryptPassword(shopDetail.getPassword());
        PasswordUtil.check(decryptPassword);
        shopDetail.setPassword(passwordEncoder.encode(decryptPassword));
        shopDetailService.insertDetail(shopDetail, count);
        return ServerResponseEntity.success(shopDetail);
    }

    @GetMapping("/checkMobile")
    @Operation(summary = "校验账号", description = "校验账号")
    @Parameters(value = {
            @Parameter(name = "mobile", description = "账号"),
            @Parameter(name = "shopId", description = "店铺id"),
    })
    public ServerResponseEntity<Boolean> checkMobile(@RequestParam("mobile") String mobile, @RequestParam("shopId") Long shopId) {
        boolean isTrue = true;
        if (Objects.isNull(shopId)) {
            shopId = 0L;
        }
        long count = shopDetailService.checkMobile(mobile, shopId);
        // 加多了一个表，并且为了适配，保证数据的唯一性，需要同时验证两个表的数据
        count = count + shopEmployeeService.checkUserName(shopId, mobile);
        if (count > 0) {
            isTrue = false;
        }
        return ServerResponseEntity.success(isTrue);
    }

    @GetMapping("/checkUsername")
    @Operation(summary = "检查用户名是否可用", description = "检查用户名是否可用")
    @Parameters(value = {
            @Parameter(name = "username", description = "用户名"),
            @Parameter(name = "shopId", description = "店铺id"),
    })
    public ServerResponseEntity<Boolean> checkUsername(@RequestParam("username") String username, @RequestParam("shopId") Long shopId) {
        if (StringUtils.isBlank(username)) {
            return ServerResponseEntity.success(Boolean.FALSE);
        }
        long count;
        if (Objects.isNull(shopId)) {
            // shopId为空，判断用户名是否重复
            count = shopEmployeeService.count(Wrappers.lambdaQuery(ShopEmployee.class)
                    .eq(ShopEmployee::getUsername, username)
            );
        } else {
            // 更改店铺商家用户名时，判断username是否重复
            count = shopEmployeeService.count(Wrappers.lambdaQuery(ShopEmployee.class)
                    .eq(ShopEmployee::getUsername, username)
                    .ne(ShopEmployee::getShopId, shopId)
            );
        }
        return ServerResponseEntity.success(count == 0);
    }

    @PutMapping("/updatePasswordOrMobile")
    @Operation(summary = "重置密码或者修改账号", description = "重置密码或者修改账号")
    public ServerResponseEntity<Boolean> updatePasswordOrMobile(@RequestBody ShopDetail shopDetail) {
        // 判断是账号是否已存在
        String decryptPassword = passwordManager.decryptPassword(shopDetail.getPassword());
        long count = shopEmployeeService.checkUserName(shopDetail.getShopId(), shopDetail.getMobile());
        if (Objects.nonNull(decryptPassword)) {
            //前端传过来密码如果为空，就不set
            PasswordUtil.check(decryptPassword);
            shopDetail.setPassword(passwordEncoder.encode(decryptPassword));
        }
        shopDetailService.updatePasswordOrMobile(shopDetail.getShopId(), decryptPassword, shopDetail.getMobile(), count);
        return ServerResponseEntity.success();
    }

    @PutMapping("/updateShopType")
    @Operation(summary = "修改店铺类型", description = "修改店铺类型")
    public ServerResponseEntity<Boolean> updateShopType(@RequestBody ShopTypeParam shopTypeParam) {
        shopDetailService.batchUpdateShopType(shopTypeParam);
        return ServerResponseEntity.success();
    }

}
