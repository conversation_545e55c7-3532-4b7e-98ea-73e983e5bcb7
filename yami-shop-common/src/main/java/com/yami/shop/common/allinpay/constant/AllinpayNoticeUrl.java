package com.yami.shop.common.allinpay.constant;

/**
 * <AUTHOR>
 */
public interface AllinpayNoticeUrl {

    String NOTICE_URL = "/notice/allinpay/member";

    /**
     * 设置公司信息
     */
    String SET_COMPANY_INFO = NOTICE_URL + "/setCompanyInfoResult";

    /**
     * 更新公司信息
     */
    String UPDATE_COMPANY_INFO = NOTICE_URL + "/updateCompanyInfoResult";

    /**
     * 影印件采集
     */
    String ID_CARD_COLLECT = NOTICE_URL + "/idCardCollectResult";

    /**
     * 签约账户提现协议
     */
    String SIGN_ACCT_PROTOCOL = NOTICE_URL + "/signAcctProtocol";

    /**
     * 设置支付密码
     */
    String SET_PAY_PWD = NOTICE_URL + "/setPayPwdResult";

    /**
     * 修改支付密码
     */
    String UPDATE_PAY_PWD = NOTICE_URL + "/updatePayPwdResult";

    /**
     * 重置支付密码
     */
    String RESET_PAY_PWD = NOTICE_URL + "/resetPayPwdResult";

    /**
     * 修改绑定手机(密码验证版)
     */
    String UPDATE_PHONE_BY_PAY_PWD = NOTICE_URL + "/updatePhoneByPayPwdResult";

    /**
     * 提现申请
     */
    String WITHDRAW_APPLY = NOTICE_URL + "/withdrawApply";

    /**
     * 充值申请
     */
    String RECHARGE_APPLY = NOTICE_URL + "/rechargeApply";
}
