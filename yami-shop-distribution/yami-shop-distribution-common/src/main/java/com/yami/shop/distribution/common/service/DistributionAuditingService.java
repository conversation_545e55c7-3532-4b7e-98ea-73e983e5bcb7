package com.yami.shop.distribution.common.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.distribution.common.dto.DistributionAuditingDto;
import com.yami.shop.distribution.common.model.DistributionAuditing;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.param.RangeTimeParam;

/**
 *
 * <AUTHOR> on 2019/04/01.
 */
public interface DistributionAuditingService extends IService<DistributionAuditing> {
      /**
       * 获取分销员审核信息
       * @param page 分页信息
       * @param distributionAuditing 审核信息
       * @param rangeTimeParam 申请时间
       * @param startExpenseNumber 订单数量
       * @param endExpenseNumber 订单数量
       * @param startPayAmount 支付金额范围
       * @param endPayAmount 支付金额范围
       * @param distributionParam 分销查询参数
       * @return 分销员审核信息
       */
      Page<DistributionAuditingDto> distributionAuditingPage(Page page, DistributionAuditing distributionAuditing, RangeTimeParam rangeTimeParam, Integer startExpenseNumber, Integer endExpenseNumber, Double startPayAmount, Double endPayAmount, DistributionParam distributionParam);

      /**
       * 分销员审核操作
       * @param distributionAuditing 审核信息
       */
      void examine(DistributionAuditing distributionAuditing);

}
