package com.yami.shop.sys.multishop.controller;

import com.yami.shop.bean.vo.DistributionConfigVO;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.config.ShopConfig;
import com.yami.shop.manager.impl.LangManager;
import com.yami.shop.service.SysConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 系统配置信息
 *
 * <AUTHOR>
 */
@RestController
@Tag(name = "系统配置信息")
@RequestMapping("/sys/config")
@AllArgsConstructor
public class SysConfigController {

    private final SysConfigService sysConfigService;
    private final ShopConfig shopConfig;
    private final LangManager langManager;

    @GetMapping("/paySettlementType")
    @Operation(summary = "获取支付系统配置", description = "获取支付系统配置")
    public ServerResponseEntity<String> getPaySettlementType() {
        return ServerResponseEntity.success(sysConfigService.getValue(Constant.PAY_SETTLEMENT_CONFIG));
    }

    @GetMapping("/signingConfig")
    @Operation(summary = "获取签约类目审核配置", description = "获取签约类目审核配置")
    public ServerResponseEntity<String> getSigningConfig() {
        return ServerResponseEntity.success(sysConfigService.getValue(Constant.PROD_SWITCH_CONFIG));
    }

    @GetMapping("/getDistribution")
    @Operation(summary = "获取分销开启状态", description = "获取分销开启状态")
    public ServerResponseEntity<Boolean> getDistribution() {
        DistributionConfigVO distributionConfigVO = sysConfigService.getSysConfigObject(Constant.DISTRIBUTION_CONFIG, DistributionConfigVO.class);
        if (Objects.isNull(distributionConfigVO)) {
            return ServerResponseEntity.success(Boolean.FALSE);
        }
        return ServerResponseEntity.success(distributionConfigVO.getDistributionSwitch() == 1);
    }
}
