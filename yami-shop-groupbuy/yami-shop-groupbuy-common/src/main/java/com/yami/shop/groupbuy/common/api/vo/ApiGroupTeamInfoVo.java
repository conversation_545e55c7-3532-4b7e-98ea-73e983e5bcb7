package com.yami.shop.groupbuy.common.api.vo;

import com.yami.shop.bean.app.vo.ProductVO;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupSkuDto;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupTeamDto;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupUserDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 拼团详情vo
 *
 * <AUTHOR>
 * @date 2019/9/2 9:11
 */
@Data
public class ApiGroupTeamInfoVo {

    @Schema(description = "拼团单信息" )
    private ApiGroupTeamDto groupTeam;

    @Schema(description = "拼团活动商品信息" )
    private ProductVO productVO;

    @Schema(description = "sku列表" )
    private List<ApiGroupSkuDto> groupSkuList;

    @Schema(description = "参团用户列表" )
    private List<ApiGroupUserDto> apiGroupUserList;

    @Schema(description = "订单编号" )
    private String orderNumber;
}
