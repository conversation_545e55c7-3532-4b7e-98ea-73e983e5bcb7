package com.yami.shop.distribution.common.listener;

import com.yami.shop.bean.event.GetDistributionUserIncomeEvent;
import com.yami.shop.bean.vo.allinpay.SplitRuleVO;
import com.yami.shop.common.allinpay.constant.PaySysType;
import com.yami.shop.common.util.Arith;
import com.yami.shop.distribution.common.dao.DistributionUserIncomeMapper;
import com.yami.shop.distribution.common.model.DistributionUserIncome;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 获取分销员收入明细
 * <AUTHOR>
 * @date 2023-08-18
 */
@Component
@AllArgsConstructor
public class OrderDistributionUserIncomeListener {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private final DistributionUserIncomeMapper distributionUserIncomeMapper;

    @EventListener(GetDistributionUserIncomeEvent.class)
    public void orderDistributionUserIncomeListener(GetDistributionUserIncomeEvent event) {
        logger.info("获取分销员收入明细事件监听器开始执行");
        List<SplitRuleVO> userSplitRuleList = event.getUserSplitRuleList();
        String orderNumber = event.getOrderNumber();
        List<DistributionUserIncome> distributionUserIncomes = distributionUserIncomeMapper.listDistributionUserIncomeByOrderNumber(orderNumber, PaySysType.ALLINPAY.value());
        for (DistributionUserIncome distributionUserIncome : distributionUserIncomes) {
            SplitRuleVO userSplitRuleVO = new SplitRuleVO();
            userSplitRuleVO.setOrderNumber(orderNumber);
            // 分销员收入金额
            userSplitRuleVO.setAmount(Arith.toLongCent(distributionUserIncome.getIncomeAmount()));
            userSplitRuleVO.setFee(0L);
            userSplitRuleVO.setBizUserId(distributionUserIncome.getUserId());
            userSplitRuleList.add(userSplitRuleVO);
        }
        logger.info("获取分销员收入明细事件监听器执行结束");
    }
}
