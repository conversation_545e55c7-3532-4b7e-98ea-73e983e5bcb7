package com.yami.shop.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.enums.*;
import com.yami.shop.bean.event.*;
import com.yami.shop.bean.model.OfflineHandleEvent;
import com.yami.shop.bean.model.ProdParameter;
import com.yami.shop.bean.model.Product;
import com.yami.shop.bean.model.Sku;
import com.yami.shop.bean.param.NotifyTemplateParam;
import com.yami.shop.bean.param.OfflineHandleEventAuditParam;
import com.yami.shop.bean.param.ProductParam;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.seckill.common.service.SeckillService;
import com.yami.shop.security.platform.util.SecurityUtils;
import com.yami.shop.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 商品列表、商品发布controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/prod/prod")
@AllArgsConstructor
@Tag(name = "商品")
public class ProductController {

    private final SkuService skuService;
    private final BasketService basketService;
    private final ProductService productService;
    private final ApplicationEventPublisher eventPublisher;
    private final ApplicationContext applicationContext;
    private final OfflineHandleEventService offlineHandleEventService;
    private final SupplierProdService supplierProdService;
    private final ProdParameterService prodParameterService;
    private final SeckillService seckillService;

    @GetMapping("/seckills")
    @Operation(summary = "分页获取秒杀商品信息")
    @PreAuthorize("@pms.hasPermission('prod:prod:pageSeckill')")
    public ServerResponseEntity<IPage<Product>> seckills(ProductParam product, PageParam<Product> page){
        product.setLang(I18nMessage.getLang());
        IPage<Product> productPage = seckillService.pageSeckillNormalProd(page, product);
        return ServerResponseEntity.success(productPage);
    }

    /**
     * 处理下活动商品的价格
     *
     * @param product
     * @param products
     */
    private void processActivityProdPrice(ProductParam product, List<Product> products) {
        Map<Integer, List<Product>> prodMap = products.stream().collect(Collectors.groupingBy(Product::getProdType));
        if (prodMap.containsKey(ProdType.PROD_TYPE_SECKILL.value())) {
            applicationContext.publishEvent(new ProcessActivityProdPriceEvent(product, prodMap.get(ProdType.PROD_TYPE_SECKILL.value())));
        }

        if (prodMap.containsKey(ProdType.PROD_TYPE_GROUP.value())) {
            applicationContext.publishEvent(new ProcessActivityProdPriceEvent(product, prodMap.get(ProdType.PROD_TYPE_GROUP.value())));
        }
    }

    @GetMapping("/info/{prodId}")
    @PreAuthorize("@pms.hasPermission('prod:prod:info')")
    @Operation(summary = "获取商品信息" , description = "获取商品信息")
    @Parameter(name = "prodId", description = "商品id" )
    public ServerResponseEntity<Product> info(@PathVariable("prodId") Long prodId) {
        Product prod = productService.getProductById(prodId);
        if (Objects.isNull(prod) || Objects.equals(prod.getStatus(), ProdStatusEnums.DELETE.getValue())) {
            // 该商品已经被删除
            throw new YamiShopBindException("yami.product.already.deleted");
        }
        List<Sku> skuList = skuService.listSkuAndSkuStockForAdmin(prod);
        prod.setSkuList(skuList);
        List<ProdParameter> prodParameters = prodParameterService.listParameterByProdId(prodId);
        prod.setProdParameterList(prodParameters);
        return ServerResponseEntity.success(prod);
    }

    @DeleteMapping("/{prodId}")
    @PreAuthorize("@pms.hasPermission('prod:prod:update')")
    @Operation(summary = "平台删除商品" , description = "平台删除商品")
    @Parameter(name = "prodId", description = "商品id" )
    public ServerResponseEntity<Void> delete(@PathVariable("prodId") Long prodId) {
        this.checkBeforeDeleteProduct(prodId);
        Product dbProduct = productService.getProductByProdId(prodId);
        List<Sku> dbSkus = skuService.listSkuByProdId(dbProduct.getProdId());
        List<Long> supplierIds = supplierProdService.listSupplierIdByProdId(prodId);
        List<Long> deleteSkuIds = dbSkus.stream().map(Sku::getSkuId).toList();
        // 删除商品
        productService.removeProductByProdId(prodId,deleteSkuIds);
        //清除供应商商品列表缓存
        if (CollectionUtils.isNotEmpty(supplierIds)) {
            for (Long supplierId : supplierIds) {
                supplierProdService.removeCacheBySupplierId(supplierId);
            }
        }
        // 商品状态改变时的发送事件，让活动下线
        applicationContext.publishEvent(new ProdChangeStatusEvent(dbProduct, ProdStatusEnums.DELETE.getValue()));
        //清除商品缓存
        productService.removeProdCacheByProdId(prodId);

        //清除sku缓存
        for (Sku sku : dbSkus) {
            skuService.removeSkuCacheBySkuId(sku.getSkuId(), sku.getProdId());
        }
        List<String> userIds = basketService.listUserIdByProdId(prodId);

        //清除购物车缓存
        basketService.removeCacheByUserIds(userIds, null);
        // 删除商品时，改变分销设置,团购订单处理。。。
        applicationContext.publishEvent(new ProdChangeEvent(dbProduct));
        eventPublisher.publishEvent(new EsProductUpdateEvent(prodId, null, EsOperationType.DELETE));
        return ServerResponseEntity.success();
    }

    @GetMapping("/listProdByIdsAndType")
    @Operation(summary = "获取商品信息列表" , description = "获取商品信息列表")
    @PreAuthorize("@pms.hasPermission('prod:prod:list')")
    public ServerResponseEntity<List<Product>> listProdByIdsAndType(ProductParam product) {
        List<Product> products = productService.listProdByIdsAndType(product);
        processActivityProdPrice(product, products);
        return ServerResponseEntity.success(products);
    }

    @PutMapping("/waterSoldNum")
    @Operation(summary = "更新注水销量" , description = "更新注水销量")
    @Parameter(name = "prodId", description = "商品id" )
    @PreAuthorize("@pms.hasPermission('prod:prod:update')")
    public ServerResponseEntity<Void> updateWaterSoldNum(Integer waterSoldNum, Long prodId) {
        if (waterSoldNum == null) {
            waterSoldNum = 0;
        }
        productService.updateWaterSaleNum(waterSoldNum, prodId);
        eventPublisher.publishEvent(new EsProductUpdateEvent(prodId, null, EsOperationType.UPDATE_SOLD_NUM));
        return ServerResponseEntity.success();
    }

    @PostMapping("/offline")
    @PreAuthorize("@pms.hasPermission('prod:prod:update')")
    @Operation(summary = "下线商品" , description = "下线商品")
    public ServerResponseEntity<Void> offline(@RequestBody OfflineHandleEvent offlineHandleEvent) {
        Product dbProduct = productService.getProductAndLang(offlineHandleEvent.getHandleId());
        if (dbProduct == null) {
            // 未找到刚商品的信息
            throw new YamiShopBindException("yami.product.not.exist");
        }
        if(Objects.equals(dbProduct.getStatus(), ProdStatusEnums.PLATFORM_OFFLINE.getValue())){
            // 该商品已被下架
            throw new YamiShopBindException("yami.platform.prod.offline.check");
        }
        Long sysUserId = SecurityUtils.getSysUser().getUserId();
        productService.offline(offlineHandleEvent.getHandleId(), offlineHandleEvent.getOfflineReason(), sysUserId);

        // 商品状态改变时的发送事件，让活动下线
        applicationContext.publishEvent(new ProdChangeStatusEvent(dbProduct, ProdStatusEnums.PLATFORM_OFFLINE.getValue()));
        List<String> userIds = basketService.listUserIdByProdId(dbProduct.getProdId());
        //清除购物车缓存
        basketService.removeCacheByUserIds(userIds, null);
        // 移除缓存
        productService.removeProdCacheByProdId(dbProduct.getProdId());
        eventPublisher.publishEvent(new EsProductUpdateEvent(dbProduct.getProdId(), null, EsOperationType.UPDATE));
        //发送商品下架提醒给商家
        NotifyTemplateParam shopParam = new NotifyTemplateParam();
        shopParam.setShopId(dbProduct.getShopId());
        shopParam.setProdId(offlineHandleEvent.getHandleId());
        shopParam.setProdName(dbProduct.getProdName());
        shopParam.setSendType(SendType.PRODUCT_OFFLINE.getValue());
        applicationContext.publishEvent(new SendMessageEvent(shopParam));
        return ServerResponseEntity.success();
    }

    @GetMapping("/getOfflineHandleEventByProdId/{prodId}")
    @Operation(summary = "获取最新下线商品的事件" , description = "获取最新下线商品的事件")
    @Parameter(name = "prodId", description = "商品id" )
    @PreAuthorize("@pms.hasPermission('prod:prod:offlineInfo')")
    public ServerResponseEntity<OfflineHandleEvent> getOfflineHandleEventByProdId(@PathVariable Long prodId) {
        OfflineHandleEvent offlineHandleEvent = offlineHandleEventService.getProcessingEventByHandleTypeAndHandleId(OfflineHandleEventType.PROD.getValue(), prodId);
        return ServerResponseEntity.success(offlineHandleEvent);
    }

    @PostMapping("/prodOfflineAudit")
    @Operation(summary = "审核违规下架的商品" , description = "审核违规下架的商品")
    @PreAuthorize("@pms.hasPermission('prod:prod:update')")
    public ServerResponseEntity<Void> prodOfflineAudit(@RequestBody OfflineHandleEventAuditParam offlineHandleEventAuditParam) {
        Long userId = SecurityUtils.getSysUser().getUserId();
        productService.prodAudit(offlineHandleEventAuditParam, userId);

        // 移除缓存
        productService.removeProdCacheByProdId(offlineHandleEventAuditParam.getHandleId());
        eventPublisher.publishEvent(new EsProductUpdateEvent(offlineHandleEventAuditParam.getHandleId(), null, EsOperationType.UPDATE));
        return ServerResponseEntity.success();
    }

    @PostMapping("/auditProd")
    @Operation(summary = "审核待审核的商品" , description = "商品审核开关打开后，新发布的或要上架的商品处于的待审核状态")
    @PreAuthorize("@pms.hasPermission('prod:prod:update')")
    public ServerResponseEntity<Void> auditProd(@RequestBody OfflineHandleEvent offlineHandleEvent) {
        Long prodId = offlineHandleEvent.getHandleId();
        Product dbProduct = productService.getProductByProdId(prodId);
        if (Objects.isNull(dbProduct)) {
            // 未找到刚商品的信息
            throw new YamiShopBindException("yami.product.not.exist");
        }
        if (!Objects.equals(dbProduct.getStatus(), ProdStatusEnums.AUDIT.getValue())) {
            // 商品状态已改变，请刷新页面
            throw new YamiShopBindException("yami.prod.status.change");
        }
        offlineHandleEvent.setHandlerId(SecurityUtils.getSysUser().getUserId());
        productService.handleAuditProd(dbProduct, offlineHandleEvent);
        // 移除缓存
        productService.removeProdCacheByProdId(dbProduct.getProdId());
        eventPublisher.publishEvent(new EsProductUpdateEvent(dbProduct.getProdId(), null, EsOperationType.UPDATE));
        return ServerResponseEntity.success();
    }

    @PutMapping("/toTop/{id}")
    @Operation(summary = "置顶商品" , description = "置顶商品")
    @Parameter(name = "id", description = "商品id" )
    @PreAuthorize("@pms.hasPermission('prod:prod:update')")
    public ServerResponseEntity<Void> removeById(@PathVariable Long id) {
        Product product = productService.getProductByProdId(id);
        if (!Objects.equals(product.getStatus(), ProdStatusEnums.NORMAL.getValue())) {
            // 只能置顶已上架的商品
            throw new YamiShopBindException("yami.prod.set.top.check");
        }
        product.setIsTop(Objects.equals(product.getIsTop(), 0) ? 1 : 0);
        productService.updateById(product);
        // 移除缓存
        productService.removeProdCacheByProdId(id);
        eventPublisher.publishEvent(new EsProductUpdateEvent(product.getProdId(), null, EsOperationType.UPDATE));
        return ServerResponseEntity.success();
    }

    private void checkBeforeDeleteProduct(Long prodId) {
        GetComboProdCountEvent getComboProdCountEvent = new GetComboProdCountEvent();
        getComboProdCountEvent.setProdId(prodId);
        applicationContext.publishEvent(getComboProdCountEvent);
        if (getComboProdCountEvent.getCount() > 0) {
            //参加以下活动的商品不能被删除：优惠套餐
            throw new YamiShopBindException("yami.combo.prod.not.delete");
        }
        GetGiveawayProdCountEvent getGiveawayProdCountEvent = new GetGiveawayProdCountEvent();
        getGiveawayProdCountEvent.setProdId(prodId);
        applicationContext.publishEvent(getGiveawayProdCountEvent);
        if (getGiveawayProdCountEvent.getCount() > 0) {
            //参加以下活动的商品不能被删除：赠品
            throw new YamiShopBindException("yami.giveaway.prod.not.delete");
        }
    }
}
