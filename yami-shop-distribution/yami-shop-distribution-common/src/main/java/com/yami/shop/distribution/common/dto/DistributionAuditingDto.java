package com.yami.shop.distribution.common.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DistributionAuditingDto {
    /**
     * 分销员申请表
     */
    private Long auditingId;

    /**
     * 店铺Id
     */
    private Long shopId;

    /**
     * 邀请人id
     */
    private Long parentDistributionUserId;

    /**
     * 申请人id
     */

    private Long distributionUserId;

    /**
     * 申请时间
     */

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditingTime;

    /**
     * 不通过原因
     */
    private Integer reason;

    /**
     * 审核状态：0 未审核 1已通过 -1未通过
     */
    private Integer state;

    /**
     * 操作时间
     */

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 操作人
     */
    private Long modifier;

    /**
     * 备注
     */
    private String remarks;

    //销售员信息
    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 手机号码
     */
    private String userMobile;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证号
     */
    private String identityCardNumber;

    /**
     * 身份证正面
     */
    private String identityCardPicFront;

    /**
     * 身份证背面
     */
    private String identityCardPicBack;

    /**
     * 手持身份证照片
     */
    private String identityCardPicHold;

    // 总消费
    /**
     * 用户消费笔数
     */
    private Double expenseNumber;

    /**
     * 用户消费金额
     */
    private Double sumOfConsumption;

    /**
     * 邀请人信息
     */
    private String parentNickName;

    private String parentUserMobile;

    /**
     * 操作人信息
     */
    private String sysUserId;

    private String sysUsername;

}
