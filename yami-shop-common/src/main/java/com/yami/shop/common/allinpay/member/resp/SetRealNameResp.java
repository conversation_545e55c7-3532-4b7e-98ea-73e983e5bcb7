package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;

/**
 * 个人实名认证
 *
 * <AUTHOR>
 */
public class SetRealNameResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String name;

    /**
     * 证件类型 目前只支持身份证
     */
    private Integer identityType = 1;

    /**
     * 证件号码
     */
    private String identityNo;

    private String bizUserId;

    public SetRealNameResp() {
    }


    public SetRealNameResp(String name,
                           String identityNo, String bizUserId) {
        this.identityType = null;
        this.identityNo = identityNo;
        this.bizUserId = bizUserId;
        this.name = name;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getIdentityType() {
        return identityType;
    }

    public void setIdentityType(Integer identityType) {
        this.identityType = identityType;
    }

    public String getIdentityNo() {
        return identityNo;
    }

    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo;
    }

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

}
