package com.yami.shop.distribution.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 等级升级所需条件
 * <AUTHOR>
 */
@Data
public class DistributionLevelConditionDataVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 绑定客户数
     */
    @Schema(description = "绑定客户数" )
    private Integer boundCustomers;

    /**
     * 邀请分销员数
     */
    @Schema(description = "邀请分销员数" )
    private Integer invitedVeeker;

    /**
     * 支付单数
     */
    @Schema(description = "支付单数" )
    private Integer payNumber;

    /**
     * 积累收益
     */
    @Schema(description = "积累收益" )
    private Double addupAmount;

    /**
     * 成交单数
     */
    @Schema(description = "成交单数" )
    private Integer successOrderNumber;

    /**
     * 成交金额
     */
    @Schema(description = "成交金额" )
    private Double successTradingVolume;

    /**
     * 消费金额
     */
    @Schema(description = "消费金额" )
    private Double sumOfConsumption;


    /**
     * 购买指定商品
     */
    @Schema(description = "购买指定商品" )
    private List<CommodityItemData> commodity;


    @Data
    public static class CommodityItemData {
        /**
         * 商品id
         */
        private Long prodId;

        /**
         * 商品名称
         */
        private String prodName;

        /**
         * 商品图片
         */
        private String pic;
    }


}
