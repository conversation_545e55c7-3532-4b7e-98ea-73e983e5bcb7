package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/15 13:06
 */
@Data
public class TakeStockVO {

    @Schema(description = "盘点id" )
    private Long takeStockId;

    @Schema(description = "盘点单号" )
    private String takeStockNo;

    @Schema(description = "盘点状态 0已作废 1盘点中 2已完成" )
    private Integer billStatus;

    @Schema(description = "制单人姓名")
    private String makerName;

    @Schema(description = "制单人手机号" )
    private String makerMobile;

    @Schema(description = "盘点时间" )
    private Date createTime;

    @Schema(description = "盘点区域名称" )
    private String stockRegionName;

    @Schema(description = "sku个数" )
    private Integer skuCount;

    @Schema(description = "备注" )
    private String remark;

    @Schema(description = "仓库id")
    private Long warehouseId;

    @Schema(description = "仓库名称")
    private String warehouseName;

    @Schema(description = "库存点类型(1:仓库, 2:门店)")
    private Integer stockPointType;

    @Schema(description = "盘点商品列表" )
    private List<TakeStockProdVO> takeStockProdList;
}
