package com.yami.shop.distribution.common.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 更新分销员状态的状态参数
 * <AUTHOR>
 */
@Data
public class UpdateDistributionUserParam {

    @Schema(description = "分销员id" )
    private Long distributionUserId;

    @Schema(description = "状态(-1永久封禁 0待审核状态 1正常 2暂时封禁 3 审核未通过)" )
    private Integer state;

    @Schema(description = "封禁原因(0 失去联系 1恶意刷单 2其他)" )
    private Integer banReason;

    @Schema(description = "备注" )
    private String remarks;
}
