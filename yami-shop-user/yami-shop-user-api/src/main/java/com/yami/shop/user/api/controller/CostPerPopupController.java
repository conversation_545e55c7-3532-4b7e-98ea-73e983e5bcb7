package com.yami.shop.user.api.controller;

import com.yami.shop.bean.model.User;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.OrderService;
import com.yami.shop.service.ShopCustomerService;
import com.yami.shop.service.UserService;
import com.yami.shop.user.common.dto.QueryPopupDTO;
import com.yami.shop.user.common.enums.PopupPageTypeEnum;
import com.yami.shop.user.common.model.PopupUserLog;
import com.yami.shop.user.common.service.*;
import com.yami.shop.user.common.vo.CostPerPopupVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 弹窗广告
 * <AUTHOR>
 */
@RestController("userCostPerPopupController")
@RequestMapping("/costPerPopup")
@Tag(name = "游客弹窗广告")
@AllArgsConstructor
public class CostPerPopupController {

    private final CostPerPopupService costPerPopupService;
    private final UserService userService;
    private final ShopCustomerService shopCustomerService;
    private final PopupUserLogService popupUserLogService;
    private final UserLevelService userLevelService;
    private final UserBalanceLogService userBalanceLogService;
    private final UserLevelLogService userLevelLogService;
    private final OrderService orderService;

    @PostMapping("/popup")
    @Operation(summary = "获取游客在当前页面的弹窗广告")
    public ServerResponseEntity<CostPerPopupVO> page(@RequestBody QueryPopupDTO queryPopupDTO) {
        if (Objects.isNull(queryPopupDTO.getUuid())){
            // uuid不能为空
            throw new YamiShopBindException("yami.cost.per.popup.exception.uuidNull");
        }
        if (!PopupPageTypeEnum.isTourist(queryPopupDTO.getPageType())) {
            return ServerResponseEntity.success();
        }
        String userId = null;
        // 先获取有没有符合条件的一个弹窗广告
        CostPerPopupVO costPerPopup = getCostPerPopup(queryPopupDTO, userId);
        if (Objects.isNull(costPerPopup)) {
            // 没有符合条件的弹窗广告直接返回
            return ServerResponseEntity.success();
        }
        // 最后保存弹窗广告记录
        popupUserLogService.insert(new PopupUserLog(costPerPopup.getPopupId(), userId, queryPopupDTO.getUuid()));
        return ServerResponseEntity.success(costPerPopup);
    }

    private CostPerPopupVO getCostPerPopup(QueryPopupDTO queryPopupDTO, String userId) {
        String uuid = queryPopupDTO.getUuid();
        Integer pageType = queryPopupDTO.getPageType();
        Long shopId = queryPopupDTO.getShopId();
        CostPerPopupVO costPerPopup;
        if (PopupPageTypeEnum.isSys(pageType)) {
            // 平台弹窗
            costPerPopup = costPerPopupService.getSysPopupByCondition(new User(), pageType, uuid);
        } else {
            // 商家弹窗
            costPerPopup = costPerPopupService.getShopPopupByCondition(null, pageType, shopId, userId, uuid);
        }
        return costPerPopup;
    }
}
