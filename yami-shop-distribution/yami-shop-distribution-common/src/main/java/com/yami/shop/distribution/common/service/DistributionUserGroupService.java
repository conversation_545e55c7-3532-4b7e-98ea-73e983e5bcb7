package com.yami.shop.distribution.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.distribution.common.model.DistributionUserGroup;

/**
 *
 * <AUTHOR> on 2019/04/01.
 */
public interface DistributionUserGroupService extends IService<DistributionUserGroup> {

    /**
     * 分销员分组信息
     * @param page 分页参数
     * @param distributionUserGroup 查询参数
     * @return 分销员分组信息
     */
    IPage<DistributionUserGroup> distributionUserGroupsAndSysUserPage(Page page, DistributionUserGroup distributionUserGroup);


}
