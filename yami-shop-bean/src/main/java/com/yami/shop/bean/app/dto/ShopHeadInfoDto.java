package com.yami.shop.bean.app.dto;

import com.yami.shop.bean.vo.search.ProductSearchVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 店铺的头信息
 * <AUTHOR>
 */
@Data
@Schema(description = "店铺的头信息")
public class ShopHeadInfoDto {

    @Schema(description = "店铺id" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long shopId;

    @Schema(description = "店铺名称" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String shopName;

    @Schema(description = "店铺logo" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String shopLogo;

    @Schema(description = "店铺评分")
    private Double shopScore;

    @Schema(description = "粉丝数量" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long fansCount;

    @Schema(description = "商品数量" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long prodCount;

    @Schema(description = "店铺简介" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String intro;

    @Schema(description = "店铺主页id" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long renovationId;

    @Schema(description = "店铺联系电话" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String tel;

    @Schema(description = "店铺状态(-1:未开通 0: 停业中 1:营业中 2:平台下线 3:平台下线待审核)，可修改" )
    private Integer shopStatus;

    @Schema(description = "店铺销量" )
    private Long shopSoldNum;

    @Schema(description = "spu列表信息" )
    private List<ProductSearchVO> products;

    @Schema(description = "0普通店铺 1优选好店" )
    private Integer type;

    /**
     * 店铺类型1自营店 2普通店
     */
    @Schema(description = "店铺类型1自营店 2普通店")
    private Integer shopType;

    @Schema(description = "签约起始时间" )
    private Date contractStartTime;

    @Schema(description = "签约终止时间" )
    private Date contractEndTime;

    @Schema(description = "装修类型 1Pc 2移动端" )
    private Integer renovationType;
}
