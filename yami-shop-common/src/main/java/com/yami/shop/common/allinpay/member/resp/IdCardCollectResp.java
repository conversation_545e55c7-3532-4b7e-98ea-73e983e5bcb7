package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;

/**
 * 影印件采集
 * <AUTHOR>
 */
public class IdCardCollectResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 上传结果 1.成功 2.失败
     */
    private Integer result;

    private String bizUserId;

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    @Override
    public String toString() {
        return "IdCardCollectResp{" +
                "result=" + result +
                ", bizUserId='" + bizUserId + '\'' +
                '}';
    }
}
