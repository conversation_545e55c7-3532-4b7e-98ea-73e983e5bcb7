package com.yami.shop.common.allinpay.member.req;

import com.yami.shop.common.allinpay.member.constant.IdentityType;

import java.io.Serial;
import java.io.Serializable;

/**
 * 个人实名认证
 *
 * <AUTHOR>
 */
public class SetRealName extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    public static final String METHOD_NAME = "setRealName";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    /**
     * 默认为true目前必须通过云账户认证
     */
    private Boolean isAuth = true;

    private String name;

    /**
     * 证件类型 目前只支持身份证
     */
    private Integer identityType = IdentityType.ID_CARD.value();

    /**
     * 证件号码(加密)
     */
    private String identityNo;


    public SetRealName() {
    }

    public SetRealName(String bizUserId, String name,
                       String identityNo) {
        this.bizUserId = bizUserId;
        this.name = name;
        this.identityNo = identityNo;
    }

    public Boolean getIsAuth() {
        return isAuth;
    }

    public void setIsAuth(Boolean isAuth) {
        this.isAuth = isAuth;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getIdentityType() {
        return identityType;
    }

    public void setIdentityType(Integer identityType) {
        this.identityType = identityType;
    }

    public String getIdentityNo() {
        return identityNo;
    }

    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo;
    }


}
