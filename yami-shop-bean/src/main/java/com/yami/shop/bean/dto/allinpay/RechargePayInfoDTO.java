package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * 充值金额id
 *
 * <AUTHOR>
 */
public class RechargePayInfoDTO extends BasePayInfoDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "用户余额充值记录id")
    private Long balanceLogId;

    @Schema(description = "商家余额充值记录id")
    private Long shopWalletLogId;

    public Long getBalanceLogId() {
        return balanceLogId;
    }

    public void setBalanceLogId(Long balanceLogId) {
        this.balanceLogId = balanceLogId;
    }

    public Long getShopWalletLogId() {
        return shopWalletLogId;
    }

    public void setShopWalletLogId(Long shopWalletLogId) {
        this.shopWalletLogId = shopWalletLogId;
    }

    @Override
    public String toString() {
        return "RechargePayInfoDTO{" +
                "balanceLogId=" + balanceLogId +
                ", shopWalletLogId=" + shopWalletLogId +
                '}';
    }
}
