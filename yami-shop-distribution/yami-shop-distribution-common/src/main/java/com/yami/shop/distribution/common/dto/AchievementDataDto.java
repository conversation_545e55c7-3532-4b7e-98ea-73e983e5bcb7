package com.yami.shop.distribution.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分销员业绩数据
 * <AUTHOR>
 */
@Data
public class AchievementDataDto {

    /**
     * 积累绑定客户数
     */
    @Schema(description = "积累绑定客户数" )
    private Integer boundCustomers;

    /**
     * 积累邀请分销员数
     */
    @Schema(description = "积累邀请分销员数" )
    private Integer invitedVeeker;

    /**
     * 积累推广订单数
     */
    @Schema(description = "积累订单数" )
    private Integer orderCount;


    @Schema(description = "分销员钱包" )
    private DistributionUserWalletDto distributionUserWallet;
}
