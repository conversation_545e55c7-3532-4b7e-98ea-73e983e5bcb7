package com.yami.shop.platform.task;

import cn.hutool.core.collection.CollUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yami.shop.bean.bo.StationBO;
import com.yami.shop.search.common.util.EsSearchUtil;
import com.yami.shop.service.StationService;
import lombok.AllArgsConstructor;
import org.elasticsearch.common.geo.GeoPoint;
import org.slf4j.ILoggerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店定时任务
 * @since 2024/6/20 13:49
 */
@Component
@AllArgsConstructor
public class StationTask {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final StationService stationService;

    /**
     * 将旧的门店数据插入到es中
     * 注意：该定时任务默认关闭，只须在xxl-job上面手动执行一遍即可
     */

    @XxlJob("syncOldStationSaveEs")
    public void syncOldStationSaveEs() {
        logger.info("执行将旧的门店数据插入到es中定时任务");
        // 获取自提门店数据
        List<StationBO> esStationBOList = stationService.getEsStaionBO();
        if (CollUtil.isEmpty(esStationBOList)) {
            return;
        }
        for (StationBO stationBO : esStationBOList) {
            stationBO.setStationScore(5.00);
            // 自提
            stationBO.setSelfPickup(1);
            stationBO.setStationLocation(new GeoPoint(stationBO.getLat(), stationBO.getLng()));
        }
        // 将门店数据保存es
        EsSearchUtil.batchEsStationSave(esStationBOList);
    }

    /**
     * 计算门店评分
     */
    @XxlJob("computeStationScore")
    public void computeStationScore() {
        logger.info("执行计算门店评分定时任务!!!!");
        stationService.computeStationScore();
    }
}
