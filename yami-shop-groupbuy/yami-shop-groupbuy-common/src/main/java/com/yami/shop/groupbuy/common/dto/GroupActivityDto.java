package com.yami.shop.groupbuy.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 拼团活动状态类型
 *
 * <AUTHOR>
 * @date 2019/8/30 9:02
 */
@Data
public class GroupActivityDto {

    @Schema(description = "拼团活动id" )
    private Long groupActivityId;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "活动名称" )
    private String activityName;

    @Schema(description = "拼团状态(1:启用、2:未启用、0:已失效、-1:删除、3:违规下架、4:平台审核 5:已结束)" )
    private Integer status;

    @Schema(description = "活动开始时间" )
    private Date startTime;

    @Schema(description = "活动结束时间" )
    private Date endTime;

    @Schema(description = "成团人数" )
    private Integer groupNumber;

    @Schema(description = "已成团订单数（统计）" )
    private Long groupOrderCount;

    @Schema(description = "活动状态（根据活动时间生成）" )
    private Integer activityStatus;

    @Schema(description = "店铺名称" )
    private String shopName;

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "商品图片" )
    private String pic;

    @Schema(description = "语言" )
    private Integer lang;

    @Schema(description = "商品id" )
    private Long prodId;
}
