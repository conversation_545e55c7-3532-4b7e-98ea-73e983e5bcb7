package com.yami.shop.common.allinpay.order.resp;

/**
 * 退款申请返回(allinpay.yunst.orderService.refund)
 *
 * <AUTHOR>
 */

public class RefundResp {
    /**
     * 云商通订单号
     */
    private String orderNo;
    /**
     * 商户订单号（支付订单）
     */
    private String bizOrderNo;
    /**
     * 本次退款总金额
     */
    private String amount;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    @Override
    public String toString() {
        return "RefundResp{" +
                "orderNo='" + orderNo + '\'' +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                ", amount='" + amount + '\'' +
                '}';
    }
}
