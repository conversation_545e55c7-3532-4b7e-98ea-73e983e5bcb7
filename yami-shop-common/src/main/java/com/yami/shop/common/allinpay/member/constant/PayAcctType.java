package com.yami.shop.common.allinpay.member.constant;

import java.util.Objects;

/**
 * 支付账户类型
 * <AUTHOR>
 */
public enum PayAcctType {
    /**
     * 微信公众号
     */
    WECHAT_PUBLIC("weChatPublic"),
    /**
     * 微信小程序
     */
    WECHAT_MINI_PROGRAM("weChatMiniProgram"),
    /**
     * 支付宝生活号
     */
    ALIPAY_SERVICE("aliPayService"),
    /**
     * 银联JS
     */
    UNION_PAY_JS("unionPayjs");

    private final String value;

    PayAcctType(String value) {
        this.value = value;
    }

    public String value() {
        return value;
    }

    public static PayAcctType instance(String value) {
        for (PayAcctType payAcctType : values()) {
            if (Objects.equals(payAcctType.value, value)) {
                return payAcctType;
            }
        }
        return null;
    }
}
