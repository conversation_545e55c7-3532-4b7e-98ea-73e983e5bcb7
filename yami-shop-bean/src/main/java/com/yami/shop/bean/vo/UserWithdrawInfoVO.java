package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户提现信息
 * <AUTHOR>
 * @date 2023-09-21
 */
@Data
public class UserWithdrawInfoVO {

    @Schema(description = "待结算金额")
    private Double unSettleAmount;

    @Schema(description = "提现中金额")
    private Double applyWithdrawAmount;

    @Schema(description = "已提现金额" )
    private Double extractedAmount;
}
