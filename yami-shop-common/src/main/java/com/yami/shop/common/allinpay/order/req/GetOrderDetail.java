package com.yami.shop.common.allinpay.order.req;

/**
 * 订单状态
 *
 * <AUTHOR>
 */
public class GetOrderDetail extends AbstractOrderReq {

    /**
     * 商户订单号
     */
    private String bizOrderNo;

    /**
     * 方法名称
     */
    public static final String METHOD_NAME = "getOrderDetail";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public GetOrderDetail() {
    }

    public GetOrderDetail(String bizUserId, String bizOrderNo) {
        this.bizUserId = bizUserId;
        this.bizOrderNo = bizOrderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }


}
