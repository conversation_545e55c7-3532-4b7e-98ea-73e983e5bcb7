package com.yami.shop.bean.vo.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
public class MessageVO {

    @Schema(description = "消息头")
    private HeaderVO header;

    @Schema(description = "消息设置 消息设置是一个 uint8的数字类型 为1个字节，完全由第三方自定义 比如定义第8位为已读未读回执标记，开启则为0000 0001 = 1")
    private Integer setting;

    @Schema(description = "消息全局唯一ID")
    private Long messageId;

    @Schema(description = "客户端定义的消息编号(一般为32位的uuid)，可用此字段去重")
    private String clientMsgNo;

    @Schema(description = "消息序列号 （频道唯一，有序递增）")
    private Integer messageSeq;

    @Schema(description = "发送者用户id")
    private String fromUid;

    @Schema(description = "频道的唯一ID")
    private String channelId;

    @Schema(description = "频道的类型 1.个人频道 2.群聊频道")
    private String channelType;

    @Schema(description = "消息10位到秒的时间戳")
    private Integer timestamp;

    @Schema(description = "base64编码的消息内容")
    private String payload;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "头像")
    private String pic;

    @Schema(description = "SendUserTypeEnum 发送者类型 0用户 1商家 2平台 -1机器人")
    private Integer sendUserType;

    public HeaderVO getHeader() {
        return header;
    }

    public void setHeader(HeaderVO header) {
        this.header = header;
    }

    public Integer getSetting() {
        return setting;
    }

    public void setSetting(Integer setting) {
        this.setting = setting;
    }

    public Long getMessageId() {
        return messageId;
    }

    public void setMessageId(Long messageId) {
        this.messageId = messageId;
    }

    public String getClientMsgNo() {
        return clientMsgNo;
    }

    public void setClientMsgNo(String clientMsgNo) {
        this.clientMsgNo = clientMsgNo;
    }

    public Integer getMessageSeq() {
        return messageSeq;
    }

    public void setMessageSeq(Integer messageSeq) {
        this.messageSeq = messageSeq;
    }

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public Integer getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Integer timestamp) {
        this.timestamp = timestamp;
    }

    public String getPayload() {
        return payload;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public Integer getSendUserType() {
        return sendUserType;
    }

    public void setSendUserType(Integer sendUserType) {
        this.sendUserType = sendUserType;
    }

    @Override
    public String toString() {
        return "MessageVO{" +
                "header=" + header +
                ", setting=" + setting +
                ", messageId=" + messageId +
                ", clientMsgNo='" + clientMsgNo + '\'' +
                ", messageSeq=" + messageSeq +
                ", fromUid='" + fromUid + '\'' +
                ", channelId='" + channelId + '\'' +
                ", channelType='" + channelType + '\'' +
                ", timestamp=" + timestamp +
                ", payload='" + payload + '\'' +
                ", nickName='" + nickName + '\'' +
                ", pic='" + pic + '\'' +
                ", systemType=" + sendUserType +
                '}';
    }
}
