package com.yami.shop.discount.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yami.shop.discount.common.model.DiscountItem;
import com.yami.shop.discount.common.model.DiscountProd;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DiscountItemMapper extends BaseMapper<DiscountItem> {

    /**
     * 批量插入折扣优惠类目
     * @param discountItems 折扣优惠类目列表
     */
    void insertDiscountItems(@Param("discountItems") List<DiscountItem> discountItems);

    /**
     * 根据商品ID批量获取折扣商品列表
     * @param prodIds
     * @return 折扣商品列表
     */
    List<DiscountProd> listDiscountByProdIds(@Param("prodIds") List<Long> prodIds);
}
