package com.yami.shop.bean.bo.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 用户相关订单统计数据
 * @author: cl
 * @date: 2021-04-14 14:04:01
 */
@Data
public class UserOrderStatisticBO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "用户id" )
    private String userId;

    @Schema(description = "最近消费时间" )
    private Date reConsTime;

    @Schema(description = "消费金额" )
    private Double consAmount;

    @Schema(description = "实付金额" )
    private Double actualAmount;

    @Schema(description = "消费次数" )
    private Integer consTimes;

    @Schema(description = "平均折扣" )
    private Double averDiscount;

    @Schema(description = "售后金额" )
    private Double afterSaleAmount;

    @Schema(description = "售后次数" )
    private Integer afterSaleTimes;
}
