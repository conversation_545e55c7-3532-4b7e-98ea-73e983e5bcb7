package com.yami.shop.bean.dto.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 同步最近会话
 * <AUTHOR>
 */
public class ConversationSyncDTO {

    @Schema(description = "当前登录用户uid")
    private String uid;

    @Schema(description = "当前客户端的会话最大版本号 当前客户端的会话最大版本号(从保存的结果里取最大的version，如果本地没有数据则传0)")
    private Long version;

    @Schema(description = "客户端所有频道会话的最后一条消息序列号拼接出来的同步串")
    private String last_msg_seqs;

    @Schema(description = "每个会话获取最大的消息数量，一般为app点进去第一屏的数据 默认为1")
    private Integer msg_count;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getLast_msg_seqs() {
        return last_msg_seqs;
    }

    public void setLast_msg_seqs(String last_msg_seqs) {
        this.last_msg_seqs = last_msg_seqs;
    }

    public Integer getMsg_count() {
        return msg_count;
    }

    public void setMsg_count(Integer msg_count) {
        this.msg_count = msg_count;
    }

    @Override
    public String toString() {
        return "ConversationSyncDTO{" +
                "uid='" + uid + '\'' +
                ", version=" + version +
                ", last_msg_seqs='" + last_msg_seqs + '\'' +
                ", msg_count=" + msg_count +
                '}';
    }
}
