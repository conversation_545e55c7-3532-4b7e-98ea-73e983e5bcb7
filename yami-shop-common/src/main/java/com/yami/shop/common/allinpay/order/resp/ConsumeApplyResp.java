package com.yami.shop.common.allinpay.order.resp;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023-04-04
 */
public class ConsumeApplyResp {
    /**
     * 支付状态
     */
    private String payStatus;

    /**
     * 支付失败信息
     */
    private String payFailMessage;

    /**
     * 商户userId(付款人)
     */
    private String bizUserId;

    /**
     * 云商通订单编号
     */
    private String orderNo;

    /**
     * 商户订单编号
     */
    private String bizOrderNo;

    /**
     * 交易编号
     */
    private String tradeNo;

    /**
     * 拓展参数
     */
    private String extendInfo;

    /**
     * 微信APP支付信息
     */
    private Map<String, String> weChatAPPInfo;

    /**
     * 	小程序收银台支付参数
     */
    private String miniprogramPayInfo_VSP;

    /**
     * 支付信息
     */
    private String payInfo;

    /**
     * 交易验证方式
     */
    private Integer validateType;

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public String getPayFailMessage() {
        return payFailMessage;
    }

    public void setPayFailMessage(String payFailMessage) {
        this.payFailMessage = payFailMessage;
    }

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }

    public Map<String, String> getWeChatAPPInfo() {
        return weChatAPPInfo;
    }

    public void setWeChatAPPInfo(Map<String, String> weChatAPPInfo) {
        this.weChatAPPInfo = weChatAPPInfo;
    }

    public String getMiniprogramPayInfo_VSP() {
        return miniprogramPayInfo_VSP;
    }

    public void setMiniprogramPayInfo_VSP(String miniprogramPayInfo_VSP) {
        this.miniprogramPayInfo_VSP = miniprogramPayInfo_VSP;
    }

    public String getPayInfo() {
        return payInfo;
    }

    public void setPayInfo(String payInfo) {
        this.payInfo = payInfo;
    }

    public Integer getValidateType() {
        return validateType;
    }

    public void setValidateType(Integer validateType) {
        this.validateType = validateType;
    }

    @Override
    public String toString() {
        return "ConsumeApplyResp{" +
                "payStatus='" + payStatus + '\'' +
                ", payFailMessage='" + payFailMessage + '\'' +
                ", bizUserId='" + bizUserId + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                ", tradeNo='" + tradeNo + '\'' +
                ", extendInfo='" + extendInfo + '\'' +
                ", weChatAPPInfo=" + weChatAPPInfo +
                ", miniprogramPayInfo_VSP='" + miniprogramPayInfo_VSP + '\'' +
                ", payInfo='" + payInfo + '\'' +
                ", validateType=" + validateType +
                '}';
    }
}
