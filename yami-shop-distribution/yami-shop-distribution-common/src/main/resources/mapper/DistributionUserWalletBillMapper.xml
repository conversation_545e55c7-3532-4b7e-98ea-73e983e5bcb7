<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yami.shop.distribution.common.dao.DistributionUserWalletBillMapper">

  <resultMap id="distributionUserWalletBillMap" type="com.yami.shop.distribution.common.model.DistributionUserWalletBill">
    <id property="id" column="id"/>
    <result property="walletId" column="wallet_id"/>
    <result property="unsettledAmount" column="unsettled_amount"/>
    <result property="settledAmount" column="settled_amount"/>
    <result property="invalidAmount" column="invalid_amount"/>
    <result property="addupAmount" column="addup_amount"/>
    <result property="createTime" column="create_time"/>
    <result property="remarks" column="remarks"/>
    <result property="remarksEn" column="remarks_en"/>
    <result property="unsettledAmountAfter" column="unsettled_amount_after"/>
    <result property="settledAmountAfter" column="settled_amount_after"/>
    <result property="invalidAmountAfter" column="invalid_amount_after"/>
    <result property="addupAmountAfter" column="addup_amount_after"/>
    <result property="type" column="type"/>
    <result property="modifier" column="modifier"/>
  </resultMap>


  <resultMap id="DistributionUserWalletBill_DistributionUserVO_SysUserVO" type="com.yami.shop.distribution.common.model.DistributionUserWalletBill">
    <id property="id" column="id"/>
    <result property="unsettledAmount" column="unsettled_amount"/>
    <result property="settledAmount" column="settled_amount"/>
    <result property="invalidAmount" column="invalid_amount"/>
    <result property="addupAmount" column="addup_amount"/>
    <result property="createTime" column="create_time"/>
    <result property="remarks" column="remarks"/>
    <result property="remarksEn" column="remarks_en"/>
    <result property="type" column="type"/>
    <association property="distributionUser" javaType="com.yami.shop.distribution.common.vo.DistributionUserVO">
      <id column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId" />
      <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
      <result column="user_mobile" jdbcType="VARCHAR" property="userMobile" />
    </association>
    <association property="sysUser" javaType="com.yami.shop.bean.vo.SysUserVO">
      <id column="user_id" jdbcType="BIGINT" property="userId" />
      <result column="username" jdbcType="VARCHAR" property="username" />
    </association>
  </resultMap>
    <update id="initWalletBill">
        TRUNCATE TABLE tz_distribution_user_wallet_bill
    </update>

    <select id="getDistributionUserWalletBillAndUserPage" resultMap="DistributionUserWalletBill_DistributionUserVO_SysUserVO">
      select
      duwb.id,
      duwb.unsettled_amount,
      duwb.settled_amount,
      duwb.invalid_amount,
      duwb.addup_amount,
      duwb.create_time,
      duwb.remarks,
      duwb.remarks_en,
      duwb.type,
      du.distribution_user_id,
      du.nick_name,
      du.user_mobile,
      su.user_id,
      su.username
      from
      tz_distribution_user_wallet_bill duwb
      left join tz_distribution_user_wallet duw on duwb.wallet_id = duw.wallet_id
      left join tz_distribution_user du on du.distribution_user_id = duw.distribution_user_id
      left join tz_sys_user su on su.user_id = duwb.modifier
      <where>
          <if test="distributionParam.userMobile != null and distributionParam.userMobile != ''">
              and du.user_mobile like concat( '%', #{distributionParam.userMobile}, '%' )
          </if>
          <if test="distributionParam.nickName != null and distributionParam.nickName != ''">
              AND du.nick_name LIKE CONCAT( '%', #{distributionParam.nickName}, '%' )
          </if>
      </where>
      order by duwb.create_time desc
  </select>

    <select id="getDistributionUserWalletBillDtoPage" resultType="com.yami.shop.distribution.common.dto.DistributionUserWalletBillDto">
        select
        duwb.unsettled_amount,
        duwb.settled_amount,
        duwb.invalid_amount,
        duwb.addup_amount,
        duwb.create_time,
        duwb.remarks,
        duwb.remarks_en
        from
        tz_distribution_user_wallet_bill duwb
        right join tz_distribution_user_wallet duw on duw.wallet_id = duwb.wallet_id
        where
        duw.distribution_user_id = #{distributionUserId}
        order by duwb.create_time
        <if test="orderBy != null and orderBy == 1">
            DESC
        </if>
    </select>

    <select id="getHaveWithdrawalSum" resultType="java.lang.Double">
        SELECT  IFNULL(SUM(- settled_amount),0.00) FROM tz_distribution_user_wallet_bill WHERE settled_amount <![CDATA[ < ]]> 0 AND wallet_id = #{walletId}
    </select>

</mapper>
