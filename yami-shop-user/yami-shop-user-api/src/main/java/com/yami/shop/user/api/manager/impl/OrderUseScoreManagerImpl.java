package com.yami.shop.user.api.manager.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yami.shop.bean.app.dto.ShopCartItemDiscountDto;
import com.yami.shop.bean.app.dto.ShopCartItemDto;
import com.yami.shop.bean.app.dto.ShopCartOrderDto;
import com.yami.shop.bean.app.dto.ShopCartOrderMergerDto;
import com.yami.shop.bean.app.param.OrderParam;
import com.yami.shop.bean.model.Category;
import com.yami.shop.bean.model.UserExtension;
import com.yami.shop.bean.param.ScoreConfigParam;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.util.Arith;
import com.yami.shop.manager.OrderUseScoreManager;
import com.yami.shop.security.api.util.SecurityUtils;
import com.yami.shop.service.CategoryService;
import com.yami.shop.service.SysConfigService;
import com.yami.shop.service.UserExtensionService;
import com.yami.shop.user.common.util.CategoryScale;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/12/23
 */
@Component
@AllArgsConstructor
public class OrderUseScoreManagerImpl implements OrderUseScoreManager {


    private final UserExtensionService userExtensionService;
    private final SysConfigService sysConfigService;

    private final CategoryService categoryService;
    private static final Integer SCORE_RATIO = 100;
    private static final Integer MIN_SCORE_RATIO = 10;

    @Override
    public void orderUseScore(ShopCartOrderMergerDto shopCartOrderMergerDto, OrderParam orderParam, List<ShopCartItemDto> allCartItem) {
        // 获取用户等级积分详细表
        UserExtension extension = userExtensionService.getOne(
                new LambdaQueryWrapper<UserExtension>().eq(UserExtension::getUserId, SecurityUtils.getUser().getUserId()));
        // 获取配置信息
        ScoreConfigParam scoreParam = sysConfigService.getSysConfigObject(Constant.SCORE_CONFIG, ScoreConfigParam.class);
        if (Objects.nonNull(scoreParam) && scoreParam.getShopScoreSwitch() != null && !scoreParam.getShopScoreSwitch()) {
            return;
        }
        if (orderParam.getUserUseScore() != null && orderParam.getUserUseScore() < 0 && extension.getScore() < 0) {
            return;
        }
        // 最大可抵现金额 = ((订单实际金额 - 订单最小金额) * 使用积分分类比例上限)
        double totalActualAmount = Arith.sub(shopCartOrderMergerDto.getActualTotal(), shopCartOrderMergerDto.getTotalTransFee());
        // 计算积分最多抵现金额
        double totalScoreAmount = 0.0;
        List<Category> categories = categoryService.listByShopId(Constant.PLATFORM_SHOP_ID);
        Map<Long, Double> categoryMap = CategoryScale.getScaleByCategory(categories, scoreParam, 0);
        double useDiscount = Objects.isNull(scoreParam.getUseDiscount()) ? 0.0 : scoreParam.getUseDiscount();
        // 单积分抵扣金额
        double scoreDeductionAmount = Arith.div(1, scoreParam.getShopUseScore(), 2);
        // 积分抵现金额可用上限 = 订单可使用积分比例 * ((实际总值)/100）
        if (scoreParam.getUseDiscountRange() == 0) {
            // 如果有等级优惠金额，还需减去等级的优惠金额、运费才是实际金额，包邮时运费为0
            double freeTransFee = Objects.nonNull(shopCartOrderMergerDto.getFreeTransFee()) ? shopCartOrderMergerDto.getFreeTransFee() : 0.0;
            double totalTransFee = Math.max(Arith.sub(shopCartOrderMergerDto.getTotalTransFee(), freeTransFee),0.0);
            double actualTotal = Arith.sub(shopCartOrderMergerDto.getActualTotal(), totalTransFee);
            double notScoreAmount =0.0;
            // 获取大类的比例并转成map
            for (ShopCartItemDto shopCartItem : allCartItem) {
                if (shopCartItem.getActualTotal() < scoreDeductionAmount) {
                    notScoreAmount = Arith.add(notScoreAmount,shopCartItem.getActualTotal());
                }
            }
            actualTotal = Arith.sub(actualTotal , notScoreAmount);
            totalScoreAmount = Arith.div(Arith.mul(actualTotal, useDiscount), 100);
        } else {
            // 获取大类的比例并转成map
            for (ShopCartItemDto shopCartItem : allCartItem) {
                if (!categoryMap.containsKey(shopCartItem.getCategoryId())) {
                    continue;
                }
                if(shopCartItem.getActualTotal() < scoreDeductionAmount){
                    continue;
                }
                // 商品总额减去总优惠，就是商品项的实际金额
                double actualTotal = Arith.sub(shopCartItem.getProductTotalAmount(), shopCartItem.getShareReduce());
                double scoreReduce = Arith.div(Arith.mul(actualTotal, categoryMap.get(shopCartItem.getCategoryId())), 100);
                totalScoreAmount = Arith.add(totalScoreAmount, scoreReduce);
            }
        }

        calculatedScoreAmount(extension, shopCartOrderMergerDto, scoreParam, categoryMap, totalScoreAmount, orderParam);
    }

    /**
     * 计算可用的积分及积分抵扣金额
     * @param extension
     * @param shopCartOrderMergerDto
     * @param scoreParam
     * @param categoryMap
     * @param totalScoreAmount
     * @param orderParam
     */
    private void calculatedScoreAmount(UserExtension extension, ShopCartOrderMergerDto shopCartOrderMergerDto, ScoreConfigParam scoreParam,
                                  Map<Long, Double> categoryMap, double totalScoreAmount, OrderParam orderParam) {
        // 计算用户可用积分 如果大于总共的积分使用比例直接使用，如果小于根据比例使用
        int canUseScore = (int) Arith.mul(totalScoreAmount, scoreParam.getShopUseScore());
        Long userUseScore = extension.getScore();
        Long totalUsableScore = extension.getScore();
        //如果是用户选择的积分数额，则用此积分数额
        if (orderParam.getUserUseScore() != null && orderParam.getIsScorePay() == 1 && orderParam.getUserUseScore() >= 0) {
            userUseScore = Math.min(orderParam.getUserUseScore(), userUseScore);
        } else {
            userUseScore = 0L;
        }
        userUseScore = Math.min(userUseScore, canUseScore);

        // 在极端积分比例的情况下，最大可用积分和使用的积分有冲突操作
        long maxScore = Math.min(totalUsableScore, canUseScore);
        double maxScoreAmount = Arith.div(maxScore, scoreParam.getShopUseScore(), 2);
        maxScore = (long) Arith.mul(maxScoreAmount, scoreParam.getShopUseScore());

        // 如果为大于100的比例，则积分需要整10使用
        if (scoreParam.getShopUseScore() > SCORE_RATIO) {
            userUseScore = userUseScore - userUseScore % MIN_SCORE_RATIO;
            maxScore = maxScore - maxScore % MIN_SCORE_RATIO;
        }

        // 计算可抵扣金额，然后算使用积分，然后再用使用积分算抵扣金额
        totalScoreAmount = Arith.div(userUseScore, scoreParam.getShopUseScore(), 2);

        Double maxAmount = 0.0;
        String maxSkuKey = "";
        // 通过for i找出最后一项，将计算偏差的1积分给最后的最大的一项
        List<ShopCartOrderDto> shopCartOrders = shopCartOrderMergerDto.getShopCartOrders();
        for (ShopCartOrderDto shopCartOrderDto : shopCartOrders) {
            for (ShopCartItemDiscountDto shopCartItemDiscount : shopCartOrderDto.getShopCartItemDiscounts()) {
                for (ShopCartItemDto shopCartItem : shopCartItemDiscount.getShopCartItems()) {
                    if (shopCartItem.getActualTotal() > maxAmount) {
                        maxSkuKey = shopCartItem.getSkuId() + Constant.UNDERLINE + shopCartItem.getStationId();
                        maxAmount = Math.max(shopCartItem.getActualTotal(),maxAmount);
                    }
                }
            }
        }
        // 重新计算一下最大可用积分,可以避免一积分问题
        maxScore = calculatedMaxScore(scoreParam, categoryMap, maxScore, shopCartOrders, maxAmount,maxSkuKey);
        double scale = 1.0;
        // 这里如果不使用最大可用积分，则后续的计算还需要乘以这个比例 - 积分抵扣比例 = 积分抵扣金额/订单实际金额
        if (userUseScore < canUseScore) {
            scale = Arith.div(userUseScore ,maxScore) ;
        }
        calculatedAmount(shopCartOrderMergerDto, scoreParam, categoryMap, totalScoreAmount, orderParam, canUseScore,
                userUseScore, maxScore, scale, shopCartOrders, maxSkuKey);
    }

    private long calculatedMaxScore(ScoreConfigParam scoreParam, Map<Long, Double> categoryMap, long maxScore,
                                    List<ShopCartOrderDto> shopCartOrders,Double maxAmount, String maxSkuKey) {
        // 用户选择积分抵现，组装积分信息
        double totalScoreReduce = 0.0;
        double useDiscount = Objects.isNull(scoreParam.getUseDiscount()) ? 0.0 : scoreParam.getUseDiscount();
        double totalScoreAmount = Arith.div(maxScore, scoreParam.getShopUseScore(), 2);
        // 通过for i找出最后一项，将计算偏差的1积分给最后的最大的一项
        long calculatedMaxScore = 0;
        for (ShopCartOrderDto shopCartOrder : shopCartOrders) {
            List<ShopCartItemDiscountDto> shopCartItemDiscounts = shopCartOrder.getShopCartItemDiscounts();
            for (ShopCartItemDiscountDto shopCartItemDiscount : shopCartItemDiscounts) {
                List<ShopCartItemDto> shopCartItems = shopCartItemDiscount.getShopCartItems();
                for (ShopCartItemDto shopCartItem : shopCartItems) {
                    // 如果是金额最大的一项并且不是套餐，直接跳过，最后在处理
                    String key = shopCartItem.getSkuId() + Constant.UNDERLINE + shopCartItem.getStationId();
                    if (StrUtil.equals(maxSkuKey, key) && Objects.equals(shopCartItem.getComboId(), 0L)) {
                        continue;
                    }
                    double scoreReduceProd = 0.0;
                    double actualTotal = shopCartItem.getActualTotal();
                    // 计算商品分摊的金额，需要乘以比例
                    if (scoreParam.getUseDiscountRange() == 0) {
                        scoreReduceProd = Arith.div(Arith.mul(actualTotal, useDiscount), 100, 2);
                    } else if (categoryMap.containsKey(shopCartItem.getCategoryId())) {
                        scoreReduceProd = Arith.div(Arith.mul(actualTotal, categoryMap.get(shopCartItem.getCategoryId())), 100, 2);
                    }
                    // 计算该商品的最大抵扣积分数量
                    long useScore = (long) Arith.mul(scoreReduceProd, scoreParam.getShopUseScore());
                    scoreReduceProd = Arith.div(useScore, scoreParam.getShopUseScore(), 2);
                    // 如果大于可用上限则直接等于，接将剩余的抵扣金额全部赋予最后一个，积分和金额直接等于 使用的 - 已经抵扣的
                    if (Arith.add(totalScoreReduce, scoreReduceProd) > totalScoreAmount) {
                        // 减去当前总共的积分，减去已分摊的积分
                        useScore = maxScore - calculatedMaxScore;
                        scoreReduceProd = Arith.sub(totalScoreAmount, totalScoreReduce);
                    }
                    totalScoreReduce = Arith.add(totalScoreReduce, scoreReduceProd);
                    calculatedMaxScore += useScore;
                }
            }
        }
        // 处理最后一项
        long useScore = maxScore - calculatedMaxScore;
        double scoreReduceProd = Arith.sub(totalScoreAmount, totalScoreReduce);
        if(scoreReduceProd > maxAmount) {
            scoreReduceProd = Math.min(scoreReduceProd, maxAmount);
            useScore = (long) Arith.mul(scoreReduceProd, scoreParam.getShopUseScore());
        }
        calculatedMaxScore += useScore;
        return calculatedMaxScore;
    }

    /**
     * 计算商品的积分抵扣金额
     *
     * @param shopCartOrderMergerDto
     * @param scoreParam
     * @param categoryMap
     * @param totalScoreAmount
     * @param orderParam
     * @param canUseScore
     * @param userUseScore
     * @param maxScore
     * @param scale
     * @param shopCartOrders
     * @param maxSkuKey
     */
    private void calculatedAmount(ShopCartOrderMergerDto shopCartOrderMergerDto, ScoreConfigParam scoreParam, Map<Long, Double> categoryMap,
                                  double totalScoreAmount, OrderParam orderParam, int canUseScore, Long userUseScore, long maxScore, double scale,
                                  List<ShopCartOrderDto> shopCartOrders, String maxSkuKey) {
        // 用户选择积分抵现，组装积分信息
        double totalScoreReduce = 0.0;

        long totalScore = 0;
        double useDiscount = Objects.isNull(scoreParam.getUseDiscount()) ? 0.0 : scoreParam.getUseDiscount();
        for (ShopCartOrderDto shopCartOrder : shopCartOrders) {
            double reduceSum = 0.0;
            long shopScore = 0;
            List<ShopCartItemDiscountDto> shopCartItemDiscounts = shopCartOrder.getShopCartItemDiscounts();
            for (ShopCartItemDiscountDto shopCartItemDiscount : shopCartItemDiscounts) {
                List<ShopCartItemDto> shopCartItems = shopCartItemDiscount.getShopCartItems();
                for (ShopCartItemDto shopCartItem : shopCartItems) {
                    // 如果是金额最大的一项，直接跳过，最后在处理
                    if (StrUtil.equals(shopCartItem.getSkuId() + Constant.UNDERLINE + shopCartItem.getStationId(), maxSkuKey)) {
                        continue;
                    }
                    double scoreReduceProd = 0.0;
                    double actualTotal;
                    // 计算商品可以用于积分抵扣的金额
                    if (userUseScore < canUseScore) {
                        // 部分积分抵扣， 需要计算商品分摊积分的比例
                        actualTotal = Arith.roundByBanker(Arith.mul(shopCartItem.getActualTotal(), scale), 2);
                    } else {
                        // 全部积分抵扣
                        actualTotal = shopCartItem.getActualTotal();
                    }
                    // 计算商品分摊的金额，需要乘以比例
                    if (Objects.equals(scoreParam.getUseDiscountRange(), 0)) {
                        scoreReduceProd = Arith.div(Arith.mul(actualTotal, useDiscount), 100, 2);
                    } else if (categoryMap.containsKey(shopCartItem.getCategoryId())) {
                        scoreReduceProd = Arith.div(Arith.mul(actualTotal, categoryMap.get(shopCartItem.getCategoryId())), 100, 2);
                    }
                    // 计算该商品的最大抵扣积分数量
                    long useScore = (long) Arith.mul(scoreReduceProd, scoreParam.getShopUseScore());
                    scoreReduceProd = Arith.div(useScore, scoreParam.getShopUseScore(), 2);
                    // 如果大于可用上限则直接等于，接将剩余的抵扣金额全部赋予最后一个，积分和金额直接等于 使用的 - 已经抵扣的
                    if (Arith.add(totalScoreReduce, scoreReduceProd) > totalScoreAmount) {
                        // 减去当前总共的积分，减去店铺已分摊的积分
                        useScore = userUseScore - totalScore - shopScore;
                        scoreReduceProd = Arith.sub(totalScoreAmount, totalScoreReduce);
                    }

                    totalScoreReduce = Arith.add(totalScoreReduce, scoreReduceProd);
                    reduceSum = Arith.add(reduceSum, scoreReduceProd);
                    shopScore += useScore;
                    if (Objects.equals(orderParam.getIsScorePay(), 1)) {
                        double platformReduce = shopCartItem.getPlatformShareReduce() == null ? 0 : shopCartItem.getPlatformShareReduce();
                        shopCartItem.setPlatformShareReduce(Arith.add(platformReduce, scoreReduceProd));
                        shopCartItem.setScorePayReduce(scoreReduceProd);
                        shopCartItem.setScorePrice(useScore);
                        shopCartItem.setShareReduce(Arith.add(Arith.roundByBanker(shopCartItem.getShareReduce(), 2), scoreReduceProd));
                        shopCartItem.setActualTotal(Arith.sub(shopCartItem.getActualTotal(), scoreReduceProd));
                    }
                }
            }
            // 设置店铺的实际总值、积分优惠金额和订单优惠金额
            shopCartOrder.setScoreReduce(reduceSum);
            shopCartOrder.setActualTotal(Arith.sub(shopCartOrder.getActualTotal(), reduceSum));
            // 放入优惠金额
            shopCartOrder.setShopReduce(Arith.add(shopCartOrder.getShopReduce(), reduceSum));
            // 放入平台优惠金额,如果用户等级免自营店运费也要放进去
            shopCartOrder.setPlatformAmount(Arith.add(shopCartOrder.getPlatformAmount(), reduceSum));
            totalScore += shopScore;
            if (orderParam.getIsScorePay() != null && orderParam.getIsScorePay() == 1) {
                shopCartOrder.setUseScore(shopScore);
            }
        }
        handleMaxShopCartOrder(maxSkuKey, shopCartOrderMergerDto, scoreParam, totalScoreAmount, orderParam,
                userUseScore, maxScore, scale, shopCartOrders, totalScoreReduce, totalScore);
    }

    /**
     * 计算商品的积分抵扣金额最大的一项
     */
    private static void handleMaxShopCartOrder(String maxSkuKey, ShopCartOrderMergerDto shopCartOrderMergerDto, ScoreConfigParam scoreParam,
                                               double totalScoreAmount, OrderParam orderParam, Long userUseScore, long maxScore, double scale,
                                               List<ShopCartOrderDto> shopCartOrders, double totalScoreReduce, long totalScore) {
        // 处理最后一项
        for (ShopCartOrderDto shopCartOrder : shopCartOrders) {
            for (ShopCartItemDiscountDto shopCartItemDiscount : shopCartOrder.getShopCartItemDiscounts()) {
                for (ShopCartItemDto shopCartItem : shopCartItemDiscount.getShopCartItems()) {
                    // 如果不是金额最大的一项，直接跳过
                    if (!StrUtil.equals(shopCartItem.getSkuId() + Constant.UNDERLINE + shopCartItem.getStationId(), maxSkuKey)) {
                        continue;
                    }
                    // 减去当前总共的积分，减去店铺已分摊的积分
                    long useScore = userUseScore - totalScore;
                    double scoreReduceProd = Arith.sub(totalScoreAmount, totalScoreReduce);
                    if(scoreReduceProd > shopCartItem.getActualTotal()) {
                        scoreReduceProd = Math.min(scoreReduceProd, shopCartItem.getActualTotal());
                        useScore = (long) Arith.mul(Arith.roundByBanker(Arith.mul(scoreReduceProd, scale), 2), scoreParam.getShopUseScore());
                        scoreReduceProd = Arith.div(useScore, scoreParam.getShopUseScore(), 2);
                    }
                    // 用计算出的积分抵扣金额跟商品实际金额进行比较，如果积分抵扣金额大于商品实际金额就不进行积分抵扣处理了，防止订单金额小于0
                    if (shopCartItem.getActualTotal() < scoreReduceProd) {
                        continue;
                    }
                    if (null != orderParam.getIsScorePay() && 1 == orderParam.getIsScorePay()) {
                        double platformReduce = Objects.isNull(shopCartItem.getPlatformShareReduce()) ? 0 : shopCartItem.getPlatformShareReduce();
                        shopCartItem.setPlatformShareReduce(Arith.add(platformReduce, scoreReduceProd));
                        shopCartItem.setScorePayReduce(scoreReduceProd);
                        shopCartItem.setScorePrice(useScore);
                        shopCartItem.setShareReduce(Arith.add(Arith.roundByBanker(shopCartItem.getShareReduce(), 2), scoreReduceProd));
                        shopCartItem.setActualTotal(Arith.sub(shopCartItem.getActualTotal(), scoreReduceProd));
                    }
                    // 设置店铺的实际总值、积分优惠金额和订单优惠金额
                    shopCartOrder.setScoreReduce(Arith.add(shopCartOrder.getScoreReduce(),scoreReduceProd));
                    shopCartOrder.setActualTotal(Arith.sub(shopCartOrder.getActualTotal(), scoreReduceProd));
                    // 放入优惠金额
                    shopCartOrder.setShopReduce(Arith.add(shopCartOrder.getShopReduce(), scoreReduceProd));
                    // 放入平台优惠金额,如果用户等级免自营店运费也要放进去
                    shopCartOrder.setPlatformAmount(Arith.add(shopCartOrder.getPlatformAmount(), scoreReduceProd));
                    if (null != orderParam.getIsScorePay() && 1 == orderParam.getIsScorePay()) {
                        shopCartOrder.setUseScore(shopCartOrder.getUseScore() + useScore);
                    }
                    totalScoreReduce = Arith.add(totalScoreReduce, scoreReduceProd);
                    totalScore += useScore;
                    break;
                }
            }
        }
        // 设置订单的实际总值和订单优惠金额
        shopCartOrderMergerDto.setTotalScoreAmount(totalScoreReduce);
        shopCartOrderMergerDto.setShopUseScore(scoreParam.getShopUseScore());
        shopCartOrderMergerDto.setTotalUsableScore(totalScore);
        shopCartOrderMergerDto.setActualTotal(Arith.sub(shopCartOrderMergerDto.getActualTotal(), totalScoreReduce));
        shopCartOrderMergerDto.setOrderReduce(Arith.add(shopCartOrderMergerDto.getOrderReduce(), totalScoreReduce));
        shopCartOrderMergerDto.setMaxUsableScore(maxScore);
        if (null != orderParam.getIsScorePay() && 1 == orderParam.getIsScorePay()) {
            shopCartOrderMergerDto.setScorePrice(totalScore);
        }
    }
}
