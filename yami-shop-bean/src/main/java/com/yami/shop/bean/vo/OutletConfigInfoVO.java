package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OutletConfigInfoVO {

    @Schema(description = "快递公司类型")
    private Integer deliveryCompanyType;

    @Schema(description = "快递公司名称")
    private String deliveryCompanyName;

    @Schema(description = "是否配置 0否1是")
    private Integer isConfig;

    @Schema(description = "是否默认 0否1是")
    private Integer isDefault;

    @Schema(description = "商家地址列表")
    private List<OutletConfigAddrVO> shopAddrList;
}
