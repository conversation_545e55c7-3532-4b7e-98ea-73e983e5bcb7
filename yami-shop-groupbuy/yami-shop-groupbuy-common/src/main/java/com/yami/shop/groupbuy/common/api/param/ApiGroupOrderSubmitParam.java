package com.yami.shop.groupbuy.common.api.param;

import com.yami.shop.bean.app.param.OrderFlowLogParam;
import com.yami.shop.bean.app.param.SubmitOrderParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "订单提交参数")
public class ApiGroupOrderSubmitParam extends SubmitOrderParam {

    @Schema(description = "拼团团队id，（如果用户为参团则需要填写对应的拼团团队Id(groupTeamId)，如果为用户为开团,拼团团队Id(groupTeamId)为0）" )
    @NotNull(message = "拼团团队id不能为空")
    private Long groupTeamId;

    @Schema(description = "用户操作参数")
    private OrderFlowLogParam orderFlowLogParam;
}
