package com.yami.shop.groupbuy.common.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupUserDto;
import com.yami.shop.groupbuy.common.model.GroupOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
public interface GroupOrderMapper extends BaseMapper<GroupOrder> {

    /**
     * 获取参团的用户列表
     *
     * @param groupTeamId 拼团单ID
     * @return ApiGroupUserDto列表
     */
    List<ApiGroupUserDto> listApiGroupUserDto(@Param("groupTeamId") Long groupTeamId);
//
//    /**
//     * 获取活动已经成团的用户数量
//     *
//     * @param groupActivityId 团购活动id
//     * @param groupProdId 团购商品id
//     * @return 成团的用户数量
//     */
//    Long getHadGroupNumberCount(@Param("groupActivityId") Long groupActivityId, @Param("groupProdId") Long groupProdId);
//
//    /**
//     * 根据团购商品id, 获取团购订单列表
//     *
//     * @param groupProdId 团购商品id
//     * @param userId 用户id
//     * @return 团购订单
//     */
//    List<GroupOrder> listByGroupProdId(@Param("groupProdId") Long groupProdId, @Param("userId") String userId);

    /**
     * 获取拼团队列中，已拼团的商品数量
     * @param userId 用户id
     * @param groupActivityId 团购活动id
     * @return 团购商品数量
     */
    Integer getUserHadOrderCountByGroupProdId(@Param("userId") String userId, @Param("groupActivityId") Long groupActivityId);

    /**
     * 获取拼团订单数量
     * @param userId 用户id
     * @param groupActivityId 团购商品id
     * @param prodId
     * @return 拼团团队订单数
     */
    int getUserUnGroupOrderCount(@Param("userId") String userId, @Param("groupActivityId") Long groupActivityId, @Param("prodId") Long prodId);

    /**
     * 取消团购订单
     * @param orderNumber 订单编号
     * @param status 状态
     * @return 更新的订单数量
     */
    int cancelGroupOrder(@Param("orderNumber") String orderNumber,@Param("status") int status);

}
