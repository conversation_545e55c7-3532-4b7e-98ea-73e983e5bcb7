<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.distribution.common.dao.DistributionUserWalletMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.distribution.common.model.DistributionUserWallet">
    <id column="wallet_id" jdbcType="BIGINT" property="walletId" />
    <result column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId" />
    <result column="unsettled_amount" jdbcType="DECIMAL" property="unsettledAmount" />
    <result column="settled_amount" jdbcType="DECIMAL" property="settledAmount" />
    <result column="addup_amount" jdbcType="DECIMAL" property="addupAmount" />
      <result column="invalid_amount" jdbcType="DECIMAL" property="invalidAmount" />
    <result column="version" jdbcType="INTEGER" property="version" />
      <result column="state" jdbcType="INTEGER" property="state" />
  </resultMap>

    <resultMap id="DistributionUserWallet_DistributionUserVO" type="com.yami.shop.distribution.common.model.DistributionUserWallet">
        <id column="wallet_id" jdbcType="BIGINT" property="walletId" />
        <result column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId" />
        <result column="unsettled_amount" jdbcType="DECIMAL" property="unsettledAmount" />
        <result column="settled_amount" jdbcType="DECIMAL" property="settledAmount" />
        <result column="addup_amount" jdbcType="DECIMAL" property="addupAmount" />
        <result column="invalid_amount" jdbcType="DECIMAL" property="invalidAmount" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="state" jdbcType="INTEGER" property="state" />
        <association property="distributionUser" javaType="com.yami.shop.distribution.common.vo.DistributionUserVO">
            <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
            <result column="user_mobile" jdbcType="VARCHAR" property="userMobile" />
        </association>
    </resultMap>

    <select id="getDistributionUserWalletAndDistributionUserVoPage" resultMap="DistributionUserWallet_DistributionUserVO">
        select
        duw.wallet_id,
        duw.distribution_user_id,
        duw.unsettled_amount,
        duw.settled_amount,
        duw.invalid_amount,
        duw.addup_amount,
        duw.version,
        du.state,
        du.nick_name,
        du.user_mobile
        from tz_distribution_user_wallet duw
        left join tz_distribution_user du on du.distribution_user_id = duw.distribution_user_id
        WHERE
            du.state != 0
            and du.state != 3
            <if test="distributionParam.userMobile != null and distributionParam.userMobile != ''">
                and du.user_mobile like concat( '%', #{distributionParam.userMobile}, '%' )
            </if>
            <if test="distributionParam.nickName != null and distributionParam.nickName != ''">
                AND du.nick_name LIKE CONCAT( '%', #{distributionParam.nickName}, '%' )
            </if>
        order by du.distribution_user_id DESC
    </select>

    <select id="getWalletIdByDistributionUserId" resultType="long">
      select
      wallet_id
      from
      tz_distribution_user_wallet
      where distribution_user_id=#{distributionUserId}
    </select>

    <select id="getDistributionUserWalletDtoByDistributionUserId" resultType="com.yami.shop.distribution.common.dto.DistributionUserWalletDto">
        select
        unsettled_amount,
        settled_amount,
        invalid_amount,
        addup_amount
        from
        tz_distribution_user_wallet
        where distribution_user_id = #{distributionUserId}
    </select>
    <update id="updateAmountByDistributionUserId">
        update tz_distribution_user_wallet
        set unsettled_amount = 0,settled_amount = 0,invalid_amount = 0,addup_amount = 0
        where
            distribution_user_id = #{distributionUserId}
    </update>

  <update id="updateSettledAmount">
      update tz_distribution_user_wallet
      set settled_amount = settled_amount + #{settledAmount}
      <if test="settledAmount > 0">
          ,addup_amount = addup_amount + #{settledAmount}
      </if>
      where distribution_user_id = #{distributionUserId}
  </update>

    <update id="updateAmount">
        update tz_distribution_user_wallet
        set settled_amount = settled_amount - #{amount}
        where settled_amount >= #{amount} and distribution_user_id = #{distributionUserId}
    </update>
</mapper>
