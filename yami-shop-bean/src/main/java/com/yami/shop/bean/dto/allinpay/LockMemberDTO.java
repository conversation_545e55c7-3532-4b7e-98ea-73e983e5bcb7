package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class LockMemberDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "用户id")
    private Long userId;

    public LockMemberDTO() {
    }

    public LockMemberDTO(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "LockMemberDTO{" +
                "userId=" + userId +
                '}';
    }
}
