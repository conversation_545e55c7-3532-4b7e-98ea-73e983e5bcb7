package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 自提点
 *
 * <AUTHOR>
 * @date 2020-04-23 15:18:29
 */
@Data
public class StationDto{
    @Schema(description = "自提点id" )
    private Long stationId;

    @Schema(description = "关联店铺" )
    private Long shopId;

    @Schema(description = "自提点名称" )
    private String stationName;

    @Schema(description = "自提点图片" )
    private String pic;

    @Schema(description = "自提点图片，多张以逗号隔开" )
    private String imgUrls;

    @Schema(description = "电话区号" )
    private String phonePrefix;

    @Schema(description = "手机/电话号码" )
    private String phone;

    @Schema(description = "0:关闭 1:营业 2:强制关闭 3:审核中 4:审核失败" )
    private Integer status;

    @Schema(description = "省id" )
    private Long provinceId;

    @Schema(description = "省" )
    private String province;

    @Schema(description = "市id" )
    private Long cityId;

    @Schema(description = "市" )
    private String city;

    @Schema(description = "区id" )
    private Long areaId;

    @Schema(description = "区" )
    private String area;

    @Schema(description = "地址" )
    private String addr;

    @Schema(description = "经度" )
    private Double lng;

    @Schema(description = "纬度" )
    private Double lat;

    @Schema(description = "时间日期数据" )
    private String timeDate;

    @Schema(description = "销售数据" )
    private List<StationSalesDto> stationSalesDtoList;

    @Schema(description = "版本" )
    private Integer version;

    @Schema(description = "自提门店用途是否支持自提（0：不支持，1：支持）")
    private Integer selfPickup;

    @Schema(description = "自提门店用途是否支持同城配送（0：不支持，1：支持）")
    private Integer sameCityDelivery;

    @Schema(description = "门店评分" )
    private Double stationScore;

    @Schema(description = "收藏数量" )
    private Integer collectionNum;

}
