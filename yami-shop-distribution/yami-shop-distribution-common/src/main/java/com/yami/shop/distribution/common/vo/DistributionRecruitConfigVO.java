package com.yami.shop.distribution.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-09-06 14:20:02
 */
@Data
public class DistributionRecruitConfigVO {


    @Schema(description = "推广封面" )
    private String pic;

    @Schema(description = "推广标题" )
    private String title;

    @Schema(description = "推广内容" )
    private String content;

    /**
     * 状态（0下线 1上线）
     */
    @Schema(description = "状态 0关 1开" )
    private Integer state;
}
