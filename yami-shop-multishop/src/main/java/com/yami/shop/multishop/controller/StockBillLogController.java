package com.yami.shop.multishop.controller;

import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.enums.EsOperationType;
import com.yami.shop.bean.enums.StockBillType;
import com.yami.shop.bean.enums.StockType;
import com.yami.shop.bean.event.EsProductUpdateEvent;
import com.yami.shop.bean.model.StockBillLog;
import com.yami.shop.bean.model.StockBillLogItem;
import com.yami.shop.bean.param.ProductParam;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.constants.SegmentIdKey;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.SegmentService;
import com.yami.shop.service.StockBillLogService;
import com.yami.shop.sys.common.service.ShopEmployeeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 出入库明细
 *
 * <AUTHOR>
 * @date 2021-09-09 13:11:15
 */
@RestController
@RequestMapping("/shop/stockBillLog" )
@Tag(name = "出入库明细接口")
@RequiredArgsConstructor
public class StockBillLogController {
    private final StockBillLogService stockBillLogService;
    private final ShopEmployeeService shopEmployeeService;
    private final SegmentService segmentService;
    private final ApplicationEventPublisher eventPublisher;


    @GetMapping("/page" )
    @Schema(description = "分页获取出入库明细信息" )
    @PreAuthorize("@pms.hasPermission('shop:stockBillLog:page')")
    public ServerResponseEntity<IPage<StockBillLog>> getStockBillLogPage(PageParam<StockBillLog> page, StockBillLog stockBillLog) {
        stockBillLog.setShopId(SecurityUtils.getShopUser().getShopId());
        IPage<StockBillLog> stockBillLogPage = stockBillLogService.pageByParams(page, stockBillLog);
        for(StockBillLog billLog : stockBillLogPage.getRecords()){
            if (StrUtil.isNotBlank(billLog.getEmployeeMobile())) {
                billLog.setEmployeeMobile(PhoneUtil.hideBetween(billLog.getEmployeeMobile()).toString());
            }
        }
        return ServerResponseEntity.success(stockBillLogPage);
    }

    @GetMapping("/info/{stockBillLogId}" )
    @Schema(description = "通过id查询出入库明细信息" )
    @PreAuthorize("@pms.hasPermission('shop:stockBillLog:info')")
    public ServerResponseEntity<StockBillLog> getById(@PathVariable("stockBillLogId") Long stockBillLogId) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        StockBillLog stockBillLog = stockBillLogService.getByStockBillLogId(stockBillLogId);
        if (!Objects.equals(shopId, stockBillLog.getShopId())) {
            // 当前出入库明细信息出错
            throw new YamiShopBindException("yami.stock.bill.log.exception.infoError");
        }
        if (StrUtil.isNotBlank(stockBillLog.getEmployeeMobile())) {
            stockBillLog.setEmployeeMobile(PhoneUtil.hideBetween(stockBillLog.getEmployeeMobile()).toString());
        }
        return ServerResponseEntity.success(stockBillLog);
    }

    @PostMapping
    @Operation(summary = "保存其他出入库明细" , description = "保存其他出入库明细")
    @PreAuthorize("@pms.hasPermission('shop:stockBillLog:save')")
    public ServerResponseEntity<Void> save(@RequestBody @Valid StockBillLog stockBillLog) {
        //出库操作下判断出库数是否超出库存数？执行出库 ： 返回错误消息
        if (stockBillLog.getType() == 1){
            List<StockBillLogItem> stockBillLogItems = stockBillLog.getStockBillLogItems();
            for (int i = 0; i < stockBillLogItems.size(); i++) {
                if (stockBillLogItems.get(i).getStockCount() > stockBillLogItems.get(i).getStocks()){
                    throw new YamiShopBindException("yami.deliverySum.exceed.stocks");
                }
            }
        }
        stockBillLog.setShopId(SecurityUtils.getShopUser().getShopId());
        stockBillLog.setEmployeeId(SecurityUtils.getShopUser().getEmployeeId());
        stockBillLog.setMakerMobile(shopEmployeeService.getShopEmployeeById(stockBillLog.getEmployeeId()).getMobile());
        if(Objects.equals(stockBillLog.getStockBillType(), StockBillType.PURCHASE_STORAGE.value())){
            String purchaseNumber = Constant.PURCHASES_ORDER + segmentService.getDateFormatSegmentId(SegmentIdKey.PURCHASES_ORDER);
            stockBillLog.setSourceOrderNo(purchaseNumber);
        }
        if (!Objects.equals(stockBillLog.getStockBillType(), StockBillType.PURCHASE_STORAGE.value())) {
            stockBillLog.setStockBillType(Objects.equals(stockBillLog.getType(), StockType.OUT_OF_STOCK.value()) ? StockBillType.OTHER_OUTBOUND.value() : StockBillType.OTHER_STORAGE.value());
        }
        stockBillLogService.saveInfo(stockBillLog);
        // 更新商品信息
        List<Long> prodIds = stockBillLog.getStockBillLogItems().stream().map(StockBillLogItem::getProdId).collect(Collectors.toList());
        eventPublisher.publishEvent(new EsProductUpdateEvent(null, prodIds, EsOperationType.UPDATE_BATCH));
        return ServerResponseEntity.success();
    }

    @SysLog("修改出入库明细")
    @PutMapping
    @Operation(summary = "更新其他出入库明细" , description = "更新其他出入库明细")
    @PreAuthorize("@pms.hasPermission('shop:stockBillLog:update')")
    public ServerResponseEntity<Void> updateById(@RequestBody @Valid StockBillLog stockBillLog) {
        if (Objects.isNull(stockBillLog.getStockBillLogId())) {
            // id不能为空
            throw new YamiShopBindException("yami.stock.bill.log.exception.idCannotNull");
        }
        stockBillLog.setShopId(SecurityUtils.getShopUser().getShopId());
        stockBillLog.setEmployeeId(SecurityUtils.getShopUser().getEmployeeId());
        stockBillLog.setMakerMobile(shopEmployeeService.getShopEmployeeById(stockBillLog.getEmployeeId()).getMobile());
        stockBillLogService.updateInfo(stockBillLog);
        // 更新商品信息
        List<Long> prodIds = stockBillLog.getStockBillLogItems().stream().map(StockBillLogItem::getProdId).collect(Collectors.toList());
        eventPublisher.publishEvent(new EsProductUpdateEvent(null, prodIds, EsOperationType.UPDATE_BATCH));
        return ServerResponseEntity.success();
    }

    @GetMapping("/exportStockBillLog")
    @Operation(summary = "导出出入库明细列表" , description = "导出出入库明细列表")
    @PreAuthorize("@pms.hasPermission('shop:stockBillLog:export')")
    public void exportStockBillLog(StockBillLog stockBillLog, HttpServletResponse response) {
        stockBillLog.setShopId(SecurityUtils.getShopUser().getShopId());
        stockBillLogService.exportStockBillLog(stockBillLog, response);
    }

    @PutMapping("/voided")
    @Operation(summary = "作废出入库明细单" , description = "作废出入库明细单")
    @PreAuthorize("@pms.hasPermission('shop:stockBillLog:voided')")
    public ServerResponseEntity<Void> voided(@RequestParam("stockBillLogId") Long stockBillLogId) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        stockBillLogService.voided(shopId, stockBillLogId);
        return ServerResponseEntity.success();
    }

    @GetMapping("/inquire")
    @Operation(summary = "库存查询" , description = "库存查询")
    public ServerResponseEntity<IPage<StockBillLogItem>> inquire(PageParam<StockBillLog> page, ProductParam productParam) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        productParam.setShopId(shopId);
        return ServerResponseEntity.success(stockBillLogService.inquire(page, productParam));
    }

    @GetMapping("/purchasePage" )
    @Schema(description = "分页获取采购库存明细信息" )
    @PreAuthorize("@pms.hasPermission('shop:stockBillLog:purchasePage')")
    public ServerResponseEntity<IPage<StockBillLog>> purchasePage(PageParam<StockBillLog> page, StockBillLog stockBillLog) {
        stockBillLog.setShopId(SecurityUtils.getShopUser().getShopId());
        stockBillLog.setStockBillType(StockBillType.PURCHASE_STORAGE.value());
        return ServerResponseEntity.success(stockBillLogService.purchasePage(page, stockBillLog));
    }

    @GetMapping("/purchaseInfo/{stockBillLogId}" )
    @Schema(description = "通过id查询出入库明细信息" )
    @PreAuthorize("@pms.hasPermission('shop:stockBillLog:purchaseInfo')")
    public ServerResponseEntity<StockBillLog> purchaseInfo(@PathVariable("stockBillLogId") Long stockBillLogId) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        StockBillLog stockBillLog = stockBillLogService.purchaseInfo(stockBillLogId);
        if (!Objects.equals(shopId, stockBillLog.getShopId())) {
            throw new YamiShopBindException("yami.data.deleted.or.not.exist");
        }
        return ServerResponseEntity.success(stockBillLog);
    }

}
