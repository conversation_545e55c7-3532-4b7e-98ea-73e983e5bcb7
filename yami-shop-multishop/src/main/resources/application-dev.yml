server:
  port: 8087
spring:
  datasource:
    url: *****************************************************************************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: hn02le.34lkdLKD
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 0
      maximum-pool-size: 20
      idle-timeout: 10000
      auto-commit: true
      connection-test-query: SELECT 1
  data:
    redis:
      host: ***************
      port: 6379
      password: hn02le.34lkdLKD
  # es默认关闭，开启es的时候，请把spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration 这两行删除
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration
  elasticsearch:
    enable: false
    uris: http://***************:9200

# 这个redis和上面的redis不一样，是用来存库存的
redis:
  aof:
    database: 0
    redis-addr: ***************:6379
    password: hn02le.34lkdLKD

logging:
  config: classpath:logback/logback-dev.xml
wukongim:
  address: http://***************:5001
  token: hn02le.34lkdLKD
# 短信配置
sms:
  # 标注从yml读取配置
  config-type: yaml
  restricted: true
  account-max: 10
  minute-max: 1
  blends:
    local:
      supplier: alibaba #默认阿里云,参考详情见 SmsTypeEnum
      access-key-id: #必填 短信服务accessKeyId
      access-key-secret: #必填 短信accessKeySecret
      signature: #必填 短信签名signName
      request-url: #APP接入地址 (亿美软通)
      sender: #国内短信签名通道号 (华为云)
      status-call-back: #短信状态报告接收地 (华为云)
      url: #APP接入地址 (华为云)
      callback-url: #短信发送后将向这个地址推送(运营商返回的)发送报告 (云片短信)
      mch-id: #企业ID （联麓短信）
      app-key: #appKey （联麓短信）
      app-id: #appId （联麓短信）
      signature-id: #签名ID （七牛云短信）
