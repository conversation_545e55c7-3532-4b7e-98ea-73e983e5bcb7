package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Schema(description = "收藏对象")
@Data
public class UserCollectionDto {

    @Schema(description = "收藏id" )
    private Long id;

    @Schema(description = "商品id" )
    private Long prodId;

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "收藏时间" )
    private Date createTime;

    @Schema(description = "商品现价" )
    private Double price;

    @Schema(description = "商品原价" )
    private Double oriPrice;

    @Schema(description = "商品主图" )
    private String pic;

    @Schema(description = "-1:删除、0:商家下架、1:上架、2:违规下架、3:平台审核" )
    private Integer status;

}
