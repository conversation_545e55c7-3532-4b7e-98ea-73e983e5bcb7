package com.yami.shop.bean.app.param;

import com.yami.shop.common.util.PrincipalUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SendSmsParam {

    @Schema(description = "手机号" )
    @Pattern(regexp= PrincipalUtil.MOBILE_REGEXP,message = "请输入正确的手机号")
    private String mobile;

    @Schema(description = "是否店铺账号，1是" )
    private Integer shopAccount;

}
