package com.yami.shop.groupbuy.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.bean.app.dto.ShopCartOrderMergerDto;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupUserDto;
import com.yami.shop.groupbuy.common.bo.GroupOrderBO;
import com.yami.shop.groupbuy.common.model.GroupOrder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
public interface GroupOrderService extends IService<GroupOrder> {

    /**
     * 获取参团的用户列表
     *
     * @param groupTeamId 拼团单ID
     * @return 参团的用户列表
     */
    List<ApiGroupUserDto> listApiGroupUserDto(Long groupTeamId);

    /**
     * 提交订单
     *
     * @param mergerOrder
     * @param groupOrderBO
     * @return 订单编号
     */
    void submit(ShopCartOrderMergerDto mergerOrder, GroupOrderBO groupOrderBO);

    /**
     * 通过活动商品id获取用户已下单的商品数量
     * @param userId 用户id
     * @param groupActivityId 团购商品id
     * @return 商品的数量
     */
    Integer getUserHadOrderCountByGroupProdId(String userId, Long groupActivityId);

    /**
     * 获取用户未成团的订单数量
     * @param userId 用户ud
     * @param groupActivityId 团购商品id
     * @param prodId
     * @return 订单数量
     */
    int getUserUnGroupOrderCount(String userId, Long groupActivityId, Long prodId);

    /**
     * 取消团购订单
     * @param orderNumber 订单编号
     * @param status 状态
     * @return 更新的数量
     */
    int cancelGroupOrder(String orderNumber, int status);
}
