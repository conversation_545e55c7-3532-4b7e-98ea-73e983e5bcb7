package com.yami.shop.platform.controller;

import cn.hutool.core.util.PhoneUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.allinpay.service.AllinpayCompanyService;
import com.yami.shop.bean.model.CompanyAuditing;
import com.yami.shop.bean.param.CompanyInfoAuditParam;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.platform.util.SecurityUtils;
import com.yami.shop.service.CompanyAuditingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2022/9/21 10:23
 */

@RestController
@RequestMapping("/platform/companyAuditing")
@Tag(name = "审核工商信息接口")
@AllArgsConstructor
public class CompanyAuditingController {

    private final CompanyAuditingService companyAuditingService;
    private final AllinpayCompanyService allinpayCompanyService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('shop:companyAuditing:page')")
    @Operation(summary = "分页获取待审核的工商信息", description = "分页获取待审核的工商信息")
    public ServerResponseEntity<IPage<CompanyAuditing>> getCompanyAuditingPage(PageParam<CompanyAuditing> page, CompanyInfoAuditParam auditParam) {
        return ServerResponseEntity.success(companyAuditingService.page(page, auditParam));
    }

    @PutMapping("/audit")
    @Operation(summary = "审核签约信息", description = "审核签约信息")
    @PreAuthorize("@pms.hasPermission('shop:companyAuditing:audit')")
    public ServerResponseEntity<Void> audit(@Valid @RequestBody CompanyAuditing companyAuditing) {
        if (allinpayCompanyService.getIsAllinpay()) {
            // 开启通联后，平台无需审核商家工商信息
            throw new YamiShopBindException("yami.company.auditing.exception.notNeedAudit");
        }
        companyAuditing.setAuditorId(SecurityUtils.getSysUser().getUserId());
        companyAuditingService.audit(companyAuditing);
        return ServerResponseEntity.success();
    }

    @GetMapping("/auditInfo")
    @Operation(summary = "查看申请审核情况", description = "查看申请审核情况")
    public ServerResponseEntity<CompanyAuditing> auditInfo(@RequestParam("shopId") Long shopId) {
        CompanyAuditing auditInfo = companyAuditingService.getAuditInfo(shopId);
        if (Objects.nonNull(auditInfo) && Objects.nonNull(auditInfo.getShopCompany())) {
            if (Objects.nonNull(auditInfo.getShopCompany().getLegalPhone())) {
                auditInfo.getShopCompany().setLegalPhone(PhoneUtil.hideBetween(auditInfo.getShopCompany().getLegalPhone()).toString());
            }
        }
        return ServerResponseEntity.success(auditInfo);
    }
}
