package com.yami.shop.distribution.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 申请成为分销员所需填写资料
 * <AUTHOR>
 */
@Data
public class DistributionAuditingInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 真实姓名
     */
    @Schema(description = "真实姓名" )
    private Boolean realName;

    /**
     * 身份证号
     */
    @Schema(description = "身份证号" )
    private Boolean identityCardNumber;

    /**
     * 身份证照片
     */
    @Schema(description = "身份证照片" )
    private Boolean identityCardPic;
}
