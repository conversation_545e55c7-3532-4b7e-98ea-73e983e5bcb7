package com.yami.shop.bean.vo.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2021-05-22 14:30:11
 */
@Data
public class CustomerRetainVO {

    public CustomerRetainVO() {
        BigDecimal bigDecimal = new BigDecimal("0");
//        String currentMonth;
//
//        Integer newCustomers = 0;
//
//        Integer firstMonthRemain = 0;
//        BigDecimal firstMonthRemainRate = bigDecimal;
//
//        Integer secondMonthRemain = 0;
//        BigDecimal secondMonthRemainRate = bigDecimal;
//
//        Integer thirdMonthRemain = 0;
//        BigDecimal thirdMonthRemainRate = bigDecimal;
//
//        Integer fourthMonthRemain = 0;
//        BigDecimal fourthMonthRemainRate = bigDecimal;
//
//        Integer fifthMonthRemain = 0;
//        BigDecimal fifthMonthRemainRate = bigDecimal;
//
//        Integer sixthMonthRemain = 0;
//        BigDecimal sixthMonthRemainRate = bigDecimal;
    }

    @Schema(description = "当前月" )
    private String currentMonth;

    @Schema(description = "新访问/成交客户数" )
    private Integer newCustomers;

    @Schema(description = "第1月留存" )
    private Integer firstMonthRemain;
    @Schema(description = "第1月留存率" )
    private BigDecimal firstMonthRemainRate;

    @Schema(description = "第2月留存" )
    private Integer secondMonthRemain;
    @Schema(description = "第2月留存率" )
    private BigDecimal secondMonthRemainRate;

    @Schema(description = "第3月留存" )
    private Integer thirdMonthRemain;
    @Schema(description = "第3月留存率" )
    private BigDecimal thirdMonthRemainRate;

    @Schema(description = "第4月留存" )
    private Integer fourthMonthRemain;
    @Schema(description = "第4月留存率" )
    private BigDecimal fourthMonthRemainRate;

    @Schema(description = "第5月留存" )
    private Integer fifthMonthRemain;
    @Schema(description = "第5月留存率" )
    private BigDecimal fifthMonthRemainRate;

    @Schema(description = "第6月留存" )
    private Integer sixthMonthRemain;
    @Schema(description = "第6月留存率" )
    private BigDecimal sixthMonthRemainRate;

    /**
     *
     */
    private Integer month;
}
