package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 自提点销售记录
 *
 * <AUTHOR>
 * @date 2020-06-04 15:31:29
 */
@Data
public class StationSalesDto {

    @Schema(description = "支付订单数" , requiredMode = Schema.RequiredMode.REQUIRED)
    private int payOrderNumber;

    /**
     * 平台优惠金额
     */
    private double platformAmount;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 核销数量
     */
    private int writeOffNum;

    /**
     * 核销数量
     */
    private String userId;

    /**
     * 核销数量
     */
    private double productNums;

    /**
     * 实际支付金额
     */
    private double actualTotal;
    /**
     * 支付人数
     */
    private int userNum;

    /**
     *  支付金额 = 平台优惠金额 + 实际支付金额
     */
    @Schema(description = "支付金额" , requiredMode = Schema.RequiredMode.REQUIRED)
    private double payAmount;

    @Schema(description = "客单价" , requiredMode = Schema.RequiredMode.REQUIRED)
    private double customerUnitPrice;

    @Schema(description = "1:单天营业额，2:该月营业额" , requiredMode = Schema.RequiredMode.REQUIRED)
    private int salesType;
}
