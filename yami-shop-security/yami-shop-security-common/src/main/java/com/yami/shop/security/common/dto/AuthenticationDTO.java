package com.yami.shop.security.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 用于登陆传递账号密码
 *
 * <AUTHOR>
 * @date 2020/7/1
 */
@Data
public class AuthenticationDTO {

    /**
     * 用户名
     */
    @NotBlank(message = "userName不能为空")
    @Schema(description = "用户名/邮箱/手机号" , requiredMode = Schema.RequiredMode.REQUIRED)
    protected String userName;

    /**
     * 密码
     */
    @NotBlank(message = "passWord不能为空")
    @Schema(description = "一般用作密码" , requiredMode = Schema.RequiredMode.REQUIRED)
    protected String passWord;

}
