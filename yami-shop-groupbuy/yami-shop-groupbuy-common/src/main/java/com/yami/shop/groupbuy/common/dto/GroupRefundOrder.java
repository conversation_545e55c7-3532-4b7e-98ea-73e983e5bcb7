package com.yami.shop.groupbuy.common.dto;

import com.yami.shop.bean.model.Order;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 拼团退款数据模型
 * <AUTHOR>
 * @date 2019/9/4 16:21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class GroupRefundOrder extends Order {

    /**
     * 支付单ID
     */
    private Long settlementId;

    /**
     * 支付单号
     */
    private String orderPayNo;

    /**
     * 支付状态
     */
    private Integer payStatus;

    /**
     * 团购订单id
     */
    private Long groupOrderId;
}
