package com.yami.shop.common.enums;

/**
 * 会员等级类型
 *
 * <AUTHOR>
 */
public enum UserLevelType {
    /** 普通会员 */
    ORDINARY(0),

    /** vip会员 */
    VIP(1),
    ;

    private final Integer num;

    public Integer value() {
        return num;
    }

    UserLevelType(Integer num){
        this.num = num;
    }

    public static UserLevelType instance(Integer value) {
        UserLevelType[] enums = values();
        for (UserLevelType statusEnum : enums) {
            if (statusEnum.value().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }
}
