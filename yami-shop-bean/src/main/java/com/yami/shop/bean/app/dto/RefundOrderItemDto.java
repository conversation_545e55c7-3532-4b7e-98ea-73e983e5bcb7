package com.yami.shop.bean.app.dto;

import com.yami.shop.bean.model.OrderItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 退款订单项
 * <AUTHOR>
 */
@Data
public class RefundOrderItemDto {

    @Schema(description = "订单项id")
    private Long orderItemId;

    @Schema(description = "产品名称" )
    private String prodName;

    @Schema(description = "sku名称" )
    private String skuName;

    @Schema(description = "商品id" )
    private Long prodId;

    @Schema(description = "销售属性组合字符串,格式是p1:v1;p2:v2" )
    private String properties;

    @Schema(description = "产品图片" )
    private String pic;

    @Schema(description = "产品价格" )
    private Double price;

    @Schema(description = "物品数量" )
    private Integer prodCount;

    @Schema(description = "产品总价格" )
    private Double productTotalAmount;

    @Schema(description = "平台补贴的优惠金额" )
    private Double platformShareReduce;

    @Schema(description = "商品实际金额" )
    private Double actualTotal;

    @Schema(description = "赠送主订单项id" )
    private Double giveawayOrderItemId;

    @Schema(description = "订单项赠品列表" , requiredMode = Schema.RequiredMode.REQUIRED)
    private List<OrderItem> giveawayList;
    @Schema(description = "订单项子组合商品列表" , requiredMode = Schema.RequiredMode.REQUIRED)
    private List<OrderItem> comboList;
}
