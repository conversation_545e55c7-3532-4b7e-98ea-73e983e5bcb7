package com.yami.shop.bean.app.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "设置用户信息")
public class UserInfoParam {

    @Schema(description = "用户昵称" )
    private String nickName;

    @Schema(description = "用户头像" )
    private String avatarUrl;

    @Schema(description = "用户性别" )
    private String sex;

    @Schema(description = "邮箱" )
    private String userMail;

    @Schema(description = "用户生日" )
    private String birthDate;

    @Schema(description = "用户手机号" )
    private String userMobile;

    @Schema(description = "商品个性化推荐 1:开启 0:关闭" )
    private Integer prodRecommendation;


}
