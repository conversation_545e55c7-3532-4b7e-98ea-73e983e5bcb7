package com.yami.shop.common.allinpay.order.resp;

import java.io.Serial;
import java.io.Serializable;

/**
 * 查询余额 返回
 *
 * <AUTHOR>
 */
public class QueryBalanceResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 总额
     */
    private Long allAmount;
    /**
     * 冻结额
     */
    private Long freezenAmount;

    /**
     * 当日充值冻结金额
     */
    private Long depositFreezenAmount;

    public Long getAllAmount() {
        return allAmount;
    }

    public void setAllAmount(Long allAmount) {
        this.allAmount = allAmount;
    }

    public Long getFreezenAmount() {
        return freezenAmount;
    }

    public void setFreezenAmount(Long freezenAmount) {
        this.freezenAmount = freezenAmount;
    }

    public Long getDepositFreezenAmount() {
        return depositFreezenAmount;
    }

    public void setDepositFreezenAmount(Long depositFreezenAmount) {
        this.depositFreezenAmount = depositFreezenAmount;
    }
}
