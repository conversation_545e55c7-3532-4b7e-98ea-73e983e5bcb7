package com.yami.shop.distribution.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class DistributionProdDto {

    @Schema(description = "商品名称" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String prodName;

    @Schema(description = "商品ID" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Long prodId;

    @Schema(description = "商品类型(0普通商品 1拼团 2秒杀 3积分)" )
    private Integer prodType;

    @Schema(description = "商品主图" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String pic;

    @Schema(description = "商品价格" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Double price;

    @Schema(description = "奖励数额(元/百分比)" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Double awardNumber;

    @Schema(description = "上级奖励数额(元/百分比)" )
    private Double  parentAwardNumber;

    @Schema(description = "奖励比例(0 按比例 1 按固定数值)" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer awardProportion;

    @Schema(description = "店铺ID" )
    private Long shopId;
}
