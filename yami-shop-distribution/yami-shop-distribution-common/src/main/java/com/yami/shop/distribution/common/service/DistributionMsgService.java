package com.yami.shop.distribution.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.distribution.common.dto.DistributionMsgDto;
import com.yami.shop.distribution.common.model.DistributionMsg;

/**
 *
 * <AUTHOR> on 2019/04/03.
 */
public interface DistributionMsgService extends IService<DistributionMsg> {

    /**
     * 根据id获取dto
     * @param msgId 消息id
     * @return
     */
    DistributionMsgDto getDistributionMsgDtoByMsgId(Long msgId);

    /**
     * 查询分销通知
     * @param page 分页信息
     * @param distributionMsg 查询参数
     * @return 分销通知列表
     */
    IPage<DistributionMsg> getDistributionMsgsAndSysUserPage(Page page, DistributionMsg distributionMsg);

    /**
     *
     * 根据分销员id 分页获取相应的公告
     * @param page 分页信息
     * @param isTop 是否置顶
     * @return 分页获取相应的公告
     */
    IPage<DistributionMsgDto> getDistributionMsgDtoShopId(Page page,Integer isTop);
}
