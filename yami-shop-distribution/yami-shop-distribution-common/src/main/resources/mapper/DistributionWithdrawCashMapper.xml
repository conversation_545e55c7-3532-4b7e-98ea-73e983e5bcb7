<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.distribution.common.dao.DistributionWithdrawCashMapper">
    <resultMap id="BaseResultMap" type="com.yami.shop.distribution.common.model.DistributionWithdrawCash">
        <id column="withdraw_cash_id" jdbcType="BIGINT" property="withdrawCashId"/>
        <result column="wallet_id" jdbcType="BIGINT" property="walletId"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="money_flow" jdbcType="TINYINT" property="moneyFlow"/>
        <result column="merchant_order_id" jdbcType="VARCHAR" property="merchantOrderId"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
    </resultMap>


    <!--提现记录-->
    <resultMap id="DistributionWithdrawCash_User" type="com.yami.shop.distribution.common.model.DistributionWithdrawCash">
        <id column="withdraw_cash_id" jdbcType="BIGINT" property="withdrawCashId"/>
        <result column="wallet_id" jdbcType="BIGINT" property="walletId"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="money_flow" jdbcType="TINYINT" property="moneyFlow"/>
        <result column="merchant_order_id" jdbcType="VARCHAR" property="merchantOrderId"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="biz_user_id" jdbcType="VARCHAR" property="bizUserId"/>
        <association property="distributionUser" javaType="com.yami.shop.distribution.common.vo.DistributionUserVO">
            <id column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId"/>
            <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
            <result column="user_mobile" jdbcType="VARCHAR" property="userMobile"/>
        </association>
    </resultMap>

    <select id="distributionWithdrawCashsPage" resultMap="DistributionWithdrawCash_User">
        select
        dwc.*,
        du.nick_name,
        du.user_mobile
        from
        tz_distribution_withdraw_cash dwc
        left join tz_distribution_user_wallet duw on duw.wallet_id = dwc.wallet_id
        left join tz_distribution_user du on du.distribution_user_id=duw.distribution_user_id
        <where>
            <if test="shopId !=null">
                and du.shop_id=#{shopId}
            </if>
            <if test="distributionWithdrawCash.merchantOrderId !=null">
                and merchant_order_id = #{distributionWithdrawCash.merchantOrderId}
            </if>
            <if test="distributionWithdrawCash.state != null">
                <if test="distributionWithdrawCash.state &lt; 0 ">
                    and dwc.state in (2,-1)
                </if>
                <if test="distributionWithdrawCash.state &gt;= 0 ">
                    and dwc.state = #{distributionWithdrawCash.state}
                </if>
            </if>
            <if test="rangeTimeParam!=null and  rangeTimeParam.startTime !=null">
                and create_time &gt;=#{rangeTimeParam.startTime}
            </if>
            <if test="rangeTimeParam!=null and  rangeTimeParam.endTime !=null">
                and create_time &lt;=#{rangeTimeParam.endTime}
            </if>
            <if test="distributionParam.userMobile != null and distributionParam.userMobile != ''">
                and du.user_mobile like concat( '%', #{distributionParam.userMobile}, '%' )
            </if>
            <if test="distributionParam.nickName != null and distributionParam.nickName != ''">
                AND du.nick_name LIKE CONCAT( '%', #{distributionParam.nickName}, '%' )
            </if>
        </where>
        GROUP BY dwc.withdraw_cash_id
        <if test="distributionWithdrawCash.sortParam != 0 and distributionWithdrawCash.sortType != 0">
            ORDER BY
            <choose>
                <when test="distributionWithdrawCash.sortParam == 1">
                    dwc.amount
                </when>
                <when test="distributionWithdrawCash.sortParam == 2">
                    dwc.fee
                </when>
                <when test="distributionWithdrawCash.sortParam == 3">
                    create_time
                </when>
                <when test="distributionWithdrawCash.sortParam == 4">
                    dwc.update_time
                </when>
                <otherwise>
                    dwc.withdraw_cash_id
                </otherwise>
            </choose>
            <choose>
                <when test="distributionWithdrawCash.sortType == 1">
                    ASC
                </when>
                <when test="distributionWithdrawCash.sortType == 2">
                    DESC
                </when>
            </choose>
        </if>
    </select>


    <!--分销员提现明细-->
    <resultMap id="distributionWithdrawCashDto" type="com.yami.shop.distribution.common.dto.DistributionWithdrawCashDto">
        <id column="withdraw_cash_id" jdbcType="BIGINT" property="withdrawCashId"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="money_flow" jdbcType="TINYINT" property="moneyFlow"/>
        <result column="merchant_order_id" jdbcType="VARCHAR" property="merchantOrderId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
    </resultMap>
    <select id="distributionWithdrawCashDtoPageByUserId" resultMap="distributionWithdrawCashDto">
        SELECT
            withdraw_cash_id,
            amount,
            fee,
            type,
            money_flow,
            merchant_order_id,
            state,
            create_time,
            update_time
        FROM
            tz_distribution_withdraw_cash
        WHERE
                wallet_id IN ( SELECT wallet_id FROM tz_distribution_user_wallet WHERE distribution_user_id = #{distributionUserId} )
        order by create_time DESC
    </select>


    <select id="getCountByRangeTimeAndDistributionUserId" resultType="integer">
        SELECT
            count(*)
        FROM
            tz_distribution_withdraw_cash
        WHERE
                wallet_id IN ( SELECT wallet_id FROM tz_distribution_user_wallet WHERE distribution_user_id = #{distributionuserId} )
          AND
            create_time &gt;= #{rangeTimeParam.startTime}
          AND
            create_time &lt;= #{rangeTimeParam.endTime}
    </select>

    <select id="getUserTotalWithdrawCash" resultType="double">
        SELECT
            COALESCE(SUM(amount), 0)
        FROM
            tz_distribution_withdraw_cash
        WHERE wallet_id = #{walletId}
    </select>
    <update id="updateUserByDistributionUserId">
        update tz_distribution_withdraw_cash
        set `state` = 2
        where wallet_id in(
            select wallet_id from tz_distribution_user_wallet where distribution_user_id = #{distributionUserId}
            )
        and `state` = 0
    </update>
    <update id="updateSuccess">
        update tz_distribution_withdraw_cash
        set `state` = 1, `update_time` = now()
        where `state` = 0 and withdraw_cash_id = #{withdrawCashId}
    </update>
</mapper>
