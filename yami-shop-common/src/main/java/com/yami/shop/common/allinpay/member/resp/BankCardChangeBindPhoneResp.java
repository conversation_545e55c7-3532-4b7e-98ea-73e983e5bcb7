package com.yami.shop.common.allinpay.member.resp;

import com.yami.shop.common.allinpay.constant.AllinpayCardType;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-03-23
 */
public class BankCardChangeBindPhoneResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 商户userId
     */
    private String bizUserId;
    /**
     * 流水号
     */
    private String tranceNum;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 银行代码
     */
    private String bankCode;
    /**
     * 银行卡类型
     * @see AllinpayCardType
     */
    private Integer cardType;

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getTranceNum() {
        return tranceNum;
    }

    public void setTranceNum(String tranceNum) {
        this.tranceNum = tranceNum;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    @Override
    public String toString() {
        return "BankCardChangeBindPhoneResp{" +
                "bizUserId='" + bizUserId + '\'' +
                ", tranceNum='" + tranceNum + '\'' +
                ", bankName='" + bankName + '\'' +
                ", bankCode='" + bankCode + '\'' +
                ", cardType=" + cardType +
                '}';
    }
}
