package com.yami.shop.distribution.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
/**
 * <AUTHOR>
 */
@Data
@Schema(description = "精简版分销员数据")
public class DistributionUserSimpleDto {

    @Schema(description = "分销员昵称" )
    private Long distributionUserId;

    @Schema(description = "分销员昵称" )
    private String nickName;

    @Schema(description = "真实姓名" )
    private String realName;

    @Schema(description = "头像" )
    private String pic;

    @Schema(description = "绑定时间" )
    @JsonFormat(pattern = "yyyy/MM/dd")
    private Date bindTime;
}
