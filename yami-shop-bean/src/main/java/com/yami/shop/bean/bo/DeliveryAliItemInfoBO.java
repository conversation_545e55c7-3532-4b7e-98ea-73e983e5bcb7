package com.yami.shop.bean.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 阿里快递信息项VO
 *
 * <AUTHOR>
 * @date 2020-05-18 15:10:00
 */
@Data
public class DeliveryAliItemInfoBO {

    @Schema(description = "接受站点" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String status;
    @Schema(description = "接受时间" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String time;
}
