<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.discount.common.dao.DiscountProdMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.discount.common.model.DiscountProd">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="discount_prod_id" jdbcType="BIGINT" property="discountProdId" />
    <result column="discount_id" jdbcType="BIGINT" property="discountId" />
    <result column="prod_id" jdbcType="BIGINT" property="prodId" />
    <result column="pic" jdbcType="VARCHAR" property="pic" />
    <result column="prod_name" jdbcType="VARCHAR" property="prodName" />
  </resultMap>
  
  <insert id="insertDiscountProds">
    insert into tz_discount_prod (discount_id,prod_id) values
    <foreach collection="discountProds" item="discountProd" separator=",">
    (#{discountProd.discountId},#{discountProd.prodId})
    </foreach>
  </insert>
  
  <select id="getDiscountProdByDiscountId" resultType="com.yami.shop.discount.common.model.DiscountProd">
    select p.pic,p.prod_name,dp.* from tz_discount_prod dp
    left join tz_prod p on p.prod_id = dp.prod_id
     where dp.discount_id = #{discountId}
  </select>
</mapper>