package com.yami.shop.common.allinpay.member.req;

import java.io.Serial;
import java.io.Serializable;
/**
 * 解绑绑定银行卡
 *
 * <AUTHOR>
 */
public class UnbindBankCard extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    public static final String METHOD_NAME = "unbindBankCard";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public UnbindBankCard(String bizUserId, String cardNo) {
        this.bizUserId = bizUserId;
        this.cardNo = cardNo;
    }

    public UnbindBankCard() {
    }

    private String cardNo;

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

}
