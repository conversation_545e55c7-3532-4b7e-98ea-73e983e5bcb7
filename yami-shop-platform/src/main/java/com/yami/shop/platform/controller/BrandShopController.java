package com.yami.shop.platform.controller;

import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.BrandShopService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2021/8/23 11:18
 */
@RestController
@RequestMapping("/platform/brandShop")
@Tag(name = "签约品牌相关接口")
@AllArgsConstructor
public class BrandShopController {

    private final BrandShopService brandShopService;

    @DeleteMapping
    @Operation(summary = "删除签约品牌" , description = "删除签约品牌")
    @Parameters(value = {
            @Parameter(name = "shopId", description = "店铺id" ),
            @Parameter(name = "brandId", description = "品牌id" )
    })
    @PreAuthorize("@pms.hasPermission('platform:brandShop:delete')")
    public ServerResponseEntity<Void> delete(@RequestParam("shopId") Long shopId, @RequestParam("brandId") Long brandId) {
        brandShopService.deleteByShopIdAndBrandId(shopId, brandId);
        return ServerResponseEntity.success();
    }
}
