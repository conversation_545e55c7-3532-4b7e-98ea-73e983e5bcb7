package com.yami.shop.bean.vo.search;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @since 2024/3/13 10:06
 */
@Data
public class ActivitProductSearchVO {

    @Schema(description = "是否有商品")
    private Boolean hasSpu;

    @Schema(description = "是否有秒杀商品")
    private Boolean hasSeckillSpu;

    @Schema(description = "是否有拼团商品")
    private Boolean hasGroupSpu;

    @Schema(description = "是否有预售商品")
    private Boolean hasPreSaleSpu;

    public ActivitProductSearchVO(Boolean hasSpu, Boolean hasSeckillSpu, Boolean hasGroupSpu, Boolean hasPreSaleSpu) {
        this.hasSpu = hasSpu;
        this.hasSeckillSpu = hasSeckillSpu;
        this.hasGroupSpu = hasGroupSpu;
        this.hasPreSaleSpu = hasPreSaleSpu;
    }

}
