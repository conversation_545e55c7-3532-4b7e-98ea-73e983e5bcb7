package com.yami.shop.groupbuy.common.listener;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yami.shop.bean.event.CategoryWordEvent;
import com.yami.shop.groupbuy.common.enums.GroupActivityStatusEnum;
import com.yami.shop.groupbuy.common.model.GroupActivity;
import com.yami.shop.groupbuy.common.service.GroupActivityService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component("CategoryGroupbuyListener")
@Slf4j
@AllArgsConstructor
public class CategoryGroupbuyListener {
    private final GroupActivityService groupActivityService;

    @EventListener(CategoryWordEvent.class)
    public void categoryListener(CategoryWordEvent event) {
        List<Long> prodIds = event.getProdIdList();
        // 失效拼团活动
        if(CollUtil.isNotEmpty(prodIds)){
            groupActivityService.update(Wrappers.lambdaUpdate(GroupActivity.class)
                    .set(GroupActivity::getStatus, GroupActivityStatusEnum.INVALID.getCode())
                    .in(GroupActivity::getProdId, prodIds)
                    .in(GroupActivity::getStatus, GroupActivityStatusEnum.ENABLE.getCode(), GroupActivityStatusEnum.DISABLE.getCode())
            );
        }
    }
}
