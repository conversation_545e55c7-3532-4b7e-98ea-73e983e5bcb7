package com.yami.shop.common.allinpay.member.req;



import com.yami.shop.common.allinpay.member.constant.MemberType;
import com.yami.shop.common.allinpay.member.constant.VisitSourceType;

import java.io.Serial;
import java.io.Serializable;

/**
 * 创建会员
 *
 * <AUTHOR>
 */
public class CreateMember extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 用户类型
     */
    private Integer memberType = MemberType.INDIVIDUAL.value();

    /**
     * 访问终端类型
     *
     * @see VisitSourceType
     */
    private Integer source;

    public static final String METHOD_NAME = "createMember";

    /**
     * 创建会员，默认个人用户
     *
     * @param bizUserId 本地用户id
     */
    public CreateMember(String bizUserId) {
        this.bizUserId = bizUserId;
        // 默认个人用户
        this.memberType = MemberType.INDIVIDUAL.value();
    }

    public CreateMember() {
    }


    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public Integer getMemberType() {
        return memberType;
    }

    public void setMemberType(Integer memberType) {
        this.memberType = memberType;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }
}
