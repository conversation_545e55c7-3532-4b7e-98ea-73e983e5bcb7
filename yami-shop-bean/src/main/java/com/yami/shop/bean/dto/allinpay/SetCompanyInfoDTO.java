package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class SetCompanyInfoDTO extends BindCompanyAccountDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "商户号")
    private String bizUserId;

    @Schema(description = "企业名称，如有括号，使用中文括号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String companyName;

    @Schema(description = "企业地址 非必填")
    private String companyAddress;

    @Schema(description = "认证类型(1:三证, 2:一证)，采用2")
    private Integer authType;

    @Schema(description = "统一社会信用(一证时必传)", requiredMode = Schema.RequiredMode.REQUIRED)
    private String uniCredit;

    @Schema(description = "联系电话 非必填")
    private String telephone;

    @Schema(description = "法人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String legalName;

    /**
     * 当前默认 1身份证
     */
    @Schema(description = "法人证件类型(1:身份证, 2:护照, 3:军官证, 4:回乡证, 5:台胞证, 6:警官证, 7:士兵证,8:户口簿,9.港澳居民来往内地通行证,10.临时身份证,11.外国人居留证,12.港澳台居民居住证 99:其他)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer identityType;

    @Schema(description = "法人证件号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String legalIds;

    @Schema(description = "法人手机号码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String legalPhone;

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public Integer getAuthType() {
        return authType;
    }

    public void setAuthType(Integer authType) {
        this.authType = authType;
    }

    public String getUniCredit() {
        return uniCredit;
    }

    public void setUniCredit(String uniCredit) {
        this.uniCredit = uniCredit;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    public Integer getIdentityType() {
        return identityType;
    }

    public void setIdentityType(Integer identityType) {
        this.identityType = identityType;
    }

    public String getLegalIds() {
        return legalIds;
    }

    public void setLegalIds(String legalIds) {
        this.legalIds = legalIds;
    }

    public String getLegalPhone() {
        return legalPhone;
    }

    public void setLegalPhone(String legalPhone) {
        this.legalPhone = legalPhone;
    }

    @Override
    public String toString() {
        return "SetCompanyInfoDTO{" +
                "bizUserId='" + bizUserId + '\'' +
                ", companyName='" + companyName + '\'' +
                ", companyAddress='" + companyAddress + '\'' +
                ", authType=" + authType +
                ", uniCredit='" + uniCredit + '\'' +
                ", telephone='" + telephone + '\'' +
                ", legalName='" + legalName + '\'' +
                ", identityType=" + identityType +
                ", legalIds='" + legalIds + '\'' +
                ", legalPhone='" + legalPhone + '\'' +
                '}';
    }
}
