package com.yami.shop.security.common.config;

import lombok.AllArgsConstructor;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger文档，只有在测试环境才会使用
 * <AUTHOR>
 */
@Configuration("securitySwaggerConfiguration")
@AllArgsConstructor
public class SwaggerConfiguration {


    @Bean
    public GroupedOpenApi securityRestApi() {
        return GroupedOpenApi.builder()
                .group("登录接口")
                .packagesToScan("com.yami.shop.security")
                .pathsToMatch("/**")
                .build();
    }

}
