package com.yami.shop.distribution.platform.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.model.DistributionUserBan;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.service.DistributionUserBanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> on 2019/04/01.
 */
@RestController
@RequestMapping("/distribution/distributionUserBan")
@Tag(name = "平台端分销员封禁接口")
@AllArgsConstructor
public class DistributionUserBanController {

    private final DistributionUserBanService distributionUserBanService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserBan:page')")
    @Operation(summary = "分页获取分销员封禁信息列表")
    public ServerResponseEntity<IPage<DistributionUserBan>> page(DistributionUserBan distributionUserBan, PageParam<DistributionUserBan> page) {
        IPage<DistributionUserBan> list = distributionUserBanService.page(page, new LambdaQueryWrapper<DistributionUserBan>());
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/page/anduser")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserBan:page')")
    @Operation(summary = "分页获取分销员封禁列表")
    @Parameter(name = "userMobile", description = "分销员手机号码" , required = true)
    public ServerResponseEntity<IPage<DistributionUserBan>> page(DistributionUserBan distributionUserBan, DistributionParam distributionParam,
                                                           PageParam<DistributionUserBan> page) {
        IPage<DistributionUserBan> list = distributionUserBanService.distributionUserBanPage(page, Constant.PLATFORM_SHOP_ID, distributionParam, distributionUserBan);
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/info/{id}")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserBan:info')")
    @Operation(summary = "根据分销员封禁id获取分销员封禁信息")
    @Parameter(name = "id", description = "分销员封禁id" , required = true)
    public ServerResponseEntity<DistributionUserBan> info(@PathVariable("id") Long id) {
        DistributionUserBan distributionUserBan = distributionUserBanService.getById(id);
        return ServerResponseEntity.success(distributionUserBan);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserBan:save')")
    @Operation(summary = "保存分销员封禁信息")
    public ServerResponseEntity<Void> save(@RequestBody @Valid DistributionUserBan distributionUserBan) {
        distributionUserBanService.save(distributionUserBan);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserBan:update')")
    @Operation(summary = "修改分销员封禁信息")
    public ServerResponseEntity<Void> update(@RequestBody @Valid DistributionUserBan distributionUserBan) {
        distributionUserBanService.updateById(distributionUserBan);
        return ServerResponseEntity.success();
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserBan:delete')")
    @Operation(summary = "根据id删除分销员封禁信息")
    @Parameter(name = "id", description = "分销员封禁id" , required = true)
    public ServerResponseEntity<Void> delete(@PathVariable Long id) {
        distributionUserBanService.removeById(id);
        return ServerResponseEntity.success();
    }
}
