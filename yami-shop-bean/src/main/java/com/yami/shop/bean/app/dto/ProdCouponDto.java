package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "商品优惠券对象")
@Data
public class ProdCouponDto {

    @Schema(description = "商品id" )
    private Long prodId;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "原价" )
    private Double oriPrice;

    @Schema(description = "现价" )
    private Double price;

    @Schema(description = "商品主图" )
    private String pic;

    @Schema(description = "优惠券ID" )
    private Long couponId;

    @Schema(description = "优惠券名称" )
    private String couponName;

    @Schema(description = "副标题" )
    private String subTitle;

    @Schema(description = " 优惠类型 1:代金券 2:折扣券 3:兑换券" )
    private Integer couponType;

    @Schema(description = "使用条件金额" )
    private Double cashCondition;

    @Schema(description = "减免金额" )
    private Double reduceAmount;

    @Schema(description = "折扣额度" )
    private Double couponDiscount;

    @Schema(description = "库存" )
    private Integer stocks;


}
