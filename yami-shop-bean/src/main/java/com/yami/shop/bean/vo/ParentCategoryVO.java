package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 父级分类信息VO
 *
 * <AUTHOR>
 * @date 2020-10-28 15:27:24
 */
@Data
public class ParentCategoryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "分类id" )
    private Long categoryId;

    @Schema(description = "父ID" )
    private Long parentId;

    @Schema(description = "分类名称" )
    private String categoryName;
}
