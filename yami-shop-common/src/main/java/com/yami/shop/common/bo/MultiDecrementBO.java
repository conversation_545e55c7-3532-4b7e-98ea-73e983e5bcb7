package com.yami.shop.common.bo;


import com.yami.shop.common.enums.MultiStockEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MultiDecrementBO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 进行多redis扣减库存的操作fang方，比如抢秒杀商品，也可能是双十一抢优惠券
     */
    private MultiStockEnum multiStockEnum;
    /**
     * 用户id，使用用户id的hashCode 作为使用分片的索引
     */
    private String userId;
    /**
     * 订单id，为啥要有订单id呢，这个是为了后期能够保证redis库存和mysql订单数量的一致性，要通过这个orderId通过redis的lua加一个锁定的记录在redis当中
     */
    private String orderNumber;
    /**
     * 活动id，对于秒杀来说就是秒杀的活动id，对于优惠券来说就是优惠券id，需要唯一
     */
    private Long activityId;
    /**
     * skuId 对于秒杀来说就是秒杀的活动skuId，对于优惠券来说就是优惠券id，需要唯一
     */
    private Long skuId;
    /**
     * 扣减的库存数量
     */
    private Integer prodCount;
    /**
     * 最大限购数量
     */
    private Integer maxNum;

    /**
     * 前缀
     */
    private String cachePrefix;

    /**
     * 库存点集合 拼接为库存点id_库存
     */
    private List<String> stockPointList;

    /**
     * 库存点id
     */
    private Long stockPointId;

    /**
     * 商品名称
     */
    private String prodName;

    /**
     * 订单数据
     */
    private String orderInfo;

    public MultiDecrementBO() {
    }

    public MultiDecrementBO(Long activityId, Long skuId, Long stockPointId) {
        this.activityId = activityId;
        this.skuId = skuId;
        this.stockPointId = stockPointId;
    }

    public MultiDecrementBO(MultiStockEnum multiStockEnum, String userId, String orderNumber, Long activityId, Long skuId, Integer prodCount, Integer maxNum, List<String> stockPointList) {
        this.multiStockEnum = multiStockEnum;
        this.userId = userId;
        this.orderNumber = orderNumber;
        this.activityId = activityId;
        this.skuId = skuId;
        this.prodCount = prodCount;
        this.maxNum = maxNum;
        this.cachePrefix = multiStockEnum.value();
        this.stockPointList = stockPointList;
    }

    public MultiDecrementBO(MultiStockEnum multiStockEnum, String userId, String orderNumber, Long activityId, Long skuId, Integer prodCount, Integer maxNum) {
        this.multiStockEnum = multiStockEnum;
        this.userId = userId;
        this.orderNumber = orderNumber;
        this.activityId = activityId;
        this.skuId = skuId;
        this.prodCount = prodCount;
        this.maxNum = maxNum;
        this.cachePrefix = multiStockEnum.value();
    }
}
