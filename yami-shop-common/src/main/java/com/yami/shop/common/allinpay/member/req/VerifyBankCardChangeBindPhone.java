package com.yami.shop.common.allinpay.member.req;

import java.io.Serial;
import java.io.Serializable;
/**
 * <AUTHOR>
 * @date 2023-03-23
 */
public class VerifyBankCardChangeBindPhone extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "verifyBankCardChangeBindPhone";

    /**
     * 流水号
     */
    private String tranceNum;
    /**
     * 银行预留手机号
     */
    private String phone;
    /**
     * 短信验证码
     */
    private String verificationCode;
    /**
     * 有效期（信用卡必填）
     */
    private String validate;
    /**
     * CVV2（信用卡必填）
     */
    private String cvv2;

    public String getTranceNum() {
        return tranceNum;
    }

    public void setTranceNum(String tranceNum) {
        this.tranceNum = tranceNum;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    public String getValidate() {
        return validate;
    }

    public void setValidate(String validate) {
        this.validate = validate;
    }

    public String getCvv2() {
        return cvv2;
    }

    public void setCvv2(String cvv2) {
        this.cvv2 = cvv2;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    @Override
    public String toString() {
        return "VerifyBankCardChangeBindPhone{" +
                "tranceNum='" + tranceNum + '\'' +
                ", phone='" + phone + '\'' +
                ", verificationCode='" + verificationCode + '\'' +
                ", validate='" + validate + '\'' +
                ", cvv2='" + cvv2 + '\'' +
                ", bizUserId='" + bizUserId + '\'' +
                '}';
    }
}
