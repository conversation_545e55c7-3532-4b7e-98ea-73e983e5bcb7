package com.yami.shop.common.allinpay.order.resp;

/**
 * <AUTHOR>
 * @date 2023-11-01
 */
public class OrderStatusResp {
    /**
     * 支付状态 1.未支付	3.交易失败	4.交易成功  5.交易成功-发生退款	 6.关闭
     */
    private Long orderStatus;

    /**
     * 支付金额
     */
    private String amount;

    /**
     * 商户订单号（支付订单）
     */
    private String bizOrderNo;

    /**
     * 云商通订单编号
     */
    private String orderNo;

    public Long getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Long orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    @Override
    public String toString() {
        return "OrderStatusResp{" +
                "orderStatus=" + orderStatus +
                ", amount='" + amount + '\'' +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                ", orderNo='" + orderNo + '\'' +
                '}';
    }
}
