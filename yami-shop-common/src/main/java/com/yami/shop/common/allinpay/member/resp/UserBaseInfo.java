package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;
/**
 * 用户基本信息
 *
 * <AUTHOR>
 */
public class UserBaseInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 姓名
     */
    private String name;

    /**
     * 国家 非必填
     */
    private String country;

    /**
     * 省份 非必填
     */
    private String province;

    /**
     * 地区 非必填
     */
    private String area;

    /**
     * 地址 非必填
     */
    private String address;

    public UserBaseInfo() {
    }

    public UserBaseInfo(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

}
