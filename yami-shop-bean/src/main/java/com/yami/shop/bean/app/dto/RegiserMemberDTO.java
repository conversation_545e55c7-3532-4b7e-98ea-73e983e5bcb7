package com.yami.shop.bean.app.dto;



import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "会员注册")
public class RegiserMemberDTO {

   @Schema(description = "会员名")
    private String memberName;

   @Schema(description = "电话")
    private String phone;

   @Schema(description = "短信验证码")
    private String smsCode;

   @Schema(description = "店铺Id")
    private Long shopId;
}
