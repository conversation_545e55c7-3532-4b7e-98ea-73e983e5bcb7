package com.yami.shop.bean.dto.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SubscriberRemoveDTO {

    @Schema(description = "频道的唯一ID")
    private String channel_id;

    @Schema(description = "频道的类型 1.个人频道 2.群聊频道")
    private Integer channel_type;

    @Schema(description = "订阅者集合 [uid1,uid2,...]")
    private List<String> subscribers;

    public SubscriberRemoveDTO() {
    }

    public SubscriberRemoveDTO(String channel_id, List<String> subscribers) {
        this.channel_id = channel_id;
        this.channel_type = 2;
        this.subscribers = subscribers;
    }

    public String getChannel_id() {
        return channel_id;
    }

    public void setChannel_id(String channel_id) {
        this.channel_id = channel_id;
    }

    public Integer getChannel_type() {
        return channel_type;
    }

    public void setChannel_type(Integer channel_type) {
        this.channel_type = channel_type;
    }

    public List<String> getSubscribers() {
        return subscribers;
    }

    public void setSubscribers(List<String> subscribers) {
        this.subscribers = subscribers;
    }

    @Override
    public String toString() {
        return "SubscriberRemoveDTO{" +
                "channel_id='" + channel_id + '\'' +
                ", channel_type=" + channel_type +
                ", subscribers=" + subscribers +
                '}';
    }
}
