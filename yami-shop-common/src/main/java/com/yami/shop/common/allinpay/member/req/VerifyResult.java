package com.yami.shop.common.allinpay.member.req;

import cn.hutool.core.date.DateUtil;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 企业信息审核结果通知
 *
 * <AUTHOR>
 */
public class VerifyResult extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "verifyResult";


    public static final String FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    /**
     * 2：审核成功。3：审核失败。
     */
    private Integer result;

    /**
     * 审核时间
     */
    private String checkTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 失败原因
     */
    private String failReason;

    public VerifyResult(String bizUserId, Integer result, Date checkTime, String remark,
                        String failReason) {
        this.result = result;
        this.remark = remark;
        this.checkTime = DateUtil.format(checkTime, FORMAT);
        this.failReason = failReason;
        this.bizUserId = bizUserId;
    }

    public VerifyResult() {
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(Date checkTime) {
        this.checkTime = DateUtil.format(checkTime, FORMAT);
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }


}
