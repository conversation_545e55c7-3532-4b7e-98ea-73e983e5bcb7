package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;

/**
 * 影印件采集（文件上传模式）
 * <AUTHOR>
 */
public class IdCardCollectByFileUploadResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 上传结果 1.成功 2.失败
     */
    private Integer result;

    private String bizUserId;

    /**
     * 失败原因，上传失败返回
     */
    private String failReason;

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    @Override
    public String toString() {
        return "IdCardCollectByFileUploadResp{" +
                "result=" + result +
                ", bizUserId='" + bizUserId + '\'' +
                ", failReason='" + failReason + '\'' +
                '}';
    }
}
