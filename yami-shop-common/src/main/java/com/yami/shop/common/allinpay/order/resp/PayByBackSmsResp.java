package com.yami.shop.common.allinpay.order.resp;

import java.io.Serial;
import java.io.Serializable;

/**
 * 确认支付 后台+短信验证 响应参数
 *
 * <AUTHOR>
 */
public class PayByBackSmsResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 成功：success
     * 进行中：pending
     * 未支付：unpay
     * 失败：fail
     * 支付状态发生变化时还将发送异步通知：提现在成功和失败都会通知商户；其他订单只在成功时通知商户。
     */
    private String payStatus;

    /**
     * 支付失败信息
     * payStatus为fail和unpay时有效；
     * 其中payStatus返回unpay时，仅针对收银宝快捷短信验证码输入错误场景，允许商户继续调用确认支付
     */
    private String payFailMessage;

    /**
     * 商户系统用户标识，商户系统中唯一编号。
     */
    private String bizUserId;

    /**
     * 商户订单号（支付订单）
     */
    private String bizOrderNo;

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public String getPayFailMessage() {
        return payFailMessage;
    }

    public void setPayFailMessage(String payFailMessage) {
        this.payFailMessage = payFailMessage;
    }

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    @Override
    public String toString() {
        return "PayByBackSmsResp{" +
                "payStatus='" + payStatus + '\'' +
                ", payFailMessage='" + payFailMessage + '\'' +
                ", bizUserId='" + bizUserId + '\'' +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                '}';
    }
}
