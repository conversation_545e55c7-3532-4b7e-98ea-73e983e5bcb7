package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/6/9 17:00
 */
public class AccountSearchDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "请求类型 1.订单&退款 2.余额充值 3.会员购买")
    private Integer financeType;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getFinanceType() {
        return financeType;
    }

    public void setFinanceType(Integer financeType) {
        this.financeType = financeType;
    }

    @Override
    public String toString() {
        return "AccountSearchDTO{" +
                "startTime=" + startTime +
                ", endTime=" + endTime +
                ", shopName='" + shopName + '\'' +
                ", shopId=" + shopId +
                ", financeType=" + financeType +
                '}';
    }
}
