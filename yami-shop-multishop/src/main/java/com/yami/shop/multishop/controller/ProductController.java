package com.yami.shop.multishop.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yami.shop.bean.app.dto.ProductDto;
import com.yami.shop.bean.dto.SkuAdminDTO;
import com.yami.shop.bean.dto.StockPointSkuDTO;
import com.yami.shop.bean.enums.*;
import com.yami.shop.bean.event.*;
import com.yami.shop.bean.model.*;
import com.yami.shop.bean.param.OfflineHandleEventAuditParam;
import com.yami.shop.bean.param.ProductParam;
import com.yami.shop.bean.vo.ComboProdSkuVO;
import com.yami.shop.bean.vo.ComboProdVO;
import com.yami.shop.bean.vo.SkuComboVO;
import com.yami.shop.bean.vo.StockPointSkuVO;
import com.yami.shop.combo.multishop.model.ComboProd;
import com.yami.shop.combo.multishop.service.ComboProdService;
import com.yami.shop.combo.multishop.service.GiveawayProdService;
import com.yami.shop.combo.multishop.service.GiveawayService;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.constants.SegmentIdKey;
import com.yami.shop.common.enums.StatusEnum;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.delivery.common.model.Transport;
import com.yami.shop.delivery.common.service.TransportService;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.security.multishop.model.YamiShopUser;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.*;
import com.yami.shop.sys.common.service.ShopEmployeeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品列表、商品发布controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/prod/prod")
@Tag(name = "商品接口")
@AllArgsConstructor
@Slf4j
public class ProductController {

    private final ProductService productService;
    private final SkuService skuService;
    private final SkuComboService skuComboService;
    private final BasketService basketService;
    private final BrandService brandService;
    private final ApplicationContext applicationContext;
    private final OfflineHandleEventService offlineHandleEventService;
    private final ShopDetailService shopDetailService;
    private final CategoryService categoryService;
    private final ApplicationEventPublisher eventPublisher;
    private final ProductExcelService productExcelService;
    private final CategoryShopService categoryShopService;
    private final BrandShopService brandShopService;
    private final CategoryBrandService categoryBrandService;
    private final SegmentService segmentService;
    private final StockPointSkuService stockPointSkuService;
    private final SupplierProdService supplierProdService;
    private final ShopEmployeeService shopEmployeeService;
    private final ProdParameterService prodParameterService;
    private final ComboProdService comboProdService;
    private final GiveawayProdService giveawayProdService;
    private final TransportService transportService;
    private final ProdExtensionService prodExtensionService;
    private final GiveawayService giveawayService;
    private final VoucherService voucherService;

    @GetMapping("/listProdByIdsAndType")
    @Operation(summary = "批量获取商品信息" , description = "批量获取商品信息")
    public ServerResponseEntity<List<ProductDto>> listProdByIdsAndType(ProductParam product) {
        product.setLang(I18nMessage.getDbLang());
        product.setShopId(SecurityUtils.getShopUser().getShopId());
        List<Product> products = productService.listProdByIdsAndType(product);
        List<ProductDto> productDtos = BeanUtil.mapAsList(products, ProductDto.class);
        processActivityProdPrice(product, products);
        return ServerResponseEntity.success(productDtos);
    }

    @GetMapping("/pageOnLineProd")
    @Operation(summary = "分页获取上架的商品信息" , description = "分页获取上架的商品信息")
    @PreAuthorize("@pms.hasPermission('prod:prod:page')")
    public ServerResponseEntity<IPage<Product>> pageOnLineProd(ProductParam product, PageParam<Product> page) {
        product.setShopId(SecurityUtils.getShopUser().getShopId());
        IPage<Product> products = productService.pageProducts(page, product);
        return ServerResponseEntity.success(products);
    }

    @GetMapping("/info/{prodId}")
    @Operation(summary = "根据商品id获取商品信息" , description = "根据商品id获取商品信息")
    @PreAuthorize("@pms.hasPermission('prod:prod:info')")
    public ServerResponseEntity<Product> info(@PathVariable("prodId") Long prodId) {
        Product prod = productService.getProductById(prodId);
        if (Objects.isNull(prod) || Objects.equals(prod.getStatus(), ProdStatusEnums.DELETE.getValue())) {
            //商品状态不正确，请退出重试
            throw new YamiShopBindException("yami.prod.status.error.Please.exit.and.try.again");
        }
        Brand brand = brandService.getByBrandId(prod.getBrandId());
        if (Objects.nonNull(brand)) {
            prod.setBrand(brand);
        }
        if (!Objects.equals(prod.getShopId(), SecurityUtils.getShopUser().getShopId())) {
            throw new YamiShopBindException("yami.prod.shop.not.exist");
        }
        List<ProdParameter> prodParameterList = prodParameterService.listParameterByProdId(prodId);
        prod.setProdParameterList(prodParameterList);
        prodExtensionService.getByProdId(prod.getProdId());
        List<Sku> skuList = skuService.listSkuAndSkuStockForAdmin(prod);
        int totalStock = 0;
        Set<Long> skuIds = getIsParticipate(prodId);
        for (Sku sku : skuList) {
            sku.setIsParticipate(skuIds.contains(sku.getSkuId()) ? 1 : 0);
            totalStock += Objects.isNull(sku.getStocks()) ? 0: sku.getStocks();
        }
        // 如果是电子卡券并且关联了卡券，则获取卡券名称
        if (Objects.equals(prod.getMold(), ProdMoldEnum.VOUCHER.value()) && Objects.equals(prod.getIsBindVoucher(), 1)) {
            List<Long> voucherIds = skuList.stream().map(Sku::getVoucherId).toList();
            List<Voucher> voucherList = voucherService.listByVoucherIds(voucherIds);
            Map<Long, Voucher> skuStockPointMap = voucherList.stream().collect(Collectors.toMap(Voucher::getVoucherId, Voucher -> Voucher));

            for (Sku sku : skuList) {
                sku.setVoucherName(skuStockPointMap.containsKey(sku.getVoucherId())? skuStockPointMap.get(sku.getVoucherId()).getName() : null);
            }
        }
        prod.setTotalStocks(totalStock);
        prod.setSkuList(skuList);
        // 平台分类、店铺分类信息
        prod.setCategoryVO(categoryService.getInfo(prod.getCategoryId()));
        if (Objects.nonNull(prod.getShopCategoryId()) && !Objects.equals(prod.getShopCategoryId(), Constant.PLATFORM_SHOP_ID)) {
            prod.setShopCategoryVO(categoryService.getInfo(prod.getShopCategoryId()));
        }
        return ServerResponseEntity.success(prod);
    }

    @NotNull
    private Set<Long> getIsParticipate(Long prodId) {
        List<GiveawayProd> giveawayProds = giveawayProdService.list(new LambdaQueryWrapper<GiveawayProd>()
                .eq(GiveawayProd::getProdId, prodId)
                .eq(GiveawayProd::getStatus, 1));
        Set<Long> skuIds = giveawayProds.stream().map(GiveawayProd::getSkuId).collect(Collectors.toSet());
        List<ComboProdVO> comboProdList = comboProdService.listComboProdByProdId(prodId);
        for (ComboProdVO comboProd : comboProdList) {
            skuIds.addAll(comboProd.getSkuList().stream().map(ComboProdSkuVO::getSkuId).collect(Collectors.toSet()));
        }
        return skuIds;
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('prod:prod:save')")
    @Operation(summary = "新增商品" , description = "新增商品")
    public ServerResponseEntity<Long> save(@Valid @RequestBody ProductParam productParam) {
        checkParam(productParam);
        YamiShopUser shopUser = SecurityUtils.getShopUser();
        Long shopId = shopUser.getShopId();
        productParam.setEmployeeId(shopUser.getEmployeeId());
        productParam.setEmployeeMobile(shopEmployeeService.getShopEmployeeById(productParam.getEmployeeId()).getMobile());
        ShopDetail shopDetail = shopDetailService.getShopDetailByShopId(shopId);
        if (Objects.equals(shopDetail.getShopStatus(), ShopStatus.OFFLINE.value())) {
            // 店铺处于违规下线中，不能修改商品，请联系管理员后重试
            throw new YamiShopBindException("yami.product.shop.offline");
        }
        if (Objects.equals(shopDetail.getShopStatus(), ShopStatus.OFFLINE_AUDIT.value())) {
            // 店铺审核中，不能修改商品，请联系管理员后重试
            throw new YamiShopBindException("yami.product.shop.AUDIT");
        }

        this.checkCategoryAndBrand(shopId, productParam.getCategoryId(), productParam.getShopCategoryId(), productParam.getBrandId());
        productParam.setShopId(shopId);
        if (Objects.nonNull(productParam.getProdType()) && Objects.equals(productParam.getProdType(), ProdType.PROD_TYPE_ACTIVE.value())) {
            productParam.setProdType(ProdType.PROD_TYPE_ACTIVE.value());
        } else {
            productParam.setProdType(ProdType.PROD_TYPE_NORMAL.value());
        }
        productService.saveProduct(productParam);
        eventPublisher.publishEvent(new EsProductUpdateEvent(productParam.getProdId(), null, EsOperationType.SAVE, shopId));
        // 移除缓存
        prodParameterService.removeCacheByProdId(productParam.getProdId());
        return ServerResponseEntity.success(productParam.getProdId());
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('prod:prod:update')")
    @Operation(summary = "修改商品" , description = "修改商品")
    public ServerResponseEntity<Long> update(@Valid @RequestBody ProductParam productParam) {
        // 校验请求数据
        this.checkParam(productParam);
        YamiShopUser shopUser = SecurityUtils.getShopUser();
        Product dbProduct = productService.getProductById(productParam.getProdId());
        // 校验商品是否可以更新
        this.checkProdUpdateInfo(productParam, dbProduct, shopUser);
        List<String> userIds = basketService.listUserIdByProdId(productParam.getProdId());
        List<Sku> dbSkus = skuService.listSkuAndSkuStockForAdmin(dbProduct);
        dbProduct.setSkuList(dbSkus);
        productParam.setProdType(productParam.getProdType());
        productParam.setEmployeeId(AuthUserContext.getEmployeeId());

        // 更新商品及相关功能的数据
        productService.updateProduct(productParam, dbProduct);
        // 清除缓存
        this.removeProdCache(productParam, userIds, dbSkus);
        // 如果是秒杀商品， 且物流类型发生改变，则更新下秒杀活动原始库存
        if (Objects.equals(dbProduct.getProdType(), ProdType.PROD_TYPE_SECKILL.value()) ||
                StrUtil.equals(dbProduct.getDeliveryMode(), JSON.toJSONString(productParam.getDeliveryModeVo()))) {
            // 查询更新后的商品数据
            dbProduct = productService.getById(productParam.getProdId());
            eventPublisher.publishEvent(new SeckillStationStockEvent(Collections.singletonList(dbProduct)));
        }
        // 同步es中的商品数据
        eventPublisher.publishEvent(new EsProductUpdateEvent(productParam.getProdId(), null, EsOperationType.UPDATE, shopUser.getShopId()));
        return ServerResponseEntity.success(Objects.equals(productParam.getStatus(), ProdStatusEnums.NORMAL.getValue()) ? productParam.getProdId() : null);
    }

    private void checkProdUpdateInfo(ProductParam productParam, Product dbProduct, YamiShopUser shopUser) {
        if (!Objects.equals(productParam.getPreSellStatus(), dbProduct.getPreSellStatus()) && Objects.equals(productParam.getPreSellStatus(), StatusEnum.ENABLE.value())) {
            // 预售商品不能
            long count = comboProdService.count(new LambdaQueryWrapper<ComboProd>()
                    .eq(ComboProd::getProdId, productParam.getProdId())
                    .gt(ComboProd::getStatus, StatusEnum.DISABLE.value())
            );
            if (count > 0) {
                throw new YamiShopBindException("yami.prod.not.pre");
            }
        }
        productParam.setEmployeeId(shopUser.getEmployeeId());
        productParam.setEmployeeMobile(shopEmployeeService.getShopEmployeeById(productParam.getEmployeeId()).getMobile());
        if (Objects.equals(dbProduct.getStatus(), ProdStatusEnums.DELETE.getValue())) {
            // 产品已被删除
            throw new YamiShopBindException("yami.product.service.delete");
        }
        if (Objects.equals(dbProduct.getStatus(), ProdStatusEnums.PLATFORM_OFFLINE.getValue())) {
            productParam.setStatus( ProdStatusEnums.PLATFORM_OFFLINE.getValue());
        }
        if (!Objects.equals(dbProduct.getShopId(), SecurityUtils.getShopUser().getShopId())) {
            // 查找不到该商品信息
            throw new YamiShopBindException("yami.product.not.exist");
        }

        if (!Objects.equals(dbProduct.getProdType(), productParam.getProdType())) {
            // 商品类型改变，请刷新页面后重试
            throw new YamiShopBindException("yami.prod.type.check");
        }

        if (!Objects.equals(dbProduct.getMold(), productParam.getMold())) {
            // 商品创建后不能修改商品类型
            throw new YamiShopBindException("yami.order.prod.type.check");
        }
        Long shopId = shopUser.getShopId();
        ShopDetail shopDetail = shopDetailService.getShopDetailByShopId(shopId);
        if (Objects.equals(shopDetail.getShopStatus(), ShopStatus.OFFLINE.value())) {
            // 店铺处于违规下线中，不能修改商品，请联系管理员后重试
            throw new YamiShopBindException("yami.product.shop.offline");
        }
        this.checkCategoryAndBrand(shopId, productParam.getCategoryId(), productParam.getShopCategoryId(), productParam.getBrandId());
    }

    @DeleteMapping("/{prodId}")
    @PreAuthorize("@pms.hasPermission('prod:prod:delete')")
    @Operation(summary = "根据商品id删除商品" , description = "根据商品id删除商品")
    public ServerResponseEntity<Void> delete(@PathVariable("prodId") Long prodId) {
        Product dbProduct = productService.getProductByProdId(prodId);
        if (!Objects.equals(dbProduct.getShopId(), SecurityUtils.getShopUser().getShopId())) {
            // 查找不到该商品信息
            throw new YamiShopBindException("yami.product.not.exist");
        }
        // 已经删除的商品
        if (Objects.equals(dbProduct.getStatus(), StatusEnum.DELETE.value())) {
            eventPublisher.publishEvent(new EsProductUpdateEvent(prodId, null, EsOperationType.DELETE));
            return ServerResponseEntity.success();
        }
        // 检查是否属于套餐或赠品
        this.checkBeforeDeleteProduct(prodId);
        List<Sku> dbSkus = skuService.listSkuByProdId(dbProduct.getProdId());
        List<Long> supplierIds = supplierProdService.listSupplierIdByProdId(prodId);
        List<Long> deleteSkuIds = dbSkus.stream().map(Sku::getSkuId).collect(Collectors.toList());
        // 删除商品
        productService.removeProductByProdId(prodId, deleteSkuIds);
        // 删除sku关联信息
        applicationContext.publishEvent(new SkuDeleteEvent(deleteSkuIds));
        if (CollectionUtils.isNotEmpty(supplierIds)) {
            for (Long supplierId : supplierIds) {
                supplierProdService.removeCacheBySupplierId(supplierId);
            }
        }
        productService.removeProdCacheByProdId(prodId);
        prodParameterService.removeCacheByProdId(prodId);
        if (Objects.nonNull(dbSkus)) {
            for (Sku sku : dbSkus) {
                skuService.removeSkuCacheBySkuId(sku.getSkuId(), sku.getProdId());
            }
        }

        List<String> userIds = basketService.listUserIdByProdId(prodId);
        // 商品状态改变时的发送事件，让活动下线
        applicationContext.publishEvent(new ProdChangeEvent(dbProduct));
        applicationContext.publishEvent(new ProdChangeStatusEvent(dbProduct, ProdStatusEnums.DELETE.getValue()));
        applicationContext.publishEvent(new ComboEvent(dbProduct, ProdStatusEnums.DELETE.getValue()));
        if (Objects.nonNull(dbSkus)) {
            for (Sku sku : dbSkus) {
                skuService.removeSkuCacheBySkuId(sku.getSkuId(), sku.getProdId());
            }
        }
        //清除购物车缓存
        basketService.removeCacheByUserIds(userIds, null);
        eventPublisher.publishEvent(new EsProductUpdateEvent(prodId, null, EsOperationType.DELETE, dbProduct.getShopId()));
        return ServerResponseEntity.success();
    }

    @PutMapping("/prodStatus")
    @PreAuthorize("@pms.hasPermission('prod:prod:status')")
    @Operation(summary = "更新商品状态" , description = "更新商品状态")
    public ServerResponseEntity<Void> prodStatus(@RequestBody ProductParam productParam) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        Long prodId = productParam.getProdId();
        Integer prodStatus = productParam.getStatus();

        Product dbProduct = productService.getProductByProdId(prodId);
        checkProdInfo(productParam, shopId, dbProduct);
        productService.updateProdStatus(Collections.singletonList(prodId),prodStatus);
        eventPublisher.publishEvent(new EsProductUpdateEvent(prodId, null, EsOperationType.SAVE));
        return ServerResponseEntity.success();
    }

    private void checkProdInfo(ProductParam productParam, Long shopId, Product dbProduct) {
        if (!Objects.equals(dbProduct.getShopId(), shopId)) {
            // 查找不到该商品信息
            throw new YamiShopBindException("yami.product.not.exist");
        }
        if (!(Objects.equals(dbProduct.getStatus(), ProdStatusEnums.NORMAL.getValue())
                || Objects.equals(dbProduct.getStatus(), ProdStatusEnums.SHOP_OFFLINE.getValue()))) {
            // 商品不在正常状态，修改失败
            throw new YamiShopBindException("yami.product.on.normal");
        }
        ShopDetail shopDetail = shopDetailService.getShopDetailByShopId(shopId);
        if (Objects.equals(shopDetail.getShopStatus(), ShopStatus.OFFLINE.value()) || Objects.equals(shopDetail.getShopStatus(), ShopStatus.OFFLINE_AUDIT.value())) {
            // 店铺处于违规下线中，不能修改商品，请联系管理员后重试
            throw new YamiShopBindException("yami.product.shop.offline");
        }


        if (Objects.equals(productParam.getStatus(), 1)) {
            Category category = categoryService.getById(dbProduct.getCategoryId());
            if (category == null || Objects.equals(category.getStatus(), 0)) {
                // 平台分类处于下线中，商品不能上架，请联系管理员后再进行操作
                throw new YamiShopBindException("yami.product.category.offline");
            }
            boolean write = Objects.equals(dbProduct.getMold(), 1) && !Objects.equals(ProdWriteOffNumEnum.WRITE_OFF_NOT.value(), dbProduct.getWriteOffNum()) &&
                    Objects.equals(dbProduct.getWriteOffTime(), 0) && Objects.nonNull(dbProduct.getWriteOffEnd()) && new Date().after(dbProduct.getWriteOffEnd());
            if(write){
                //商品已过核销有效期
                throw new YamiShopBindException("yami.product.exception.writeOffOutTime");
            }
            Category shopCategory = categoryService.getById(dbProduct.getShopCategoryId());
            if (shopCategory == null || Objects.equals(shopCategory.getStatus(), 0)) {
                // 本店分类处于下线中，商品不能上架
                throw new YamiShopBindException("yami.product.shop.category.offline");
            }
            CategoryShop categoryShop = categoryShopService.getOne(Wrappers.lambdaQuery(CategoryShop.class).eq(CategoryShop::getShopId, shopId).eq(CategoryShop::getCategoryId, dbProduct.getCategoryId()));
            if (Objects.isNull(categoryShop)) {
                // 商品的分类签约失效
                throw new YamiShopBindException("yami.product.signing.category.invalid");
            }
            if(Objects.equals(dbProduct.getMold(),ProdMoldEnum.COMBO.value())){
                checkEnableComboSpu(dbProduct);
            }
            if(Objects.equals(dbProduct.getMold(),ProdMoldEnum.VOUCHER.value())){
                checkEnableVoucherSpu(dbProduct);
            }
        }
    }

    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('prod:prod:batchDelete')")
    @Operation(summary = "根据商品id列表批量删除商品" , description = "根据商品id列表批量删除商品")
    public ServerResponseEntity<Boolean> batchRemoveById(@RequestBody Long[] ids) {
        // TODO 待优化
        StringBuilder stringBuilder = new StringBuilder();
        for (Long prodId : ids) {
            try {
                delete(prodId);
            } catch (Exception e) {
                Product dbProduct = productService.getProductAndLang(prodId);
                stringBuilder.append("【").append(dbProduct.getProdName()).append("】  " + e.getMessage());
            }
        }
        if (!stringBuilder.isEmpty()) {
            throw new YamiShopBindException(String.format(I18nMessage.getMessage("yami.product.exception.deleteError"), stringBuilder));
        }
        eventPublisher.publishEvent(new EsProductUpdateEvent(null, Arrays.asList(ids), EsOperationType.DELETE_BATCH));
        return ServerResponseEntity.success();
    }

    @PutMapping("/batchProdStatus/{status}")
    @PreAuthorize("@pms.hasPermission('prod:prod:batchStatus')")
    @Operation(summary = "批量更新商品状态" , description = "批量更新商品状态")
    public ServerResponseEntity<Void> batchProdStatus(@RequestBody Long[] ids, @PathVariable("status") Integer status) {
        // TODO 待优化
        ProductParam product = new ProductParam();
        product.setStatus(status);
        StringBuilder stringBuilder = new StringBuilder();
        for (Long prodId : ids) {
            product.setProdId(prodId);
            try {
                prodStatus(product);
            } catch (Exception e) {
                Product dbProduct = productService.getProductAndLang(prodId);
                //【商品名】:错误信息xxx (【数码相机】:本店分类处于下线中，商品不能上架)
                stringBuilder.append("【" + dbProduct.getProdName() + "】:" + e.getMessage() + "\n");
            }
        }
        eventPublisher.publishEvent(new EsProductUpdateEvent(null, Arrays.asList(ids), EsOperationType.UPDATE_BATCH));
        if (!stringBuilder.isEmpty()) {
            throw new YamiShopBindException(String.format(I18nMessage.getMessage("yami.product.exception.updateStatusError"), stringBuilder));
        }
        return ServerResponseEntity.success();
    }

    @PostMapping("/auditApply")
    @PreAuthorize("@pms.hasPermission('prod:prod:audit')")
    @Operation(summary = "违规商品提交审核" , description = "违规商品提交审核")
    public ServerResponseEntity<Void> auditApply(@RequestBody OfflineHandleEventAuditParam offlineHandleEventAuditParam) {
        Product product = productService.getProductByProdId(offlineHandleEventAuditParam.getHandleId());
        if (product == null) {
            // 商品信息不存在
            throw new YamiShopBindException("yami.product.no.exist");
        }
        if(Objects.equals(product.getStatus(), ProdStatusEnums.DELETE.getValue())){
            // 商品状态已发生改变，请刷新页面
            throw new YamiShopBindException("yami.product.exception.statusChange");
        }
        productService.auditApply(offlineHandleEventAuditParam.getEventId(), offlineHandleEventAuditParam.getHandleId(), offlineHandleEventAuditParam.getReapplyReason());
        // 移除缓存
        productService.removeProdCacheByProdId(product.getProdId());
        eventPublisher.publishEvent(new EsProductUpdateEvent(product.getProdId(), null, EsOperationType.UPDATE));
        return ServerResponseEntity.success();
    }

    @GetMapping("/getOfflineHandleEventByProdId/{prodId}")
    @Operation(summary = "通过prodId获取最新下线商品的事件" , description = "通过prodId获取最新下线商品的事件")
    @PreAuthorize("@pms.hasPermission('prod:prod:offlineInfo')")
    public ServerResponseEntity<OfflineHandleEvent> getByProdId(@PathVariable Long prodId) {
        OfflineHandleEvent offlineHandleEvent = offlineHandleEventService.getProcessingEventByHandleTypeAndHandleId(OfflineHandleEventType.PROD.getValue(), prodId);
        return ServerResponseEntity.success(offlineHandleEvent);
    }

    @GetMapping("/prodSkuPage")
    @Operation(summary = "分页获取商品及商品sku信息" , description = "分页获取商品及商品sku信息")
    public ServerResponseEntity<IPage<Product>> prodSkuPage(ProductParam product, PageParam<Product> page) {
        product.setLang(I18nMessage.getDbLang());
        product.setShopId(SecurityUtils.getShopUser().getShopId());
        IPage<Product> products = productService.prodSkuPage(page, product);
        return ServerResponseEntity.success(products);
    }


    @GetMapping(value = "/downloadModel")
    @PreAuthorize("@pms.hasPermission('prod:prod:downloadModel')")
    @Operation(summary = "下载商品导入模板" , description = "下载商品导入模板")
    public void downloadModel(HttpServletResponse response) {
        productExcelService.downloadModel(response, SecurityUtils.getShopUser().getShopId());
    }

    @RequestMapping(value = "/exportExcel", method = RequestMethod.POST)
    @ResponseBody
    @Operation(summary = "导入商品" , description = "导入商品")
    @PreAuthorize("@pms.hasPermission('prod:prod:import')")
    public ServerResponseEntity exportExcel(@RequestParam("excelFile") MultipartFile excelFile) throws Exception {
        if (excelFile == null) {
            // 网络繁忙，请稍后重试
            throw new YamiShopBindException("yami.network.busy");
        }
        Long shopId = SecurityUtils.getShopUser().getShopId();
        List<Long> prodIds = new ArrayList<>();
        Long employeeId = SecurityUtils.getShopUser().getEmployeeId();
        Object o = productExcelService.parseFile(excelFile, shopId, employeeId, prodIds);
        eventPublisher.publishEvent(new EsProductUpdateEvent(null, prodIds, EsOperationType.SAVE_BATCH));
        return ServerResponseEntity.success(o);
    }

    private void checkParam(ProductParam productParam) {
        Long prodId = productParam.getProdId();
        Product.DeliveryModeVO deliveryMode = productParam.getDeliveryModeVo();
        boolean hasDeliverMode = deliveryMode != null
                && (deliveryMode.getHasShopDelivery() || deliveryMode.getHasUserPickUp() || deliveryMode.getHasCityDelivery());
        if (!hasDeliverMode) {
            // 请选择配送方式
            throw new YamiShopBindException("yami.product.dvy.type");
        }
        if (Objects.equals(productParam.getMold(), 1) || Objects.equals(productParam.getMold(), 3)) {
            // 虚拟商品和卡券商品的配送方式
            Product.DeliveryModeVO mode = new Product.DeliveryModeVO();
            mode.setHasShopDelivery(true);
            mode.setHasUserPickUp(false);
            mode.setHasCityDelivery(false);
            productParam.setDeliveryModeVo(mode);
        }
        //运费模板
        if(Objects.nonNull(productParam.getDeliveryTemplateId()) && !DeliveryTemplateType.isUnifiedTemplate(productParam.getDeliveryTemplateId())) {
            Transport transport = transportService.getTransportAndAllItems(productParam.getDeliveryTemplateId());
            if (Objects.isNull(transport)) {
                // 产品运费模板不存在
                throw new YamiShopBindException("yami.prod.transport.not.exist");
            }
        }

        // 统一运费-运费金额不能小于0
        if(Objects.equals(productParam.getDeliveryTemplateId(), DeliveryTemplateType.FREIGHT.getValue()) && productParam.getDeliveryAmount() < 0) {
            // 运费金额不能小于0
            throw new YamiShopBindException("yami.Shipping.amount.error");
        }
        Category category = categoryService.getById(productParam.getCategoryId());
        if (Objects.isNull(category)) {
            // 该平台分类已删除，请选择新的分类
            throw new YamiShopBindException("yami.prod.platform.category.deleted");
        }
        if (category.getGrade() < CategoryLevel.THIRD.value()) {
            // 请选择三级分类
            throw new YamiShopBindException("yami.prod.category.error");
        }
        //校验参与活动的sku不能修改价格
        Set<Long> skuIds = getIsParticipate(prodId);
        List<Sku> skus = new ArrayList<>();
        if (CollUtil.isNotEmpty(skuIds)) {
            skus = skuService.listSkuByProdId(prodId);
        }
        Map<Long, Double> skuMap = skus.stream().collect(Collectors.toMap(Sku::getSkuId, Sku::getPrice));
        List<SkuAdminDTO> skuList = productParam.getSkuList();
        Long shopId = SecurityUtils.getShopUser().getShopId();
        //商品编码
        List<Product> products = productService.list(new LambdaQueryWrapper<Product>().eq(Product::getShopId, shopId)
                .ne(Product::getStatus, -1));
        List<Long> prodIds = products.stream().map(Product::getProdId).collect(Collectors.toList());
        List<String> partyCodes = skuService.listSkuByProdIds(prodIds, prodId);
        // 校验sku
        checkSku(skuList, partyCodes,productParam.getMold(), productParam.getIsBindVoucher());
        // 判断组合选品的预售状态
        if(Objects.equals(productParam.getProdType(), ProdType.PROD_TYPE_ACTIVE.value())) {
            productParam.setPreSellStatus(0);
        }
    }

    private void checkSku(List<SkuAdminDTO> skuList, List<String> partyCodes, Integer mold, Integer isBindVoucher) {
        // 检查库存点是否被删除了
        List<StockPointSkuDTO> stockPointList = new ArrayList<>(Constant.INITIAL_CAPACITY);
        boolean isAllUnUse = true;
        for (SkuAdminDTO sku : skuList) {
            //雪花算法生成商品编码
            if (StrUtil.isBlank(sku.getPartyCode())) {
                String partyCode = StringUtils.join("RM", String.valueOf(segmentService.getSegmentId(SegmentIdKey.PRODUCT)));
                sku.setPartyCode(partyCode);
            }
            if (CollectionUtils.isNotEmpty(partyCodes) && partyCodes.contains(sku.getPartyCode())) {
                String message = I18nMessage.getMessage("yami.sku.party.code");
                String isExit = I18nMessage.getMessage("yami.is.exist");
                //商品编码已存在
                throw new YamiShopBindException(message + sku.getPartyCode() + isExit);
            }
            // sku价格最低只能为0.01
            if (Constant.MIN_PRODUCT_AMOUNT > sku.getPrice()) {
                sku.setPrice(Constant.MIN_PRODUCT_AMOUNT);
            }
            if (1 == sku.getStatus()) {
                isAllUnUse = false;
            }
            if (Objects.isNull(sku.getStockWarning())) {
                sku.setStockWarning(0);
            }
            if(sku.getStockWarning()>Constant.STOCK_WARNING_MAX){
                //超过库存预警最大值
                throw new YamiShopBindException("yami.sku.stockWarning.limit");
            }
            stockPointList.addAll(sku.getStockPointList());
        }
        if (isAllUnUse) {
            // 至少要启用一种商品规格
            throw new YamiShopBindException("yami.product.enable.sku");
        }
        // 如果是组合商品不需要判断
        if(Objects.equals(mold, ProdMoldEnum.COMBO.value())){
            return;
        }
        // 如果是卡券商品，判断sku关联
        if(Objects.equals(mold, ProdMoldEnum.VOUCHER.value())){
            List<SkuAdminDTO> listCount = skuList.stream().filter(sku -> Objects.equals(sku.getStatus(), StatusEnum.ENABLE.value())).collect(Collectors.toList());
            long size = listCount.stream().filter(sku -> Objects.nonNull(sku.getVoucherId())).count();
            if ((Objects.equals(isBindVoucher, 1) && size != listCount.size())
                    || (Objects.equals(isBindVoucher, 0) && size > 0) ) {
                // 卡券商品关联有误
                throw new YamiShopBindException("yami.product.voucher.error");
            }
        }
        stockPointSkuService.checkStockPoint(BeanUtil.mapAsList(stockPointList, StockPointSkuVO.class));
    }

    /**
     * 检查分类与品牌是否可用
     *
     * @param shopId         店铺id
     * @param categoryId     平台分类
     * @param shopCategoryId 店铺分类
     * @param brandId        品牌id
     */
    private void checkCategoryAndBrand(Long shopId, Long categoryId, Long shopCategoryId, Long brandId) {
        Category category = categoryService.getOne(Wrappers.lambdaQuery(Category.class).eq(Category::getCategoryId, categoryId));
        if (Objects.isNull(category) || !Objects.equals(category.getStatus(), StatusEnum.ENABLE.value())) {
            //如果是下架了分类，就提示 该平台分类已被下线，无法上架该商品
            throw new YamiShopBindException("yami.product.category.is.offline");
        }

        CategoryShop categoryShop = categoryShopService.getOne(Wrappers.lambdaQuery(CategoryShop.class).eq(CategoryShop::getShopId, shopId).eq(CategoryShop::getCategoryId, categoryId));
        //如果是签约丢了，就提示 您已取消与该商品类型的签约，请重新签约该商品类型方可上架
        if (Objects.isNull(categoryShop) || !Objects.equals(categoryShop.getStatus(), SigningStatus.SUCCESS.value())) {
            throw new YamiShopBindException("yami.product.category.not.exit");
        }

        Category shopCategory = categoryService.getOne(Wrappers.lambdaQuery(Category.class).eq(Category::getShopId, shopId).eq(Category::getCategoryId, shopCategoryId));
        if (Objects.isNull(shopCategory) || !Objects.equals(shopCategory.getStatus(), SigningStatus.SUCCESS.value())) {
            // 当前店铺分类已不可用，请刷新分类列表重新选择
            throw new YamiShopBindException("yami.product.shop.category.not.under");
        }
        if (Objects.isNull(brandId) || Objects.equals(brandId, 0L)) {
            return;
        }
        Brand brand = brandService.getOne(Wrappers.lambdaQuery(Brand.class).eq(Brand::getBrandId, brandId));
        if (Objects.isNull(brand) || !Objects.equals(brand.getStatus(), StatusEnum.ENABLE.value())) {
            throw new YamiShopBindException("yami.product.brand.not.under");
        }
        BrandShop brandShop = brandShopService.getOne(Wrappers.lambdaQuery(BrandShop.class).eq(BrandShop::getShopId, shopId).eq(BrandShop::getBrandId, brandId));
        long categoryBrandCount = categoryBrandService.count(Wrappers.lambdaQuery(CategoryBrand.class).eq(CategoryBrand::getCategoryId, categoryId).eq(CategoryBrand::getBrandId, brandId));
        if (Objects.isNull(brandShop)) {
            if (categoryBrandCount <= 0) {
                throw new YamiShopBindException("yami.product.brand.not.under");
            }
        } else {
            if (!Objects.equals(brandShop.getStatus(), SigningStatus.SUCCESS.value())) {
                throw new YamiShopBindException("yami.product.brand.not.under");
            }
        }
    }

    private void checkBeforeDeleteProduct(Long prodId) {
        GetComboProdCountEvent getComboProdCountEvent = new GetComboProdCountEvent();
        getComboProdCountEvent.setProdId(prodId);
        applicationContext.publishEvent(getComboProdCountEvent);
        if (getComboProdCountEvent.getCount() > 0) {
            //参加以下活动的商品不能被删除：优惠套餐
            throw new YamiShopBindException("yami.combo.prod.not.delete");
        }
        GetGiveawayProdCountEvent getGiveawayProdCountEvent = new GetGiveawayProdCountEvent();
        getGiveawayProdCountEvent.setProdId(prodId);
        applicationContext.publishEvent(getGiveawayProdCountEvent);
        if (getGiveawayProdCountEvent.getCount() > 0) {
            //参加以下活动的商品不能被删除：赠品
            throw new YamiShopBindException("yami.giveaway.prod.not.delete");
        }
    }

    private void checkGiveawayProd(Long prodId){
        List<GiveawayProd> giveawayProdList = giveawayProdService.list(new LambdaQueryWrapper<>(GiveawayProd.class)
                .eq(GiveawayProd::getProdId, prodId)
                .eq(GiveawayProd::getStatus, StatusEnum.ENABLE.value()));
        if(CollectionUtils.isNotEmpty(giveawayProdList)){
            log.info("商品{}参加了赠品活动，需要清除赠品缓存", prodId);
            List<Long> giveawayIds = giveawayProdList.stream().map(GiveawayProd::getGiveawayId).collect(Collectors.toList());
            List<Giveaway> giveawayList = giveawayService.list(new LambdaQueryWrapper<>(Giveaway.class)
                    .in(Giveaway::getGiveawayId, giveawayIds)
                    .eq(Giveaway::getStatus, StatusEnum.ENABLE.value()));
            List<Long> prodIds = giveawayList.stream().map(Giveaway::getProdId).collect(Collectors.toList());

            giveawayProdService.removeGiveawayCacheBatch(prodIds);
        }
    }

    /**
     * 处理下活动商品的价格
     *
     * @param product  筛选参数
     * @param products 商品列表
     */
    private void processActivityProdPrice(ProductParam product, List<Product> products) {
        Map<Integer, List<Product>> prodMap = products.stream().collect(Collectors.groupingBy(Product::getProdType));
        if (prodMap.containsKey(ProdType.PROD_TYPE_SECKILL.value())) {
            applicationContext.publishEvent(new ProcessActivityProdPriceEvent(product, prodMap.get(ProdType.PROD_TYPE_SECKILL.value())));
        }

        if (prodMap.containsKey(ProdType.PROD_TYPE_GROUP.value())) {
            applicationContext.publishEvent(new ProcessActivityProdPriceEvent(product, prodMap.get(ProdType.PROD_TYPE_GROUP.value())));
        }
    }

    /**
     * 组合商品上架条件:
     * 1.关联商品 > 0
     * 2.关联商品不为(预售类型
     * 3.关联的商品(sku)都为上架状态
     * @param productDb
     */
    private void checkEnableComboSpu(Product productDb) {
        // 这里不需要供应商发货的条件判断,因为代销组合商品只能供应商发货
        List<Sku> skuList = skuService.listSkuByProdId(productDb.getProdId());
        skuList = skuList.stream().filter(sku -> Objects.equals(sku.getStatus(), StatusEnum.ENABLE.value())).toList();
        List<SkuComboVO> combSpus = skuComboService.listCombProdByProdIds(Collections.singletonList(productDb.getProdId()));
        Map<Long, List<SkuComboVO>> skuComboMap = combSpus.stream().collect(Collectors.groupingBy(SkuComboVO::getSkuId));
        for (Sku skuVO : skuList) {
            if (!skuComboMap.containsKey(skuVO.getSkuId())) {
                // 组合商品的每个规格至少关联一个商品
                throw new YamiShopBindException("yami.product.exception.comboLeastOne");
            }
        }
        Set<Long> prodIds = combSpus.stream().map(SkuComboVO::getComboProdId).collect(Collectors.toSet());
        Set<Long> skuIds = combSpus.stream().map(SkuComboVO::getComboSkuId).collect(Collectors.toSet());
        long count = productService.count(new LambdaQueryWrapper<Product>().in(Product::getProdId,prodIds)
                .and(wrapper -> wrapper.eq(Product::getPreSellStatus,1).or().ne(Product::getStatus, 1)));
        long skuCount = skuService.count(new LambdaQueryWrapper<Sku>().in(Sku::getSkuId,skuIds).ne(Sku::getStatus, 1));
        if(count > 0 || skuCount > 0){
            // 当前组合商品关联的商品状态异常或者类型不对不能进行上架
            throw new YamiShopBindException("yami.product.exception.comboStatusError");
        }
    }

    private void removeProdCache(ProductParam productParam, List<String> userIds, List<Sku> dbSkus) {
        checkGiveawayProd(productParam.getProdId());
        //清除购物车缓存
        basketService.removeCacheByUserIds(userIds, null);
        productService.removeProdCacheByProdId(productParam.getProdId());
        prodParameterService.removeCacheByProdId(productParam.getProdId());
        for (Sku sku : dbSkus) {
            skuService.removeSkuCacheBySkuId(sku.getSkuId(), sku.getProdId());
        }
    }


    /**
     * 卡券商品上架条件:
     * 关联卡券没被删除
     * @param productDb
     */
    private void checkEnableVoucherSpu(Product productDb) {
        // 判断关联卡券商品类型商品，sku关联的卡券商品是否被删除了
        if (productDb.getIsBindVoucher() == 1) {
            List<Sku> skuList = skuService.listSkuByProdId(productDb.getProdId());
            List<Long> voucherIds = skuList.stream().filter(sku -> sku.getStatus() == StatusEnum.ENABLE.value()).map(Sku::getVoucherId).collect(Collectors.toList());
            List<Voucher> voucherList = voucherService.listByVoucherIds(voucherIds);
            if (CollectionUtils.isEmpty(voucherList) || voucherList.size() != voucherIds.size()) {
                // 卡券商品的每个规格至少关联一个卡券
                throw new YamiShopBindException("yami.product.exception.voucher");
            }
        }
    }
}
