package com.yami.shop.bean.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单退款介入记录
 *
 * <AUTHOR>
 * @date 2023-10-26 15:52:39
 */
@Data
@Schema(description = "订单退款介入记录")
public class OrderRefundInterventionVO implements Serializable{
    private static final long serialVersionUID = 1L;

    @TableId
    private Long refundIntervertionId;
    @Schema(description = "退款id")
    private Long refundId;
    @Schema(description = "创建时间")
    private Date createTime;
    @Schema(description = "更新时间")
    private Date updateTime;
    @Schema(description = "商家/供应商/用户id")
    private Long bizId;
    @Schema(description = "商家/供应商/用户名称")
    private String bizName;
    @Schema(description = "系统类型，0.普通用户 1.商家端 2.平台端 3.供应商端")
    private Integer sysType;
    @Schema(description = "凭证说明")
    private String voucherDesc;
    @Schema(description = "文件凭证(逗号隔开)")
    private String imgUrls;
}
