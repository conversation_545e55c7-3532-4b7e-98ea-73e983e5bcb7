package com.yami.shop.multishop.controller;


import cn.hutool.core.date.DateUtil;
import com.yami.shop.bean.app.dto.OrderCountData;
import com.yami.shop.bean.dto.StatisticsRefundDto;
import com.yami.shop.bean.param.OrderPayParam;
import com.yami.shop.bean.param.PayTopParam;
import com.yami.shop.bean.param.StatisticsRefundParam;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.OrderItemService;
import com.yami.shop.service.StatisticsOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
/**
 * <AUTHOR> on 2019/10/17.
 */
@RestController
@RequestMapping("/shop/statisticsOrder")
@AllArgsConstructor
@Tag(name = "订单数据统计接口")
public class StatisticsOrderController {

    private final StatisticsOrderService statisticsOrderService;
    private final OrderItemService orderItemService;

    @GetMapping("/orderCount")
    @Operation(summary = "查询店铺订单各状态数量" , description = "查询店铺订单各状态数量")
    public ServerResponseEntity<OrderCountData> getOrderCount() {
        OrderCountData orderCountData = statisticsOrderService.getOrderCountOfStatusByShopId(SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success(orderCountData);
    }

    @GetMapping("/orderPayByShopId")
    @Operation(summary = "通过时间获取支付信息")
    public ServerResponseEntity<OrderPayParam> orderPayByShopId() {
        OrderPayParam actualTotal = statisticsOrderService.getPayUserCountByshopId(
                SecurityUtils.getShopUser().getShopId(),DateUtil.beginOfDay(DateUtil.date()), DateUtil.endOfDay(DateUtil.date()));
        return ServerResponseEntity.success(actualTotal);
    }

    @GetMapping("/getActualTotalByHour")
    @Operation(summary = "通过24小时分段获取支付金额")
    public ServerResponseEntity<OrderPayParam> getActualTotalByHour() {
        OrderPayParam payList = statisticsOrderService.getActualTotalByHour(
                SecurityUtils.getShopUser().getShopId(),DateUtil.beginOfDay(DateUtil.date()),DateUtil.endOfDay(DateUtil.date()));
        return ServerResponseEntity.success(payList);
    }

    @GetMapping("/getActualTotalByDay")
    @Operation(summary = "通过天数分段获取支付金额")
    public ServerResponseEntity<List<OrderPayParam>> getActualTotalByDay() {
        List<OrderPayParam> payList = statisticsOrderService.getActualTotalByDay(
                SecurityUtils.getShopUser().getShopId(),DateUtil.endOfDay(DateUtil.lastMonth()),DateUtil.endOfDay(DateUtil.date()));
        return ServerResponseEntity.success(payList);
    }

    @GetMapping("/getOrderRefundByTime")
    @Operation(summary = "通过时间获取比率信息")
    public ServerResponseEntity<StatisticsRefundParam> getOrderRefundByTime() {
        StatisticsRefundParam refundParam = statisticsOrderService.getOrderRefundByShopId(
                SecurityUtils.getShopUser().getShopId(),DateUtil.beginOfDay(DateUtil.date()), DateUtil.endOfDay(DateUtil.date()));
        return ServerResponseEntity.success(refundParam);
    }

    @GetMapping("/getOrderRefundDayByTime")
    @Operation(summary = "通过时间获取分段比率信息及退款金额信息")
    public ServerResponseEntity<List<StatisticsRefundParam>> getOrderRefundById() {
        List<StatisticsRefundParam> refundList = statisticsOrderService.getOrderRefundByShopIdAndDay(
                SecurityUtils.getShopUser().getShopId(),DateUtil.endOfDay(DateUtil.lastMonth()),DateUtil.endOfDay(DateUtil.date()));
        return ServerResponseEntity.success(refundList);
    }

    @GetMapping("/getRefundRankingByProd")
    @Operation(summary = "根据商品名生成退款排行")
    public ServerResponseEntity<List<StatisticsRefundParam>> getRefundRankingByProd(StatisticsRefundDto statisticsRefundDto) {
        statisticsRefundDto.setShopId(SecurityUtils.getShopUser().getShopId());
        List<StatisticsRefundParam> refundList = statisticsOrderService.getRefundRankingByProd(statisticsRefundDto);
        return ServerResponseEntity.success(refundList);
    }

    @GetMapping("/getRefundRankingByReason")
    @Operation(summary = "根据退款原因生成退款排行")
    public ServerResponseEntity<List<StatisticsRefundParam>> getRefundRankingByReason() {
        List<StatisticsRefundParam> refundList = statisticsOrderService.getRefundRankingByReason(
                SecurityUtils.getShopUser().getShopId(),DateUtil.endOfDay(DateUtil.lastMonth()),DateUtil.endOfDay(DateUtil.date()));
        return ServerResponseEntity.success(refundList);
    }


    /**
     * 支付数量TOP
     */
    @GetMapping("/getPayAmountTop")
    @Operation(summary = "支付数量TOP" , description = "支付数量TOP")
    public ServerResponseEntity<List<PayTopParam>> getPayAmountTop(StatisticsRefundDto statisticsRefundDto) {
        statisticsRefundDto.setShopId(SecurityUtils.getShopUser().getShopId());
        List<PayTopParam> pages = orderItemService.getOrderProdPayCount(statisticsRefundDto);
        return ServerResponseEntity.success(pages);
    }

}
