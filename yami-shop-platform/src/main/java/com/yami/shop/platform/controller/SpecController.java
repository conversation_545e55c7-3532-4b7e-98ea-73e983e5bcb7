package com.yami.shop.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.enums.ProdPropRule;
import com.yami.shop.bean.model.ProdProp;
import com.yami.shop.bean.model.ProdPropValue;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.ProdPropService;
import com.yami.shop.service.ProdPropValueService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 规格管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/prod/spec")
@Tag(name = "规格")
@AllArgsConstructor
public class SpecController {

    private final ProdPropService prodPropService;
    private final ProdPropValueService prodPropValueService;

    @GetMapping("/list")
    @Operation(summary = "获取所有的规格" , description = "获取所有的规格")
    @PreAuthorize("@pms.hasPermission('prod:spec:list')")
    public ServerResponseEntity<List<ProdProp>> list() {
        ProdProp prodProp = new ProdProp();
        prodProp.setShopId(Constant.PLATFORM_SHOP_ID);
        prodProp.setRule(ProdPropRule.SPEC.value());
        List<ProdProp> list = prodPropService.listByLang(prodProp);
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/page")
    @Operation(summary = "分页获取" , description = "分页获取")
    @PreAuthorize("@pms.hasPermission('prod:spec:page')")
    public ServerResponseEntity<IPage<ProdProp>> page(ProdProp prodProp, PageParam<ProdProp> page) {
        prodProp.setRule(ProdPropRule.SPEC.value());
        prodProp.setShopId( Constant.PLATFORM_SHOP_ID);
        IPage<ProdProp> list = prodPropService.pagePropAndValue(prodProp, page);
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/listSpecValue/{specId}")
    @Operation(summary = "根据规格id获取规格值" , description = "根据规格id获取规格值")
    @Parameter(name = "specId", description = "规格id" )
    @PreAuthorize("@pms.hasPermission('prod:spec:list')")
    public ServerResponseEntity<List<ProdPropValue>> listSpecValue(@PathVariable("specId") Long specId) {
        List<ProdPropValue> list = prodPropValueService.propValueListByPropId(specId);
        return ServerResponseEntity.success(list);
    }

    @PostMapping
    @Operation(summary = "保存" , description = "保存")
    @PreAuthorize("@pms.hasPermission('prod:spec:save')")
    public ServerResponseEntity<Void> save(@Valid @RequestBody ProdProp prodProp) {
        prodProp.setRule(ProdPropRule.SPEC.value());
        prodProp.setShopId(Constant.PLATFORM_SHOP_ID);
        prodPropService.saveProdPropAndValues(prodProp);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @Operation(summary = "修改" , description = "修改")
    @PreAuthorize("@pms.hasPermission('prod:spec:update')")
    public ServerResponseEntity<Void> update(@Valid @RequestBody ProdProp prodProp) {
        prodProp.setRule(ProdPropRule.SPEC.value());
        prodProp.setShopId(Constant.PLATFORM_SHOP_ID);
        prodPropService.updateProdPropAndValues(prodProp);
        return ServerResponseEntity.success();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除" , description = "删除")
    @Parameter(name = "id", description = "规格id" )
    @PreAuthorize("@pms.hasPermission('prod:spec:delete')")
    public ServerResponseEntity<Void> delete(@PathVariable Long id) {
        prodPropService.deleteProdPropAndValues(id, ProdPropRule.SPEC.value(), Constant.PLATFORM_SHOP_ID);
        return ServerResponseEntity.success();
    }
}
