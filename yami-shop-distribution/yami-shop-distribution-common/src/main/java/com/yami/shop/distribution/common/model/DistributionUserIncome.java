package com.yami.shop.distribution.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yami.shop.bean.model.Order;
import com.yami.shop.bean.model.OrderItem;
import com.yami.shop.bean.vo.SysUserVO;
import com.yami.shop.distribution.common.vo.DistributionUserVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * <AUTHOR>
 */
@Data
@TableName( "tz_distribution_user_income")
public class DistributionUserIncome implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    @Schema(description = "收入记录id" )
    private Long incomeId;

    @Schema(description = "钱包id" )
    private Long walletId;

    @Schema(description = "收入类型(0其他、1直推奖励、2间推奖励、3邀请奖励、4平台修改)" )
    private Integer incomeType;

    @Schema(description = "佣金状态(0:待支付、1:待结算、2:已结算、-1:订单失效)" )
    private Integer state;

    @Schema(description = "佣金数额" )
    private Double incomeAmount;

    @Schema(description = "关联订单项号" )
    private Long orderItemId;

    @Schema(description = "商户订单号" )
    private String merchantOrderId;

    @Schema(description = "分销员id" )
    private Long distributionUserId;

    @Schema(description = "订单号" )
    private String orderNumber;

    @Schema(description = "创建时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "更新时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "失效原因(0正常，1佣金为0订单失效，2订单项退款)" )
    private Integer reson;

    @Schema(description = "支付系统类型(0:原生支付, 1:通联支付)")
    private Integer paySysType;

    @Schema(description = "关联分销员" )
    @TableField(exist = false)
    private DistributionUserVO distributionUser;

    @Schema(description = "关联订单" )
    @TableField(exist = false)
    private Order order;

    @Schema(description = "关联操作人" )
    @TableField(exist = false)
    private SysUserVO sysUserVO;

    @Schema(description = "订单项" )
    @TableField(exist = false)
    private OrderItem orderItem;

    @Schema(description = "排序字段：分销业绩-推广效果：0无 1佣金 2创建时间 3更新时间" )
    @TableField(exist = false)
    private Integer sortParam = 0;

    @Schema(description = "排序类型 0无 1 正序 2倒序" )
    @TableField(exist = false)
    private Integer sortType = 0;

    /**
     * 用户id
     */
    @TableField(exist = false)
    private String userId;
}
