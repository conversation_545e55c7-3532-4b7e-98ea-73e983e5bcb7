package com.yami.shop.bean.app.param;

import com.yami.shop.bean.dto.allinpay.BasePayInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR> on 2020/03/02
 */
@Data
@Schema(description = "会员支付参数")
public class PayUserParam extends BasePayInfoDTO {

    @NotBlank(message = "会员等级id不能为空")
    @Schema(description = "会员等级id，当id=-1时，充值自定义金额" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "二维码携带的参数" )
    private String scene;

    @Schema(description = "支付方式 (1:微信小程序支付 2:支付宝 3微信扫码支付 4 微信h5支付 5微信公众号支付 6支付宝H5支付 7支付宝APP支付 8微信APP支付 9余额支付)" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer payType;

    @Schema(description = "支付完成回跳地址" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String returnUrl;

    @Schema(description = "自定义充值金额" )
    private Double customRechargeAmount;

    @Schema(description = "用户等级id/余额支付id" )
    private Long orderIds;

    @Schema(description = "支付单号" )
    private String payNo;

    @Schema(description = "需要支付价格" )
    private Double needAmount;

}
