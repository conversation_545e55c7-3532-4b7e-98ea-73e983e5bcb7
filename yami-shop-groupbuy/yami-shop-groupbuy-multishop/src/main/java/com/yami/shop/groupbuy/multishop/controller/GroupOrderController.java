package com.yami.shop.groupbuy.multishop.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.groupbuy.common.model.GroupOrder;
import com.yami.shop.groupbuy.common.service.GroupOrderService;
import com.yami.shop.security.multishop.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
@RestController
@RequestMapping("/group/order")
@Tag(name = "商家端团购订单接口")
@AllArgsConstructor
public class GroupOrderController {

    private final GroupOrderService groupOrderService;

    @GetMapping("/page")
    @Operation(summary = "分页查找团购订单列表")
    @PreAuthorize("@pms.hasPermission('group:order:page')")
    public ServerResponseEntity<IPage<GroupOrder>> getGroupOrderPage(PageParam<GroupOrder> page, GroupOrder groupOrder) {
        return ServerResponseEntity.success(groupOrderService.page(page, new LambdaQueryWrapper<GroupOrder>()));
    }

    @GetMapping("/info/{groupOrderId}")
    @Operation(summary = "通过id查询团购订单活动")
    @Parameter(name = "groupOrderId", description = "团购订单id" , required = true)
    @PreAuthorize("@pms.hasPermission('group:order:info')")
    public ServerResponseEntity<GroupOrder> getById(@PathVariable("groupOrderId") Long groupOrderId) {
        GroupOrder groupOrder = groupOrderService.getById(groupOrderId);
        if (!Objects.equals(SecurityUtils.getShopUser().getShopId(), groupOrder.getShopId())) {
            throw new YamiShopBindException("yami.no.auth");
        }
        return ServerResponseEntity.success(groupOrder);
    }

    @PostMapping
    @Operation(summary = "新增团购订单")
    @PreAuthorize("@pms.hasPermission('group:order:save')")
    public ServerResponseEntity<Boolean> save(@RequestBody @Valid GroupOrder groupOrder) {
        return ServerResponseEntity.success(groupOrderService.save(groupOrder));
    }

    @PutMapping
    @Operation(summary = "修改团购订单")
    @PreAuthorize("@pms.hasPermission('group:order:update')")
    public ServerResponseEntity<Boolean> updateById(@RequestBody @Valid GroupOrder groupOrder) {
        return ServerResponseEntity.success(groupOrderService.updateById(groupOrder));
    }

    @DeleteMapping("/{groupOrderId}")
    @Operation(summary = "通过id删除团购订单活动")
    @Parameter(name = "groupOrderId", description = "团购订单id" , required = true)
    @PreAuthorize("@pms.hasPermission('group:order:delete')")
    public ServerResponseEntity<Boolean> removeById(@PathVariable Long groupOrderId) {
        return ServerResponseEntity.success(groupOrderService.removeById(groupOrderId));
    }

}
