package com.yami.shop.distribution.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 分销收入状态
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistributionUserIncomeStateEnum {
    /**
     * 待支付
     */
    UNPAY(0, "待支付"),
    /**
     * 待结算
     */
    UNCOMMISSION(1, "待结算"),

    /**
     * 已结算
     */
    COMMISSION(2, "已结算"),

    /**
     * 已失效
     */
    INVALID(-1, "已失效"),
    ;

    private final int value;
    private final String desc;
}
