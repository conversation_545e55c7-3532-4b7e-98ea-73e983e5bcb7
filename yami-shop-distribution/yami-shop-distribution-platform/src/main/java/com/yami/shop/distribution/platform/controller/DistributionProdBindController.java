package com.yami.shop.distribution.platform.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.model.DistributionProdBind;
import com.yami.shop.distribution.common.service.DistributionProdBindService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2019-04-22 10:01:44
 */
@RestController
@RequestMapping("/distribution/distributionProdBind")
@Tag(name = "平台端分销商品绑定表接口")
@AllArgsConstructor
public class DistributionProdBindController {

    private final DistributionProdBindService distributionProdBindService;

    @GetMapping("/page")
    @Operation(summary = "分页获取分销商品绑定信息列表")
    @PreAuthorize("@pms.hasPermission('distribution:distributionProdBind:page')")
    public ServerResponseEntity<IPage<DistributionProdBind>> getDistributionProdBindPage(PageParam<DistributionProdBind> page,
                                                                                         DistributionProdBind distributionProdBind) {
        return ServerResponseEntity.success(distributionProdBindService.page(page, new LambdaQueryWrapper<DistributionProdBind>()));
    }

    @GetMapping("/info/{id}")
    @Operation(summary = "通过id查询分销商品绑定表")
    @Parameter(name = "id", description = "分销商品绑定id" , required = true)
    @PreAuthorize("@pms.hasPermission('distribution:distributionProdBind:info')")
    public ServerResponseEntity<DistributionProdBind> getById(@PathVariable("id") Long id) {
        return ServerResponseEntity.success(distributionProdBindService.getById(id));
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionProdBind:save')")
    @Operation(summary = "新增分销商品绑定表")
    public ServerResponseEntity<Boolean> save(@RequestBody @Valid DistributionProdBind distributionProdBind) {
        return ServerResponseEntity.success(distributionProdBindService.save(distributionProdBind));
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionProdBind:update')")
    @Operation(summary = "修改分销商品绑定表")
    public ServerResponseEntity<Boolean> updateById(@RequestBody @Valid DistributionProdBind distributionProdBind) {
        return ServerResponseEntity.success(distributionProdBindService.updateById(distributionProdBind));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('distribution:distributionProdBind:delete')")
    @Operation(summary = "通过id删除分销商品绑定表")
    @Parameter(name = "id", description = "分销商品绑定id" , required = true)
    public ServerResponseEntity<Boolean> removeById(@PathVariable Long id) {
        return ServerResponseEntity.success(distributionProdBindService.removeById(id));
    }
}
