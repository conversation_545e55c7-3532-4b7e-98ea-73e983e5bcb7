package com.yami.shop.platform.controller;

import com.yami.shop.bean.model.Sku;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.SkuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/platform/sku")
@AllArgsConstructor
@Tag(name = "商品sku")
public class SkuController {

    private final SkuService skuService;

    @GetMapping("/getAllSkuList")
    @PreAuthorize("@pms.hasPermission('plateform:sku:list')")
    @Operation(summary = "获取指定商品sku列表" , description = "获取指定商品sku列表")
    @Parameter(name = "prodId", description = "商品id" )
    public ServerResponseEntity<List<Sku>> getSkuListByProdId(Long prodId) {
        List<Sku> skus = skuService.listSkuAndSkuStockForAdmin(prodId);
        return ServerResponseEntity.success(skus);
    }
}
