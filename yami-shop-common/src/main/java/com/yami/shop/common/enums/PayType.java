package com.yami.shop.common.enums;

import java.util.Objects;

/**
 * 支付类型
 * <AUTHOR>
 */
public enum PayType {

    /** 积分支付*/
    SCOREPAY(0,"积分支付", ""),

    /** 微信支付*/
    WECHATPAY(1,"小程序支付", "WECHATPAY_MINIPROGRAM_ORG"),

    /** 支付宝*/
    ALIPAY(2,"支付宝", "SCAN_ALIPAY_ORG"),

    /** 微信扫码支付*/
    WECHATPAY_SWEEP_CODE(3,"微信扫码支付", "SCAN_WEIXIN_ORG"),

    /** 微信H5支付*/
    WECHATPAY_H5(4,"微信H5支付", "H5_CASHIER_VSP_ORG"),

    /** 微信公众号*/
    WECHATPAY_MP(5,"微信公众号支付", "WECHAT_PUBLIC_ORG"),

    /** 支付宝H5支付*/
    ALIPAY_H5(6,"支付宝H5支付", "H5_CASHIER_VSP_ORG"),

    /** 支付宝APP支付*/
    ALIPAY_APP(7,"支付宝APP支付", "ALIPAY_MINIPROGRAM_CASHIER_VSP_ORG"),

    /** 微信APP支付*/
    WECHATPAY_APP(8,"微信APP支付", "WECHATPAY_APP_OPEN"),

    /** 余额支付*/
    BALANCE(9,"余额支付", "BALANCE"),

    /** paypal支付 */
    PAYPAL(10,"paypal支付", ""),
    /**
     * 平台余额支付
     */
    PLATFORM_BALANCE(11, "平台余额支付", "COUPON");



    private final Integer num;

    private final String payTypeName;

    private final String allinpayCode;

    public Integer value() {
        return num;
    }

    public String payTypeName() {return payTypeName;}

    public String allinpayCode() {return allinpayCode;}

    PayType(Integer num, String payTypeName, String allinpayCode){
        this.num = num;
        this.payTypeName = payTypeName;
        this.allinpayCode = allinpayCode;
    }

    public static PayType instance(Integer value) {
        PayType[] enums = values();
        for (PayType statusEnum : enums) {
            if (statusEnum.value().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }

    public static boolean isWxPay(PayType payType) {
        return (Objects.equals(payType, PayType.WECHATPAY)
                || Objects.equals(payType, PayType.WECHATPAY_SWEEP_CODE)
                || Objects.equals(payType, PayType.WECHATPAY_H5)
                || Objects.equals(payType, PayType.WECHATPAY_MP)
                || Objects.equals(payType, PayType.WECHATPAY_APP));
    }

    public static boolean isAliPay(PayType payType) {
        return (Objects.equals(payType, PayType.ALIPAY_H5)
                || Objects.equals(payType, PayType.ALIPAY)
                || Objects.equals(payType, PayType.ALIPAY_APP));
    }
}
