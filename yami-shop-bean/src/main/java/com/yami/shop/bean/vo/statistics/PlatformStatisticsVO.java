package com.yami.shop.bean.vo.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 平台基本信息统计
 * <AUTHOR>
 */
@Data
public class PlatformStatisticsVO {

    @Schema(description = "基本信息-商品数量" )
    private Long prodNum;

    @Schema(description = "基本信息-商品数量提升比例" )
    private Double prodNumRatio;

    @Schema(description = "基本信息-会员数量" )
    private Long userNum;

    @Schema(description = "基本信息-会员数量提升比例" )
    private Double userNumRatio;

    @Schema(description = "基本信息-订单数量" )
    private Long orderNum;

    @Schema(description = "基本信息-订单数量提升比例" )
    private Double orderNumRatio;

    @Schema(description = "基本信息-店铺数量" )
    private Long shopNum;

    @Schema(description = "基本信息-店铺数量提升比例" )
    private Double shopNumRatio;

    @Schema(description = "基本信息-退单数量" )
    private Long refundNum;

    @Schema(description = "基本信息-退单数量提升比例" )
    private Double refundNumRatio;




    @Schema(description = "今日待办-商品违规审核数量" )
    private Long prodAuditNum;

    @Schema(description = "今日待办-待审核店铺" )
    private Long shopAuditNum;

    @Schema(description = "今日待办-待审核分销体现" )
    private Long distributionAuditNum;

    @Schema(description = "今日待办-待审核提现" )
    private Long withdrawalAuditNum;





    @Schema(description = "实时概况-今日订单数" )
    private Long currentOrderNum;

    @Schema(description = "实时概况-今日新增会员数" )
    private Long currentUserNum;

    @Schema(description = "实时概况-今日新增店铺数" )
    private Long currentShopNum;

    @Schema(description = "实时概况-今日交易额" )
    private Double currentPayAmount;

    @Schema(description = "实时概况-今日新增商品数" )
    private Long currentProdNum;

    @Schema(description = "实时概况-今日新增评论数" )
    private Long currentProdCommNum;

    @Schema(description = "实时概况-今日访客数" )
    private Long todayVisitors;

    @Schema(description = "实时概况-昨日访客数" )
    private Long yesterdayVisitors;

    @Schema(description = "实时概况-前七天访客数" )
    private Long firstSevenDayVisitors;

    @Schema(description = "实时概况-前30天访客数" )
    private Long firstThirtyDayVisitors;
}
