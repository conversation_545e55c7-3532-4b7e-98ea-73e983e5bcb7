package com.yami.shop.multishop.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yami.shop.bean.dto.ShopWithdrawCashConfigDto;
import com.yami.shop.bean.dto.allinpay.AllinpayShopWithdrawCashDTO;
import com.yami.shop.bean.enums.PositionType;
import com.yami.shop.bean.enums.ShopWithdrawCashStatus;
import com.yami.shop.bean.model.ShopWithdrawCash;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.ShopDetailService;
import com.yami.shop.service.ShopWithdrawCashService;
import com.yami.shop.service.SmsLogService;
import com.yami.shop.sys.common.model.ShopEmployee;
import com.yami.shop.sys.common.service.ShopEmployeeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Objects;


/**
 * 商家提现申请信息
 *
 * <AUTHOR>
 * @date 2019-09-19 14:22:08
 */
@RestController
@AllArgsConstructor
@RequestMapping("/shop/withdrawCash")
@Tag(name = "商家提现申请信息接口")
public class ShopWithdrawCashController {

    private final ShopWithdrawCashService shopWithdrawCashService;
    private final ShopEmployeeService shopEmployeeService;
    private ShopDetailService shopDetailService;
    private SmsLogService smsLogService;


    @PostMapping("/apply")
    @PreAuthorize("@pms.hasPermission('shop:withdrawCash:apply')")
    @Operation(summary = "商家提交申请提现", description = "商家提交申请提现")
    public ServerResponseEntity<Void> apply(@RequestBody ShopWithdrawCash shopWithdrawCash) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        // 查询店长信息
        ShopEmployee shopEmployee = shopEmployeeService.getOne(Wrappers.lambdaQuery(ShopEmployee.class)
                .eq(ShopEmployee::getShopId, shopId).eq(ShopEmployee::getType, PositionType.ADMIN.value()));
        shopWithdrawCash.setUpdateTime(new Date());
        shopWithdrawCash.setCreateTime(new Date());
        shopWithdrawCash.setStatus(ShopWithdrawCashStatus.WAITAUDIT.value());
        shopWithdrawCash.setShopId(SecurityUtils.getShopUser().getShopId());
        shopWithdrawCashService.withdrawCash(shopWithdrawCash);
        return ServerResponseEntity.success();
    }

    @PostMapping("/allinpayApply")
    @Operation(summary = "商家提交通联申请提现", description = "通联独有")
    @PreAuthorize("@pms.hasPermission('shop:withdrawCash:apply')")
    public ServerResponseEntity<String> allinpayApply(@RequestBody AllinpayShopWithdrawCashDTO allinpayShopWithdrawCashDTO) {
        if (Objects.isNull(allinpayShopWithdrawCashDTO.getAmount()) || Objects.isNull(allinpayShopWithdrawCashDTO.getBankCardNo())) {
            // 提现金额和银行卡号不能为空
            throw new YamiShopBindException("yami.shop.withdraw.cash.exception.amountCardNoEmpty");
        }
        String bizOrderNo = shopWithdrawCashService.allinpayApply(allinpayShopWithdrawCashDTO, SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success(bizOrderNo);
    }

    @Operation(summary = "提现确认支付", description = "通联独有")
    @PostMapping("/confirmWithdrawPay")
    @PreAuthorize("@pms.hasPermission('shop:withdrawCash:apply')")
    public ServerResponseEntity<Void> confirmWithdrawPay(@RequestBody AllinpayShopWithdrawCashDTO allinpayShopWithdrawCashDTO) {
        if (Objects.isNull(allinpayShopWithdrawCashDTO.getBizOrderNo()) || Objects.isNull(allinpayShopWithdrawCashDTO.getVerificationCode())) {
            // 短信验证码和订单号不能为空
            throw new YamiShopBindException("yami.shop.withdraw.cash.exception.codeOrderNoEmpty");
        }
        shopWithdrawCashService.confirmWithdrawPay(allinpayShopWithdrawCashDTO, SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success();
    }

    @Operation(summary = "重新发送提现支付验证码", description = "通联独有")
    @PostMapping("/resendPaySms")
    @PreAuthorize("@pms.hasPermission('shop:withdrawCash:sendCode')")
    public ServerResponseEntity<Void> resendPaySms(@RequestBody AllinpayShopWithdrawCashDTO allinpayShopWithdrawCashDTO) {
        if (Objects.isNull(allinpayShopWithdrawCashDTO.getBizOrderNo())) {
            // 提现订单号不能为空
            throw new YamiShopBindException("yami.shop.withdraw.cash.exception.orderNoEmpty");
        }
        shopWithdrawCashService.resendPaySms(allinpayShopWithdrawCashDTO.getBizOrderNo(), SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success();
    }

    @GetMapping("/getShopInfo")
    @Operation(summary = "获取店铺钱包信息", description = "获取店铺钱包信息")
    @PreAuthorize("@pms.hasPermission('shop:withdrawCash:info')")
    public ServerResponseEntity<ShopWithdrawCash> getShopInfo() {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        ShopWithdrawCash shopWithdrawCash = shopWithdrawCashService.getShopWithdrawCashByShopId(shopId);
        return ServerResponseEntity.success(shopWithdrawCash);
    }

    /**
     * 分页查询
     *
     * @param page             分页对象
     * @param shopWithdrawCash 商家提现申请信息
     * @return 分页数据
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询商家提现信息", description = "分页查询商家提现信息")
    @PreAuthorize("@pms.hasPermission('shop:withdrawCash:page')")
    public ServerResponseEntity<IPage<ShopWithdrawCash>> getShopWithdrawCashPage(PageParam<ShopWithdrawCash> page, ShopWithdrawCash shopWithdrawCash) {
        shopWithdrawCash.setShopId(SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success(shopWithdrawCashService.pageShopWithdrawCash(page, shopWithdrawCash));
    }

    @GetMapping("/getWithdrawCash")
    public ServerResponseEntity<ShopWithdrawCashConfigDto> getWithdrawCashConfig(){
        return ServerResponseEntity.success(shopWithdrawCashService.getConfig());
    }
}
