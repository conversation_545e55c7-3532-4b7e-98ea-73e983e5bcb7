package com.yami.shop.bean.vo.allinpay;

import lombok.Data;

import java.io.Serial;

/**
 * 收入明细
 *
 * <AUTHOR>
 */
@Data
public class SplitRuleVO {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 用户id
     */
    private String bizUserId;
    /**
     * 如果向会员分账，不上送，默认为唯一托管账户集。
     * 如果向平台分账，请填写平台的标准账户集编号（不支持100003-准备金额度账户集）。详细
     */
    private String accountSetNo;
    /**
     * 非必填，一年前的代收订单必须上送，yyyy-MM-dd 精确到天
     */
    private String bizOrderCreateDate;
    /**
     * 金额，单位：分；部分代付时，可以少于或等于托管代收订单金额
     */
    private Long amount;
    /**
     * 手续费
     */
    private Long fee;
    /**
     * 备注，非必填
     */
    private Long remark;
    /**
     * 订单号
     */
    private String orderNumber;

}
