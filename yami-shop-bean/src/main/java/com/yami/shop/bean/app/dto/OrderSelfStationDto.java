package com.yami.shop.bean.app.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yami.shop.bean.vo.SameCityVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 自提订单信息表
 *
 * <AUTHOR>
 * @date 2020-04-23 15:18:29
 */
@Data
public class OrderSelfStationDto{

    /**
     *
     */
    @Schema(description = "自提信息id" )
    private Long id;

    private Long orderId;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "自提点id" )
    private Long stationId;

    @Schema(description = "自提点名称" )
    private String stationName;

    @Schema(description = "自提点图片" )
    private String pic;

    @Schema(description = "自提人的手机" )
    private String stationUserMobile;

    @Schema(description = "自提人的名字" )
    private String stationUserName;

    @Schema(description = "自提时间(用户下单时选择)" )
    private String stationTime;

    @Schema(description = "提货码" )
    private String stationCode;

    @Schema(description = "自提点的地址" )
    private String stationAddress;

    @Schema(description = "自提点的联系电话" )
    private String stationPhone;

    @Schema(description = "经度" )
    private Double lng;

    @Schema(description = "纬度" )
    private Double lat;

    @Schema(description = "距离" )
    private double distance;

    @Schema(description = "自提点时间日期数据" )
    private String timeDate;

    @Schema(description = "自提门店状态 0：没有门店 1:门店没有库存 2:库存充足 ")
    private Integer stationStockStatus;

    @Schema(description = "库存模式 1共享总部库存 2独立销售库存" )
    private Integer stockMode;

    @Schema(description = "自提门店用途是否支持自提（0：不支持，1：支持）")
    private Integer selfPickup;

    @Schema(description = "自提门店用途是否支持同城配送（0：不支持，1：支持）")
    private Integer sameCityDelivery;

    @TableField(exist = false)
    private SameCityVO sameCityVO;
}
