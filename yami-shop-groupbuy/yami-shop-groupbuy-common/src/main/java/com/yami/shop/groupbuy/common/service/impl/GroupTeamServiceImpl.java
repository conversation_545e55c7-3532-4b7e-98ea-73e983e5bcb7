package com.yami.shop.groupbuy.common.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yami.shop.bean.dto.OrderRefundDto;
import com.yami.shop.bean.enums.*;
import com.yami.shop.bean.model.*;
import com.yami.shop.bean.vo.VoucherStockVO;
import com.yami.shop.common.constants.LuaOperateEnum;
import com.yami.shop.common.constants.SegmentIdKey;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupTeamDto;
import com.yami.shop.groupbuy.common.api.dto.ApiJoinGroupTeamDto;
import com.yami.shop.groupbuy.common.dao.GroupActivityMapper;
import com.yami.shop.groupbuy.common.dao.GroupTeamMapper;
import com.yami.shop.groupbuy.common.dto.GroupOrderDTO;
import com.yami.shop.groupbuy.common.dto.GroupRefundOrder;
import com.yami.shop.groupbuy.common.dto.GroupTeamDto;
import com.yami.shop.groupbuy.common.enums.GroupOrderStatusEnum;
import com.yami.shop.groupbuy.common.enums.TeamStatusEnum;
import com.yami.shop.groupbuy.common.model.GroupOrder;
import com.yami.shop.groupbuy.common.model.GroupTeam;
import com.yami.shop.groupbuy.common.service.GroupOrderService;
import com.yami.shop.groupbuy.common.service.GroupTeamService;
import com.yami.shop.manager.impl.StockManager;
import com.yami.shop.service.*;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 拼团团队表
 *
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
@Service
@AllArgsConstructor
public class GroupTeamServiceImpl extends ServiceImpl<GroupTeamMapper, GroupTeam> implements GroupTeamService {

    private final GroupTeamMapper groupTeamMapper;
    private final GroupActivityMapper groupActivityMapper;
    private final GroupOrderService groupOrderService;
    private final ProductService productService;
    private final OrderRefundService orderRefundService;
    private final NotifyTemplateService notifyTemplateService;
    private final OrderService orderService;
    private final SegmentService segmentService;
    private final VoucherItemService voucherItemService;
    private final StockManager stockManager;
    private final OrderItemService orderItemService;
    private final VoucherService voucherService;


    @Override
    public IPage<GroupTeamDto> getPage(Page page, GroupTeamDto groupTeamDto) {
        IPage<GroupTeamDto> groupPage = groupTeamMapper.getPage(page, groupTeamDto);
        if (CollUtil.isEmpty(groupPage.getRecords())) {
            return groupPage;
        }
        List<Long> grouTeamIds = groupPage.getRecords().stream().map(GroupTeamDto::getGroupTeamId).toList();
        return groupPage;
    }

    @Override
    public IPage<GroupOrderDTO> getGroupOrderPage(PageParam<GroupOrder> page, GroupOrder groupOrder) {
        return groupTeamMapper.getGroupOrderPage(page, groupOrder);
    }
    @Override
    public ApiGroupTeamDto getApiGroupTeamDto(Long groupTeamId) {
        return groupTeamMapper.getApiGroupTeamDtoByTeamId(groupTeamId);
    }
    @Override
    public ApiJoinGroupTeamDto getJoinGroupTeamHasMaxNum(Long groupActivityId) {
        return groupTeamMapper.getJoinGroupTeamHasMaxNum(groupActivityId);
    }
    @Override
    public List<GroupTeam> getNotGroupTeam() {
        return groupTeamMapper.getNotGroupTeamByNowDate(new Date());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateNotGroupTeam(GroupTeam groupTeam) {
        Date nowDate = new Date();
        // 机器人列表
        List<GroupOrder> robot = new ArrayList<>();
//        // 拼团成功的活动商品列表（机器人）
//        List<GroupProd> groupProdList = new ArrayList<>();

        // 修改订单状态(拼团中的状态定时任务修改不了及防止待成团订单重新进行支付)
        List<Order> changeStatusOrderList = new ArrayList<>();

        // 需要还原库存的订单
        List<Order> returnStockOrders = new ArrayList<>();

        // 未支付的拼团，不需要进行退款的操作
        if (groupTeam.getStatus().equals(TeamStatusEnum.WAITING_GROUP.value())) {
            groupTeam.setStatus(TeamStatusEnum.FAIL.value());
            // 获取拼团失败要取消的订单
            List<Order> teamOrderList = groupTeamMapper.getOrders(groupTeam.getGroupTeamId(),null);
            if(CollectionUtils.isNotEmpty(teamOrderList)){
                // 需要还原库存的订单
                returnStockOrders.addAll(teamOrderList);
                for (Order order : teamOrderList) {
                    order.setStatus(OrderStatus.CLOSE.value());
                    order.setCancelTime(nowDate);
                    order.setUpdateTime(nowDate);
                    changeStatusOrderList.add(order);
                }
            }
        }

        // 如果未成团，有机器人可以成团，不需要进行退款的操作
        if (groupTeam.getStatus().equals(TeamStatusEnum.IN_GROUP.value()) && groupTeam.getHasRobot() == 1) {
            handleWaitingGroupOrder(nowDate, groupTeam, robot, changeStatusOrderList, returnStockOrders);
        }

        // 如果在拼团中订单且没有模拟成团则需要退款
        if (groupTeam.getStatus().equals(TeamStatusEnum.IN_GROUP.value()) && groupTeam.getHasRobot() == 0) {
            // 判断处理一下，如果是支付成功但拼团失败的关联卡券的订单，需要还原卡券库存
            List<Order> teamOrderList = groupTeamMapper.getOrders(groupTeam.getGroupTeamId(),null);
            List<Order> checkOrder = teamOrderList.stream().filter(order -> Objects.equals(order.getOrderMold(), 3) && Objects.equals(order.getIsBindVoucher(), 1)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(checkOrder)) {
                handGroupVoucherOrder(checkOrder);
            }
            handleRefundGroupOrder(groupTeam, changeStatusOrderList);

        }

        // 更新拼团团队信息
        groupTeam.setUpdateTime(nowDate);
        updateById(groupTeam);

        // 取消订单，还原库存
        if (CollectionUtils.isNotEmpty(returnStockOrders)) {
            orderService.cancelOrders(returnStockOrders);
        }
        // 批量保存机器人
        if (!robot.isEmpty()) {
            groupOrderService.saveBatch(robot);
        }
        // 更新订单状态
        if (!changeStatusOrderList.isEmpty()) {
            orderService.saveOrUpdateBatch(changeStatusOrderList);
        }

        // 批量清除商品拼团活动ID
        List<Product> products = groupActivityMapper.getActivityFinishProduct(nowDate);
        for (Product product : products) {
            product.setActivityId(0L);
            product.setProdType(ProdType.PROD_TYPE_NORMAL.value());
        }
        if (!products.isEmpty()) {
            productService.saveOrUpdateBatch(products);
        }
    }

    /**
     * 如果未成团，有机器人可以成团，不需要进行退款的操作
     * @param nowDate 当前时间
     * @param groupTeam 团购团队
     * @param robot 参团机器人
     * @param changeStatusOrderList 需要改变状态的订单列表
     * @param returnStockOrders 需要还原库存的订单列表
     */
    private void handleWaitingGroupOrder(Date nowDate, GroupTeam groupTeam, List<GroupOrder> robot, List<Order> changeStatusOrderList, List<Order> returnStockOrders) {

        // 模拟参团操作（添加机器人）
        for (int i = 0; i < groupTeam.getGroupNumber() - groupTeam.getJoinNum(); i++) {
            GroupOrder groupOrder = new GroupOrder();
            groupOrder.setShopId(groupTeam.getShopId());
            groupOrder.setGroupTeamId(groupTeam.getGroupTeamId());
            groupOrder.setUserId("0");
            groupOrder.setIdentityType(0);
            groupOrder.setStatus(1);
            groupOrder.setCreateTime(nowDate);
            robot.add(groupOrder);
        }
        // 更新拼团团队订单
        groupTeam.setJoinNum(groupTeam.getGroupNumber());
        groupTeam.setStatus(TeamStatusEnum.SUCCESS.value());

        // 更新所有的已支付订单状态
        List<Order> teamOrderList = groupTeamMapper.getOrders(groupTeam.getGroupTeamId(),1);
        for (Order order : teamOrderList) {
            // 虚拟订单成团的订单状态为待发货
            if(Objects.equals(order.getOrderMold(),1) || Objects.equals(order.getOrderMold(),3)){
                order.setStatus(OrderStatus.CONSIGNMENT.value());
                order.setDvyTime(nowDate);
                order.setDvyType(DvyType.NOT_DELIVERY.value());
            }else {
                order.setStatus(OrderStatus.PADYED.value());
            }
            changeStatusOrderList.add(order);
            order.setUpdateTime(nowDate);
            //商家开启模拟成团后，机器人填充的成团队伍也要给用户发送拼团成功消息提醒
            notifyTemplateService.sendNotifyOfGroupStart(order, SendType.GROUP_SUCCESS);
        }
        // 获取未支付要取消的订单
        List<Order> unPayOrderList = groupTeamMapper.getOrders(groupTeam.getGroupTeamId(),0);
        if(CollectionUtils.isNotEmpty(unPayOrderList)){
            // 需要还原库存的订单
            returnStockOrders.addAll(unPayOrderList);
            for (Order order : unPayOrderList) {
                order.setStatus(OrderStatus.CLOSE.value());
                order.setUpdateTime(nowDate);
                order.setCancelTime(nowDate);
                changeStatusOrderList.add(order);
            }
        }
    }

    @Override
    public List<GroupTeam> getNotGroupTeamsByActivityId(Long groupActivityId) {
        return groupTeamMapper.getNotGroupTeamsByActivityId(groupActivityId);
    }

    @Override
    public List<GroupTeam> getNotGroupTeamByProdId(Long prodId) {
        return groupTeamMapper.getNotGroupTeamByProdId(prodId);
    }

    @Override
    public List<ApiGroupTeamDto> listJoinGroup(Long groupActivityId, Integer showSize) {
        return groupTeamMapper.listByJoinGroupAndNowDate(groupActivityId, new Date(), showSize);
    }

    /**
     * 并生成退款单并保存到数据库，且获组装退款dto
     * @param groupRefundOrder 团购退款订单数据
     * @return 团购退款订单列表
     */
    private List<OrderRefundDto> saveRefundOrderAndGetOrderRefundDtoByGroupTeamId(List<GroupRefundOrder> groupRefundOrder) {
        // 退款单列表
        List<OrderRefund> orderRefundList = new ArrayList<>();
        // 退款数据列表
        List<OrderRefundDto> orderRefundDtoList = new ArrayList<>();

        for (GroupRefundOrder refundOrder : groupRefundOrder) {
            // 生成退款单信息
            OrderRefund orderRefund = new OrderRefund();
            orderRefund.setRefundSn(String.valueOf(segmentService.getDateFormatSegmentId(SegmentIdKey.REFUND)));
            orderRefund.setShopId(refundOrder.getShopId());
            orderRefund.setUserId(refundOrder.getUserId());
            orderRefund.setOrderId(refundOrder.getOrderId());
            orderRefund.setRefundType(RefundType.ALL.value());
            orderRefund.setRefundAmount(refundOrder.getActualTotal());
            // 计算平台退款金额（退款时将这部分钱退回给平台，所以商家要扣除从平台这里获取的金额）
            orderRefund.setPlatformRefundAmount(refundOrder.getPlatformAmount());
            orderRefund.setPlatformRefundCommission(refundOrder.getPlatformCommission());

            orderRefund.setApplyType(1);
            orderRefund.setIsReceiver(false);
            orderRefund.setBuyerReason(BuyerReasonType.GROUP_FAILED.value()+"");
            orderRefund.setBuyerDesc("拼团失败：系统自动退款");
            orderRefund.setReturnMoneySts(ReturnMoneyStsType.SUCCESS.value());
            orderRefund.setSellerMsg("拼团失败：系统自动退款");
            orderRefund.setApplyTime(new Date());
            orderRefund.setHandelTime(new Date());
            orderRefund.setUpdateTime(new Date());
            orderRefundList.add(orderRefund);

            // 生成退款数据
            OrderRefundDto orderRefundDto = BeanUtil.map(orderRefund, OrderRefundDto.class);
            orderRefundDto.setOrderNumber(refundOrder.getOrderNumber());
            orderRefundDto.setOrderAmount(refundOrder.getActualTotal());
            orderRefundDto.setSettlementId(refundOrder.getSettlementId());
            orderRefundDto.setOrderPayNo(refundOrder.getOrderPayNo());
            orderRefundDto.setDistributionTotalAmount(0.0);
            orderRefundDtoList.add(orderRefundDto);
        }

        if (!orderRefundList.isEmpty()) {
            orderRefundService.saveBatch(orderRefundList);
        }
        return orderRefundDtoList;
    }

    /**
     * 如果在拼团中订单且没有模拟成团则需要退款
     * @param groupTeam 团购团队
     * @param changeStatusOrderList 需要改变状态的订单列表
     */
    private void handleRefundGroupOrder(GroupTeam groupTeam, List<Order> changeStatusOrderList) {
        // 更新拼团团队订单
        groupTeam.setStatus(TeamStatusEnum.FAIL.value());

        // 查询未成团的订单列表
        List<GroupRefundOrder> groupRefundOrders = groupTeamMapper.getJoinNotGroupTeamOrder(groupTeam.getGroupTeamId());
        // 需要退款订单
        List<GroupRefundOrder> needRefundOrder = new ArrayList<>();
        // 无需退款订单
        List<GroupOrder> noNeedGroupOrder = new ArrayList<>();

        for (GroupRefundOrder groupRefundOrder : groupRefundOrders) {
            if (groupRefundOrder.getPayStatus() == 1) {
                needRefundOrder.add(groupRefundOrder);
            } else {
                GroupOrder groupOrder = new GroupOrder();
                groupOrder.setGroupOrderId(groupRefundOrder.getGroupOrderId());
                groupOrder.setStatus(GroupOrderStatusEnum.FAIL.value());
                noNeedGroupOrder.add(groupOrder);
            }

            // 修改订单退款状态
            Order order = new Order();
            order.setOrderId(groupRefundOrder.getOrderId());
            order.setStatus(OrderStatus.CLOSE.value());
            order.setCancelTime(new Date());
            order.setUpdateTime(new Date());
            order.setRefundStatus(RefundStatusEnum.SUCCEED.value());
            changeStatusOrderList.add(order);
        }

        // 将订单变为失效状态
        if (CollectionUtil.isNotEmpty(noNeedGroupOrder)) {
            groupOrderService.updateBatchById(noNeedGroupOrder);
        }


        List<OrderRefundDto> orderRefundDtos = saveRefundOrderAndGetOrderRefundDtoByGroupTeamId(needRefundOrder);
        // 真正的退款操作
        orderRefundService.systemAutoRefundBatch(orderRefundDtos);

        // 消息推送-拼团失败
        List<String> orderNumbers = new ArrayList<>();
        orderRefundDtos.forEach(orderRefundDto ->  orderNumbers.add(orderRefundDto.getOrderNumber()));
        List<Order> orderList = orderService.list(new LambdaQueryWrapper<Order>().in(Order::getOrderNumber, orderNumbers));
        for (Order order : orderList) {
            notifyTemplateService.sendNotifyOfGroupStart(order, SendType.GROUP_FAIL);
        }
    }

    private void handGroupVoucherOrder (List<Order> orders) {
        Map<String, Order> orderMap = orders.stream().collect(Collectors.toMap(Order::getOrderNumber, order -> order));
        List<String> orderNumbers = orders.stream().map(Order::getOrderNumber).collect(Collectors.toList());
        List<VoucherItem> voucherItems = voucherItemService.list(new LambdaUpdateWrapper<VoucherItem>()
                .in(VoucherItem::getOrderNumber, orderNumbers));
        List<Long> voucherIds = voucherItems.stream().map(VoucherItem::getVoucherId).collect(Collectors.toList());
        List<Voucher> vouchers = voucherService.list(new LambdaQueryWrapper<>(Voucher.class).in(Voucher::getVoucherId, voucherIds));
        Map<Long, Integer> voucherStatusMap = vouchers.stream().collect(Collectors.toMap(Voucher::getVoucherId, Voucher::getStatus));
        // 需要更新卡券状态的详情集合
        List<Long> updateList = new ArrayList<>();
        List<Long> orderItemIds = new ArrayList<>();
        List<VoucherStockVO> voucherStocks = new ArrayList<>();
        for (VoucherItem item : voucherItems) {
            OrderItem orderItem = orderMap.get(item.getOrderNumber()).getOrderItems().get(0);
            if (!Objects.equals(item.getStatus(), VoucherItemStatus.DELETE.value()) && !Objects.equals(voucherStatusMap.get(item.getVoucherId()), -1)) {
                VoucherStockVO voucherStockVO = new VoucherStockVO(item.getVoucherId(), 1, orderItem.getStockPointId(), orderItem.getStockPointType());
                voucherStocks.add(voucherStockVO);
                updateList.add(item.getVoucherItemId());
                orderItemIds.add(orderItem.getOrderItemId());
            }
        }
        // 更新卡券库存
        if (CollectionUtils.isNotEmpty(updateList)) {
            // 更新关联卡券商品状态
            voucherItemService.update(new LambdaUpdateWrapper<VoucherItem>()
                    .set(VoucherItem::getStatus, VoucherItemStatus.NOTENABLED.value())
                    .set(VoucherItem::getOrderNumber, null)
                    .in(VoucherItem::getVoucherItemId, updateList));
            stockManager.voucherStock(voucherStocks, LuaOperateEnum.SKU_ADD);
            // 修改详情关联，让后续的商品库存能正常回退
            orderItemService.update(new LambdaUpdateWrapper<>(OrderItem.class)
                    .set(OrderItem::getVoucherId, null)
                    .in(OrderItem::getOrderItemId, orderItemIds));
        }
    }

}
