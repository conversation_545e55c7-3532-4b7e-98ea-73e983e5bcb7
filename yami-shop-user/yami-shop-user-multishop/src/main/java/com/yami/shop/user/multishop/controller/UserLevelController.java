package com.yami.shop.user.multishop.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yami.shop.bean.dto.UserLevelRecruitStatusDTO;
import com.yami.shop.bean.param.UserUpdateParam;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.user.common.dto.UserLevelDto;
import com.yami.shop.user.common.model.UserLevel;
import com.yami.shop.user.common.service.UserLevelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 会员等级
 *
 * <AUTHOR>
 * @date 2020-02-26 16:03:14
 */
@Tag(name = "会员等级")
@RestController
@AllArgsConstructor
@RequestMapping("/user/userLevel" )
public class UserLevelController {
    private final UserLevelService userLevelService;

    @SysLog("查看会员等级" )
    @GetMapping("/list" )
    @Operation(summary = "获取用户等级列表", description =  "获取用户等级列表")
    @PreAuthorize("@pms.hasPermission('user:userLevel:list')")
    public ServerResponseEntity<List<UserLevelDto>> listUserLevel(Integer userLevelType) {
        Long shopId = AuthUserContext.getShopId();
        List<UserLevelDto> userLevelDtos = userLevelService.listLevel(userLevelType, shopId);
        return ServerResponseEntity.success(userLevelDtos);
    }

    @GetMapping("/info/{id}" )
    @Operation(summary = "查询会员等级", description =  "通过id查询会员等级")
    @PreAuthorize("@pms.hasPermission('user:userLevel:info')")
    @Parameter(name = "id", description = "会员等级id")
    public ServerResponseEntity<UserLevel> getById(@PathVariable("id") Long id) {
        UserLevel userLevel = userLevelService.getUserLevelById(id);
        return ServerResponseEntity.success(userLevel);
    }

    @SysLog("新增会员等级" )
    @PostMapping
    @Operation(summary = "新增会员等级", description =  "新增会员等级")
    @PreAuthorize("@pms.hasPermission('user:userLevel:save')" )
    public ServerResponseEntity<Void> save(@RequestBody @Valid UserLevel userLevel) {
        Long shopId = AuthUserContext.getShopId();
        userLevel.setShopId(shopId);
        userLevelService.saveShopLevel(userLevel);
        return ServerResponseEntity.success();
    }

    @SysLog("修改会员等级" )
    @PutMapping
    @Operation(summary = "修改会员等级", description =  "修改会员等级")
    @PreAuthorize("@pms.hasPermission('user:userLevel:update')" )
    public ServerResponseEntity<Void> updateById(@RequestBody @Valid UserLevel userLevel) {
        Long shopId = AuthUserContext.getShopId();
        userLevel.setShopId(shopId);
        userLevelService.updateShopLevel(userLevel);
        return ServerResponseEntity.success();
    }

    @SysLog("删除会员等级" )
    @DeleteMapping
    @Operation(summary = "删除会员等级", description =  "删除会员等级")
    @PreAuthorize("@pms.hasPermission('user:userLevel:delete')" )
    public ServerResponseEntity<Void> removeById(@RequestBody @Valid UserLevel userLevel) {
        Long shopId = AuthUserContext.getShopId();
        userLevel.setShopId(shopId);
        userLevelService.deleteShopLevel(userLevel);
        return ServerResponseEntity.success();
    }

    @PutMapping("/updateUserLevel")
    @Operation(summary = "更新用户的免费会员等级", description =  "更新用户的免费会员等级")
    @PreAuthorize("@pms.hasPermission('user:userLevel:updateUserLevel')")
    public ServerResponseEntity<Void> updateUserLevel() {
        Long shopId = AuthUserContext.getShopId();
        userLevelService.refreshShopFreeLevel(shopId);
        return ServerResponseEntity.success();
    }

    @PutMapping("/updateBatchGrowth")
    @Operation(summary = "批量修改成长值", description =  "批量修改成长值")
    @PreAuthorize("@pms.hasPermission('user:userLevel:updateBatchGrowth')")
    public ServerResponseEntity<Void> updateBatchGrowth(@RequestBody UserUpdateParam updateParam) {
        Long shopId = AuthUserContext.getShopId();
        userLevelService.updateBatchShopGrowth(updateParam, shopId);
        return ServerResponseEntity.success();
    }

    @PutMapping("/updateRecruitStatus")
    @Operation(summary = "更新用户等级招募状态", description =  "更新用户等级招募状态")
    @PreAuthorize("@pms.hasPermission('user:userLevel:updateRecruitStatus')")
    public ServerResponseEntity<Void> updateRecruitStatus(@RequestBody UserLevelRecruitStatusDTO recruitStatusDTO) {
        userLevelService.update(new LambdaUpdateWrapper<UserLevel>()
                .set(UserLevel::getRecruitStatus, recruitStatusDTO.getRecruitStatus())
                .eq(UserLevel::getId, recruitStatusDTO.getLevelId()));
        return ServerResponseEntity.success();
    }
}
