package com.yami.shop.distribution.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.model.DistributionUserGroup;
import com.yami.shop.distribution.common.service.DistributionUserGroupService;
import com.yami.shop.security.platform.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> on 2019/04/01.
 */
@RestController
@RequestMapping("/distribution/distributionUserGroup")
@Tag(name = "平台端分销员分组接口")
@AllArgsConstructor
public class DistributionUserGroupController {

    private final DistributionUserGroupService distributionUserGroupService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserGroup:page')")
    @Operation(summary = "分页获取分销员分组信息列表")
    public ServerResponseEntity<IPage<DistributionUserGroup>> page(DistributionUserGroup distributionUserGroup,
                                                             PageParam<DistributionUserGroup> page) {
        distributionUserGroup.setShopId(Constant.PLATFORM_SHOP_ID);
        IPage<DistributionUserGroup> list = distributionUserGroupService.distributionUserGroupsAndSysUserPage(page, distributionUserGroup);
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/info/{id}")
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserGroup:info')")
    @Operation(summary = "根据id获取分销员分组信息")
    @Parameter(name = "id", description = "分销员分组id" )
    public ServerResponseEntity<DistributionUserGroup> info(@PathVariable("id") Long id) {
        DistributionUserGroup distributionUserGroup = distributionUserGroupService.getById(id);
        return ServerResponseEntity.success(distributionUserGroup);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserGroup:save')")
    @Operation(summary = "保存分销员分组信息")
    public ServerResponseEntity<Void> save(@RequestBody @Valid DistributionUserGroup distributionUserGroup) {
        distributionUserGroup.setShopId(Constant.PLATFORM_SHOP_ID);
        distributionUserGroup.setUpdateTime(new Date());
        distributionUserGroup.setModifier(SecurityUtils.getSysUser().getUserId());
        distributionUserGroupService.save(distributionUserGroup);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserGroup:update')")
    @Operation(summary = "修改分销员分组信息")
    public ServerResponseEntity<Void> update(@RequestBody @Valid DistributionUserGroup distributionUserGroup) {
        distributionUserGroup.setUpdateTime(new Date());
        distributionUserGroup.setModifier(SecurityUtils.getSysUser().getUserId());
        distributionUserGroupService.updateById(distributionUserGroup);
        return ServerResponseEntity.success();
    }

    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('distribution:distributionUserGroup:delete')")
    @Operation(summary = "根据分销员分组id列表批量删除分销员分组信息")
    @Parameter(name = "ids", description = "分销员分组id" )
    public ServerResponseEntity<Void> delete(@RequestBody List<Long> ids) {
        distributionUserGroupService.removeByIds(ids);
        return ServerResponseEntity.success();
    }
}
