package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-11-30
 */
@Data
public class AllotSpuVO {

    @Schema(description = "商品spuId")
    private Long spuId;

    @Schema(description = "spu名称多语言")
    private List<ProdLangVO> spuLangVOList;

    @Schema(description = "商品skuId")
    private Long skuId;

    @Schema(description = "商品编码")
    private String partyCode;

    @Schema(description = "sku名称多语言")
    private List<SkuLangVO> skuLangVOList;

    @Schema(description = "商品主图")
    private String mainImgUrl;

    @Schema(description = "商品规格图")
    private String imgUrl;

    @Schema(description = "入库仓库存")
    private Integer inWarehouseStock;

    @Schema(description = "出库仓库存")
    private Integer outWarehouseStock;

    @Schema(description = "调拨数量")
    private Integer allotCount;
}
