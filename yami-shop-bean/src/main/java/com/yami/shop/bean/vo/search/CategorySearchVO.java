package com.yami.shop.bean.vo.search;

import com.yami.shop.bean.bo.CategoryLangBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 分类信息VO
 * <AUTHOR>
 */
@Data
public class CategorySearchVO{
    private static final long serialVersionUID = 1L;

    @Schema(description = "分类id" )
    private Long categoryId;

    @Schema(description = "分类名称" )
    private String name;

    /**
     * 分类名称列表
     */
    private List<CategoryLangBO> categoryLangList;
}
