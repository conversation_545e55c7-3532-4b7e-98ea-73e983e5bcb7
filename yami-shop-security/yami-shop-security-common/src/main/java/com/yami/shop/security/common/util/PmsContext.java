package com.yami.shop.security.common.util;


import com.alibaba.ttl.TransmittableThreadLocal;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/7/16
 */
public class PmsContext {

    private static final ThreadLocal<Set<String>> PMS_HOLDER = new TransmittableThreadLocal<>();

    public static Set<String> get() {
        return PMS_HOLDER.get();
    }

    public static void set(Set<String> prems) {
        PMS_HOLDER.set(prems);
    }
    public static void clean() {
        if (PMS_HOLDER.get() != null) {
            PMS_HOLDER.remove();
        }
    }
}
