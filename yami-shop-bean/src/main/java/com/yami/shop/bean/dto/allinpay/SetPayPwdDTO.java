package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class SetPayPwdDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "跳转页面类型(1:H5页面, 2:小程序页面)，不传默认H5")
    private Long jumpPageType;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "身份证号码")
    private String identityNo;

    @Schema(description = "密码设置/修改/重置后跳转地址")
    private String jumpUrl;

    @Schema(description = "密码重置出错跳转地址")
    private String errorJumpUrl;

    public Long getJumpPageType() {
        return jumpPageType;
    }

    public void setJumpPageType(Long jumpPageType) {
        this.jumpPageType = jumpPageType;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getIdentityNo() {
        return identityNo;
    }

    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getErrorJumpUrl() {
        return errorJumpUrl;
    }

    public void setErrorJumpUrl(String errorJumpUrl) {
        this.errorJumpUrl = errorJumpUrl;
    }

    @Override
    public String toString() {
        return "SetPayPwdDTO{" +
                "jumpPageType=" + jumpPageType +
                ", phone='" + phone + '\'' +
                ", name='" + name + '\'' +
                ", identityNo='" + identityNo + '\'' +
                ", jumpUrl='" + jumpUrl + '\'' +
                ", errorJumpUrl='" + errorJumpUrl + '\'' +
                '}';
    }
}
