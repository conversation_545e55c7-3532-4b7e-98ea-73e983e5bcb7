package com.yami.shop.bean.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 套装商品项
 *
 * <AUTHOR>
 * @date 2021-11-08 13:29:16
 */
@Data
public class GiveawayProdVO implements Serializable{
    private static final long serialVersionUID = 1L;

    @Schema(description = "赠品商品项id" )
    private Long giveawayProdId;

    @Schema(description = "赠品id" )
    private Long giveawayId;

    @Schema(description = "商品id" )
    @NotNull(message = "商品id不能为空")
    private Long prodId;

    @Schema(description = "skuId" )
    @NotNull(message = "skuId不能为空")
    private Long skuId;

    @Schema(description = "赠送数量" )
    @NotNull(message = "赠送数量不能为空")
    private Integer giveawayNum;

    @Schema(description = "退货价" )
    @NotNull(message = "退货价不能为空")
    private Double refundPrice;

    @Schema(description = "状态 -1：删除 1：正常" )
    private Integer status;

    @Schema(description = "商品名称" )
    @TableField(exist = false)
    private String prodName;

    @Schema(description = "商品图片" )
    @TableField(exist = false)
    private String pic;

    @Schema(description = "sku名称" )
    private String skuName;

    @Schema(description = "销售属性组合字符串 格式是p1:v1;p2:v2" )
    private String properties;

    @Schema(description = "商品现价" )
    private Double price;

    @TableField(exist = false)
    @Schema(description = "sku库存" )
    private Integer skuStock;

    @TableField(exist = false)
    @Schema(description = "配送方式" )
    private String delieveryMode;
}
