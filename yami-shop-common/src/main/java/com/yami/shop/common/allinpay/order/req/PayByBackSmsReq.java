package com.yami.shop.common.allinpay.order.req;

/**
 * 确认支付请求 后台+短信验证码
 *
 * <AUTHOR>
 */
public class PayByBackSmsReq extends AbstractOrderReq {

    public static final String METHOD_NAME = "payByBackSMS";

    /**
     * 商户系统用户标识，商户系统中唯一编号。
     */
    private String bizUserId;

    /**
     * 订单申请的商户订单号（支付订单）
     */
    private String bizOrderNo;

    /**
     * 交易编号
     */
    private String tradeNo;

    /**
     * 短信验证码
     */
    private String verificationCode;

    /**
     * ip地址
     */
    private String consumerIp;

    public PayByBackSmsReq() {
    }

    public PayByBackSmsReq(String bizUserId, String bizOrderNo, String verificationCode) {
        this.bizUserId = bizUserId;
        this.bizOrderNo = bizOrderNo;
        this.verificationCode = verificationCode;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    @Override
    public String getBizUserId() {
        return bizUserId;
    }

    @Override
    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    public String getConsumerIp() {
        return consumerIp;
    }

    public void setConsumerIp(String consumerIp) {
        this.consumerIp = consumerIp;
    }

    @Override
    public String toString() {
        return "PayByBackSMSReq{" +
                "bizUserId='" + bizUserId + '\'' +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                ", tradeNo='" + tradeNo + '\'' +
                ", verificationCode='" + verificationCode + '\'' +
                ", consumerIp='" + consumerIp + '\'' +
                '}';
    }
}
