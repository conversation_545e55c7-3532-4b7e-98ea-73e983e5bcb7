package com.yami.shop.common.allinpay.member.req;

import java.io.Serial;
import java.io.Serializable;
/**
 * <AUTHOR>
 */
public class UpdateCompanyInfo extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "updateCompanyInfo";

    /**
     * 请求流水号 全局唯一
     */
    private String reqsn;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 法人姓名
     */
    private String legalName;
    /**
     * 法人证件类型
     */
    private Integer identityType;
    /**
     * 法人证件号码
     */
    private String legalIds;
    /**
     * 法人手机号
     */
    private String legalPhone;
    /**
     * 修改结果通知地址
     */
    private String backUrl;

    public String getReqsn() {
        return reqsn;
    }

    public void setReqsn(String reqsn) {
        this.reqsn = reqsn;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    public Integer getIdentityType() {
        return identityType;
    }

    public void setIdentityType(Integer identityType) {
        this.identityType = identityType;
    }

    public String getLegalIds() {
        return legalIds;
    }

    public void setLegalIds(String legalIds) {
        this.legalIds = legalIds;
    }

    public String getLegalPhone() {
        return legalPhone;
    }

    public void setLegalPhone(String legalPhone) {
        this.legalPhone = legalPhone;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }
}
