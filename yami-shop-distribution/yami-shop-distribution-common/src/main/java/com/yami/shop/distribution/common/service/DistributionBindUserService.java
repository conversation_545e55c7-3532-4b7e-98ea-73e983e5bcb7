package com.yami.shop.distribution.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.distribution.common.dto.BindUserInfoDto;
import com.yami.shop.distribution.common.model.DistributionUser;

/**
 * <AUTHOR>
 */
public interface DistributionBindUserService {

    /**
     * 根据分享人的卡号，判断该用户是否能与该分享人进行绑定
     * @param shareUser 分享人
     * @param userId
     * @param type 0 扫码 1 下单
     * @return
     */
    ServerResponseEntity bindDistribution(DistributionUser shareUser, String userId, int type);


    /**
     * 获取绑定用户的列表
     * @param page 分页信息
     * @param shopId 店铺id
     * @param userId 用户id
     * @return 绑定用户的列表
     */
    IPage<BindUserInfoDto> bindUserList(Page page, Long shopId, String userId);

    /**
     * 邀请好友成为分销员绑定用户关系
     * @param shareDistributionId 邀请分销员id
     * @param distributionId    审核通过分销员id
     */
    void bindUser(Long shareDistributionId, Long distributionId);

}
