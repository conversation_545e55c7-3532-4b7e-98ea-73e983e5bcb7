package com.yami.shop.security.common.listener;

import com.yami.shop.bean.event.allinpay.AllinpayInitEvent;
import com.yami.shop.service.ShopDetailService;
import lombok.AllArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class AllinpayShopInitListener {

    private final ShopDetailService shopDetailService;

    /**
     * 通联用户初始化
     * @return
     */
    @EventListener(AllinpayInitEvent.class)
    public void allinpayUserInit(AllinpayInitEvent event) {
        shopDetailService.createAllinpayMember();
    }
}
