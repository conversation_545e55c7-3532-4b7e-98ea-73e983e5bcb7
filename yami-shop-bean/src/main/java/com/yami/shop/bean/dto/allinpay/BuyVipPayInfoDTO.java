package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;

/**
 * 购买vip支付信息
 *
 * <AUTHOR>
 */
public class BuyVipPayInfoDTO extends BasePayInfoDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @NotNull
    @Schema(description = "用户等级id")
    private Long userLevelLogId;

    @Hidden
    @Schema(description = "支付系统类型(非填)")
    private Integer paySysType;

    public Long getUserLevelLogId() {
        return userLevelLogId;
    }

    public void setUserLevelLogId(Long userLevelLogId) {
        this.userLevelLogId = userLevelLogId;
    }

    public Integer getPaySysType() {
        return paySysType;
    }

    public void setPaySysType(Integer paySysType) {
        this.paySysType = paySysType;
    }

    @Override
    public String toString() {
        return "BuyVipPayInfoDTO{" +
                "userLevelLogId=" + userLevelLogId +
                ", paySysType=" + paySysType +
                '}';
    }
}
