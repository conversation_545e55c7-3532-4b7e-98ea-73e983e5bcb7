package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
/**
 * 购物车中选中的套餐活动项信息
 * <AUTHOR>
 */
@Data
public class ChooseComboItemDto implements Serializable {

    @Schema(description = "套餐id" )
    private Long comboId;

    @Schema(description = "主商品购物车id" )
    private Long mainProdBasketId;

    @Schema(description = "套餐总金额" )
    private Double comboTotalAmount;

    @Schema(description = "套餐金额(套餐单价)" )
    private Double comboAmount;

    @Schema(description = "套餐优惠金额" )
    private Double preferentialAmount;

    @Schema(description = "套餐数量" )
    private Integer comboCount;

    @Schema(description = "套餐名称" )
    private String name;

    /**
     * 套餐序号
     * 套餐可能会进行分单操作，且一个订单中可能包含多个相同套餐不同规格，所以需要一个独立的参数来标识套餐
     */
    private Integer index;
}
