package com.yami.shop.distribution.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yami.shop.bean.vo.SysUserVO;
import com.yami.shop.distribution.common.vo.DistributionUserVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName( "tz_distribution_auditing")
public class DistributionAuditing implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    @Schema(description = "分销员申请表id" )
    private Long auditingId;

    @Schema(description = "店铺Id" )
    private Long shopId;

    @Schema(description = "邀请人id" )
    private Long parentDistributionUserId;

    @Schema(description = "申请人id" )
    private Long distributionUserId;

    @Schema(description = "申请时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditingTime;

    @Schema(description = "不通过原因" )
    private Integer reason;

    @Schema(description = "审核状态：0 未审核 1已通过 -1未通过" )
    private Integer state;

    @Schema(description = "操作时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "操作人" )
    private Long modifier;

    @Schema(description = "备注" )
    private String remarks;

    @Schema(description = "关联邀请人" )
    @TableField(exist = false)
    private DistributionUserVO parentDistributionUser;

    @Schema(description = "关联用户" )
    @TableField(exist = false)
    private DistributionUser distributionUser;

    @Schema(description = "关联操作人" )
    @TableField(exist = false)
    private SysUserVO sysUser;

    @Schema(description ="排序字段(0:无, 1:积累消费金额, 2:积累消费笔数, 3:申请时间)")
    @TableField(exist = false)
    private Integer sortParam = 0;

    @Schema(description = "排序类型 0无 1 正序 2倒序" )
    @TableField(exist = false)
    private Integer sortType = 0;
}
