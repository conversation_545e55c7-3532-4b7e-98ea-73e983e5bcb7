package com.yami.shop.bean.app.param;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SearchParam {


    @Schema(description = "商品名" )
    private String prodName;

    @Schema(description = "排序(0 默认排序 1销量排序 2价格排序)" )
    private Integer sort;

    @Schema(description = "排序(0升序 1降序)" )
    private Integer orderBy;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "平台一级分类id" )
    private Long primaryCategoryId;

    @Schema(description = "平台二级分类id" )
    private Long secondaryCategoryId;

    @Schema(description = "平台三级分类id" )
    private Long categoryId;

    @Schema(description = "商品ids" )
    private List<Long> prodIds;

    @Schema(description = "店铺分类id" )
    private Long shopCategoryId;

    @Schema(description = "商品类型(0普通商品 1拼团 2秒杀 3积分)" )
    private Integer prodType;

    @Schema(description = "查询所有的商品类型" )
    private Boolean isAllProdType;

    private Integer lang;

}
