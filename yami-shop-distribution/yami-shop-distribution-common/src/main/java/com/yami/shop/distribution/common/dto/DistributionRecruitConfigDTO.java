package com.yami.shop.distribution.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021-09-06 14:20:02
 */
@Data
@Schema(description = "分销招募推广配置")
public class DistributionRecruitConfigDTO {

    @Schema(description = "推广图" )
    private String pic;

    @Schema(description = "推广标题" )
    @NotBlank(message = "推广标题不能为空")
    private String title;

    @Schema(description = "推广内容" )
    @NotBlank(message = "推广内容不能为空")
    private String content;

    @Schema(description = "推广链接" )
    private String url;

    @Schema(description = "推广开关： 状态（0下线 1上线）" )
    private Integer state;
}
