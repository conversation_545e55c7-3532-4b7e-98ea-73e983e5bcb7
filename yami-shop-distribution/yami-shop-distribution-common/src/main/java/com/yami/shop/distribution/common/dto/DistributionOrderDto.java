package com.yami.shop.distribution.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "分销员推广订单对象")
public class DistributionOrderDto {

    @Schema(description = "佣金数额" )
    private Double incomeAmount;

    @Schema(description = "佣金状态(0待支付,1用户未收货待结算，2收货已结算 -1订单失效)" )
    private Integer state;

    @Schema(description = "建单时间" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private Date createTime;

    @Schema(description = "商品图片" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String pic;

    @Schema(description = "商品名称" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String prodName;

    @Schema(description = "商品id" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String prodId;



}
