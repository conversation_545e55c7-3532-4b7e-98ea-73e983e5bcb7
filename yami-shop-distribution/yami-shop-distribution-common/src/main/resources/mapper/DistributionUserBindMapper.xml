<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.distribution.common.dao.DistributionUserBindMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.distribution.common.model.DistributionUserBind">
    <result column="bind_id" jdbcType="BIGINT" property="bindId" />
    <result column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="invalid_reason" jdbcType="TINYINT" property="invalidReason" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime" />
    <result column="invalid_time" jdbcType="TIMESTAMP" property="invalidTime" />

  </resultMap>

  <resultMap id="DistributionUserBind_User_User_DistributionUser"  type="com.yami.shop.distribution.common.model.DistributionUserBind">
    <result column="bind_id" jdbcType="BIGINT" property="bindId" />
    <result column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="invalid_reason" jdbcType="TINYINT" property="invalidReason" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime" />
    <result column="invalid_time" jdbcType="TIMESTAMP" property="invalidTime" />
    <association property="user" javaType="com.yami.shop.bean.vo.UserVO">
      <id column="user_id" jdbcType="VARCHAR" property="userId" />
      <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
      <result column="u_user_mobile" jdbcType="BIGINT" property="userMobile" />
    </association>
    <association property="distributionUser" javaType="com.yami.shop.distribution.common.vo.DistributionUserVO">
      <result column="distribution_user_id" jdbcType="BIGINT" property="distributionUserId" />
      <result column="parent_nick_name" jdbcType="VARCHAR" property="nickName" />
      <result column="parent_user_mobile" jdbcType="VARCHAR" property="userMobile" />
    </association>
  </resultMap>

  <select id="distributionMsgsAndUserPage" resultMap="DistributionUserBind_User_User_DistributionUser">
      select dub.*,
      du.nick_name as parent_nick_name,
      du.user_mobile as parent_user_mobile,
      u.nick_name,
      u.user_mobile as u_user_mobile
      from
      tz_distribution_user_bind dub
      left join tz_distribution_user du on du.distribution_user_id=dub.distribution_user_id
      left join tz_user u on dub.user_id=u.user_id
     <where>
       <if test="userName!= null and userName!='' ">
         and trim(replace(u.nick_name,' ','')) like trim(replace(concat('%',#{userName},'%'),' ',''))
       </if>

       <if test="parentName!= null and parentName!='' ">
         and trim(replace(du.nick_name,' ','')) like trim(replace(concat('%',#{parentName},'%'),' ',''))
       </if>

       <if test="distributionUserBind.state!= null ">
          and dub.state=#{distributionUserBind.state}
       </if>

       <if test="bindTime != null  and bindTime.startTime!= null ">
         and dub.bind_time &gt;= #{bindTime.startTime}
       </if>
       <if test="bindTime != null  and bindTime.endTime!= null ">
         and dub.bind_time &lt;= #{bindTime.endTime}
       </if>

       <if test="invalidTime != null  and invalidTime.startTime!= null ">
         and dub.invalid_time &gt;= #{invalidTime.startTime}
       </if>
       <if test="invalidTime != null  and invalidTime.endTime!= null ">
         and dub.invalid_time &lt;= #{invalidTime.endTime}
       </if>
       <if test="cUserMobile != null ">
         and trim(replace(u.user_mobile,' ','')) like trim(replace(concat('%',#{cUserMobile},'%'),' ',''))
       </if>
       <if test="dUserMobile != null ">
         and trim(replace(du.user_mobile,' ','')) like trim(replace(concat('%',#{dUserMobile},'%'),' ',''))
       </if>
     </where>
      <if test="sort == 1">
          order by dub.bind_time
      </if>
      <if test="sort == 2">
          order by dub.invalid_time
      </if>
      <if test="orderBy == 2">
          asc
      </if>
      <if test="orderBy == 1">
          desc
      </if>
  </select>


    <!--根据分销员id修改绑定状态-->
    <update id="updateStateAndReasonByDistributionUserId">
        update tz_distribution_user_bind
        set state = #{state},invalid_reason = 3,invalid_time = now()
        where
        distribution_user_id = #{distributionUserId} and state = 1
    </update>
    <update id="updateStateAndReasonByUserId">
        update tz_distribution_user_bind
        set state = #{state},invalid_reason = 3,invalid_time = now()
        where
                user_id = #{userId} and state = 1
    </update>

    <select id="bindUserList" resultType="com.yami.shop.distribution.common.dto.BindUserInfoDto">
        select dub.*,
        u.nick_name,
        u.pic
        from
        tz_distribution_user_bind dub
        left join tz_distribution_user du on du.distribution_user_id=dub.distribution_user_id
        left join tz_user u on dub.user_id=u.user_id
        where du.user_id = #{userId}  and dub.state = 1 and du.shop_id = #{shopId} order by dub.bind_time desc
    </select>

    <update id="recoveryRelationsByUserId">
        update tz_distribution_user_bind
        set state = 1,invalid_reason = null,invalid_time = null
        where invalid_reason = 3 and user_id in
        <foreach collection="distributionUserBinds" item="distributionUserBind" separator="," open="(" close=")">
            #{distributionUserBind.userId}
        </foreach>
    </update>

    <select id="selectClearUserByDistributionUserId" resultType="string">
        select user_id from tz_distribution_user_bind
        where state = 1 and user_id in
        <foreach collection="distributionUserBindList" item="distributionUserBind" separator="," open="(" close=")">
            #{distributionUserBind.userId}
        </foreach>
    </select>
</mapper>
