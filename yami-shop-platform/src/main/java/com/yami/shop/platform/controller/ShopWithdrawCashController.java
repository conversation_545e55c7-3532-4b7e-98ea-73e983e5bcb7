package com.yami.shop.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.dto.ShopWithdrawCashConfigDto;
import com.yami.shop.bean.model.ShopDetail;
import com.yami.shop.bean.model.ShopWithdrawCash;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.platform.util.SecurityUtils;
import com.yami.shop.service.ShopDetailService;
import com.yami.shop.service.ShopWithdrawCashService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


/**
 * 商家提现申请信息
 *
 * <AUTHOR>
 * @date 2019-09-29 11:10:12
 */
@RestController
@AllArgsConstructor
@RequestMapping("/shop/shopWithdrawCash")
@Tag(name = "商家提现申请信息")
public class ShopWithdrawCashController {

    private final ShopWithdrawCashService shopWithdrawCashService;

    private final ShopDetailService shopDetailService;

    @GetMapping("/page")
    @Operation(summary = "分页查询" , description = "分页查询")
    @PreAuthorize("@pms.hasPermission('shop:shopWithdrawCash:page')")
    public ServerResponseEntity<IPage<ShopWithdrawCash>> getShopWithdrawCashPage(PageParam<ShopWithdrawCash> page, ShopWithdrawCash shopWithdrawCash) {
        return ServerResponseEntity.success(shopWithdrawCashService.pageShopWithdrawCash(page, shopWithdrawCash));
    }

    @GetMapping("/exportShopWithdrawCash")
    @Operation(summary = "导出商家提现信息" , description = "导出商家提现信息")
    @PreAuthorize("@pms.hasPermission('shop:shopWithdrawCash:export')")
    public void exportShopWithdrawCash(ShopWithdrawCash shopWithdrawCash, HttpServletResponse response) {
        shopWithdrawCashService.exportShopWithdrawCash(shopWithdrawCash, response);
    }

    @GetMapping("/info/{cashId}")
    @Operation(summary = "查询商家提现申请信息" , description = "查询商家提现申请信息")
    @Parameter(name = "cashId", description = "商家提现申请id" )
    @PreAuthorize("@pms.hasPermission('shop:shopWithdrawCash:info')")
    public ServerResponseEntity<ShopWithdrawCash> getById(@PathVariable("cashId") Long cashId) {
        return ServerResponseEntity.success(shopWithdrawCashService.getById(cashId));
    }

    @SysLog("审核商家提现信息")
    @PutMapping("/audit")
    @PreAuthorize("@pms.hasPermission('shop:shopWithdrawCash:audit')")
    @Operation(summary = "审核信息" , description = "审核信息")
    public ServerResponseEntity<Void> audit(@RequestBody ShopWithdrawCash shopWithdrawCash) {
        ShopWithdrawCash dbShopWithdrawCash = shopWithdrawCashService.getById(shopWithdrawCash.getCashId());
        if (dbShopWithdrawCash == null) {
            // 未找到申请信息
            throw new YamiShopBindException("yami.store.apply.no.exist");
        }
        ShopDetail shopdetail = shopDetailService.getShopDetailByShopId(dbShopWithdrawCash.getShopId());
        if (shopdetail == null) {
            // 未找到该店铺信息
            throw new YamiShopBindException("yami.store.not.exist");
        }
        shopWithdrawCash.setCreateTime(null);

        shopWithdrawCashService.auditWithdrawCash(shopWithdrawCash.getCashId(), shopWithdrawCash, SecurityUtils.getSysUser().getUserId(), null);
        // 减少冻结金额
        return ServerResponseEntity.success();
    }

    @PostMapping("/save")
    @Operation(summary = "设置提现金额" , description = "设置提现金额")
    @PreAuthorize("@pms.hasPermission('shop:shopWithdrawCash:save')")
    public ServerResponseEntity<Void> saveWithdrawCashConfig(@RequestBody ShopWithdrawCashConfigDto shopWithdrawCashDto) {
        shopWithdrawCashService.saveConfig(shopWithdrawCashDto);
        return ServerResponseEntity.success();
    }

    @GetMapping("/getWithdrawCash")
    @PreAuthorize("@pms.hasPermission('shop:shopWithdrawCash:info')")
    public ServerResponseEntity<ShopWithdrawCashConfigDto> getWithdrawCashConfig(){
        return ServerResponseEntity.success(shopWithdrawCashService.getConfig());
    }

}
