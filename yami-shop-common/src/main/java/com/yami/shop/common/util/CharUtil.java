package com.yami.shop.common.util;

import org.springframework.util.ObjectUtils;

/**
 * 获取带有中文字符的长度
 * <AUTHOR>
 */
public class CharUtil {
    /**
     * 得到一个字符串的长度,显示的长度,一个汉字或日韩文长度为2,英文字符长度为1
     * @param s 需要得到长度的字符串
     * @return int 得到的字符串长度
     */
    public static int length(String s) {
        if (s == null) {
            return 0;
        }
        int len = 0;
        for (char item : s.toCharArray()) {
            len++;
            if (item / 0x80 == 0) {
                continue;
            }
            len++;
        }
        return len;
    }

    /**
     * 字符串空格分隔(正则表达式)
     * @param str 字符串
     * @param splitLen 分割位数
     * @return
     */
    public static String splitWithSpace(String str, int splitLen) {
        if (ObjectUtils.isEmpty(str)
                || splitLen <= 0) {
            return str;
        }
        boolean subTail = str.length() % splitLen == 0;
        String regex = String.format("(.{%d})", splitLen);
        str = str.replaceAll(regex, "$1 ");
        str = subTail ? str.substring(0, str.length() - 1) : str;
        return str;
    }


}
