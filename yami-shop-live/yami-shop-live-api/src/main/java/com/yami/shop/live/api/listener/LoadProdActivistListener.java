package com.yami.shop.live.api.listener;

import com.yami.shop.bean.app.vo.ProductVO;
import com.yami.shop.bean.event.LoadProdActivistEvent;
import com.yami.shop.bean.order.LoadProdActivistOrder;
import com.yami.shop.bean.param.LiveRoomParam;
import com.yami.shop.live.common.service.LiveRoomService;
import lombok.AllArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 加载商品直播间信息
 * <AUTHOR>
 */
@Component("livingRoomLoadProdActivistListener")
@AllArgsConstructor
public class LoadProdActivistListener {

    private final LiveRoomService liveRoomService;

    @EventListener(LoadProdActivistEvent.class)
    @Order(LoadProdActivistOrder.DEFAULT)
    public void loadProdLivingRoomHandle(LoadProdActivistEvent event) {
        ProductVO productVO = event.getProductVO();
        // 查询商品包含的所有直播间
        List<LiveRoomParam> liveRoomParams = liveRoomService.getLivingRoomByProdId(event.getProdId());
        productVO.setLiveRoomParams(liveRoomParams);
    }


}
