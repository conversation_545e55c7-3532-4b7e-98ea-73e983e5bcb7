package com.yami.shop.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.enums.SendType;
import com.yami.shop.bean.model.NotifyTemplate;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.NotifyTemplateService;
import com.yami.shop.user.common.service.UserTagService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;


/**
 *
 *
 * <AUTHOR>
 * @date 2020-07-01 16:13:08
 */
@RestController
@AllArgsConstructor
@RequestMapping("/platform/sendTagNotify")
@Tag(name = "平台消息模板")
public class PlatformNotifyController {

    private final NotifyTemplateService notifyTemplateService;
    private final UserTagService userTagService;

    @GetMapping("/page" )
    @Operation(summary = "分页查询" , description = "分页查询")
    @PreAuthorize("@pms.hasPermission('platform:sendTagNotify:page')")
    public ServerResponseEntity<IPage<NotifyTemplate>> getNotifyTemplatePage(PageParam<NotifyTemplate> page, NotifyTemplate notifyTemplate) {
        IPage<NotifyTemplate> templatePage = notifyTemplateService.pageTagNotify(page);
        return ServerResponseEntity.success(templatePage);
    }

    @GetMapping("/info/{templateId}" )
    @Operation(summary = "获取模板信息" , description = "获取模板信息")
    @Parameter(name = "templateId", description = "模板id" )
    @PreAuthorize("@pms.hasPermission('platform:sendTagNotify:info')")
    public ServerResponseEntity<NotifyTemplate> getById(@PathVariable("templateId") Long templateId) {
        NotifyTemplate template = notifyTemplateService.getInfoById(templateId);
        return ServerResponseEntity.success(template);
    }

    @SysLog("新增" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('platform:notifyTemplate:save')" )
    @Operation(summary = "新增" , description = "新增")
    public ServerResponseEntity<Boolean> save(@RequestBody @Valid NotifyTemplate notifyTemplate) {
        if(CollectionUtils.isEmpty(notifyTemplate.getSelTagIds())){
            throw new YamiShopBindException("yami.notify.tag.msg");
        }
        notifyTemplate.setCreateTime(new Date());
        notifyTemplate.setStatus(1);
        notifyTemplate.setSendType(SendType.CUSTOMIZE.getValue());
        // 站内消息1
        notifyTemplate.setTemplateTypes("3");
        notifyTemplateService.saveTagNotify(notifyTemplate);
        return ServerResponseEntity.success();
    }

    @SysLog("修改" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('platform:notifyTemplate:update')" )
    @Operation(summary = "修改" , description = "修改")
    public ServerResponseEntity<Boolean> updateById(@RequestBody @Valid NotifyTemplate notifyTemplate) {
        notifyTemplateService.updateInfoById(notifyTemplate);
        return ServerResponseEntity.success(true);
    }

    @DeleteMapping("/{templateId}" )
    @Operation(summary = "删除消息模板" , description = "删除消息模板")
    @Parameter(name = "templateId", description = "模板id" )
    @PreAuthorize("@pms.hasPermission('platform:sendTagNotify:delete')")
    public ServerResponseEntity<Boolean> deleteUserTag(@PathVariable Long templateId) {
        notifyTemplateService.deleteTemplateInfoById(templateId);
        return ServerResponseEntity.success(true);
    }

}
