package com.yami.shop.multishop.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.OrderItem;
import com.yami.shop.bean.model.Product;
import com.yami.shop.bean.param.*;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.coupon.common.service.CouponUserService;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.FlowProductAnalysisService;
import com.yami.shop.service.ProductExcelService;
import com.yami.shop.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 商品分析接口
 * <AUTHOR>
@Tag(name = "商家端商品分析接口")
@RestController
@RequestMapping("/multishop/prodAnalysis")
@AllArgsConstructor
public class ProdAnalysisController {

    private final ProductService productService;
    private final CouponUserService couponUserService;
    private final ProductExcelService productExcelService;
    private final FlowProductAnalysisService flowProductAnalysisService;


    /**
     * 获取商品概况
     */
    @GetMapping("/getProdSurvey")
    @Operation(summary = "获取商品概况" , description = "获取商品概况")
    @PreAuthorize("@pms.hasPermission('multishop:prodAnalysis:info')")
    public ServerResponseEntity<ProdAnalysisParam> getProdSurvey(ProdAnalysisSurveyParam param) {
        param.setShopId(SecurityUtils.getShopUser().getShopId());
        ProdAnalysisParam analysis = flowProductAnalysisService.getProdSurvey(param);
        return ServerResponseEntity.success(analysis);
    }

    /**
     * 获取商品趋势分析
     */
    @GetMapping("/getProdTrendAnalysis")
    @Operation(summary = "获取商品趋势分析" , description = "获取商品趋势分析")
    @PreAuthorize("@pms.hasPermission('multishop:prodAnalysis:list')")
    public ServerResponseEntity<List<ProdAnalysisDataParam>> getProdTrendAnalysis(ProdAnalysisSurveyParam param) {
        param.setShopId(SecurityUtils.getShopUser().getShopId());
        List<ProdAnalysisDataParam> trendAnalysisa = productService.getProdTrendAnalysis(param);
        return ServerResponseEntity.success(trendAnalysisa);
    }

    /**
     * 支付金额TOP
     * 访客数TOP
     */
    @GetMapping("/getPayAmountTop")
    @Operation(summary = "支付金额TOP、访客数TOP" , description = "支付金额TOP、访客数TOP")
    @PreAuthorize("@pms.hasPermission('multishop:prodAnalysis:info')")
    public ServerResponseEntity<VisitorAndPayTopParam> getPayAmountTop(PageParam<OrderItem> page, ProdAnalysisSurveyParam param) {
        param.setShopId(SecurityUtils.getShopUser().getShopId());
        VisitorAndPayTopParam visitorAndPayTopParam = flowProductAnalysisService.getPayAmountTop(page, param);

        return ServerResponseEntity.success(visitorAndPayTopParam);
    }

    /**
     * 导出支付金额TOP
     * 导出访客数TOP
     */
    @GetMapping("/payAmountTopExport")
    @Operation(summary = "导出支付金额TOP、访客数TOP" , description = "导出支付金额TOP、访客数TOP")
    @PreAuthorize("@pms.hasPermission('multishop:prodAnalysis:export')")
    public void payAmountTopExport(HttpServletResponse response, ProdAnalysisSurveyParam param) {
        param.setShopId(SecurityUtils.getShopUser().getShopId());
        flowProductAnalysisService.payAmountTopExport(response, param);
    }


    /**
     * 获取商品效果分析数据
     */
    @GetMapping("/getProdEffect")
    @Operation(summary = "获取商品效果分析数据" , description = "获取商品效果分析数据")
    @PreAuthorize("@pms.hasPermission('multishop:prodAnalysis:pageEffect')")
    public ServerResponseEntity<IPage<ProdEffectRespParam>> getProdEffect(PageParam<Product> page, ProdEffectParam param) {
        param.setShopId(SecurityUtils.getShopUser().getShopId());
        if (Objects.equals(1,param.getGroup())) {
            IPage<ProdEffectRespParam> map = new PageParam<>();
            map.setCurrent(page.getCurrent());
            map.setPages(page.getPages());
            map.setSize(page.getSize());
            map.setRecords(new ArrayList<>());
            return ServerResponseEntity.success(map);
        }
        IPage<ProdEffectRespParam> resPage = productService.pageProdEffect(page,param,I18nMessage.getDbLang(), false);
        return ServerResponseEntity.success(resPage);
    }

    /**
     * 导出商品洞察数据
     */
    @GetMapping("/prodEffectExport")
    @Operation(summary = "导出商品洞察数据" , description = "导出商品洞察数据")
    @PreAuthorize("@pms.hasPermission('multishop:prodAnalysis:exportEffect')")
    public void prodEffectExport(HttpServletResponse response, ProdEffectParam param) {
        productExcelService.prodEffectExport(response,param,I18nMessage.getDbLang());
    }

    /**
     * 单个商品的趋势分析
     */
    @GetMapping("/getSingleProdTrend")
    @Operation(summary = "获取商品效果分析数据" , description = "获取商品效果分析数据")
    @PreAuthorize("@pms.hasPermission('multishop:prodAnalysis:effectInfo')")
    public ServerResponseEntity<List<ProdSingleTrendParam>> getSingleProdTrend(Long prodId, ProdEffectParam param) {
        param.setShopId(SecurityUtils.getShopUser().getShopId());
        List<ProdSingleTrendParam> resList = productService.getSingleProdTrend(prodId,param);
        return ServerResponseEntity.success(resList);
    }

    /**
     * 卡券分析，卡券昨日关键指标
     */
    @Operation(summary = "卡券分析，卡券昨日关键指标" , description = "卡券分析，卡券昨日关键指标")
    @GetMapping("/getCouponAnalysis")
    @PreAuthorize("@pms.hasPermission('multishop:prodAnalysis:list')")
    public ServerResponseEntity<List<CouponAnalysisParam>> getCouponAnalysis(ProdEffectParam param) {
        param.setShopId(SecurityUtils.getShopUser().getShopId());
        // 统计近7天的数据
        List<CouponAnalysisParam> resList = couponUserService.getCouponAnalysis(param);
        return ServerResponseEntity.success(resList);
    }

    @GetMapping("/getCouponAnalysisParamByDate")
    @Operation(summary = "根据日期获取卡券分析数据" , description = "根据日期获取卡券分析数据")
    @PreAuthorize("@pms.hasPermission('multishop:prodAnalysis:page')")
    public ServerResponseEntity<IPage<CouponAnalysisParam>> getCouponAnalysisParamByDate(PageParam<CouponAnalysisParam> page, ProdEffectParam param){
        param.setShopId(SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success(couponUserService.getCouponAnalysisParamByDate(page,param));
    }
}
