package com.yami.shop.bean.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class VoucherItemVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long voucherItemId;
    @Schema(description = "卡券id")
    private Long voucherId;
    @Schema(description = "卡号")
    private String cardNumber;
    @Schema(description = "卡密")
    private String cardPwd;
    @Schema(description = "优惠码")
    private String couponCode;
    @Schema(description = "创建时间")
    private Date createTime;
    @Schema(description = "更新时间")
    private Date updateTime;
    @Schema(description = "关联订单号")
    private Long orderNumber;
    @Schema(description = "导入日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date importTime;
    @Schema(description = "失效时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expirationTime;
    @Schema(description = "状态 -1 已删除 0：已失效 1已发送 2未发送")
    private Integer status;
    @Schema(description = "卡券名称")
    private String voucherName;
    @Schema(description = "店铺id")
    private Long shopId;

    public VoucherItemVO(String cardNumber, String cardPwd, String couponCode) {
        this.cardNumber = cardNumber;
        this.cardPwd = cardPwd;
        this.couponCode = couponCode;
    }
}
