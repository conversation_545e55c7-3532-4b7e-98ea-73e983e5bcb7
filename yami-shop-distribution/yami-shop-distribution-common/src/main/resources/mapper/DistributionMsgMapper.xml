<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yami.shop.distribution.common.dao.DistributionMsgMapper">
  <resultMap id="BaseResultMap" type="com.yami.shop.distribution.common.model.DistributionMsg">
    <id column="msg_id" jdbcType="BIGINT" property="msgId" />
    <result column="level" jdbcType="TINYINT" property="level" />
    <result column="msg_title" jdbcType="VARCHAR" property="msgTitle" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="is_top" jdbcType="TINYINT" property="isTop" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="pic" jdbcType="VARCHAR" property="pic" />
    <result column="msg_type" jdbcType="TINYINT" property="msgType" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
  </resultMap>

  <resultMap id="getDistributionMsgsAndSysUserMap" type="com.yami.shop.distribution.common.model.DistributionMsg">
    <id column="msg_id" jdbcType="BIGINT" property="msgId" />
    <result column="level" jdbcType="TINYINT" property="level" />
    <result column="msg_title" jdbcType="VARCHAR" property="msgTitle" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="is_top" jdbcType="TINYINT" property="isTop" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="pic" jdbcType="VARCHAR" property="pic" />
    <result column="msg_type" jdbcType="TINYINT" property="msgType" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="modifier" jdbcType="BIGINT" property="modifier" />
    <association property="sysUser" javaType="com.yami.shop.bean.vo.SysUserVO">
      <id column="user_id" jdbcType="BIGINT" property="userId" />
      <result column="username" jdbcType="VARCHAR" property="username" />
    </association>
  </resultMap>

  <select id="getDistributionMsgsAndSysUserPage" resultMap="getDistributionMsgsAndSysUserMap">
    select
    msg.*,
    su.user_id,su.username
    from
    tz_distribution_msg msg
    left join tz_sys_user su on msg.modifier=su.user_id
    <where>
      <if test="distributionMsg.msgTitle!= null and distributionMsg.msgTitle!='' ">
        and trim(replace(msg_title,' ','')) like trim(replace(concat('%',#{distributionMsg.msgTitle},'%'),' ',''))
      </if>
      <if test="distributionMsg.state !=null">
        and state =#{distributionMsg.state}
      </if>
      <if test="distributionMsg.startTime !=null">
        and start_time &gt;=#{distributionMsg.startTime}
      </if>
      <if test="distributionMsg.endTime !=null">
        and end_time &lt;=#{distributionMsg.endTime}
      </if>
    </where>
  </select>

  <sql id="DistributionMsgDto">
    msg_id,
    level,
    msg_title,
    start_time,
    is_top,
    content,
    pic,
    msg_type
  </sql>

  <select id="getDistributionMsgDtoByMsgId" resultType="com.yami.shop.distribution.common.dto.DistributionMsgDto">
    select
    <include refid="DistributionMsgDto"/>
    from tz_distribution_msg
    where
    msg_id = #{msgId}
  </select>

  <select id="getDistributionMsgDtoByShopId" resultType="com.yami.shop.distribution.common.dto.DistributionMsgDto">
    select
    msg_id,
    level,
    msg_title,
    start_time,
    is_top,
    pic,
    msg_type
    from tz_distribution_msg
    where
    start_time &lt; now()
    and end_time &gt; now()
    <if test="isTop != null">
      and is_top=#{isTop}
    </if>
    order by is_top desc,start_time DESC
  </select>
</mapper>