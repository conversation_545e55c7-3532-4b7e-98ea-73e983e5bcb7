package com.yami.shop.bean.vo.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MemberOverviewVO {

    public MemberOverviewVO(){
        this.totalMember = 0;
        this.totalMemberRate = 0.0;
        this.newMember = 0;
        this.newMemberRate = 0.0;
        this.payMember = 0;
        this.payMemberRate = 0.0;
        this.couponMember = 0;
        this.couponMemberRate = 0.0;
        this.memberPayAmount = 0.0;
        this.memberPayAmountRate = 0.0;
        this.memberPayOrder = 0;
        this.memberPayOrderRate = 0.0;
        this.memberSingleAmount = 0.0;
        this.memberSingleAmountRate = 0.0;
    }
    private Long currentDay;

    @Schema(description = "累积会员数" )
    private Integer totalMember;

    @Schema(description = "新增会员数" )
    private Integer newMember;

    @Schema(description = "付费会员数" )
    private Integer paidMember;

    @Schema(description = "支付会员数" )
    private Integer payMember;

    @Schema(description = "领券会员数" )
    private Integer couponMember;

    @Schema(description = "会员支付金额" )
    private Double memberPayAmount;

    @Schema(description = "会员支付订单数" )
    private Integer memberPayOrder;

    @Schema(description = "会员客单价" )
    private Double memberSingleAmount;

    @Schema(description = "累积会员与之前的  >0 上升/ <0下降 率," )
    private Double totalMemberRate;

    @Schema(description = "新增会员会员与之前的 上升/下降 率" )
    private Double newMemberRate;

    @Schema(description = "支付会员数，变化率" )
    private Double payMemberRate;

    @Schema(description = "领券会员数，变化率" )
    private Double couponMemberRate;

    @Schema(description = "会员支付金额，变化率" )
    private Double memberPayAmountRate;

    @Schema(description = "会员支付订单数, 变化率" )
    private Double memberPayOrderRate;

    @Schema(description = "会员客单价, 变化率" )
    private Double memberSingleAmountRate;

    @Schema(description = "符合条件的userIds" )
    private List<Long> userIds;

    @Schema(description = "储值会员数" )
    private Integer storedValue;
}
