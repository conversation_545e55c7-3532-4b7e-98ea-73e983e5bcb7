package com.yami.shop.common.bean;

import lombok.Data;

/**
 * 阿里配置信息
 * <AUTHOR>
 */
@Data
public class Sms4jConfig {

    private String accessKeyId;

    private String accessKeySecret;

    private String signName;

    /**
     * 参考详情见 SmsTypeEnum
     */
    private String smsType;

    /**
     * APP接入地址 (亿美软通)
     */
    private String requestUrl;

    /**
     * 国内短信签名通道号 (华为云)
     */
    private String sender;
    /**
     * 短信状态报告接收地 (华为云)
     */
    private String statusCallBack;
    /**
     *  APP接入地址 (华为云)
     */
    private String url;

    /**
     * 短信发送后将向这个地址推送(运营商返回的)发送报告 (云片短信)
     */
    private String callbackUrl;

    /**
     * 企业ID （联麓短信）
     */
    private String mchId;

    /**
     * appKey （联麓短信）
     */
    private String appKey;

    /**
     * appId （联麓短信）
     */
    private String appId;

    /**
     * 签名ID （七牛云短信）
     */
    private String signatureId;
}
