package com.yami.shop.distribution.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业绩归属
 *
 * <AUTHOR>
 * @Date 2021/5/13 20:10
 */
@Getter
@AllArgsConstructor
public enum DistributionBindAttributionEnum {
    /**
     * 允许绑定,关系优先
     */
    ALLOW_BINDING(0, "允许绑定,关系优先"),

    /**
     * 不绑定 分享人优先
     */
    NOT_BOUND(1, "不绑定 分享人优先"),

    ;
    private final int value;
    private final String desc;
}
