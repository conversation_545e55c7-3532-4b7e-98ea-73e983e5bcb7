package com.yami.shop.bean.vo.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 最近会话返回
 * <AUTHOR>
 */
public class ConversationSyncVO {

    @Schema(description = "频道的唯一ID")
    private String channelId;

    @Schema(description = "频道的类型 1.个人频道 2.群聊频道")
    private Integer channelType;

    @Schema(description = "消息未读数量")
    private Integer unread;

    @Schema(description = "10位到秒的时间戳")
    private Integer timestamp;

    @Schema(description = "最后一条消息的message_seq")
    private Integer lastMsgSeq;

    @Schema(description = "最后一条消息的客户端消息编号")
    private String lastClientMsgNo;

    @Schema(description = "数据版本编号")
    private Long version;

    @Schema(description = "最近N条消息")
    private List<MessageVO> recents;

    @Schema(description = "头像")
    private String pic;

    @Schema(description = "店铺名称")
    private String shopName;

    @Schema(description = "用户名称")
    private String nickName;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "是否为注销用户")
    private Integer isDestroy;

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public Integer getChannelType() {
        return channelType;
    }

    public void setChannelType(Integer channelType) {
        this.channelType = channelType;
    }

    public Integer getUnread() {
        return unread;
    }

    public void setUnread(Integer unread) {
        this.unread = unread;
    }

    public Integer getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Integer timestamp) {
        this.timestamp = timestamp;
    }

    public Integer getLastMsgSeq() {
        return lastMsgSeq;
    }

    public void setLastMsgSeq(Integer lastMsgSeq) {
        this.lastMsgSeq = lastMsgSeq;
    }

    public String getLastClientMsgNo() {
        return lastClientMsgNo;
    }

    public void setLastClientMsgNo(String lastClientMsgNo) {
        this.lastClientMsgNo = lastClientMsgNo;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public List<MessageVO> getRecents() {
        return recents;
    }

    public void setRecents(List<MessageVO> recents) {
        this.recents = recents;
    }

    public String getPic() {
        return pic;
    }

    public void setPic(String pic) {
        this.pic = pic;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Integer getIsDestroy() {
        return isDestroy;
    }

    public void setIsDestroy(Integer isDestroy) {
        this.isDestroy = isDestroy;
    }

    @Override
    public String toString() {
        return "ConversationSyncVO{" +
                "channelId='" + channelId + '\'' +
                ", channelType=" + channelType +
                ", unread=" + unread +
                ", timestamp=" + timestamp +
                ", lastMsgSeq=" + lastMsgSeq +
                ", lastClientMsgNo='" + lastClientMsgNo + '\'' +
                ", version=" + version +
                ", recents=" + recents +
                ", pic='" + pic + '\'' +
                ", shopName='" + shopName + '\'' +
                ", nickName='" + nickName + '\'' +
                ", shopId=" + shopId +
                ", userId='" + userId + '\'' +
                ", isDestroy=" + isDestroy +
                '}';
    }
}

