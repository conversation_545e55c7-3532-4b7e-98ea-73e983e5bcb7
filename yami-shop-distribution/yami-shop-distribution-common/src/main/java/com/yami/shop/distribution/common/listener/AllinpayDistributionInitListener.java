package com.yami.shop.distribution.common.listener;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yami.shop.bean.enums.AuditStatus;
import com.yami.shop.bean.event.allinpay.AllinpayInitEvent;
import com.yami.shop.bean.vo.DistributionConfigVO;
import com.yami.shop.common.config.Constant;
import com.yami.shop.distribution.common.model.DistributionAuditing;
import com.yami.shop.distribution.common.model.DistributionUser;
import com.yami.shop.distribution.common.model.DistributionUserWallet;
import com.yami.shop.distribution.common.model.DistributionWithdrawCash;
import com.yami.shop.distribution.common.service.*;
import com.yami.shop.service.SysConfigService;
import lombok.AllArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 通联分销初始化
 * <AUTHOR>
 * @date 2023-08-10
 */
@Component
@AllArgsConstructor
public class AllinpayDistributionInitListener {

    private final DistributionAuditingService distributionAuditingService;
    private final DistributionUserService distributionUserService;
    private final DistributionUserWalletService distributionUserWalletService;
    private final DistributionUserWalletBillService distributionUserWalletBillService;
    private final DistributionWithdrawCashService distributionWithdrawCashService;
    private final SysConfigService sysConfigService;

    @EventListener(AllinpayInitEvent.class)
    public void AllinpayDistributionInit(AllinpayInitEvent event) {
        // 取消分销配置
        this.cancelDistributionConfig();
        // 拒绝分销员申请
        this.rejectDistributionApply();
        // 拒绝分销提现申请
        this.rejectWithdrawApply();
        // 分销钱包初始化
        this.initDistributionWallet();
        // 分销钱包流水初始化
        this.initDistributionWalletBill();
    }

    /**
     * 取消分销配置中分销员申请所需信息
     */
    private void cancelDistributionConfig() {
        DistributionConfigVO config = sysConfigService.getSysConfigObject(Constant.DISTRIBUTION_CONFIG, DistributionConfigVO.class);
        config.setIdentityCardNumber(false);
        config.setIdentityCardPic(false);
        config.setRealName(false);
        String value = JSON.toJSONString(config);
        sysConfigService.updateValueByKey(Constant.DISTRIBUTION_CONFIG, value);
    }

    /**
     * 拒绝所有未审核的分销员申请
     */
    private void rejectDistributionApply() {
        // 查找所有未审核的申请
        List<DistributionAuditing> audits = distributionAuditingService.list(new LambdaQueryWrapper<DistributionAuditing>()
                .eq(DistributionAuditing::getState, AuditStatus.WAITAUDIT.value()));
        for (DistributionAuditing audit : audits) {
            audit.setState(AuditStatus.FAILAUDIT.value());
            audit.setReason(-1);
            audit.setUpdateTime(new Date());
            audit.setModifier(0L);
            distributionAuditingService.examine(audit);
            DistributionUser distributionUser = distributionUserService.getById(audit.getDistributionUserId());
            distributionUserService.removeCacheByUserIdAndShopId(distributionUser.getUserId(), audit.getShopId());
            distributionUserService.removeCacheByCardNo(distributionUser.getCardNo());
        }
    }

    /**
     * 拒接所有分销提现申请
     */
    private void rejectWithdrawApply() {
        distributionWithdrawCashService.update(new LambdaUpdateWrapper<DistributionWithdrawCash>()
                .set(DistributionWithdrawCash::getState, -1)
                .eq(DistributionWithdrawCash::getState, 0));
    }

    /**
     * 分销钱包初始化(归0)
     */
    private void initDistributionWallet() {
        distributionUserWalletService.update(new LambdaUpdateWrapper<DistributionUserWallet>()
                .set(DistributionUserWallet::getSettledAmount, 0)
                .set(DistributionUserWallet::getUnsettledAmount, 0)
                .set(DistributionUserWallet::getAddupAmount, 0)
                .set(DistributionUserWallet::getInvalidAmount, 0));
    }

    /**
     * 分销钱包记录初始化(清空)
     */
    private void initDistributionWalletBill() {
        distributionUserWalletBillService.initWalletBill();
    }
}
