package com.yami.shop.bean.dto.allinpay;

import java.io.Serial;
import java.io.Serializable;
/**
 * <AUTHOR>
 * @date 2023-05-31
 */
public class BindPayAcctDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 通联userId
     */
    private String bizUserId;

    /**
     * 支付标识
     */
    private String acct;

    /**
     * 支付账户类型
     */
    private Integer socialType;

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getAcct() {
        return acct;
    }

    public void setAcct(String acct) {
        this.acct = acct;
    }

    public Integer getSocialType() {
        return socialType;
    }

    public void setSocialType(Integer socialType) {
        this.socialType = socialType;
    }

    @Override
    public String toString() {
        return "BindPayAcctDTO{" +
                "bizUserId='" + bizUserId + '\'' +
                ", acct='" + acct + '\'' +
                ", socialType=" + socialType +
                '}';
    }
}
