package com.yami.shop.groupbuy.common.bo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/05/17
 */
@Data
public class GroupOrderBO {

    /**
     * 拼团活动id
     */
    private Long groupActivityId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 拼团团队id
     */
    private Long groupTeamId;

    /**
     * user_id(当user_id为0时标识为机器人)
     */
    private String userId;

    /**
     * 团长id
     */
    private String shareUserId;

    /**
     * 活动商品金额
     */
    private Double activityProdPrice;

    /**
     * 支付金额
     */
    private Double payPrice;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 商品数量
     */
    private Integer count;

    private Long prodId;
}
