package com.yami.shop.platform.controller;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.param.CustomerReqParam;
import com.yami.shop.bean.vo.AccountDetailVO;
import com.yami.shop.bean.vo.ShopAccountDetailVO;
import com.yami.shop.bean.vo.ShopAccountVO;
import com.yami.shop.common.allinpay.constant.PaySysType;
import com.yami.shop.common.bean.PaySettlementConfig;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.common.util.PoiExcelUtil;
import com.yami.shop.service.PayInfoService;
import com.yami.shop.service.RefundInfoService;
import com.yami.shop.service.SysConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/11 15:57
 */
@RestController("platformAccountDetailController")
@RequestMapping("/platform/accountDetail")
@Tag(name = "账户详情")
@AllArgsConstructor
public class AccountDetailController {

    private final PayInfoService payInfoService;
    private final RefundInfoService refundInfoService;
    private final SysConfigService sysConfigService;

    @GetMapping("/getIncomeAccountDetail")
    @Operation(summary = "获取收入账户详情" , description = "根据时间获取")
    @PreAuthorize("@pms.hasPermission('platform:accountDetail:info')")
    public ServerResponseEntity<AccountDetailVO> getIncomeAccountDetail(CustomerReqParam accountSearchDTO){
        if (Objects.equals(accountSearchDTO.getShopId(), Constant.PLATFORM_SHOP_ID)) {
            AccountDetailVO platformIncomeAccountDetail = payInfoService.getPlatformIncomeAccountDetail(accountSearchDTO.getStartTime(), accountSearchDTO.getEndTime());
            return ServerResponseEntity.success(platformIncomeAccountDetail);
        }
        AccountDetailVO accountDetailVO = payInfoService.getIncomeAccountDetail(accountSearchDTO.getStartTime(), accountSearchDTO.getEndTime(), accountSearchDTO.getShopName());
        return ServerResponseEntity.success(accountDetailVO);
    }

    @GetMapping("/getRefundAccountDetail")
    @Operation(summary = "获取退款账户详情" , description = "根据时间获取")
    @PreAuthorize("@pms.hasPermission('platform:accountDetail:info')")
    public ServerResponseEntity<AccountDetailVO> getRefundAccountDetail(CustomerReqParam accountSearchDTO){
        if (Objects.equals(accountSearchDTO.getShopId(), Constant.PLATFORM_SHOP_ID)) {
            AccountDetailVO platformRefundAccountDetail = refundInfoService.getPlatformRefundAccountDetail(accountSearchDTO.getStartTime(), accountSearchDTO.getEndTime());
            return ServerResponseEntity.success(platformRefundAccountDetail);
        }
        AccountDetailVO accountDetailVO = refundInfoService.getRefundAccountDetail(accountSearchDTO.getStartTime(), accountSearchDTO.getEndTime(), accountSearchDTO.getShopName());
        return ServerResponseEntity.success(accountDetailVO);
    }

    @GetMapping("/getIncomeAccountDetailInfo")
    @Operation(summary = "分页获取收入账户详情" , description = "分页获取收入账户详情")
    @PreAuthorize("@pms.hasPermission('platform:accountDetail:info')")
    public ServerResponseEntity<IPage<ShopAccountVO>> getIncomeAccountDetailInfo(PageParam<ShopAccountVO> page, CustomerReqParam accountSearchDTO) {
        IPage<ShopAccountVO> accountDetailPage = payInfoService.pageIncomeAccountDetail(page, accountSearchDTO);
        return ServerResponseEntity.success(accountDetailPage);
    }

    @GetMapping("/getRefundAccountDetailInfo")
    @Operation(summary = "分页获取退款账户详情" , description = "分页获取退款账户详情")
    @PreAuthorize("@pms.hasPermission('platform:accountDetail:info')")
    public ServerResponseEntity<IPage<ShopAccountVO>> getRefundAccountDetailInfo(PageParam<ShopAccountVO> page, CustomerReqParam accountSearchDTO) {
        IPage<ShopAccountVO> accountDetailPage = refundInfoService.listRefundAccountDetail(page, accountSearchDTO);
        return ServerResponseEntity.success(accountDetailPage);
    }

    @GetMapping("/pageShopIncomeAccountDetail")
    @Operation(summary = "分页获取指定店铺的收入结算明细" , description = "根据店铺id，时间获取")
    @PreAuthorize("@pms.hasPermission('platform:accountDetail:page')")
    public ServerResponseEntity<IPage<ShopAccountDetailVO>> pageShopIncomeAccountDetail(PageParam<ShopAccountDetailVO> page, CustomerReqParam customerReqParam) {
        IPage<ShopAccountDetailVO> accountDetailPage = payInfoService.pageShopIncomeAccountDetail(page, customerReqParam);
        return ServerResponseEntity.success(accountDetailPage);
    }

    @GetMapping("/pageShopRefundAccountDetail")
    @Operation(summary = "分页获取指定店铺的退款结算明细", description = "根据店铺id，时间获取")
    @PreAuthorize("@pms.hasPermission('platform:accountDetail:page')")
    public ServerResponseEntity<IPage<ShopAccountDetailVO>> pageShopRefundAccountDetail(PageParam<ShopAccountDetailVO> page, CustomerReqParam customerReqParam) {
        IPage<ShopAccountDetailVO> accountDetailPage = refundInfoService.pageShopRefundAccountDetail(page, customerReqParam);
        return ServerResponseEntity.success(accountDetailPage);
    }

    @GetMapping("/getPayAndRefundInfoForm")
    @Operation(summary = "导出报表" , description = "导出收入以及退款报表")
    @PreAuthorize("@pms.hasPermission('pay:refund:excel')")
    public void getPayAndRefundInfoForm(CustomerReqParam customerReqParam, HttpServletResponse response) {
        if (Objects.isNull(customerReqParam.getStartTime()) || Objects.isNull(customerReqParam.getEndTime())) {
            // 请选择导出报表的交易时间
            throw new YamiShopBindException("yami.finance.form.excel");
        }
        List<ShopAccountDetailVO> incomeAccountDetails = payInfoService.listIncomeAccountDetail(customerReqParam);
        List<ShopAccountDetailVO> refundAccountDetails = refundInfoService.listRefundAccountDetail(customerReqParam);
        Map<Integer, List<ShopAccountDetailVO>> shopAccountDetailMap = new HashMap<>(2);
        shopAccountDetailMap.put(0, incomeAccountDetails);
        shopAccountDetailMap.put(1, refundAccountDetails);
        excelMultiSheet(response, shopAccountDetailMap);
    }

    public void excelMultiSheet(HttpServletResponse response, Map<Integer, List<ShopAccountDetailVO>> shopAccountDetailMap) {
        ExcelWriter writer = ExcelUtil.getBigWriter();
        writer.renameSheet(0, I18nMessage.getMessage("yami.pay.info.reportTitle"));
        Integer paySysType = this.getPaySysType();
        for (int i = 0; i < shopAccountDetailMap.size(); i++) {
            if (i != 0) {
                writer.setSheet(I18nMessage.getMessage("yami.refund.info.reportTitle"));
            }
            handleSheet(writer, paySysType);
            List<ShopAccountDetailVO> shopAccountDetails = shopAccountDetailMap.get(i);
            int row = 1;
            for (ShopAccountDetailVO shopAccountDetail : shopAccountDetails) {
                int firstRow = ++row;
                int col = -1;
                PoiExcelUtil.mergeIfNeed(writer, firstRow, firstRow, ++col, col, row - 1);
                PoiExcelUtil.mergeIfNeed(writer, firstRow, firstRow, ++col, col, shopAccountDetail.getShopName());
                PoiExcelUtil.mergeIfNeed(writer, firstRow, firstRow, ++col, col, shopAccountDetail.getPayTime());
                PoiExcelUtil.mergeIfNeed(writer, firstRow, firstRow, ++col, col, shopAccountDetail.getOrderNumber());
                PoiExcelUtil.mergeIfNeed(writer, firstRow, firstRow, ++col, col, shopAccountDetail.getPayNo());
                PoiExcelUtil.mergeIfNeed(writer, firstRow, firstRow, ++col, col, shopAccountDetail.getBizPayNo());
                PoiExcelUtil.mergeIfNeed(writer, firstRow, firstRow, ++col, col, I18nMessage.getMessage("yami.payType.pay" + shopAccountDetail.getPayType()));
                PoiExcelUtil.mergeIfNeed(writer, firstRow, firstRow, ++col, col, shopAccountDetail.getAlipayAmount());
                PoiExcelUtil.mergeIfNeed(writer, firstRow, firstRow, ++col, col, shopAccountDetail.getWechatAmount());
                PoiExcelUtil.mergeIfNeed(writer, firstRow, firstRow, ++col, col, shopAccountDetail.getScoreCount());
                PoiExcelUtil.mergeIfNeed(writer, firstRow, firstRow, ++col, col, shopAccountDetail.getBalanceAmount());
                // 判断支付系统类型（通联支付移除PayPal支付数据）
                if (!Objects.equals(paySysType, PaySysType.ALLINPAY.value())) {
                    PoiExcelUtil.mergeIfNeed(writer, firstRow, firstRow, ++col, col, shopAccountDetail.getPaypalAmount());
                }
                PoiExcelUtil.mergeIfNeed(writer, firstRow, firstRow, ++col, col, shopAccountDetail.getTotal());
            }
        }
        PoiExcelUtil.writeExcel(response, writer);
    }

    private void handleSheet(ExcelWriter writer, Integer paySysType) {
        String[] headerCn = {"序号","店铺名称","支付时间","订单编号","支付单号","外部流水号","支付方式","支付宝","微信","支付积分","余额支付","PayPal支付","合计"};
        String[] headerEn = {"Serial Number", "Store Name", "Payment Time", "Order Number", "Payment Order Number", "External Streaming Number", "Payment Method", "Alipay", "WeChat", "Payment Credits", "Balance Payment", "PayPal Payment", "Total"};
        List<String> header = Arrays.stream(Objects.equals(I18nMessage.getDbLang(), 0) ? headerCn : headerEn).collect(Collectors.toList());
        // 判断支付系统类型（通联支付移除PayPal支付数据）
        if (Objects.equals(paySysType, PaySysType.ALLINPAY.value())) {
            header.remove(header.size() - 2);
        }
        writer.merge(header.size() - 1, I18nMessage.getMessage("yami.account.info.reportTitle"));
        writer.writeRow(header);
        Sheet sheet = writer.getSheet();
        for (int j = 1; j < header.size() - 1; j++) {
            sheet.setColumnWidth(j, 20 * 256);
        }
    }

    private Integer getPaySysType() {
        PaySettlementConfig config = sysConfigService.getSysConfigObject(Constant.PAY_SETTLEMENT_CONFIG, PaySettlementConfig.class);
        return config.getPaySettlementType();
    }
}
