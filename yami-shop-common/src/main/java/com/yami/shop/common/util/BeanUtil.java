package com.yami.shop.common.util;


import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * 解析SPEL 表达式
 * <AUTHOR>
 * @date 2018/5/21 10:51
 */
public class BeanUtil {
    public static <S, D> List<D> mapAsList(final Iterable<S> sourceObject, Class<D> clazz) {
        return JSONObject.parseArray(JSONObject.toJSONString(sourceObject), clazz);
    }
    public static <S, D> D map(final S sourceObject, Class<D> clazz) {
        return JSONObject.parseObject(JSONObject.toJSONString(sourceObject), clazz);
    }
}
