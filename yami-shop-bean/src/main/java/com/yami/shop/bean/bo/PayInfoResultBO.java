package com.yami.shop.bean.bo;

import lombok.Data;

/**
 * 支付后，微信/支付宝返回的一些基础数据
 * <AUTHOR>
 */
@Data
public class PayInfoResultBO {

    /**
     * 商城支付单号
     */
    private String payNo;

    /**
     * 第三方订单流水号
     */
    private String bizPayNo;

    /**
     * 第三方订单订单号
     */
    private String bizOrderNo;

    /**
     * 是否支付成功
     */
    private Boolean isPaySuccess;

    /**
     * 支付成功的标记
     */
    private String successString;

    /**
     * 支付金额
     */
    private Double payAmount;

    /**
     * 回调内容
     */
    private String callbackContent;

    /**
     * 支付系统类型(0:默认，1:通联)
     */
    private Integer paySysType;

    /**
     * 拓展信息
     */
    private String extendInfo;

    /**
     * 商城退款单号
     */
    private String refundSn;

    /**
     * 是否为支付宝退款回调
     */
    private Boolean isAliRefund;
}
