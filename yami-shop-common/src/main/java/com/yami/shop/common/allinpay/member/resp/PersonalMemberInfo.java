package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-04-27
 */
public class PersonalMemberInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    private String name;

    /**
     * 国家 非必填
     */
    private String country;

    /**
     * 省份 非必填
     */
    private String province;

    /**
     * 地区 非必填
     */
    private String area;

    /**
     * 地址 非必填
     */
    private String address;

    /**
     * 用户状态
     */
    private Integer userState;

    /**
     * 会员类型
     */
    private Integer memberType;

    /**
     * 云账户用户id
     */
    private String userId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 身份证号码
     */
    private String identityCardNo;

    /**
     * 是否绑定手机
     */
    private Boolean isPhoneChecked;

    /**
     * 创建时间
     */
    private String registerTime;

    /**
     * 创建ip
     */
    private String registerIp;

    /**
     * 支付失败次数
     */
    private Long payFailAmount;

    /**
     * 是否进行实名认证
     */
    private Boolean isIdentityChecked;

    /**
     * 实名认证时间
     */
    private String realNameTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 访问终端类型
     */
    private String source;

    /**
     * 是否设置支付密码
     */
    private Boolean isSetPayPwd;

    /**
     * 账户提现签约协议时间
     */
    private String signAcctProtocolTime;

    /**
     * 账户提现协议编号
     */
    private String acctProtocolNo;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getUserState() {
        return userState;
    }

    public void setUserState(Integer userState) {
        this.userState = userState;
    }

    public Integer getMemberType() {
        return memberType;
    }

    public void setMemberType(Integer memberType) {
        this.memberType = memberType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getIdentityCardNo() {
        return identityCardNo;
    }

    public void setIdentityCardNo(String identityCardNo) {
        this.identityCardNo = identityCardNo;
    }

    public Boolean getPhoneChecked() {
        return isPhoneChecked;
    }

    public void setPhoneChecked(Boolean phoneChecked) {
        isPhoneChecked = phoneChecked;
    }

    public String getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(String registerTime) {
        this.registerTime = registerTime;
    }

    public String getRegisterIp() {
        return registerIp;
    }

    public void setRegisterIp(String registerIp) {
        this.registerIp = registerIp;
    }

    public Long getPayFailAmount() {
        return payFailAmount;
    }

    public void setPayFailAmount(Long payFailAmount) {
        this.payFailAmount = payFailAmount;
    }

    public Boolean getIdentityChecked() {
        return isIdentityChecked;
    }

    public void setIdentityChecked(Boolean identityChecked) {
        isIdentityChecked = identityChecked;
    }

    public String getRealNameTime() {
        return realNameTime;
    }

    public void setRealNameTime(String realNameTime) {
        this.realNameTime = realNameTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Boolean getSetPayPwd() {
        return isSetPayPwd;
    }

    public void setSetPayPwd(Boolean setPayPwd) {
        isSetPayPwd = setPayPwd;
    }

    public String getSignAcctProtocolTime() {
        return signAcctProtocolTime;
    }

    public void setSignAcctProtocolTime(String signAcctProtocolTime) {
        this.signAcctProtocolTime = signAcctProtocolTime;
    }

    public String getAcctProtocolNo() {
        return acctProtocolNo;
    }

    public void setAcctProtocolNo(String acctProtocolNo) {
        this.acctProtocolNo = acctProtocolNo;
    }

    @Override
    public String toString() {
        return "PersonalMemberInfo{" +
                "name='" + name + '\'' +
                ", country='" + country + '\'' +
                ", province='" + province + '\'' +
                ", area='" + area + '\'' +
                ", address='" + address + '\'' +
                ", userState=" + userState +
                ", memberType=" + memberType +
                ", userId='" + userId + '\'' +
                ", phone='" + phone + '\'' +
                ", identityCardNo='" + identityCardNo + '\'' +
                ", isPhoneChecked=" + isPhoneChecked +
                ", registerTime='" + registerTime + '\'' +
                ", registerIp='" + registerIp + '\'' +
                ", payFailAmount=" + payFailAmount +
                ", isIdentityChecked=" + isIdentityChecked +
                ", realNameTime='" + realNameTime + '\'' +
                ", remark='" + remark + '\'' +
                ", source='" + source + '\'' +
                ", isSetPayPwd=" + isSetPayPwd +
                ", signAcctProtocolTime='" + signAcctProtocolTime + '\'' +
                ", acctProtocolNo='" + acctProtocolNo + '\'' +
                '}';
    }
}
