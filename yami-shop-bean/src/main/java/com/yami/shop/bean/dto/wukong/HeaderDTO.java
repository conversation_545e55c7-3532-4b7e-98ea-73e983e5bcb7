package com.yami.shop.bean.dto.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 消息头
 * <AUTHOR>
 */
public class HeaderDTO {

    @Schema(description = "是否不存储消息 0.存储 1.不存储")
    private Integer no_persist;

    @Schema(description = "是否显示红点计数，0.不显示 1.显示")
    private Integer red_dot;

    @Schema(description = "是否是写扩散，这里一般是0，只有cmd消息才是1")
    private Integer sync_once;

    public HeaderDTO() {
    }

    public HeaderDTO(Integer no_persist, Integer red_dot, Integer sync_once) {
        this.no_persist = no_persist;
        this.red_dot = red_dot;
        this.sync_once = sync_once;
    }

    public Integer getNo_persist() {
        return no_persist;
    }

    public void setNo_persist(Integer no_persist) {
        this.no_persist = no_persist;
    }

    public Integer getRed_dot() {
        return red_dot;
    }

    public void setRed_dot(Integer red_dot) {
        this.red_dot = red_dot;
    }

    public Integer getSync_once() {
        return sync_once;
    }

    public void setSync_once(Integer sync_once) {
        this.sync_once = sync_once;
    }

    @Override
    public String toString() {
        return "HeaderDTO{" +
                "no_persist=" + no_persist +
                ", red_dot=" + red_dot +
                ", sync_once=" + sync_once +
                '}';
    }
}
