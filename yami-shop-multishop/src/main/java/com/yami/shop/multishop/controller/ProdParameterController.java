package com.yami.shop.multishop.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.ProdParameter;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.ProdParameterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2021-11-01 16:50:52
 */
@RestController
@RequestMapping("/prod/prodParameter")
@Tag(name = "商家端商品参数接口")
@AllArgsConstructor
public class ProdParameterController {

    private final ProdParameterService prodParameterService;

    @GetMapping("/page")
    @Operation(summary = "返回商品参数分页数据")
    public ServerResponseEntity<IPage<ProdParameter>> getProdParameterPage(PageParam<ProdParameter> page, ProdParameter prodParameter) {
        return ServerResponseEntity.success(prodParameterService.page(page, new LambdaQueryWrapper<ProdParameter>()));
    }

    @GetMapping("/info/{prodParameterId}")
    @Operation(summary = "根据id查询")
    @Parameter(name = "prodParameterId", description = "商品参数id" , required = true)
    public ServerResponseEntity<ProdParameter> getById(@PathVariable("prodParameterId") Long prodParameterId) {
        return ServerResponseEntity.success(prodParameterService.getById(prodParameterId));
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('multishop:prodParameter:save')")
    @Operation(summary = "新增商品参数")
    public ServerResponseEntity<Boolean> save(@RequestBody @Valid ProdParameter prodParameter) {
        return ServerResponseEntity.success(prodParameterService.save(prodParameter));
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('multishop:prodParameter:update')")
    @Operation(summary = "修改商品参数")
    public ServerResponseEntity<Boolean> updateById(@RequestBody @Valid ProdParameter prodParameter) {
        return ServerResponseEntity.success(prodParameterService.updateById(prodParameter));
    }

    @DeleteMapping("/{prodParameterId}")
    @PreAuthorize("@pms.hasPermission('multishop:prodParameter:delete')")
    @Operation(summary = "根据id删除商品参数")
    @Parameter(name = "prodParameterId", description = "商品参数id" , required = true)
    public ServerResponseEntity<Boolean> removeById(@PathVariable Long prodParameterId) {
        return ServerResponseEntity.success(prodParameterService.removeById(prodParameterId));
    }
}
