package com.yami.shop.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 库存点类型
 * <AUTHOR>
 * @since 2023-11-09
 */
@Getter
@AllArgsConstructor
public enum StockPointType {

    /**
     * 仓库
     */
    WAREHOUSE(1, "仓库", "Warehouse", "yami.stock.point.type.warehouse"),

    /**
     * 门店
     */
    STATION(2, "门店", "Station", "yami.stock.point.type.station");

    private final int value;

    private final String desc;

    private final String descEN;

    private final String i18nTag;

    public static StockPointType instance(int value) {
        for (StockPointType stockPointType : StockPointType.values()) {
            if (Objects.equals(value, stockPointType.value)) {
                return stockPointType;
            }
        }
        return null;
    }
}
