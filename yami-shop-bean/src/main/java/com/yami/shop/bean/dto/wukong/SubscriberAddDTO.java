package com.yami.shop.bean.dto.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 添加订阅者
 * <AUTHOR>
 */
public class SubscriberAddDTO {

    @Schema(description = "频道的唯一ID")
    private String channel_id;

    @Schema(description = "频道的类型 1.个人频道 2.群聊频道")
    private Integer channel_type;

    @Schema(description = "是否重置订阅者 （0.不重置 1.重置），选择重置，则删除旧的订阅者，选择不重置则保留旧的订阅者")
    private Integer reset;

    @Schema(description = "订阅者集合 [uid1,uid2,...]")
    private List<String> subscribers;

    @Schema(description = "是否为临时频道 0.否 1.是 临时频道的订阅者将在下次重启后自动删除")
    private Integer temp_subscriber;

    public SubscriberAddDTO() {
    }

    public SubscriberAddDTO(String channel_id, List<String> subscribers) {
        this.channel_id = channel_id;
        this.subscribers = subscribers;
        this.channel_type = 2;
        this.reset = 0;
        this.temp_subscriber = 0;
    }

    public String getChannel_id() {
        return channel_id;
    }

    public void setChannel_id(String channel_id) {
        this.channel_id = channel_id;
    }

    public Integer getChannel_type() {
        return channel_type;
    }

    public void setChannel_type(Integer channel_type) {
        this.channel_type = channel_type;
    }

    public Integer getReset() {
        return reset;
    }

    public void setReset(Integer reset) {
        this.reset = reset;
    }

    public List<String> getSubscribers() {
        return subscribers;
    }

    public void setSubscribers(List<String> subscribers) {
        this.subscribers = subscribers;
    }

    public Integer getTemp_subscriber() {
        return temp_subscriber;
    }

    public void setTemp_subscriber(Integer temp_subscriber) {
        this.temp_subscriber = temp_subscriber;
    }

    @Override
    public String toString() {
        return "SubscriberAddDTO{" +
                "channel_id='" + channel_id + '\'' +
                ", channel_type=" + channel_type +
                ", reset=" + reset +
                ", subscribers=" + subscribers +
                ", temp_subscriber=" + temp_subscriber +
                '}';
    }
}
