package com.yami.shop.distribution.common.service.impl;

import cn.hutool.core.util.PhoneUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yami.shop.common.util.PrincipalUtil;
import com.yami.shop.distribution.common.dao.DistributionUserBanMapper;
import com.yami.shop.distribution.common.model.DistributionUserBan;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.service.DistributionUserBanService;
import com.yami.shop.distribution.common.vo.DistributionUserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> on 2019/04/01.
 */
@Service
@RequiredArgsConstructor
public class DistributionUserBanServiceImpl extends ServiceImpl<DistributionUserBanMapper, DistributionUserBan> implements DistributionUserBanService {

    private final DistributionUserBanMapper distributionUserBanMapper;

    @Override
    public IPage<DistributionUserBan> distributionUserBanPage(Page page, Long shopId, DistributionParam distributionParam, DistributionUserBan distributionUserBan) {
        if (distributionUserBan.getSortParam() == 0 ){
            distributionUserBan.setSortParam(1);
            distributionUserBan.setSortType(2);
        }
        IPage<DistributionUserBan> userBanPage = distributionUserBanMapper.distributionUserBanPage(page, shopId, distributionParam, distributionUserBan);
        for (DistributionUserBan record : userBanPage.getRecords()) {
            DistributionUserVO distributionUser = record.getDistributionUser();
            distributionUser.setUserMobile(PhoneUtil.hideBetween(distributionUser.getUserMobile()).toString());
            if (PrincipalUtil.isMobile(distributionUser.getNickName())) {
                distributionUser.setNickName(PhoneUtil.hideBetween(distributionUser.getNickName()).toString());
            }
        }
        return userBanPage;
    }

}
