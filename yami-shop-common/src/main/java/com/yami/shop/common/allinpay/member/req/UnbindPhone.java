package com.yami.shop.common.allinpay.member.req;

import java.io.Serial;
import java.io.Serializable;/**
 * <AUTHOR>
 */
public class UnbindPhone extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "unbindPhone";

    /**
     * 手机号
     */
    private String phone;

    /**
     * 验证码
     */
    private String verificationCode;

    public UnbindPhone() {
    }

    public UnbindPhone(String bizUserId, String phone, String verificationCode) {
        this.bizUserId = bizUserId;
        this.phone = phone;
        this.verificationCode = verificationCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }
}
