package com.yami.shop.common.allinpay.member.req;

import cn.hutool.poi.word.PicType;

import java.io.Serial;
import java.io.Serializable;

/**
 * 影印件采集（文件上传模式）
 *
 * <AUTHOR>
 */
public class IdCardCollectByFileUpload extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "idcardCollectByFileUpload";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    private String bizUserId;

    /**
     * 请求流水号, 15位数字，不可包含“|”字符，客户确保唯一
     */
    private String reqSerialNo;

    /**
     * 影印件核对结果异步通知地址
     */
    private String ocrComparisonResultBackUrl;

    /**
     * 身份证人像面,上传身份证影印件时，则人像面和国徽面必须同时上传
     * 通过【文件上传下载接口】上传文件图片后获取的token，有效期30分钟，图片不超过2M
     */
    private String legalPicFaceToken;

    /**
     * 身份证国徽面,上传身份证影印件时，则人像面和国徽面必须同时上传
     * 通过【文件上传下载接口】上传文件图片后获取的token，有效期30分钟，图片不超过2M
     */
    private String legalPicEmblemToken;

    /**
     * 其他影印件类型，文件上传模式 1-营业执照/统一信用证
     *
     * @see PicType
     */
    private Integer picType;

    /**
     * 影印件图片文件,“其他影印件类型”时，通过本字段上送图片文件内容
     * 通过【文件上传下载接口】上传文件图片后获取的token，有效期30分钟，图片不超过2M
     */
    private String token;

    @Override
    public String getBizUserId() {
        return bizUserId;
    }

    @Override
    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getReqSerialNo() {
        return reqSerialNo;
    }

    public void setReqSerialNo(String reqSerialNo) {
        this.reqSerialNo = reqSerialNo;
    }

    public String getOcrComparisonResultBackUrl() {
        return ocrComparisonResultBackUrl;
    }

    public void setOcrComparisonResultBackUrl(String ocrComparisonResultBackUrl) {
        this.ocrComparisonResultBackUrl = ocrComparisonResultBackUrl;
    }

    public String getLegalPicFaceToken() {
        return legalPicFaceToken;
    }

    public void setLegalPicFaceToken(String legalPicFaceToken) {
        this.legalPicFaceToken = legalPicFaceToken;
    }

    public String getLegalPicEmblemToken() {
        return legalPicEmblemToken;
    }

    public void setLegalPicEmblemToken(String legalPicEmblemToken) {
        this.legalPicEmblemToken = legalPicEmblemToken;
    }

    public Integer getPicType() {
        return picType;
    }

    public void setPicType(Integer picType) {
        this.picType = picType;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @Override
    public String toString() {
        return "IdCardCollectByFileUpload{" +
                "bizUserId='" + bizUserId + '\'' +
                ", reqSerialNo='" + reqSerialNo + '\'' +
                ", ocrComparisonResultBackUrl='" + ocrComparisonResultBackUrl + '\'' +
                ", legalPicFaceToken='" + legalPicFaceToken + '\'' +
                ", legalPicEmblemToken='" + legalPicEmblemToken + '\'' +
                ", picType=" + picType +
                ", token='" + token + '\'' +
                '}';
    }
}
