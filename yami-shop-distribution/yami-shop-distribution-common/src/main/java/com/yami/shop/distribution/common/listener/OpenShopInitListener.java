package com.yami.shop.distribution.common.listener;

import com.yami.shop.bean.event.OpenShopInitEvent;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 开店初始化事件
 * <AUTHOR>
 */
@Slf4j
@Component("distributionOpenShopInitListener")
@AllArgsConstructor
public class OpenShopInitListener {

//    private final DistributionBasicSetService distributionBasicSetService;

    @EventListener(OpenShopInitEvent.class)
    public void openShopInitDistributionSetEvent(OpenShopInitEvent event) {
//        ShopDetail shopDetail = event.getShopDetail();
//        // 添加设置
//        DistributionBasicSet distributionBasicSet = new DistributionBasicSet();
//        // 分销互购
//        distributionBasicSet.setParallelDeal(0);
//        // 分销自购
//        distributionBasicSet.setOwnBuyAward(0);
//        // 会员价购买
//        distributionBasicSet.setVipBuyAward(0);
//        // 分销总开关
//        distributionBasicSet.setDistributionSwitch(1);
//
//        // 0表示为系统设置
//        distributionBasicSet.setModifierId(0L);
//        distributionBasicSet.setUpdateTime(new Date());
//        distributionBasicSet.setShopId(shopDetail.getShopId());
//        distributionBasicSet.setShopName(shopDetail.getShopName());
//        distributionBasicSetService.save(distributionBasicSet);
    }
}
