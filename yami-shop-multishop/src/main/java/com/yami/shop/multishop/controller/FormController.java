package com.yami.shop.multishop.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.Form;
import com.yami.shop.bean.model.FormItem;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.FormService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> on 2018/11/26.
 */
@RestController
@RequestMapping("/admin/form")
@Tag(name = "数据报表接口")
@AllArgsConstructor
public class FormController {

    private final FormService formService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('admin:form:page')")
    @Operation(summary = "根据参数分页获取报表信息" , description = "根据参数分页获取报表信息")
    public ServerResponseEntity<IPage<Form>> page(Form form, PageParam<Form> page) {
        IPage<Form> iPage = formService.page(page, new LambdaQueryWrapper<Form>()
                .eq(Form::getShopId,SecurityUtils.getShopUser().getShopId())
                .like(StrUtil.isNotBlank(form.getFormName()),Form::getFormName,form.getFormName())
                .eq(Objects.nonNull(form.getTimeType()),Form::getTimeType,form.getTimeType())
                .orderByAsc(Form::getSeq));
        return ServerResponseEntity.success(iPage);
    }

    @GetMapping("/info/{formId}")
    @Operation(summary = "根据报表id获取报表信息" , description = "根据报表id获取报表信息")
    @PreAuthorize("@pms.hasPermission('admin:form:info')")
    public ServerResponseEntity<Form> info(@PathVariable("formId") Long formId) {
        Form form = formService.getById(formId);
        return ServerResponseEntity.success(form);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('admin:form:save')")
    @Operation(summary = "新增报表" , description = "新增报表")
    public ServerResponseEntity<Void> save(@RequestBody @Valid Form form) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        form.setShopId(shopId);
        formService.saveForm(form);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('admin:form:update')")
    @Operation(summary = "修改报表" , description = "修改报表")
    public ServerResponseEntity<Void> update(@RequestBody @Valid Form form) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        form.setShopId(shopId);
        formService.updateForm(form);
        return ServerResponseEntity.success();
    }

    @DeleteMapping("/{formId}")
    @PreAuthorize("@pms.hasPermission('admin:form:delete')")
    @Operation(summary = "根据报表id删除报表" , description = "根据报表id删除报表")
    public ServerResponseEntity<Void> delete(@PathVariable("formId") Long formId) {
        formService.removeById(formId);
        return ServerResponseEntity.success();
    }

    @GetMapping("/getFormItem")
    @Operation(summary = "获取报表项列表" , description = "获取报表项列表")
    @PreAuthorize("@pms.hasPermission('admin:form:list')")
    public ServerResponseEntity<List<FormItem>> getFormItem() {
        List<FormItem> formItemEnumList = formService.getFormItem(2, I18nMessage.getDbLang());
        return ServerResponseEntity.success(formItemEnumList);
    }

    @GetMapping("/formExcel")
    @PreAuthorize("@pms.hasPermission('admin:form:formExcel')")
    @Operation(summary = "根据报表id生成对应的报表统计数据" , description = "根据报表id生成对应的报表统计数据")
    public void formExcel(@RequestParam("formId") Long formId, HttpServletResponse response) {
        formService.formExcel(formId,response);
    }

    @GetMapping("/getRecommendFormList")
    @Operation(summary = "获取全部推荐报表" , description = "获取全部推荐报表")
    @PreAuthorize("@pms.hasPermission('admin:form:list')")
    public ServerResponseEntity<List<Form>> getRecommendFormList() {
        List<Form> formList = formService.getRecommendFormList();
        return ServerResponseEntity.success(formList);
    }

}
