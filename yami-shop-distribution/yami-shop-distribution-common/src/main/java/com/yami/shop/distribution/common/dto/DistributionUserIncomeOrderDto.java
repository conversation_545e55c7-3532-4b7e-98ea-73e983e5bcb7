package com.yami.shop.distribution.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
/**
 * <AUTHOR>
 */
@Data
@Schema(description = "分销员推广订单对象")
public class DistributionUserIncomeOrderDto {

    @Schema(description = "订单编号" )
    private String orderNumber;

    @Schema(description = "收入金额" )
    private Double incomeAmount;

    @Schema(description = "商品数量" )
    private String productNums;

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "实际支付" )
    private Double actualTotal;

    @Schema(description = "订单状态(0:待支付 1:待结算 2:已结算 -1:订单失效)" )
    private Integer state;

    @Schema(description = "分销订单项列表" )
    private List<DistributionOrderItemDto> orderItemDtoList;

}
