package com.yami.shop.distribution.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-09-06 14:20:02
 */
@Data
@Schema(description = "分销配置")
public class DistributionConfigDTO {

    @Schema(description = "分销开关： 0:关闭 1:开启" )
    private Integer distributionSwitch;


    /**
     * 申请条件配置-------------------------------------------------------------
     */

    @Schema(description = "0:人工判定 1:系统判定" )
    private Integer autoCheck;

    @Schema(description = "购买指定商品，确认收货任意一件商品后计入" )
    private List<Long> prodIdList;

    @Schema(description = "消费金额大于等于expenseAmount元，实付金额+积分抵扣+余额抵扣总金额，收货后计入" )
    @DecimalMax(value = "92233720368547758", message = "最大不能超过" + 92233720368547758L)
    private BigDecimal expenseAmount;

    @Schema(description = "消费笔数大于等于expenseNumber次,下单次数，收货后计入" )
    private Integer expenseNumber;

    @Schema(description = "是否需要真实姓名 true 需要 false不需要" )
    private Boolean realName;

    @Schema(description = "是否需要身份证号码 true 需要 false不需要" )
    private Boolean identityCardPic;

    @Schema(description = "是否需要身份证照片 true 需要 false不需要" )
    private Boolean identityCardNumber;

    @Schema(description = "提现发放方式： 0.无需审核直接发放，1.审核后系统发放，2.审核后人工发放" )
    private Integer withdrawal;

    /**
     * 提现申请配置-------------------------------------------------------------
     */

    @Schema(description = "提现频率(天)" )
    private Integer frequency;

    @Schema(description = "单笔提现最高（元）" )
    @Max(value = 20000, message = "单笔提现最高不能超过2W")
    private BigDecimal amountMax;

    @Schema(description = "单笔提现最低 （元）" )
    @Max(value = 19999, message = "单笔提现最低不能超过19999元")
    @Min(value = 1, message = "单笔提现最低不能低于1元")
    private BigDecimal amountMin;

    @Schema(description = "打款说明" )
    private String paymentExplain;

    @Schema(description = "提现次数" )
    @Min(value = 0, message = "提现次数不能小于0")
    private Integer number;
}
