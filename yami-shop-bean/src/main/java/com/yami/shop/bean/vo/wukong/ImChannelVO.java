package com.yami.shop.bean.vo.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 频道关联表
 * <AUTHOR>
 */
@Schema(description = "频道关联表")
public class ImChannelVO {

    @Schema(description = "频道id")
    private String channelId;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "店铺名")
    private String shopName;

    @Schema(description = "订阅者信息")
    private List<SubscriberVO> subscribers;

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public List<SubscriberVO> getSubscribers() {
        return subscribers;
    }

    public void setSubscribers(List<SubscriberVO> subscribers) {
        this.subscribers = subscribers;
    }

    @Override
    public String toString() {
        return "ImChannelVO{" +
                "channelId='" + channelId + '\'' +
                ", shopId=" + shopId +
                ", shopName='" + shopName + '\'' +
                ", subscribers=" + subscribers +
                '}';
    }
}
