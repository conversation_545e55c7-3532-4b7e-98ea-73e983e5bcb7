package com.yami.shop.multishop.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.app.dto.NoticeDto;
import com.yami.shop.bean.enums.NoticeType;
import com.yami.shop.bean.model.Notice;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.NoticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 公告管理
 *
 * <AUTHOR>
 * @date
 */
@RestController
@AllArgsConstructor
@RequestMapping("/shop/notice")
@Tag(name = "公告接口")
public class NoticeController {

    private final NoticeService noticeService;


    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('shop:notice:page')")
    @Operation(summary = "分页获取公告列表" , description = "分页获取公告列表")
    public ServerResponseEntity<IPage<Notice>> getNoticePage(PageParam<Notice> page, Notice notice) {
        IPage<Notice> noticePage = noticeService.page(page, new LambdaQueryWrapper<Notice>()
                .eq(Notice::getShopId, SecurityUtils.getShopUser().getShopId())
                .eq(notice.getStatus() != null, Notice::getStatus, notice.getStatus())
                .eq(notice.getIsTop() != null, Notice::getIsTop, notice.getIsTop())
                .like(notice.getTitle() != null, Notice::getTitle, notice.getTitle()).orderByDesc(Notice::getUpdateTime));
        return ServerResponseEntity.success(noticePage);
    }

    @GetMapping("/listPage")
    @Operation(summary = "分页查询平台公告" , description = "分页查询平台公告")
    public ServerResponseEntity<IPage<Notice>> getNoticeListPage(PageParam<Notice> page, Notice notice) {
        notice.setAccountId(SecurityUtils.getShopUser().getShopId().toString());
        notice.setType(NoticeType.TO_MULTISHOP.value());
        notice.setSendTime(new Date());
        return ServerResponseEntity.success(noticeService.listPage(page,notice));
    }


    @GetMapping("/info/{id}")
    @Operation(summary = "通过公告id查询公告信息" , description = "通过公告id查询公告信息")
    @PreAuthorize("@pms.hasPermission('shop:notice:info')")
    public ServerResponseEntity<Notice> getById(@PathVariable("id") Long id) {
        return ServerResponseEntity.success(noticeService.getById(id));
    }

    @SysLog("新增公告管理")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('shop:notice:save')")
    @Operation(summary = "新增公告" , description = "新增公告")
    public ServerResponseEntity<Void> save(@RequestBody @Valid NoticeDto noticeDto) {
        Notice notice = BeanUtil.map(noticeDto, Notice.class);
        notice.setShopId(SecurityUtils.getShopUser().getShopId());
        if (notice.getStatus() == 1) {
            notice.setPublishTime(new Date());
        }
        notice.setTypes(String.valueOf(NoticeType.TO_USER.value()));
        notice.setUpdateTime(new Date());
        notice.setImmediatelySend(1);
        noticeService.save(notice);

        noticeService.removeNoticeCacheById(notice.getId());
        noticeService.removeTopNoticeListCacheByShopId(SecurityUtils.getShopUser().getShopId());
        return ServerResponseEntity.success();
    }

    @SysLog("修改公告管理")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('shop:notice:update')")
    @Operation(summary = "修改公告" , description = "修改公告")
    public ServerResponseEntity<Void> updateById(@RequestBody @Valid NoticeDto noticeDto) {
        Notice oldNotice = noticeService.getById(noticeDto.getId());
        Notice notice = BeanUtil.map(noticeDto, Notice.class);
        notice.setShopId(SecurityUtils.getShopUser().getShopId());
        if (oldNotice.getStatus() == 0 && notice.getStatus() == 1) {
            notice.setPublishTime(new Date());
        }
        notice.setUpdateTime(new Date());
        noticeService.updateById(notice);

        noticeService.removeTopNoticeListCacheByShopId(SecurityUtils.getShopUser().getShopId());
        noticeService.removeNoticeCacheById(notice.getId());
        return ServerResponseEntity.success();
    }

    @SysLog("删除公告管理")
    @DeleteMapping("/{id}")
    @PreAuthorize("@pms.hasPermission('shop:notice:delete')")
    @Operation(summary = "通过id删除公告" , description = "通过id删除公告")
    public ServerResponseEntity<Void> removeById(@PathVariable Long id) {
        noticeService.removeByIdAndShopId(id, SecurityUtils.getShopUser().getShopId());
        noticeService.removeTopNoticeListCacheByShopId(SecurityUtils.getShopUser().getShopId());
        noticeService.removeNoticeCacheById(id);
        return ServerResponseEntity.success();
    }

}
