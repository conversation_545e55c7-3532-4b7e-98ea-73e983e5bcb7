package com.yami.shop.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.dto.BrandShopDTO;
import com.yami.shop.bean.model.Brand;
import com.yami.shop.bean.model.Category;
import com.yami.shop.bean.model.CategoryShop;
import com.yami.shop.bean.model.SigningAuditing;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.platform.util.SecurityUtils;
import com.yami.shop.service.SigningAuditingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2021/8/19 13:51
 */
@RestController
@RequestMapping("/platform/signingAuditing")
@Tag(name = "签约信息相关接口")
@AllArgsConstructor
public class SigningAuditingController {

    private final SigningAuditingService signingAuditingService;

    @GetMapping("/listApplySigningCategory")
    @Operation(summary = "获取可以签约的平台分类列表（已经签约的平台分类不会返回）" , description = "获取可以签约的平台分类列表（已经签约的平台分类不会返回）")
    @Parameter(name = "shopId", description = "店铺id" )
    public ServerResponseEntity<List<Category>> listApplySigningCategory(@RequestParam("shopId") Long shopId) {
        List<Category> categoryList = signingAuditingService.listApplySigningCategory(shopId);
        return ServerResponseEntity.success(categoryList);
    }

    @GetMapping("/listApplySigningBrand")
    @Operation(summary = "获取可以签约的平台品牌列表（已经签约的平台品牌不会返回)" , description = "获取可以签约的平台品牌列表（已经签约的平台品牌不会返回)")
    public ServerResponseEntity<List<Brand>> listApplySigningBrand(Brand brand) {
        Long shopId = brand.getShopId();
        if (Objects.isNull(shopId)) {
            return ServerResponseEntity.success(new ArrayList<>());
        }
        brand.setShopId(null);
        List<Brand> brandList = signingAuditingService.listApplySigningBrand(shopId, brand);
        return ServerResponseEntity.success(brandList);
    }

    @GetMapping("/page")
    @Operation(summary = "分页获取待审核的签约信息" , description = "分页获取待审核的签约信息")
    public ServerResponseEntity<IPage<SigningAuditing>> page(PageParam<SigningAuditing> page, SigningAuditing signingAuditing) {
        IPage<SigningAuditing> signingAuditingPage = signingAuditingService.pageSigningAuditing(page, signingAuditing);
        return ServerResponseEntity.success(signingAuditingPage);
    }

    @PutMapping("/audit")
    @Operation(summary = "审核签约信息" , description = "审核签约信息")
    public ServerResponseEntity<Void> audit(@Valid @RequestBody SigningAuditing signingAuditing) {
        signingAuditing.setAuditorId(SecurityUtils.getSysUser().getUserId());
        signingAuditingService.audit(signingAuditing);
        return ServerResponseEntity.success();
    }

    @PutMapping("/updateCategoryRate")
    @Operation(summary = "更新店铺签约分类自定义扣率" , description = "更新店铺签约分类自定义扣率")
    @Parameters(value = {
            @Parameter(name = "categoryShopId", description = "店铺签约分类id" ),
            @Parameter(name = "rate", description = "扣率" )
    })
    public ServerResponseEntity<Void> updateCategoryRate(@RequestParam("categoryShopId") Long categoryShopId, @RequestParam("rate") Double rate) {
        signingAuditingService.updateCategoryRate(categoryShopId, rate);
        return ServerResponseEntity.success();
    }

    @PostMapping("/addSigningCategory")
    @Operation(summary = "增加签约分类" , description = "增加签约分类")
    @Parameters(value = {
            @Parameter(name = "categoryShopList", description = "店铺签约分类id列表" ),
            @Parameter(name = "shopId", description = "店铺id" )
    })
    public ServerResponseEntity<Void> addSigningCategory(@RequestBody List<CategoryShop> categoryShopList, @RequestParam("shopId") Long shopId) {
        signingAuditingService.addSigningCategory(categoryShopList, shopId);
        return ServerResponseEntity.success();
    }

    @PostMapping("/addSigningBrand")
    @Operation(summary = "增加签约品牌" , description = "增加签约品牌")
    @Parameters(value = {
            @Parameter(name = "brandShopList", description = "店铺签约品牌id列表" ),
            @Parameter(name = "shopId", description = "店铺id" )
    })
    public ServerResponseEntity<Void> addSigningBrand(@RequestBody List<BrandShopDTO> brandShopList, @RequestParam("shopId") Long shopId) {
        signingAuditingService.addSigningBrand(brandShopList, shopId);
        return ServerResponseEntity.success();
    }
}
