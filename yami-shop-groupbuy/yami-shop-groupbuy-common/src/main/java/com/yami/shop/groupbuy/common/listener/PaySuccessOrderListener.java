package com.yami.shop.groupbuy.common.listener;

import com.yami.shop.bean.enums.OrderType;
import com.yami.shop.bean.event.PaySuccessOrderEvent;
import com.yami.shop.bean.order.PaySuccessOrderOrder;
import com.yami.shop.groupbuy.common.service.GroupPayService;
import lombok.AllArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 团购支付回调
 *
 * <AUTHOR>
 */
@Component("groupPaySuccessListener")
@AllArgsConstructor
public class PaySuccessOrderListener {

    private GroupPayService groupPayService;

    /**
     * 团购支付成功回调
     */
    @EventListener(PaySuccessOrderEvent.class)
    @Order(PaySuccessOrderOrder.GROUPON)
    public void grouponPaySuccessListener(PaySuccessOrderEvent event) {
        List<com.yami.shop.bean.model.Order> orders = event.getOrders();

        for (com.yami.shop.bean.model.Order order : orders) {
            if (!Objects.equals(order.getOrderType(), OrderType.GROUP.value())) {
                continue;
            }
            // 支付成功回调通知处理
            groupPayService.paySuccessNoticeHandle(order);
        }

    }
}
