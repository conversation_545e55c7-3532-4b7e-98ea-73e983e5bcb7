package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 调拨订单VO
 * <AUTHOR>
 * @since 2023-11-20
 */
@Data
public class AllotOrderVO {

    @Schema(description = "调拨订单id")
    private Long allotOrderId;

    @Schema(description = "调出点仓库id")
    private Long outWarehouseId;

    @Schema(description = "调出点库存点类型(1:仓库, 2:门店)")
    private Integer outStockPointType;

    @Schema(description = "调出点名称")
    private String outStockPointName;

    @Schema(description = "调入点仓库id")
    private Long inWarehouseId;

    @Schema(description = "调入点库存点类型(1:仓库, 2:门店)")
    private Integer inStockPointType;

    @Schema(description = "调入点名称")
    private String inStockPointName;

    @Schema(description = "物流公司id")
    private Long dvyCompanyId;

    @Schema(description = "物流公司名称")
    private String dvyCompanyName;

    @Schema(description = "物流方式")
    private Integer dvyType;

    @Schema(description = "物流单号")
    private String dvyOrderNumber;

    @Schema(description = "状态(0:作废, 1:待入库, 2:部分入库, 3:已完成)")
    private Integer status;

    @Schema(description = "总调拨数量")
    private Integer totalAllotCount;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "系统类型")
    private Integer sysType;

    @Schema(description = "调拨订单商品集合")
    private List<AllotOrderItemVO> allotOrderItemVOList;
}
