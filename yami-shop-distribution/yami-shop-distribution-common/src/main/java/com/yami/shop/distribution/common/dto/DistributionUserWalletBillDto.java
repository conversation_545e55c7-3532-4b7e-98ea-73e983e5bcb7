package com.yami.shop.distribution.common.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
/**
 * <AUTHOR>
 */
@Data
@Schema(description = "钱包流水记录")
public class DistributionUserWalletBillDto {
    /**
     * 待结算金额变更数额
     */
    @Schema(description = "待结算金额变更数额" )
    private Double unsettledAmount;
    /**
     * 可提现金额变更数额
     */
    @Schema(description = "可提现金额变更数额" )
    private Double settledAmount;
    /**
     * 失效金额变更数额
     */
    @Schema(description = "失效金额变更数额" )
    private Double invalidAmount;
    /**
     * 积累收益变更数额
     */
    @Schema(description = "积累收益变更数额" )
    private Double addupAmount;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间" )
    private Date createTime;
    /**
     * 备注
     */
    @Schema(description = "备注" )
    private String remarks;
}
