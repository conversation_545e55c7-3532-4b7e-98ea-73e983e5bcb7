package com.yami.shop.security.common.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "更新密码参数")
public class UpdatePasswordDto {

    @NotBlank(message="旧密码不能为空")
    @Schema(description = "旧密码" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String password;

    @NotBlank(message="新密码不能为空")
    @Schema(description = "新密码" ,requiredMode = Schema.RequiredMode.REQUIRED)
    private String newPassword;
}
