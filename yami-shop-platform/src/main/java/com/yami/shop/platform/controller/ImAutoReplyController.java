package com.yami.shop.platform.controller;

import com.yami.shop.bean.model.ImAutoReply;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ResponseEnum;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.ImAutoReplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 客服自动回复
 * <AUTHOR>
 */
@RestController
@RequestMapping("/platform/imAutoReply")
@Tag(name = "平台客服自动回复")
@AllArgsConstructor
public class ImAutoReplyController {

    private final ImAutoReplyService imAutoReplyService;

    @GetMapping("/info")
    @Operation(summary = "获取客服自动回复")
    @PreAuthorize("@pms.hasPermission('platform:imAutoReply:get')")
    public ServerResponseEntity<ImAutoReply> getById() {
        ImAutoReply imAutoReply = imAutoReplyService.getByShopId(Constant.PLATFORM_SHOP_ID);
        return ServerResponseEntity.success(imAutoReply);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('platform:imAutoReply:save')")
    @Operation(summary = "保存客服自动回复", description = "保存客服自动回复")
    public ServerResponseEntity<Void> save(@RequestBody @Valid ImAutoReply imAutoReply) {
        imAutoReply.setShopId(Constant.PLATFORM_SHOP_ID);
        ImAutoReply autoReply = imAutoReplyService.getByShopId(imAutoReply.getShopId());
        if (Objects.nonNull(autoReply)) {
            // 自动回复已存在
            throw new YamiShopBindException("yami.im.auto.reply.exception.autoReplyExist");
        }
        imAutoReplyService.insert(imAutoReply);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('platform:imAutoReply:update')")
    @Operation(summary = "更新客服自动回复", description = "更新客服自动回复")
    public ServerResponseEntity<Void> update(@RequestBody @Valid ImAutoReply imAutoReply) {
        ImAutoReply dbImAutoReply = imAutoReplyService.getById(imAutoReply.getAutoReplyId());
        if (Objects.nonNull(dbImAutoReply) && !Objects.equals(dbImAutoReply.getShopId(), Constant.PLATFORM_SHOP_ID)) {
            throw new YamiShopBindException(ResponseEnum.UNAUTHORIZED);
        }
        imAutoReply.setShopId(Constant.PLATFORM_SHOP_ID);
        imAutoReplyService.updateContent(imAutoReply);
        return ServerResponseEntity.success();
    }
}
