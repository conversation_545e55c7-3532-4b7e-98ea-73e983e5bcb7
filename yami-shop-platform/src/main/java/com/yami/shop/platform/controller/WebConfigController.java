package com.yami.shop.platform.controller;

import cn.hutool.core.util.BooleanUtil;
import com.yami.shop.bean.enums.WebConfigTypeEnum;
import com.yami.shop.bean.model.WebConfig;
import com.yami.shop.bean.vo.WebConfigVO;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.bean.SysConfig;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.WebConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


/**
 * 由于权限要求不同，所以不能把功能合并到sysController
 *
 * <AUTHOR>
 * @date 2021-02-20 09:44:42
 * @description: 平台网站配置、商家网站配置、PC配置、H5配置、自提点网站配置
 */

@RestController
@RequestMapping("/sys/webConfig")
@Tag(name = "网站配置")
@RequiredArgsConstructor
public class WebConfigController {

    private final WebConfigService webConfigService;

    @Value("${yami.expose.operation.auth:}")
    private Boolean permission;

    @SysLog("获取配置信息")
    @GetMapping("/info/{key}")
    @Operation(summary = "获取配置信息", description = "获取配置信息")
    @Parameter(name = "key", description = "参数名")
    @PreAuthorize("@pms.hasPermission('sys:webConfig:info')")
    public ServerResponseEntity<WebConfig> info(@PathVariable("key") String key) {
        WebConfig webConfig = webConfigService.info(key);
        return ServerResponseEntity.success(webConfig);
    }

    @SysLog("保存配置")
    @PostMapping("/save")
    @Operation(summary = "保存配置" , description = "保存配置")
    @PreAuthorize("@pms.hasPermission('sys:webConfig:save')")
    public ServerResponseEntity<Void> save(@RequestBody @Valid SysConfig sysConfig) {
        if (BooleanUtil.isFalse(permission)) {
            return ServerResponseEntity.showFailMsg(I18nMessage.getMessage("yami.operate.auth"));
        }
        webConfigService.saveWebConfig(sysConfig);
        return ServerResponseEntity.success();
    }

    /**
     * 获取当前激活的后台网站配置
     *
     * @return
     */
    @GetMapping("/getActivity")
    public ServerResponseEntity<WebConfigVO> getActivityWebConfig() {
        WebConfigVO webConfig = webConfigService.getActivityWebConfig(WebConfigTypeEnum.PLATFROM.value());
        return ServerResponseEntity.success(webConfig);
    }
}
