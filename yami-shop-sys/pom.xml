<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yami.shop</groupId>
        <artifactId>yami-shop</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>yami-shop-sys</artifactId>

    <description>商城管理员</description>
    <packaging>pom</packaging>

    <modules>
        <module>yami-shop-sys-api</module>
        <module>yami-shop-sys-common</module>
        <module>yami-shop-sys-multishop</module>
        <module>yami-shop-sys-platform</module>
    </modules>

</project>
