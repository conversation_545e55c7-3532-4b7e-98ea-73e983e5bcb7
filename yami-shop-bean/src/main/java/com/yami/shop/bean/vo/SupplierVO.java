package com.yami.shop.bean.vo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SupplierVO {

    @Schema(description = "供应商id" )
    private Long supplierId;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "供应商分类id" )
    private Long supplierCategoryId;

    @Schema(description = "供应商名称" )
    private String supplierName;

    @Schema(description = "供应商分类名称" )
    private String categoryName;

    @Schema(description = "供应商商品数量" )
    private Integer supplierProdCount;

    @Schema(description = "是否为默认供应商" )
    private Integer isDefault;
}
