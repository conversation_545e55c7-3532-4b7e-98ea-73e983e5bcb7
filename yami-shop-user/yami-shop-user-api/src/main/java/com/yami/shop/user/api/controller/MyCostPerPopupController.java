package com.yami.shop.user.api.controller;

import com.yami.shop.bean.model.User;
import com.yami.shop.bean.vo.ShopCustomerVO;
import com.yami.shop.common.enums.UserLevelType;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.service.ShopCustomerService;
import com.yami.shop.service.UserService;
import com.yami.shop.user.common.dto.QueryPopupDTO;
import com.yami.shop.user.common.dto.UserLevelDto;
import com.yami.shop.user.common.enums.PopupPageTypeEnum;
import com.yami.shop.user.common.model.PopupUserLog;
import com.yami.shop.user.common.service.CostPerPopupService;
import com.yami.shop.user.common.service.PopupUserLogService;
import com.yami.shop.user.common.service.UserLevelService;
import com.yami.shop.user.common.vo.CostPerPopupVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 弹窗广告
 * <AUTHOR>
 */
@RestController("myCostPerPopupController")
@RequestMapping("/p/costPerPopup")
@Tag(name = "用户弹窗广告")
@AllArgsConstructor
public class MyCostPerPopupController {

    private final CostPerPopupService costPerPopupService;
    private final UserService userService;
    private final ShopCustomerService shopCustomerService;
    private final PopupUserLogService popupUserLogService;
    private final UserLevelService userLevelService;

    @PostMapping("/popup")
    @Operation(summary = "获取用户在当前页面的弹窗广告")
    public ServerResponseEntity<CostPerPopupVO> page(@RequestBody QueryPopupDTO queryPopupDTO) {
        String userId = AuthUserContext.getUserId();
        // 先获取有没有符合条件的一个弹窗广告
        CostPerPopupVO costPerPopup = getCostPerPopup(queryPopupDTO, userId);
        if (Objects.isNull(costPerPopup)) {
            // 没有符合条件的弹窗广告直接返回
            return ServerResponseEntity.success();
        }
        // 最后保存弹窗广告记录
        popupUserLogService.insert(new PopupUserLog(costPerPopup.getPopupId(), userId, queryPopupDTO.getUuid()));
        return ServerResponseEntity.success(costPerPopup);
    }

    private CostPerPopupVO getCostPerPopup(QueryPopupDTO queryPopupDTO, String userId) {
        String uuid = queryPopupDTO.getUuid();
        Integer pageType = queryPopupDTO.getPageType();
        Long shopId = queryPopupDTO.getShopId();
        CostPerPopupVO costPerPopup;
        if (PopupPageTypeEnum.isSys(pageType)) {
            // 平台弹窗
            User userApiVO = getUser(userId);
            costPerPopup = costPerPopupService.getSysPopupByCondition(userApiVO, pageType, uuid);
        } else {
            // 商家弹窗
            ShopCustomerVO shopCustomerVO = shopCustomerService.getIsShopCustomer(shopId, userId);
            costPerPopup = costPerPopupService.getShopPopupByCondition(shopCustomerVO, pageType, shopId, userId, uuid);
        }
        return costPerPopup;
    }

    private User getUser(String userId) {
        User userApiVO = userService.getUserByUserId(userId);
        List<UserLevelDto> ordinaryList = userLevelService.listUserLevelsByUserLevelType(UserLevelType.ORDINARY.value());
        for (UserLevelDto userLevelDto : ordinaryList) {
            if (Objects.equals(userLevelDto.getLevel(), userApiVO.getLevel())) {
                userApiVO.setLevelId(userLevelDto.getId());
                break;
            }
        }
        if (Objects.equals(userApiVO.getLevelType(), UserLevelType.VIP.value()) && Objects.nonNull(userApiVO.getLevel())) {
            List<UserLevelDto> vipList = userLevelService.listUserLevelsByUserLevelType(UserLevelType.VIP.value());
            for (UserLevelDto userLevelDto : vipList) {
                if (Objects.equals(userLevelDto.getLevel(), userApiVO.getLevel())) {
                    userApiVO.setVipLevelId(userLevelDto.getId());
                    break;
                }
            }
        }
        return userApiVO;
    }
}
