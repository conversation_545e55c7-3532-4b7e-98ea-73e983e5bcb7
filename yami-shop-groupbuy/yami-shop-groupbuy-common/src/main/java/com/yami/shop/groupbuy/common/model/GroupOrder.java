package com.yami.shop.groupbuy.common.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
@Data
@TableName("tz_group_order")
public class GroupOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    @Schema(description = "拼团订单id" )
    private Long groupOrderId;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "拼团团队id" )
    private Long groupTeamId;

    @Schema(description = "拼团活动id" )
    private Long groupActivityId;

    @Schema(description = "user_id(当user_id为0时标识为机器人)" )
    private String userId;

    @Schema(description = "身份标识(0:成员  1:团长)" )
    private Integer identityType;

    @Schema(description = "活动商品金额" )
    private Double activityProdPrice;

    @Schema(description = "支付金额" )
    private Double payPrice;

    @Schema(description = "订单编号" )
    private String orderNumber;

    @Schema(description = "创建时间" )
    private Date createTime;

    @Schema(description = "状态 0:待支付、1:支付成功、-1:失效" )
    private Integer status;
}
