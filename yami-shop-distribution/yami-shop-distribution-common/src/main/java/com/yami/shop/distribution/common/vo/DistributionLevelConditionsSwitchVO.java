package com.yami.shop.distribution.common.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 升级所需条件开关设置
 * <AUTHOR>
 */
@Data
public class DistributionLevelConditionsSwitchVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 绑定客户数
     */
    @Schema(description = "绑定客户数" )
    private Boolean boundCustomers;

    /**
     * 邀请分销员数
     */
    @Schema(description = "邀请分销员数" )
    private Boolean invitedVeeker;

    /**
     * 支付单数
     */
    @Schema(description = "支付单数" )
    private Boolean payNumber ;

    /**
     * 积累收益
     */
    @Schema(description = "积累收益" )
    private Boolean addupAmount ;

    /**
     * 成功成交单数
     */
    @Schema(description = "成功成交单数" )
    private Boolean successOrderNumber ;

    /**
     * 成功成交金额
     */
    @Schema(description = "成功成交金额" )
    private Boolean successTradingVolume ;

    /**
     * 消费金额
     */
    @Schema(description = "消费金额" )
    private Boolean sumOfConsumption ;

//    /**
//     * 充值金额
//     */
//    @Schema(description = "充值金额" )
//    private Boolean rechargeAmount ;

    /**
     * 购买指定商品
     */
    @Schema(description = "购买指定商品" )
    private Boolean commodity ;
}
