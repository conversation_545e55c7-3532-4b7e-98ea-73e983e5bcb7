package com.yami.shop.groupbuy.common.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupSkuDto;
import com.yami.shop.groupbuy.common.model.GroupSku;

import java.util.List;

/**
 * 拼团活动商品规格
 *
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
public interface GroupSkuService extends IService<GroupSku> {

    /**
     * 获取SKU列表数据
     *
     * @param groupProdId
     * @param mold
     * @param groupActivityId 拼团活动ID
     * @return SKU列表
     */
    List<ApiGroupSkuDto> getApiByGroupActivityIdAndProdId(Long groupProdId, Integer mold, Long groupActivityId);

}
