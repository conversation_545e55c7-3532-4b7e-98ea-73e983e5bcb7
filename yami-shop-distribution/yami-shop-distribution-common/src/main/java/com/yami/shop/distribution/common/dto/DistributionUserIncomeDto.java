package com.yami.shop.distribution.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
/**
 * <AUTHOR>
 */
@Data
@Schema(description = "分销员收入数据")
public class DistributionUserIncomeDto {

    @Schema(description = "收入类型(0其他、1直推奖励、2间推奖励、3邀请奖励、4平台修改 )" )
    private Integer incomeType;

    @Schema(description = "佣金数额" )
    private Double incomeAmount;

    @Schema(description = "更新时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "佣金状态(0:待支付、1:待结算、2:已结算、-1:订单失效)" )
    private Integer state;
}
