package com.yami.shop.live.api.controller;

import com.yami.shop.bean.vo.wukong.RegisterOrLoginVO;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.live.common.dto.LiveMessageDTO;
import com.yami.shop.live.common.service.LiveRoomService;
import com.yami.shop.live.common.vo.LiveRoomVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/live/liveUser")
@Tag(name = "直播用户接口")
@AllArgsConstructor
public class LiveUserController {

    private final LiveRoomService liveRoomService;

    @PostMapping("/registerOrLogin")
    @Operation(summary = "游客注册或登录到im")
    public ServerResponseEntity<RegisterOrLoginVO> registerOrLogin(@RequestBody LiveMessageDTO liveMessageDTO) {
        Long roomId = liveMessageDTO.getRoomId();
        String uuid = liveMessageDTO.getUuid();
        String userId = uuid;
        LiveRoomVO livingRoomInfo = liveRoomService.getLivingRoomInfo(null, roomId);
        RegisterOrLoginVO registerOrLoginVO = liveRoomService.liveUserLogin(livingRoomInfo, roomId, uuid, userId);
        return ServerResponseEntity.success(registerOrLoginVO);
    }
}
