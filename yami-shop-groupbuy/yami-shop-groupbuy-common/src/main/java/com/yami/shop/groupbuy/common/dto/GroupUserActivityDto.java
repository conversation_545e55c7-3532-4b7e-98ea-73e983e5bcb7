package com.yami.shop.groupbuy.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户参团信息
 *
 * <AUTHOR>
 * @date 2019/8/31 11:33
 */
@Data
public class GroupUserActivityDto {

    @Schema(description = "用户是可参加当前商品拼团活动（1：可参加，2：已参加）" )
    private Integer isJoinActivity;

    @Schema(description = "拼团团队ID（已参加活动状态下）" )
    private Integer groupTeamId;

    @Schema(description = "已参加购买数量" )
    private Integer joinNumber;
}
