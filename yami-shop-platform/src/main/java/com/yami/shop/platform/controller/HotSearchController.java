package com.yami.shop.platform.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.model.HotSearch;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.HotSearchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR> on 2019/03/27.
 */
@RestController
@RequestMapping("/platform/hotSearch")
@Tag(name = "热搜")
@AllArgsConstructor
public class HotSearchController {

    private final HotSearchService hotSearchService;

    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('platform:hotSearch:page')")
    @Operation(summary = "分页获取热搜" , description = "分页获取热搜")
    public ServerResponseEntity<IPage<HotSearch>> page(HotSearch hotSearch, PageParam<HotSearch> page){
        IPage<HotSearch> hotSearchs = hotSearchService.page(page,new LambdaQueryWrapper<HotSearch>()
                .eq(HotSearch::getShopId, Constant.PLATFORM_SHOP_ID)
                .like(StrUtil.isNotBlank(hotSearch.getContent()), HotSearch::getContent,hotSearch.getContent())
                .like(StrUtil.isNotBlank(hotSearch.getTitle()), HotSearch::getTitle,hotSearch.getTitle())
                .eq(hotSearch.getStatus()!=null, HotSearch::getStatus,hotSearch.getStatus())
                .orderByDesc(true, HotSearch::getSeq)
                .orderByDesc(HotSearch::getRecDate)
        );
        return ServerResponseEntity.success(hotSearchs);
    }

    @GetMapping("/info/{id}")
    @PreAuthorize("@pms.hasPermission('platform:hotSearch:info')")
    @Operation(summary = "获取信息" , description = "获取信息")
    @Parameter(name = "id", description = "热搜id" )
    public ServerResponseEntity<HotSearch> info(@PathVariable("id") Long id){
        HotSearch hotSearch = hotSearchService.getById(id);
        return ServerResponseEntity.success(hotSearch);
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('platform:hotSearch:save')")
    @Operation(summary = "保存" , description = "保存")
    public ServerResponseEntity<Void> save(@RequestBody @Valid HotSearch hotSearch){
        hotSearch.setRecDate(new Date());
        hotSearch.setShopId(Constant.PLATFORM_SHOP_ID);
        hotSearchService.save(hotSearch);
        //清除缓存
        hotSearchService.removeHotSearchDtoCacheByshopId(Constant.PLATFORM_SHOP_ID);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('platform:hotSearch:update')")
    @Operation(summary = "修改" , description = "修改")
    public ServerResponseEntity<Void> update(@RequestBody @Valid HotSearch hotSearch){
        hotSearchService.updateById(hotSearch);
        //清除缓存
        hotSearchService.removeHotSearchDtoCacheByshopId(Constant.PLATFORM_SHOP_ID);
        return ServerResponseEntity.success();
    }

    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('platform:hotSearch:delete')")
    @Operation(summary = "删除" , description = "删除")
    @Parameter(name = "ids", description = "热搜id列表" )
    public ServerResponseEntity<Void> delete(@RequestBody List<Long> ids){
        hotSearchService.removeByIds(ids);
        //清除缓存
        hotSearchService.removeHotSearchDtoCacheByshopId(Constant.PLATFORM_SHOP_ID);
        return ServerResponseEntity.success();
    }
}
