package com.yami.shop.groupbuy.common.listener;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yami.shop.bean.enums.ProdType;
import com.yami.shop.bean.event.EsProductActivityInfoEvent;
import com.yami.shop.bean.order.EsProductOrder;
import com.yami.shop.bean.vo.search.GroupActivitySearchVO;
import com.yami.shop.bean.vo.search.ProductSearchVO;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.groupbuy.common.dao.GroupActivityMapper;
import com.yami.shop.groupbuy.common.enums.GroupActivityStatusEnum;
import com.yami.shop.groupbuy.common.model.GroupActivity;
import lombok.AllArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 获取商品团购数据事件
 * <AUTHOR>
 */
@Component("groupBuyEsProductActivityInfoListener")
@AllArgsConstructor
public class EsProductActivityInfoListener {

    private final GroupActivityMapper groupActivityMapper;


    @EventListener(EsProductActivityInfoEvent.class)
    @Order(EsProductOrder.GROUP_BUY)
    public void groupBuyEsProductListener(EsProductActivityInfoEvent event) {
        Date now = new Date();
        Long currentTime = System.currentTimeMillis();
        List<ProductSearchVO> productList = event.getProductList();
        List<Long> activityIds = productList.stream()
                .filter(productSearchVO -> Objects.equals(productSearchVO.getProdType(), ProdType.PROD_TYPE_GROUP.value())
                )
                .map(ProductSearchVO::getActivityId)
                .collect(Collectors.toList());
        // 没有开始中的团购商品需要处理
        if (CollUtil.isEmpty(activityIds)) {
            return;
        }
        List<GroupActivity> groupActivities = groupActivityMapper.selectList(new LambdaQueryWrapper<GroupActivity>().in(GroupActivity::getGroupActivityId, activityIds));
        Map<Long, GroupActivity> groupMap = groupActivities.stream().collect(Collectors.toMap(GroupActivity::getGroupActivityId, g -> g));
        for (ProductSearchVO productSearchVO : productList) {
            GroupActivity groupActivity = groupMap.get(productSearchVO.getActivityId());
            if (Objects.isNull(groupActivity)){
                continue;
            }
            // 团购没有启用不展示活动价
            if(!Objects.equals(groupActivity.getStatus(),GroupActivityStatusEnum.ENABLE.value())){
                productSearchVO.setActivityPrice(null);
                productSearchVO.setActivityOriginalPrice(null);
                continue;
            }
            // 如果没有开启预热且开始时间未到不展示活动价,反之无论是开启预热还是时间到了都要展示
            if (Objects.equals(groupActivity.getIsPreheat(),0) && DateUtil.compare(groupActivity.getStartTime(),now) > 0){
                productSearchVO.setActivityPrice(null);
                productSearchVO.setActivityOriginalPrice(null);
                continue;
            }
            // 如果启用状态时开启预热展示团购信息
            if(Objects.equals(groupActivity.getIsPreheat(),1)){
                productSearchVO.setActivityStatus(1);
                productSearchVO.setActivityInProgress(Boolean.TRUE);
                GroupActivitySearchVO groupActivitySearchVO = BeanUtil.map(groupActivity, GroupActivitySearchVO.class);
                productSearchVO.setGroupActivitySearchVO(groupActivitySearchVO);
                continue;
            }
            productSearchVO.setActivityStatus(groupActivity.getStatus());
            productSearchVO.setGroupNumber(groupActivity.getGroupNumber());
            if (Objects.isNull(productSearchVO.getActivityStartTime()) || (productSearchVO.getActivityStartTime() > currentTime)){
                continue;
            }
            GroupActivitySearchVO groupActivitySearchVO = BeanUtil.map(groupActivity, GroupActivitySearchVO.class);
            productSearchVO.setActivityInProgress(Boolean.TRUE);
            productSearchVO.setGroupActivitySearchVO(groupActivitySearchVO);
        }
    }

}
