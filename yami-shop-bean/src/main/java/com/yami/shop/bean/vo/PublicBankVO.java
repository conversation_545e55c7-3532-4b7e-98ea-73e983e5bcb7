package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 2023-03-21
 */
public class PublicBankVO {

    @Schema(description = "银行id")
    private Long bankId;

    @Schema(description = "银行名")
    private String bankName;

    @Schema(description = "银行代码")
    private String bankCode;

    public Long getBankId() {
        return bankId;
    }

    public void setBankId(Long bankId) {
        this.bankId = bankId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    @Override
    public String toString() {
        return "PublicBankVO{" +
                "bankId=" + bankId +
                ", bankName='" + bankName + '\'' +
                ", bankCode='" + bankCode + '\'' +
                '}';
    }
}
