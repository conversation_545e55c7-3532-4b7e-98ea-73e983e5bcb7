package com.yami.shop.distribution.common.dto;

import com.yami.shop.distribution.common.vo.DistributionLevelConditionDataVO;
import com.yami.shop.distribution.common.vo.DistributionLevelConditionsSwitchVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "分销员等级对象")
public class DistributionLevelDto {
    @Schema(description = "等级id" )
    private Long levelId;

    @Schema(description = "等级名称" )
    private String name;

    @Schema(description = "等级级别" )
    private Integer level;

    @Schema(description = "等级所需条件" )
    private DistributionLevelConditionDataVO distributionLevelConditionDataVO;

    @Schema(description = "等级所需条件开关" )
    private DistributionLevelConditionsSwitchVO levelSetConditionsSwitch;

    @Schema(description = "用户是否在当前等级" )
    private Boolean isCurrentLevel;
}
