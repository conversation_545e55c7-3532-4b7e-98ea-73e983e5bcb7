package com.yami.shop.bean.app.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderRefundExpressParam {

    @Schema(description = "退款编号名称" , requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "退款编号不能为空")
    private String refundSn;

    @Schema(description = "物流公司id" , requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物流公司id不能为空")
    private Long expressId;

    @Schema(description = "物流公司名称" , requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物流公司名称不能为空")
    private String expressName;

    @Schema(description = "物流单号" , requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物流单号不能为空")
    private String expressNo;

    @Schema(description = "手机号码" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String mobile;

    @Schema(description = "备注信息" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String senderRemarks;

    @Schema(description = "图片举证" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String imgs;


}
