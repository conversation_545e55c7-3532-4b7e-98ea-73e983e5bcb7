package com.yami.shop.common.allinpay.order.resp;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
public class GetOrderStatusResp {

    @Schema(description = "云商通订单号")
    private String orderNo;

    @Schema(description = "商户订单号")
    private String bizOrderNo;

    @Schema(description = "订单状态")
    private Long orderStatus;

    @Schema(description = "失败原因")
    private String errorMessage;

    @Schema(description = "订单金额")
    private Long amount;

    @Schema(description = "订单支付完成时间")
    private String payDateTime;

    @Schema(description = "是否记账成功")
    private Long isAccountSuccess;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public Long getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Long orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getPayDateTime() {
        return payDateTime;
    }

    public void setPayDateTime(String payDateTime) {
        this.payDateTime = payDateTime;
    }

    public Long getIsAccountSuccess() {
        return isAccountSuccess;
    }

    public void setIsAccountSuccess(Long isAccountSuccess) {
        this.isAccountSuccess = isAccountSuccess;
    }

    @Override
    public String toString() {
        return "GetOrderStatusResp{" +
                "orderNo='" + orderNo + '\'' +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                ", orderStatus=" + orderStatus +
                ", errorMessage='" + errorMessage + '\'' +
                ", amount=" + amount +
                ", payDateTime='" + payDateTime + '\'' +
                ", isAccountSuccess=" + isAccountSuccess +
                '}';
    }
}
