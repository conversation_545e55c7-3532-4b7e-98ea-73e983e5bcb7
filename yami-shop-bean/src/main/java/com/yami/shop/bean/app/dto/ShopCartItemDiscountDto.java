package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ShopCartItemDiscountDto implements Serializable {

    @Schema(description = "已选满减项" , requiredMode = Schema.RequiredMode.REQUIRED)
    private ChooseDiscountItemDto chooseDiscountItemDto;

    @Schema(description = "已选套餐项" , requiredMode = Schema.RequiredMode.REQUIRED)
    private ChooseComboItemDto chooseComboItemDto;

    @Schema(description = "商品列表" )
    private List<ShopCartItemDto> shopCartItems;
    @Schema(description = "当前满减/套餐下最新加入购物车的商品项" )
    private Long maxBasketId;
    @Schema(description = "订单活动类型：0无 1满减 2套餐" )
    private Integer activityType;

    public ShopCartItemDiscountDto(){}

    public ShopCartItemDiscountDto(List<ShopCartItemDto> shopCartItems) {
        this.shopCartItems = shopCartItems;
    }
}
