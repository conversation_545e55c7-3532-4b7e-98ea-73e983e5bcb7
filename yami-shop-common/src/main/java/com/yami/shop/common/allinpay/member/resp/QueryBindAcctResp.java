package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-04-25
 */
public class QueryBindAcctResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 商户用户id
     */
    private String bizUserId;

    /**
     * 绑定账户支付信息列表
     */
    private List<BindAcctInfo> acctInfoList;

    /**
     * 结果
     */
    private String result;

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public List<BindAcctInfo> getAcctInfoList() {
        return acctInfoList;
    }

    public void setAcctInfoList(List<BindAcctInfo> acctInfoList) {
        this.acctInfoList = acctInfoList;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "QueryBindAcctResp{" +
                "bizUserId='" + bizUserId + '\'' +
                ", acctInfoList=" + acctInfoList +
                ", result='" + result + '\'' +
                '}';
    }
}
