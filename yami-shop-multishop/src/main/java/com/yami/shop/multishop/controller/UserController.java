package com.yami.shop.multishop.controller;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yami.shop.bean.model.ShopCustomer;
import com.yami.shop.bean.model.User;
import com.yami.shop.bean.param.CouponUserParam;
import com.yami.shop.bean.param.UserManagerParam;
import com.yami.shop.bean.param.UserTagParam;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.coupon.common.service.CouponUserService;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.ShopCustomerService;
import com.yami.shop.service.UserService;
import com.yami.shop.user.common.service.UserTagUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@RequestMapping("/user")
@RestController
@Tag(name = "用户信息")
@AllArgsConstructor
public class UserController {

    private final UserService userService;
    private final ShopCustomerService shopCustomerService;
    private final UserTagUserService userTagUserService;
    private final CouponUserService couponUserService;

    @GetMapping("/info/{userId}")
    @Operation(summary = "获取品牌用户信息", description =  "获取品牌用户信息")
    @PreAuthorize("@pms.hasPermission('user:info')")
    public ServerResponseEntity<UserManagerParam> getInfo(@PathVariable("userId") String userId) {
        Long shopId =SecurityUtils.getShopUser().getShopId();
        UserManagerParam userManagerParam = new UserManagerParam();
        User user = userService.getById(userId);
        // 补充用户基础信息
        if (user != null) {
            userManagerParam.setUserId(userId);
            userManagerParam.setNickName(user.getNickName());
            userManagerParam.setStatus(user.getStatus());
            userManagerParam.setPic(user.getPic());
        }
        // 补充注册时间
        ShopCustomer shopCustomer = shopCustomerService.getOne(new LambdaQueryWrapper<ShopCustomer>()
                .eq(ShopCustomer::getUserId, userId)
                .eq(ShopCustomer::getShopId, shopId));
        userManagerParam.setRegistTime(DateUtil.toLocalDateTime(shopCustomer.getRegistTime()));
        // 补充客户标签
        List<UserTagParam> userTags = userTagUserService.getUserShopTag(userId, shopId);
        Map<Integer, List<UserTagParam>> tagCategoryTagsMap = userTags.stream().collect(Collectors.groupingBy(UserTagParam::getTagCategory));
        userManagerParam.setUserTagParam(userTags);
        userManagerParam.setCustomerTags(tagCategoryTagsMap.get(0));
        userManagerParam.setMemberTags(tagCategoryTagsMap.get(1));
        // 补充优惠券信息
        CouponUserParam couponCountInfo = couponUserService.getCouponCountInfo(userId, shopId);
        userManagerParam.setCouponUserParam(couponCountInfo);
        return ServerResponseEntity.success(userManagerParam);
    }
}
