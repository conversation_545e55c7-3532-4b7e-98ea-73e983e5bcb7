package com.yami.shop.bean.vo.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
@Data
public class MemberContributeValueVO {

    public MemberContributeValueVO(){
        this.payOrderNum = 0;
        this.totalMember = 0;
        this.totalMemberRate = 0.00;
        this.payMemberNum = 0;
        this.payMemberNumRate = 0.00;
        this.payAmount = 0.00;
        this.payAmountRate = 0.00;
        this.pricePerMember = 0.00;
        this.frequencyOfConsume = 0.00;
    }
    @Schema(description = "累积会员数" )
    private Integer totalMember;
    @Schema(description = "累积会员数占比" )
    private Double totalMemberRate;

    @Schema(description = "成交会员数" )
    private Integer payMemberNum;
    @Schema(description = "成交会员数占比" )
    private Double payMemberNumRate;

    @Schema(description = "支付订单数" )
    private Integer payOrderNum;

    @Schema(description = "支付金额" )
    private Double payAmount;
    @Schema(description = "支付金额占比" )
    private Double payAmountRate;

    @Schema(description = "客单价" )
    private Double pricePerMember;

    @Schema(description = "人均消费频次" )
    private Double frequencyOfConsume;
}
