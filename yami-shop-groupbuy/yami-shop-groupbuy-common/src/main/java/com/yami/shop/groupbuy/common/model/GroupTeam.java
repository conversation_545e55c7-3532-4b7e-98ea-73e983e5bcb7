package com.yami.shop.groupbuy.common.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 拼团团队表
 *
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
@Data
@TableName("tz_group_team")
public class GroupTeam implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "拼团团队id" )
    @TableId
    private Long groupTeamId;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "拼团活动id" )
    private Long groupActivityId;

    @Schema(description = "商品Id" )
    private Long prodId;

    @Schema(description = "已参团人数" )
    private Integer joinNum;

    @Schema(description = "拼团状态(0:待成团，1:拼团中，2:拼团成功，3:拼团失败)" )
    private Integer status;

    @Schema(description = "团队订单总额" )
    private Double totalPrice;

    @Schema(description = "开始时间（团长支付成功时间）" )
    private Date startTime;

    @Schema(description = "结束时间" )
    private Date endTime;

    @Schema(description = "创建时间" )
    private Date createTime;

    @Schema(description = "团长user_Id" )
    private String shareUserId;

    @Schema(description = "更新时间" )
    private Date updateTime;

    @Schema(description = "是否模拟参团（1:模拟参团、0:不模拟）" )
    @TableField(exist = false)
    private Integer hasRobot;

    @Schema(description = "是否模拟参团（1:模拟参团、0:不模拟）" )
    @TableField(exist = false)
    private Integer groupNumber;
}
