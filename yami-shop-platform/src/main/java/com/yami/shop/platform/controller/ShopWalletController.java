package com.yami.shop.platform.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.allinpay.service.AllinpayCompanyService;
import com.yami.shop.bean.model.ShopWallet;
import com.yami.shop.bean.model.ShopWalletLog;
import com.yami.shop.bean.param.CustomerReqParam;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.ShopDetailService;
import com.yami.shop.service.ShopWalletLogService;
import com.yami.shop.service.ShopWalletService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;


/**
 * 商家钱包信息
 *
 * <AUTHOR>
 * @date 2019-09-29 11:10:12
 */
@RestController
@AllArgsConstructor
@RequestMapping("/shop/shopWallet" )
@Tag(name = "商家钱包")
public class ShopWalletController {

    private final ShopWalletService shopWalletService;
    private final ShopWalletLogService shopWalletLogService;
    private final ShopDetailService shopDetailService;
    private final AllinpayCompanyService allinpayCompanyService;

    @GetMapping("/page" )
    @Operation(summary = "分页查询" , description = "分页查询")
    @PreAuthorize("@pms.hasPermission('shop:shopWallet:page')")
    public ServerResponseEntity<IPage<ShopWallet>> getShopWalletPage(PageParam<ShopWallet> page, ShopWallet shopWallet) {
        return ServerResponseEntity.success(shopWalletService.pageShopWallet(page, shopWallet));
    }

    @GetMapping("/getShopWallet")
    @Operation(summary = "查看店铺钱包信息" , description = "根据店铺id查看店铺钱包信息")
    @PreAuthorize("@pms.hasPermission('shop:shopWallet:info')")
    public ServerResponseEntity<ShopWallet> getShopWalletVoByShopId(){
        ShopWallet shopWallet = shopWalletService.getShopWalletByShopId(Constant.PLATFORM_SHOP_ID);
        if (allinpayCompanyService.getIsAllinpay() && Objects.isNull(shopWallet)) {
            // 如果通联开启配置错误导致初始化没有添加平台钱包，在这里添加一个
            shopWallet = new ShopWallet();
            shopWallet.setVersion(0L);
            shopWallet.setUnsettledAmount(0D);
            shopWallet.setSettledAmount(0D);
            shopWallet.setFreezeAmount(0D);
            shopWallet.setTotalSettledAmount(0D);
            shopWallet.setShopId(Constant.PLATFORM_SHOP_ID);
            shopWallet.setPaySysType(1);
            shopWalletService.save(shopWallet);
        }
        return ServerResponseEntity.success(shopWallet);
    }

    @GetMapping("/pagePlatformInfo")
    @Operation(summary = "分页查询平台收入明细" , description = "分页查询平台收入明细")
    @PreAuthorize("@pms.hasPermission('shop:shopWallet:page')")
    public ServerResponseEntity<IPage<ShopWalletLog>> getShopWalletLogPage(PageParam<ShopWalletLog> page, ShopWalletLog shopWalletLog){
        shopWalletLog.setShopId(Constant.PLATFORM_SHOP_ID);
        IPage<ShopWalletLog> shopWalletLogPage = shopWalletLogService.pageByParam(page, shopWalletLog);
        return ServerResponseEntity.success(shopWalletLogPage);
    }

    @GetMapping("/getAllShopWallet")
    @Operation(summary = "查看店铺钱包总信息")
    @PreAuthorize("@pms.hasPermission('shop:shopWallet:info')")
    public ServerResponseEntity<ShopWallet> getAllShopWalletVoByShopId(CustomerReqParam customerReqParam) {
        ShopWallet shopWallet = shopWalletService.getAllShop(customerReqParam);
        return ServerResponseEntity.success(shopWallet);
    }

    @GetMapping("/pageShopWalletByTime")
    @Operation(summary = "分页查看店铺钱包总信息")
    @PreAuthorize("@pms.hasPermission('shop:shopWallet:page')")
    public ServerResponseEntity<IPage<ShopWallet>> pageShopWalletByTime(PageParam<ShopWallet> page, CustomerReqParam customerReqParam) {
        IPage<ShopWallet> shopWalletPage = shopWalletService.pageShopWalletByTime(page, customerReqParam);
        return ServerResponseEntity.success(shopWalletPage);
    }

    @GetMapping("/pageAllShop")
    @Operation(summary = "查看所有店铺的日志")
    @PreAuthorize("@pms.hasPermission('shop:shopWallet:page')")
    public ServerResponseEntity<IPage<ShopWalletLog>> getAllShopWalletLogPage(PageParam<ShopWallet> page, ShopWalletLog shopWalletLog) {
        IPage<ShopWalletLog> shopWalletLogPage = shopWalletLogService.pageAllShop(page, shopWalletLog);
        return ServerResponseEntity.success(shopWalletLogPage);
    }

    @GetMapping("/getShopWalletLogForm")
    @Operation(summary = "导出报表" , description = "导出店铺结算报表")
    @PreAuthorize("@pms.hasPermission('shop:wallet:excel')")
    public void getShopWalletLogForm(CustomerReqParam customerReqParam, HttpServletResponse response) {
        shopWalletLogService.excelShopWalletLog(customerReqParam, response);
    }
}
