<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yami.shop.groupbuy.common.dao.GroupSkuMapper">

    <resultMap id="groupSkuMap" type="com.yami.shop.groupbuy.common.model.GroupSku">
        <id column="group_sku_id" property="groupSkuId"/>
        <result column="sku_id" property="skuId"/>
        <result column="act_price" property="actPrice"/>
        <result column="sell_num" property="sellNum"/>
    </resultMap>

    <select id="getApiByGroupActivityIdAndProdId" resultType="com.yami.shop.groupbuy.common.api.dto.ApiGroupSkuDto">
        SELECT
            gs.*,
            s.price AS price,
            s.pic AS pic
        FROM tz_group_sku gs
        LEFT JOIN tz_sku s ON gs.sku_id = s.sku_id
        LEFT JOIN tz_group_activity ga ON ga.group_activity_id = gs.group_activity_id
        WHERE gs.group_activity_id = #{groupActivityId} and s.status = 1
    </select>

    <select id="listSkuByProdIds" resultType="com.yami.shop.groupbuy.common.model.GroupSku">
        SELECT s.prod_id,s.price,gs.act_price FROM tz_group_sku gs
        JOIN tz_sku s ON gs.sku_id = s.sku_id
        WHERE group_activity_id IN (
            SELECT group_activity_id FROM tz_group_activity WHERE `status` IN (1,2,3,4) AND prod_id IN
            <foreach collection="prodIds" item="prodId" open="(" close=")" separator=",">
                #{prodId}
            </foreach>
        )
    </select>

    <select id="getSkuIdByProdIdAndActPrice" resultType="java.lang.Long">
        SELECT sku_id FROM tz_group_sku WHERE group_activity_id = #{groupActivityId} AND act_price = #{actPrice} LIMIT 1
    </select>
</mapper>
