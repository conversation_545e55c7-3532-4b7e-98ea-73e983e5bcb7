package com.yami.shop.platform.task;

import cn.hutool.core.collection.CollectionUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yami.shop.bean.model.NotifyLog;
import com.yami.shop.common.config.Constant;
import com.yami.shop.service.NotifyLogService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class NotifyTask {


    private final Logger logger = LoggerFactory.getLogger(getClass());

    private final NotifyLogService notifyLogService;


    @XxlJob("sendMessage")
    public void sendMessage(){
        logger.info("获取还未进行消息推送的通知。。。");
        // 获取还未进行消息推送的通知
        List<NotifyLog> logList = notifyLogService.listUnSendMsgList();
        if (CollectionUtil.isEmpty(logList)) {
            return;
        }
        List<Long> failIds = new ArrayList<>(Constant.INITIAL_CAPACITY);
        for (NotifyLog notifyLog : logList) {
            try {
                // 推送消息
                notifyLogService.sendMessage(notifyLog);
            } catch (Exception e) {
                failIds.add(notifyLog.getLogId());
            }
        }
        logger.info("共{}条通知；成功推送{}，失败推送{}；失败消息id为：{}", logList.size(), logList.size()-failIds.size(), failIds.size(), failIds);
    }
}
