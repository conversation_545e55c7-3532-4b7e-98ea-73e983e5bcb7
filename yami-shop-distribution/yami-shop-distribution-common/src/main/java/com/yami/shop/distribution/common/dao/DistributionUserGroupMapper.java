package com.yami.shop.distribution.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.distribution.common.model.DistributionUserGroup;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface DistributionUserGroupMapper extends BaseMapper<DistributionUserGroup> {
    /**
     * 分销员分组信息
     * @param page 分页参数
     * @param distributionUserGroup 查询参数
     * @return 分销员分组信息
     */
    IPage<DistributionUserGroup> distributionUserGroupsAndSysUserPage(Page page, @Param("distributionUserGroup") DistributionUserGroup distributionUserGroup);
}
