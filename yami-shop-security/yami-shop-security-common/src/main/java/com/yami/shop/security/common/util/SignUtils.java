package com.yami.shop.security.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.crypto.digest.Digester;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yami.shop.bean.model.SysAccessKey;
import com.yami.shop.common.enums.SysTypeEnum;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.Json;
import com.yami.shop.common.wrapper.RequestWrapper;
import com.yami.shop.security.common.bo.SignResponse;
import com.yami.shop.service.SysAccessKeyService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

/**
 * 签名工具类
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class SignUtils {

    public static final String GRANT_TYPE = "grantType";

    public static final String GRANT_TYPE_VALUE = "sign";

    public static final String THRID_API_VALUE   = "thridApi";

    private static final long TEN_MINUTES = 1000 * 60 * 10;

    /**
     * 签名，通过签名机制，防止应用的请求参数被非法篡改，业务系统必须保证该值不被泄露。
     */
    public static final String SIGN = "sign";

    /**
     * 分配给应用的系统编号
     */
    public static final String APP_ID = "appId";

    /**
     * 分配给应用的系统密钥
     */
    public static final String APP_SECRET = "appSecret";

    /**
     * 请求的时间戳，接入系统的时间误差不能超过 10 分钟
     */
    public static final String TIMESTAMP = "timestamp";

    /**
     * 真正请求的数据
     */
    public static final String DATA = "data";
    /**
     * 签名方式的接口传参类型,如果是纯数组类型需要传这个请求头为list
     */
    public static final String DATA_TYPE = "data-type";
    public static final String SHOP_ID = "shopId";
    public static final String USER_ID = "userId";
    public static final String LIST = "list";
    private final SysAccessKeyService sysAccessKeyService;
    private final ObjectMapper objectMapper;

    /**
     * 验签方法
     */
    public ServerResponseEntity<SignResponse> verify(HttpServletRequest req,String dataType) throws IOException {
        // 这里的param map是包含sign, timestamp等信息的，如果是json形式那么data在下一层，如果不是就在同一层
        TreeMap<String, Object> paramMap;
        // data所在那一层的map数据
        Map<String, Object> dataMap;
        if (StrUtil.isNotBlank(req.getContentType()) && req.getContentType().contains(ContentType.JSON.getValue())) {
            RequestWrapper requestWrapper = null;
            try {
                requestWrapper = new RequestWrapper(req);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            String messagePayload = requestWrapper.getBody();
            // 签名是有顺序的
            paramMap = objectMapper.readValue(messagePayload, TreeMap.class);

            dataMap = (Map<String, Object>) paramMap.get(DATA);
            if(StrUtil.equals(dataType,LIST)) {
                for (String key : dataMap.keySet()) {
                    boolean listType = dataMap.get(key) instanceof List;
                    if (!listType) {
                        continue;
                    }
                    // 这里改写了请求将data里面的数据发送到controller
                    try {
                        requestWrapper = new RequestWrapper(req);
                        requestWrapper.setBody(Json.toJsonString(dataMap.get(key)));
                        req = requestWrapper;
                        break;
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }else {
                // 将data里面的数据发送到controller
                requestWrapper.setBody(objectMapper.writeValueAsString(dataMap));
                req = requestWrapper;
            }
        } else {
            paramMap = new TreeMap<>(req.getParameterMap());
            for (Map.Entry<String, Object> objectEntry : paramMap.entrySet()) {
                String[] list = (String[]) objectEntry.getValue();
                if (list.length == 1) {
                    paramMap.put(objectEntry.getKey(), list[0]);
                    continue;
                }
                Arrays.sort(list);
            }
            dataMap = paramMap;
        }
        ServerResponseEntity<SignResponse> verifyResponse = verify(paramMap, dataMap);

        if (!verifyResponse.isSuccess()) {
            return verifyResponse;
        }
        SignResponse signResponse = verifyResponse.getData();
        signResponse.setDataMap(dataMap);
        signResponse.setReq(req);
        return ServerResponseEntity.success(signResponse);
    }

    /**
     * 校验参数，如果校验成功则返回data数据信息
     * @return
     */
    public ServerResponseEntity<SignResponse> verify(TreeMap<String, Object> paramMap,Map<String, Object> dataMap) {
        SignResponse signResponse = new SignResponse();

        try {
            String appId = (String)paramMap.get(APP_ID);
            if (StrUtil.isBlank(appId)) {
                return ServerResponseEntity.showFailMsg("appid missing").setData(signResponse);
            }
            SysAccessKey sysAccessKey = sysAccessKeyService.getByAccessIdAndPrems(appId);
            if (sysAccessKey == null) {
                return ServerResponseEntity.showFailMsg("appid error").setData(signResponse);
            }
            // 签名
            String sign = (String)paramMap.get(SIGN);
            // 移除签名
            paramMap.remove(SIGN);
            // 移除appId
            paramMap.remove(APP_ID);
            // 用密钥来做签名
            paramMap.put(APP_SECRET, sysAccessKey.getAccessKey());
            signResponse.setAppSecret(sysAccessKey.getAccessKey());
            signResponse.setSysAccessKey(sysAccessKey);


            long currentTimeMillis = System.currentTimeMillis();
            Long timestamp = Long.valueOf(paramMap.get(TIMESTAMP).toString());
            // 签名时间大于十分钟，提示签名超时
            if (timestamp + TEN_MINUTES < currentTimeMillis) {
                return ServerResponseEntity.showFailMsg("The request time has exceeded ten minutes").setData(signResponse);
            }
            // 生成签名
            Digester sha256 = new Digester(DigestAlgorithm.SHA256);
            String sysSign = sha256.digestHex(Json.toJsonString(paramMap));

            // 进行验签
            if (!Objects.equals(sign, sysSign)) {
                return ServerResponseEntity.showFailMsg("sign error").setData(signResponse);
            }

            if (Objects.equals(sysAccessKey.getSysType(), SysTypeEnum.MULTISHOP.value())) {
                // 有没有这加店铺的管理权限
                if(!Objects.equals(sysAccessKey.getUsabilityShopId(),0L)
                        && !Objects.equals(sysAccessKey.getUsabilityShopId(),Long.valueOf(dataMap.get("shopId").toString()))){
                    return ServerResponseEntity.showFailMsg("shop unauthorized").setData(signResponse);
                }
            }


        } catch (Exception e) {
            log.info("sign verify error : {}", e.getMessage());
            return ServerResponseEntity.showFailMsg("system error").setData(signResponse);
        }
        return ServerResponseEntity.success(signResponse);
    }

    public static String sign(String appSecret, Long timestamp, Object data) {

        TreeMap<String, Object> requestMap = new TreeMap<>();
        requestMap.put(TIMESTAMP, timestamp);
        requestMap.put(APP_SECRET, appSecret);
        requestMap.put(DATA, data);

        // 签名
        Digester sha256 = new Digester(DigestAlgorithm.SHA256);
        String sign = sha256.digestHex(Json.toJsonString(requestMap));
        // 请求数据添加签名和账号id
        requestMap.put("sign", sign);
        return sign;
    }




}
