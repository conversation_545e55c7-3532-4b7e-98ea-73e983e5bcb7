
package com.yami.shop.common.allinpay.util;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;

/**
 * <AUTHOR>
 */
public class OpTrustManager implements X509TrustManager {
    private static volatile OpTrustManager instance;
    private SSLSocketFactory sslFactory;

    @Override
    public void checkClientTrusted(X509Certificate[] arg0, String arg1) {
    }

    @Override
    public void checkServerTrusted(X509Certificate[] chain, String authType) {
    }

    @Override
    public X509Certificate[] getAcceptedIssuers() {
        return null;
    }

    public SSLSocketFactory getSslSocketFactory() {
        return this.sslFactory;
    }

    private OpTrustManager() {
    }

    @SuppressWarnings("rawtypes")
    public static OpTrustManager instance() throws NoSuchAlgorithmException, KeyManagementException {
        if (instance == null) {
            Class var0 = OpTrustManager.class;
            synchronized (OpTrustManager.class) {
                if (instance == null) {
                    instance = new OpTrustManager();
                    SSLContext sc = SSLContext.getInstance("TLSv1.2");
                    sc.init(null, new TrustManager[]{instance}, null);
                    instance.sslFactory = sc.getSocketFactory();
                }
            }
        }

        return instance;
    }
}
