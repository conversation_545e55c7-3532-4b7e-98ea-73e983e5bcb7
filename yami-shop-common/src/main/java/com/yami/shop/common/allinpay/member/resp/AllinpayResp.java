package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;
/**
 * <AUTHOR>
 */
public class AllinpayResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 云商通订单号
     */
    private String orderNo;
    /**
     * 商户订单号（支付订单）
     */
    private String bizOrderNo;
    /**
     * 原云商通订单号,退款订单该字段才返回
     */
    private String oriOrderNo;
    /**
     * 原商户订单号,退款订单该字段才返回
     */
    private String oriBizOrderNo;
    /**
     * 订单金额
     */
    private Long amount;
    /**
     * 订单支付完成时间
     * 云商通订单支付完成时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String payDatetime;
    /**
     * 商户系统用户标识，商户系统中唯一编号。
     * 付款人,若平台返回大B的uuid，对应门户上设置中“商户号”字段
     */
    private String buyerBizUserId;
    /**
     * 退款去向
     * 1：到账户余额
     * 2：到原支付账户银行卡/微信/支付宝等
     */
    private Long refundWhereabouts;
    /**
     * 支付人帐号
     * 微信支付的openid
     * 支付宝平台的user_id
     * 刷卡交易：隐藏的卡号,例如621700****4586
     */
    private String acct;
    /**
     * 扩展参数
     */
    private String extendInfo;
    /**
     * 订单是否成功
     * “OK”标识支付成功；
     * “pending”表示进行中（中间状态）
     * “error”表示支付失败；
     * 提现在成功和失败时都会通知商户；其他订单只在成功时会通知商户。
     */
    private String status;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getOriOrderNo() {
        return oriOrderNo;
    }

    public void setOriOrderNo(String oriOrderNo) {
        this.oriOrderNo = oriOrderNo;
    }

    public String getOriBizOrderNo() {
        return oriBizOrderNo;
    }

    public void setOriBizOrderNo(String oriBizOrderNo) {
        this.oriBizOrderNo = oriBizOrderNo;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public String getPayDatetime() {
        return payDatetime;
    }

    public void setPayDatetime(String payDatetime) {
        this.payDatetime = payDatetime;
    }

    public String getBuyerBizUserId() {
        return buyerBizUserId;
    }

    public void setBuyerBizUserId(String buyerBizUserId) {
        this.buyerBizUserId = buyerBizUserId;
    }

    public Long getRefundWhereabouts() {
        return refundWhereabouts;
    }

    public void setRefundWhereabouts(Long refundWhereabouts) {
        this.refundWhereabouts = refundWhereabouts;
    }

    public String getAcct() {
        return acct;
    }

    public void setAcct(String acct) {
        this.acct = acct;
    }

    public String getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "AllinpayResp{" +
                "orderNo='" + orderNo + '\'' +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                ", oriOrderNo='" + oriOrderNo + '\'' +
                ", oriBizOrderNo='" + oriBizOrderNo + '\'' +
                ", amount=" + amount +
                ", payDatetime='" + payDatetime + '\'' +
                ", buyerBizUserId='" + buyerBizUserId + '\'' +
                ", refundWhereabouts=" + refundWhereabouts +
                ", acct='" + acct + '\'' +
                ", extendInfo='" + extendInfo + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
