package com.yami.shop.bean.vo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 *
 * <AUTHOR>
 * @date 2021-09-07 10:12:32
 */
@Data
public class SupplierSkuVO {
    /**
     * 供应商商品id
     */
    private Long supplierProdId;
    /**
     * skuid
     */
    private Long skuId;
    /**
     * sku名称
     */
    @Schema(description = "sku名称" )
    private String skuName;
    /**
     * 最小订货量
     */
    private Integer minOrderQuantity;
    /**
     * 采购价
     */
    private Double purchasePrice;

    /**
     * 库存
     */
    @Schema(description = "库存" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer stocks;

    /**
     * 商家编码
     */
    @Schema(description = "商家编码" )
    private String partyCode;

    /**
     * sku图片
     */
    @Schema(description = "sku图片" )
    private String pic;

    @Schema(description = "入库点库存")
    private Integer inStock;
}
