package com.yami.shop.security.common.bo;

import com.yami.shop.bean.model.SysAccessKey;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;

import java.util.Map;

/**
 * 签名校验完毕之后返回的信息
 * <AUTHOR>
 * @date 2021/12/29
 */
@Data
public class SignResponse {

    /**
     * data所在那一层的map数据
     */
    private Map<String, Object> dataMap;

    private String appSecret;

    private Long timestamp;

    private SysAccessKey sysAccessKey;

    private HttpServletRequest req;
}
