package com.yami.shop.platform.controller;

import com.yami.shop.bean.vo.OrderDetailVO;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.OrderItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/6/9 9:19
 */
@RestController("platformOrderItemController")
@RequestMapping("/platform/order_item")
@Tag(name = "platform-订单项信息")
@AllArgsConstructor
public class OrderItemController {

    private final OrderItemService orderItemService;

    @GetMapping("/get_order_detail")
    @Operation(summary = "查询订单项、退款详情" , description = "根据id查询")
    @Parameters(value = {
            @Parameter(name = "orderNumber", description = "订单编号" ),
            @Parameter(name = "refundSn", description = "退款编号" ),
            @Parameter(name = "reason", description = "店铺钱包金额发生改变的原因 0用户支付 1用户确认收货 2 用户退款申请 3 拒绝用户退款申请 4 提现申请 5 提现申请被拒绝 6 提现申请通过" )
    })
    @PreAuthorize("@pms.hasPermission('platform:orderItem:info')")
    public ServerResponseEntity<OrderDetailVO> getOrderItemDetail(String orderNumber, String refundSn, Integer reason){
        OrderDetailVO orderDetailVO = orderItemService.listDetailByParam(orderNumber, refundSn, reason, Constant.PLATFORM_SHOP_ID);
        return ServerResponseEntity.success(orderDetailVO);
    }
}
