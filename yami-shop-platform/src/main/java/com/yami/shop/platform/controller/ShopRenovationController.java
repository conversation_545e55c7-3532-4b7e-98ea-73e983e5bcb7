package com.yami.shop.platform.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.enums.RenovationType;
import com.yami.shop.bean.model.ShopRenovation;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.ShopRenovationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;


/**
 * 店铺装修信息
 *
 * <AUTHOR>
 * @date 2021-01-05 11:03:38
 */
@RestController
@AllArgsConstructor
@RequestMapping("/platform/shopRenovation" )
@Tag(name = "店铺装修页面接口")
public class ShopRenovationController {

    private final ShopRenovationService shopRenovationService;

    @GetMapping("/pagePC" )
    @PreAuthorize("@pms.hasPermission('platform:shopRenovation:pagePC')")
    @Operation(summary = "PC端分页获取店铺装修信息" , description = "PC端分页获取店铺装修信息")
    public ServerResponseEntity<IPage<ShopRenovation>> getShopRenovationPagePc(PageParam<ShopRenovation> page, ShopRenovation shopRenovation) {
        return ServerResponseEntity.success(shopRenovationService.page(page, new LambdaQueryWrapper<ShopRenovation>()
                .eq(ShopRenovation::getShopId,Constant.PLATFORM_SHOP_ID)
                .eq(Objects.nonNull(shopRenovation.getRenovationType()), ShopRenovation::getRenovationType, shopRenovation.getRenovationType())
                .like(Objects.nonNull(shopRenovation.getName()), ShopRenovation::getName, shopRenovation.getName())
                .eq(ShopRenovation::getRenovationType,shopRenovation.getRenovationType())
                .orderByDesc(ShopRenovation::getHomeStatus, ShopRenovation::getCreateTime))
        );
    }

    @GetMapping("/pageMove" )
    @PreAuthorize("@pms.hasPermission('platform:shopRenovation:pageMove')")
    @Operation(summary = "移动端分页获取店铺装修信息" , description = "移动端分页获取店铺装修信息")
    public ServerResponseEntity<IPage<ShopRenovation>> getShopRenovationPageMove(PageParam<ShopRenovation> page, ShopRenovation shopRenovation) {
        return ServerResponseEntity.success(shopRenovationService.page(page, new LambdaQueryWrapper<ShopRenovation>()
                .eq(ShopRenovation::getShopId,Constant.PLATFORM_SHOP_ID)
                .eq(Objects.nonNull(shopRenovation.getRenovationType()), ShopRenovation::getRenovationType, shopRenovation.getRenovationType())
                .like(Objects.nonNull(shopRenovation.getName()), ShopRenovation::getName, shopRenovation.getName())
                .eq(ShopRenovation::getRenovationType,shopRenovation.getRenovationType())
                .eq(ShopRenovation::getPageType, shopRenovation.getPageType())
                .orderByDesc(ShopRenovation::getHomeStatus, ShopRenovation::getCreateTime)
        ));
    }

    @GetMapping("/info/{renovationId}" )
    @Operation(summary = "查询店铺装修信息" , description = "查询店铺装修信息")
    @Parameter(name = "renovationId", description = "店铺装修id" )
    public ServerResponseEntity<ShopRenovation> getById(@PathVariable("renovationId") Long renovationId) {
        return ServerResponseEntity.success(shopRenovationService.getById(renovationId));
    }

    @SysLog("新增店铺装修信息" )
    @PostMapping("/savePC")
    @PreAuthorize("@pms.hasPermission('platform:shopRenovation:savePC')")
    @Operation(summary = "PC端新增店铺装修信息" , description = "PC端新增店铺装修信息")
    public ServerResponseEntity<Long> savePc(@RequestBody @Valid ShopRenovation shopRenovation) {
        shopRenovation.setShopId(Constant.PLATFORM_SHOP_ID);
        if (Objects.isNull(shopRenovation.getRenovationType())) {
            shopRenovation.setRenovationType(RenovationType.H5.value());
        }
        shopRenovationService.save(shopRenovation);
        return ServerResponseEntity.success(shopRenovation.getRenovationId());
    }

    @SysLog("新增店铺装修信息" )
    @PostMapping("/saveMove")
    @PreAuthorize("@pms.hasPermission('platform:shopRenovation:saveMove')")
    @Operation(summary = "移动端新增店铺装修信息" , description = "移动端新增店铺装修信息")
    public ServerResponseEntity<Long> saveMove(@RequestBody @Valid ShopRenovation shopRenovation) {
        shopRenovation.setShopId(Constant.PLATFORM_SHOP_ID);
        if (Objects.isNull(shopRenovation.getRenovationType())) {
            shopRenovation.setRenovationType(RenovationType.H5.value());
        }
        shopRenovationService.save(shopRenovation);
        return ServerResponseEntity.success(shopRenovation.getRenovationId());
    }
}
