package com.yami.shop.bean.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/8/30 10:25
 */
@Data
public class GroupSkuVO {

    @Schema(description = "拼团活动商品规格id" )
    private Long groupSkuId;

    @Schema(description = "商品规格Id" )
    private Long skuId;

    @Schema(description = "原售价格" )
    private Double price;

    @Schema(description = "活动价格" )
    private Double actPrice;

    @Schema(description = "sku名称" )
    private String skuName;

    @Schema(description = "库存" )
    private Integer stocks;

    @Schema(description = "sku图片" )
    private String pic;

    @Schema(description = "销售属性组合字符串 格式是p1:v1;p2:v2" )
    private String properties;

    @Schema(description = "是否有库存" )
    private Boolean isHasStock = true;
}
