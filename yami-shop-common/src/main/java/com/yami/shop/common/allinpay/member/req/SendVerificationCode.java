package com.yami.shop.common.allinpay.member.req;

import com.yami.shop.common.allinpay.member.constant.VerificationCodeType;

import java.io.Serial;
import java.io.Serializable;

/**
 * 发送短信验证码
 *
 * <AUTHOR>
 */
public class SendVerificationCode extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    public static final String METHOD_NAME = "sendVerificationCode";

    private String phone;

    /**
     * 验证码类型 9-绑定手机
     */
    private Integer verificationCodeType = VerificationCodeType.BIND_PHONE.value();


    public SendVerificationCode() {
    }

    public SendVerificationCode(String bizUserId, String phone) {
        this.phone = phone;
        this.bizUserId = bizUserId;
    }

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getVerificationCodeType() {
        return verificationCodeType;
    }

    public void setVerificationCodeType(Integer verificationCodeType) {
        this.verificationCodeType = verificationCodeType;
    }


}
