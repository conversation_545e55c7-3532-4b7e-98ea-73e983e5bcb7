package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;
/**
 * 影印件采集通知回调
 *
 * <AUTHOR>
 */
public class IdCardCollectResultResp extends BizUserId implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * OCR识别与企业工商认证信息是否一致 0否1是
     */
    private Integer ocrRegnumComparisonResult;
    /**
     * OCR识别与企业法人实名信息是否一致 0否1是
     */
    private Integer ocrIdcardComparisonResult;
    /**
     * 比对结果信息 存在多种结果信息一起返回，使用“;”进行拼接
     */
    private String resultInfo;

    public Integer getOcrRegnumComparisonResult() {
        return ocrRegnumComparisonResult;
    }

    public void setOcrRegnumComparisonResult(Integer ocrRegnumComparisonResult) {
        this.ocrRegnumComparisonResult = ocrRegnumComparisonResult;
    }

    public Integer getOcrIdcardComparisonResult() {
        return ocrIdcardComparisonResult;
    }

    public void setOcrIdcardComparisonResult(Integer ocrIdcardComparisonResult) {
        this.ocrIdcardComparisonResult = ocrIdcardComparisonResult;
    }

    public String getResultInfo() {
        return resultInfo;
    }

    public void setResultInfo(String resultInfo) {
        this.resultInfo = resultInfo;
    }

    @Override
    public String toString() {
        return "IdCardCollectResultResp{" +
                "ocrRegnumComparisonResult=" + ocrRegnumComparisonResult +
                ", ocrIdcardComparisonResult=" + ocrIdcardComparisonResult +
                ", resultInfo='" + resultInfo + '\'' +
                '}';
    }
}
