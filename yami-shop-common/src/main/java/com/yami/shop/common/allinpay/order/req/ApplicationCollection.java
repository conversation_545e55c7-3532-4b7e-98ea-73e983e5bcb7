package com.yami.shop.common.allinpay.order.req;

import java.util.List;
import java.util.Map;

/**
 * 托管代收申请
 * 说明：
 * 1)托管代收交易即将用户（买方）资金代收到中间账户。
 * 2)托管代收申请成功后，需要支付确认。
 *
 * <AUTHOR>
 */

public class ApplicationCollection extends AbstractOrderReq {
    /**
     * 商户订单号（支付订单）
     * 全局唯一，不可重复，商户侧务必保障此订单号全局唯一，不可重复，如订单号重复，则影响订单代付及退款。
     * 不可包含“|”字符
     */
    private String bizOrderNo;
    /**
     * 商户系统用户标识，商户系统中唯一编号。
     * 付款人
     */
    private String payerId;
    /**
     * 收款列表
     */
    private List<Reciever> recieverList;
    /**
     * 电商业务：代收消费金[3001] + 代付购买金[4001]
     */
    private String tradeCode;
    /**
     * 订单金额,单位：分。订单金额=收款列表总金额+手续费
     */
    private Long amount;
    /**
     * 手续费
     * 内扣，如果不存在，则填0。
     * 单位：分。
     * 如amount为100，fee为2，实际到账金额为98。
     * 如果不填，默认为0。
     */
    private Long fee;
    /**
     * 交易验证方式 0.仅渠道验证，通商云不做交易验证 1.通商云发送并验证短信验证码，有效期3分钟。2.验证通商云支付密码
     */
    private Long validateType;
    /**
     * 前台通知地址
     * 前台交易时必填，支付后，跳转的前台页面；
     * 1.收银宝网关必传
     * 2.收银宝h5收银台必传
     * 3.收银宝h5网关必传
     * 4.收银宝银联JS选填，若指定页面跳转上送该字段
     * 5.收银宝微信JS选填，若指定页面跳转上送该字段
     * 6.收银宝微信正扫选填，若指定页面跳转则上送该字段
     * 注：必须为https协议地址，且不允许带参数
     */
    private String frontUrl;
    /**
     * 订单过期时间
     * yyyy-MM-dd HH:mm:ss
     * 控制订单可支付时间，订单最长时效为24小时，即过期时间不能大于订单创建时间24小时；
     * 1） 需确认支付情况-确认支付时间不能大于订单过期时间；
     * 注：收银宝H5收银台若未上送订单过期时间，确认支付后默认订单有效期5分钟
     * 2） 无需确认支付情况-透传至渠道方，最大不超过60分钟，控制订单支付时间范围
     */
    private String orderExpireDatetime;
    /**
     * TODO 有空优化
     * 行业代码 2510.服装鞋帽 2511.箱包配饰 2522.其他
     */
    private String industryCode;
    /**
     * 行业名称
     */
    private String industryName;
    /**
     * 访问终端类型 1.mobile 2.pc
     */
    private Long source;
    /**
     * 摘要
     */
    private String summary;
    /**
     * 拓展参数，接口将原样返回，不可包含“|”及换行符+，空格，/，?，%，#，&，=等特殊字符，最多100个字符
     */
    private String extendInfo;
    /**
     * 后台通知地址
     */
    private String backUrl;
    /**
     * 支付方式
     */
    private Map<String, Object> payMethod;

    private static final String METHOD_NAME = "agentCollectApply";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    public ApplicationCollection() {
    }

    public ApplicationCollection(String bizOrderNo, String payerId, List<Reciever> recieverList, String tradeCode, Long amount, Long fee, Long validateType, String frontUrl, String orderExpireDatetime, String industryCode, String industryName, Long source, String summary, String extendInfo) {
        this.bizOrderNo = bizOrderNo;
        this.payerId = payerId;
        this.recieverList = recieverList;
        this.tradeCode = tradeCode;
        this.amount = amount;
        this.fee = fee;
        this.validateType = validateType;
        this.frontUrl = frontUrl;
        this.orderExpireDatetime = orderExpireDatetime;
        this.industryCode = industryCode;
        this.industryName = industryName;
        this.source = source;
        this.summary = summary;
        this.extendInfo = extendInfo;
    }

    public Map<String, Object> getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Map<String, Object> payMethod) {
        this.payMethod = payMethod;
    }

    public String getBackUrl() {
        return backUrl;
    }

    public void setBackUrl(String backUrl) {
        this.backUrl = backUrl;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getPayerId() {
        return payerId;
    }

    public void setPayerId(String payerId) {
        this.payerId = payerId;
    }

    public List<Reciever> getRecieverList() {
        return recieverList;
    }

    public void setRecieverList(List<Reciever> recieverList) {
        this.recieverList = recieverList;
    }

    public String getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Long getFee() {
        return fee;
    }

    public void setFee(Long fee) {
        this.fee = fee;
    }

    public Long getValidateType() {
        return validateType;
    }

    public void setValidateType(Long validateType) {
        this.validateType = validateType;
    }

    public String getFrontUrl() {
        return frontUrl;
    }

    public void setFrontUrl(String frontUrl) {
        this.frontUrl = frontUrl;
    }

    public String getOrderExpireDatetime() {
        return orderExpireDatetime;
    }

    public void setOrderExpireDatetime(String orderExpireDatetime) {
        this.orderExpireDatetime = orderExpireDatetime;
    }

    public String getIndustryCode() {
        return industryCode;
    }

    public void setIndustryCode(String industryCode) {
        this.industryCode = industryCode;
    }

    public String getIndustryName() {
        return industryName;
    }

    public void setIndustryName(String industryName) {
        this.industryName = industryName;
    }

    public Long getSource() {
        return source;
    }

    public void setSource(Long source) {
        this.source = source;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }

    @Override
    public String toString() {
        return "ApplicationCollection{" +
                "bizOrderNo='" + bizOrderNo + '\'' +
                ", payerId='" + payerId + '\'' +
                ", recieverList=" + recieverList +
                ", tradeCode='" + tradeCode + '\'' +
                ", amount=" + amount +
                ", fee=" + fee +
                ", validateType=" + validateType +
                ", frontUrl='" + frontUrl + '\'' +
                ", orderExpireDatetime='" + orderExpireDatetime + '\'' +
                ", industryCode='" + industryCode + '\'' +
                ", industryName='" + industryName + '\'' +
                ", source=" + source +
                ", summary='" + summary + '\'' +
                ", extendInfo='" + extendInfo + '\'' +
                ", backUrl='" + backUrl + '\'' +
                ", payMethod=" + payMethod +
                '}';
    }

    /**
     * 收款人信息
     */
    public static class Reciever {
        private String bizUserId;
        private Long amount;

        public Reciever() {
        }

        public Reciever(String bizUserId, Long amount) {
            this.bizUserId = bizUserId;
            this.amount = amount;
        }

        public String getBizUserId() {
            return bizUserId;
        }

        public void setBizUserId(String bizUserId) {
            this.bizUserId = bizUserId;
        }

        public Long getAmount() {
            return amount;
        }

        public void setAmount(Long amount) {
            this.amount = amount;
        }

        @Override
        public String toString() {
            return "Reciever{" +
                    "bizUserId='" + bizUserId + '\'' +
                    ", amount=" + amount +
                    '}';
        }
    }


}
