package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class RefundDeliveryDto {

    @Schema(description = "物流公司id" )
    private Long deyId;

    @Schema(description = "物流公司名称" )
    private String deyName;

    @Schema(description = "物流编号" )
    private String deyNu;

    @Schema(description = "收件人姓名" )
    private String receiverName;

    @Schema(description = "收件人手机" )
    private String receiverMobile;

    @Schema(description = "收件人座机" )
    private String receiverTelephone;

    @Schema(description = "收件人邮政编码" )
    private String receiverPostCode;

    @Schema(description = "收件人地址" )
    private String receiverAddr;

    @Schema(description = "发送人手机号码" )
    private String senderMobile;

    @Schema(description = "描述" )
    private String senderRemarks;

    @Schema(description = "图片凭证" )
    private String imgs;

    @Schema(description = "创建时间" )
    private Date createTime;
}
