package com.yami.shop.multishop.controller;

import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.dto.WarehouseDTO;
import com.yami.shop.bean.vo.WarehouseVO;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.common.util.PrincipalUtil;
import com.yami.shop.security.common.bo.UidInfoBO;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.service.WarehouseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 *
 *
 * <AUTHOR>
 * @date 2023-11-08 11:11:44
 */
@RestController("multishopWarehouseController")
@RequestMapping("/m/warehouse")
@Tag(name = "商家仓库管理")
@AllArgsConstructor
public class WarehouseController {

    private final WarehouseService warehouseService;

    @GetMapping("/page")
    @Operation(summary = "获取仓库列表", description = "分页获取列表")
    public ServerResponseEntity<IPage<WarehouseVO>> page(@Valid PageParam pageDTO, WarehouseDTO warehouseDTO) {
        warehouseDTO.setShopId(AuthUserContext.get().getShopId());
        warehouseDTO.setSysType(AuthUserContext.get().getSysType().value());
        IPage<WarehouseVO> warehousePage = warehouseService.page(pageDTO, warehouseDTO);
        return ServerResponseEntity.success(warehousePage);
    }

    @GetMapping
    @Operation(summary = "获取仓库", description = "根据warehouseId获取")
    public ServerResponseEntity<WarehouseVO> getByWarehouseId(@RequestParam Long warehouseId) {
        WarehouseVO warehouseVO = warehouseService.getByWarehouseId(warehouseId);
        if (StrUtil.isNotBlank(warehouseVO.getPhone()) && PrincipalUtil.isMobile(warehouseVO.getPhone())) {
            warehouseVO.setPhone(PhoneUtil.hideBetween(warehouseVO.getPhone()).toString());
        }
        return ServerResponseEntity.success(warehouseVO);
    }

    @PostMapping
    @Operation(summary = "保存仓库", description = "保存仓库")
    public ServerResponseEntity<Void> save(@Valid @RequestBody WarehouseDTO warehouseDTO) {
        Long shopId = AuthUserContext.get().getShopId();
        Integer sysType = AuthUserContext.get().getSysType().value();
        warehouseDTO.setShopId(shopId);
        warehouseDTO.setSysType(sysType);
        warehouseDTO.setType(1);
        warehouseService.insertWarehouseAndArea(warehouseDTO);
        // 清除缓存
        warehouseService.removeWarehouseCache(warehouseDTO.getWarehouseId(), warehouseDTO.getShopId(), warehouseDTO.getSysType());
        return ServerResponseEntity.success();
    }

    @PutMapping
    @Operation(summary = "更新仓库", description = "更新仓库")
    public ServerResponseEntity<Void> update(@Valid @RequestBody WarehouseDTO warehouseDTO) {
        if (Objects.isNull(warehouseDTO.getCityList()) && warehouseDTO.getType() == 1) {
            // 请选择供应区域
            throw new YamiShopBindException("yami.warehouse.excption.selectArea");
        }
        UidInfoBO token = AuthUserContext.get();
        warehouseDTO.setShopId(token.getShopId());
        warehouseDTO.setSysType(token.getSysType().value());
        warehouseService.updateWarehouseAndArea(warehouseDTO);
        // 清除缓存
        warehouseService.removeWarehouseCache(warehouseDTO.getWarehouseId(), warehouseDTO.getShopId(), warehouseDTO.getSysType());
        return ServerResponseEntity.success();
    }

    @DeleteMapping
    @Operation(summary = "删除仓库", description = "根据id删除")
    public ServerResponseEntity<Void> delete(@RequestParam Long warehouseId) {
        WarehouseVO warehouseVO = warehouseService.deleteWarehouseAndArea(warehouseId);
        // 删除仓库的缓存
        warehouseService.removeWarehouseCache(warehouseVO.getWarehouseId(), warehouseVO.getShopId(), warehouseVO.getSysType());
        return ServerResponseEntity.success();
    }

    @GetMapping("/list_warehouse_city")
    @Operation(summary = "获取店铺所有的供货城市", description = "获取店铺所有的供货城市")
    public ServerResponseEntity<List<Long>> getListWarehouseCity(@RequestParam(value = "warehouseId", required = false)  Long warehouseId) {
        Long shopId = AuthUserContext.get().getShopId();
        Integer sysType = AuthUserContext.get().getSysType().value();
        return ServerResponseEntity.success(warehouseService.listWarehouseAreaByShopId(shopId, sysType, warehouseId));
    }

    @GetMapping("/list_warehouse")
    @Operation(summary = "获取店铺所有的仓库", description = "获取店铺所有的仓库")
    public ServerResponseEntity<List<WarehouseVO>> getListWarehouse() {
        WarehouseDTO warehouseDTO = new WarehouseDTO();
        warehouseDTO.setShopId(AuthUserContext.get().getShopId());
        warehouseDTO.setSysType(AuthUserContext.get().getSysType().value());
        return ServerResponseEntity.success(warehouseService.listWarehouse(warehouseDTO));
    }
}
