package com.yami.shop.common.allinpay.member.resp;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class SetCompanyInfoResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 商户的用户id
     */
    private String bizUserId;
    /**
     * 审核结果(2:成功, 3:失败)
     */
    private Integer result;
    /**
     * 审核时间
     */
    private String checkTime;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 备注
     */
    private String remark;

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(String checkTime) {
        this.checkTime = checkTime;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "SetCompanyInfoResp{" +
                "bizUserId='" + bizUserId + '\'' +
                ", result=" + result +
                ", checkTime='" + checkTime + '\'' +
                ", failReason='" + failReason + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
