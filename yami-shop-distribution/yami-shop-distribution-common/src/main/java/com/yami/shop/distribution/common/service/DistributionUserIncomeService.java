package com.yami.shop.distribution.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.distribution.common.dto.DistributionOrderDto;
import com.yami.shop.distribution.common.dto.DistributionUserIncomeDto;
import com.yami.shop.distribution.common.dto.StatisticsDisUserIncomeDto;
import com.yami.shop.distribution.common.model.DistributionUserIncome;
import com.yami.shop.distribution.common.param.DistributionParam;
import com.yami.shop.distribution.common.param.RangeTimeParam;
import com.yami.shop.distribution.common.vo.DistributionOrdersVO;
import com.yami.shop.distribution.common.vo.DistributionSettleInfoVO;

import java.util.List;

/**
 * <AUTHOR> on 2019/04/01.
 */
public interface DistributionUserIncomeService extends IService<DistributionUserIncome> {

    /**
     * 获取分销员的收入记录
     * @param page 分页参数
     * @param shopId 店铺id
     * @param rangeTimeParam 时间范围
     * @param distributionParam 分销查询参数
     * @param orderNumber 订单号
     * @param state 收入状态
     * @param distributionUserIncome 分销员收入信息
     * @return 分销员的收入记录
     */
    IPage<DistributionUserIncome> incomeAndDistributionUserPage(Page page, Long shopId, RangeTimeParam rangeTimeParam, DistributionParam distributionParam, String orderNumber, Integer state, DistributionUserIncome distributionUserIncome);

    /**
     * 分销员推广订单信息
     * @param page 分页参数
     * @param distributionUserId 分销员id
     * @return 分销员推广订单信息
     */
    IPage<DistributionOrderDto> getDistributionOrderDtoByDistributionUserId(Page page, Long distributionUserId);

    /**
     * 统计分销员当日收入
     * @param distributionUserId 分销员id
     * @return 收入金额
     */
    StatisticsDisUserIncomeDto statisticsDistributionUserIncome(Long distributionUserId);

    /**
     * 查询分销员收入记录
     * @param page 分页参数
     * @param distributionUserId 分销员id
     * @return 收入记录
     */
    IPage<DistributionUserIncomeDto> getDistributionUserIncomePage(PageParam<DistributionUserIncome> page, Long distributionUserId);

    /**
     * 通过状态获取我的推广订单(0:待支付 1:待结算 2:已结算 -1:订单失效)
     *
     * @param page 分页参数
     * @param distributionUserId 分销员id
     * @param state 收入状态 0:待支付 1:待结算 2:已结算 -1:订单失效
     * @return 我的推广订单
     */
    IPage<DistributionOrdersVO> getMyPromotionOrderByState(PageParam<DistributionOrdersVO> page, Long distributionUserId, Integer state);

    /**
     * 分销员佣金结算处理
     *
     * @param orderNumbers 最后结算时间
     * @param paySysType
     */
    void commissionSettlementHandle(List<String> orderNumbers, Integer paySysType);

    /**
     * 批量更新分销收入记录
     * @param distributionUserIncomeList
     * @return
     */
     int updateBatchState(List<DistributionUserIncome> distributionUserIncomeList);

    /**
     * 获取分销结算信息
     * @param userId 用户id
     * @return 分销结算信息
     */
    DistributionSettleInfoVO getSettleInfo(String userId);
}
