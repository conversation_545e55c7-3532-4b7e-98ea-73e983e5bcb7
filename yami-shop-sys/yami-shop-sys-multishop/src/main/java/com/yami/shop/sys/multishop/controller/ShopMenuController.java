package com.yami.shop.sys.multishop.controller;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.security.multishop.model.YamiShopUser;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.sys.common.model.ShopMenu;
import com.yami.shop.sys.common.service.ShopMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * 菜单管理
 *
 * <AUTHOR>
 * @date 2021-03-01 17:42:09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sys/shopMenu")
@Tag(name = "菜单管理接口")
public class ShopMenuController {

    private final ShopMenuService shopMenuService;

    /**
     * 分页查询
     *
     * @param page     分页对象
     * @param shopMenu 菜单管理
     * @return 分页数据
     */
    @GetMapping("/page")
    @Operation(summary = "分页获取菜单" , description = "分页获取菜单")
    @PreAuthorize("@pms.hasPermission('sys:shopMenu:page')")
    public ServerResponseEntity<IPage<ShopMenu>> getShopMenuPage(PageParam<ShopMenu> page, ShopMenu shopMenu) {
        return ServerResponseEntity.success(shopMenuService.page(page, new LambdaQueryWrapper<ShopMenu>()));
    }

    @GetMapping("/nav")
    @Operation(summary = "获取用户所拥有的菜单和权限" , description = "通过登陆用户的userId获取用户所拥有的菜单和权限")
    public ServerResponseEntity<Map<Object, Object>> nav(){
        List<ShopMenu> shopMenus = shopMenuService.listMenuByEmployeeId(SecurityUtils.getShopUser().getEmployeeId());
        //获取用户所有权限
        Set<String> authorities = shopMenuService.getShopPermissions(AuthUserContext.getEmployeeId(), AuthUserContext.getIsAdmin());
        //获取已经隐藏的按钮权限信息
        Set<String> perms = shopMenuService.listConcealButtonPerms();
        //移除掉隐藏按钮的权限信息
        authorities.removeAll(perms);
        return ServerResponseEntity.success(MapUtil.builder().put("menuList", shopMenus).put("authorities", authorities).build());
    }

    /**
     * 获取菜单页面的表
     *
     * @return
     */
    @GetMapping("/table")
    @Operation(summary = "获取菜单页面的表" , description = "获取菜单页面的表")
    @PreAuthorize("@pms.hasPermission('sys:shopMenu:table')")
    public ServerResponseEntity<List<ShopMenu>> table() {
        YamiShopUser shopUser = SecurityUtils.getShopUser();
        List<ShopMenu> shopMenus = shopMenuService.listMenuAndBtn(shopUser.getEmployeeId());
        return ServerResponseEntity.success(shopMenus);
    }

    /**
     * 所有菜单列表(用于新建、修改角色时 获取菜单的信息)
     */
    @GetMapping("/list")
    @Operation(summary = "获取用户所拥有的菜单(不包括按钮)" , description = "通过登陆用户的userId获取用户所拥有的菜单和权限")
    @PreAuthorize("@pms.hasPermission('sys:shopMenu:list')")
    public ServerResponseEntity<List<ShopMenu>> list() {
        List<ShopMenu> shopMenus = shopMenuService.listSimpleMenuNoButton();
        return ServerResponseEntity.success(shopMenus);
    }

    /**
     * 选择菜单
     */
    @GetMapping("/listRootMenu")
    @Operation(summary = "选择菜单" , description = "选择菜单")
    @PreAuthorize("@pms.hasPermission('sys:shopMenu:listRootMenu')")
    public ServerResponseEntity<List<ShopMenu>> listRootMenu() {
        //查询列表数据
        List<ShopMenu> shopMenus = shopMenuService.listRootMenu();
        return ServerResponseEntity.success(shopMenus);
    }

    /**
     * 选择子菜单
     */
    @GetMapping("/listChildrenMenu")
    @Operation(summary = "选择子菜单" , description = "选择子菜单")
    @PreAuthorize("@pms.hasPermission('sys:shopMenu:listChildrenMenu')")
    public ServerResponseEntity<List<ShopMenu>> listChildrenMenu(Long parentId) {
        //查询列表数据
        List<ShopMenu> shopMenus = shopMenuService.listChildrenMenuByParentId(parentId);
        return ServerResponseEntity.success(shopMenus);
    }

    /**
     * 通过id查询菜单管理
     *
     * @param menuId id
     * @return 单个数据
     */
    @GetMapping("/info/{menuId}")
    @Operation(summary = "通过id查询菜单" , description = "通过id查询菜单")
    @Parameter(name = "menuId", description = "菜单id" )
    @PreAuthorize("@pms.hasPermission('sys:shopMenu:info')")
    public ServerResponseEntity<ShopMenu> getById(@PathVariable("menuId") Long menuId) {
        return ServerResponseEntity.success(shopMenuService.getById(menuId));
    }
}
