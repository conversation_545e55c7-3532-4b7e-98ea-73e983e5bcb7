package com.yami.shop.distribution.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.distribution.common.model.DistributionUserBind;
import com.yami.shop.distribution.common.param.RangeTimeParam;

/**
 *
 * <AUTHOR> on 2019/04/01.
 */
public interface DistributionUserBindService extends IService<DistributionUserBind> {
    /**
     * 分销员和用户绑定关系列表
     * @param page 分页参数
     * @param distributionUserBind 绑定信息
     * @param bindTime 绑定时间
     * @param invalidTime 失效时间
     * @param userName 用户昵称
     * @param parentName 分销员昵称
     * @param sort 排序字段
     * @param orderBy 排序方式
     * @param cUserMobile 用户号码
     * @param dUserMobile 分销员号码
     * @return 分销员和用户绑定关系列表
     */
    IPage<DistributionUserBind> distributionMsgsAndUserPage(Page page, DistributionUserBind distributionUserBind, RangeTimeParam bindTime, RangeTimeParam invalidTime, String userName, String  parentName, String cUserMobile, String dUserMobile, Integer sort, Integer orderBy);
}
