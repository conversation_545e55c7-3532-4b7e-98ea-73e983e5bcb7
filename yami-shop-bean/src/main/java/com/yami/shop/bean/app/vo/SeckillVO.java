package com.yami.shop.bean.app.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.yami.shop.bean.app.dto.SeckillSkuDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 秒杀信息
 *
 * <AUTHOR>
 * @date 2019-08-28 09:36:59
 */
@Data
public class SeckillVO{

    @TableId
    @Schema(description = "秒杀活动id" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long seckillId;

    @Schema(description = "商品id" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long prodId;

    @NotNull
    @Schema(description = "活动名称" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String seckillName;

    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Date startTime;

    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "结束时间" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Date endTime;

    @Schema(description = "活动标签" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String seckillTag;

    @Schema(description = "限购数量" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer maxNum;

    @Schema(description = "店铺id" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Long shopId;

    @Schema(description = "秒杀活动剩余库存" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer seckillTotalStocks;

    @Schema(description = "秒杀活动原始库存" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer seckillOriginStocks;

    @Schema(description = "秒杀活动最低价" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Double seckillPrice;

    @Schema(description = "秒杀sku列表" )
    private List<SeckillSkuDto> seckillSkuList;

    @Schema(description = "状态：0 失效 1正常" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer status;

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "语言" )
    private Integer lang;
}
