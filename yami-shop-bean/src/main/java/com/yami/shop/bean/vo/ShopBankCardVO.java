package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/4/26 10:12
 */
public class ShopBankCardVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "主键id")
    private Long shopBankCardId;

    @Schema(description = "银行名称")
    private String bankName;

    @Schema(description = "账户名")
    private String recipientName;

    @Schema(description = "账号")
    private String cardNo;

    @Schema(description = "银行开户行")
    private String openingBank;

    @Schema(description = "是否为默认")
    private Integer isDefault;

    @Schema(description = "是否已验证")
    private Integer isVerify;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "银行卡/账户属性 0个人银行卡 1企业对公账户")
    private Integer bankCardPro;

    @Schema(description = "支付行号")
    private String unionBank;

    @Schema(description = "卡号")
    private String bankCardNo;

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopBankCardId() {
        return shopBankCardId;
    }

    public void setShopBankCardId(Long shopBankCardId) {
        this.shopBankCardId = shopBankCardId;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getRecipientName() {
        return recipientName;
    }

    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public Integer getIsVerify() {
        return isVerify;
    }

    public void setIsVerify(Integer isVerify) {
        this.isVerify = isVerify;
    }

    public Integer getBankCardPro() {
        return bankCardPro;
    }

    public void setBankCardPro(Integer bankCardPro) {
        this.bankCardPro = bankCardPro;
    }

    public String getUnionBank() {
        return unionBank;
    }

    public void setUnionBank(String unionBank) {
        this.unionBank = unionBank;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getOpeningBank() {
        return openingBank;
    }

    public void setOpeningBank(String openingBank) {
        this.openingBank = openingBank;
    }

    @Override
    public String toString() {
        return "ShopBankCardVO{" +
                "shopBankCardId=" + shopBankCardId +
                ", bankName='" + bankName + '\'' +
                ", recipientName='" + recipientName + '\'' +
                ", cardNo='" + cardNo + '\'' +
                ", openingBank='" + openingBank + '\'' +
                ", isDefault=" + isDefault +
                ", isVerify=" + isVerify +
                ", shopId=" + shopId +
                ", bankCardPro=" + bankCardPro +
                ", unionBank='" + unionBank + '\'' +
                ", bankCardNo='" + bankCardNo + '\'' +
                '}';
    }
}
