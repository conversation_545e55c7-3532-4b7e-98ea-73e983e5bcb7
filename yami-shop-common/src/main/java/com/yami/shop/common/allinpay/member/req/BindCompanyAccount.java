package com.yami.shop.common.allinpay.member.req;

import java.io.Serial;
import java.io.Serializable;

/**
 * 企业会员绑定对公户
 *
 * <AUTHOR>
 */
public class BindCompanyAccount extends AbstractMemberReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    public static final String METHOD_NAME = "bindCompanyAccount";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }

    /**
     * 企业对公账户 AES加密
     */
    private String accountNo;
    /**
     * 开户银行名称
     */
    private String parentBankName;
    /**
     * 开户行地区代码 非必填
     */
    private String bankCityNo;
    /**
     * 开户支行名称 	如：“中国工商银行股份有限公司北京樱桃园支行”
     */
    private String bankName;
    /**
     * 支付行号 12位数字
     */
    private String unionBank;
    /**
     * 开户行所在省 开户行所在市必须同时上送 非必填
     */
    private String province;
    /**
     * 开户行所在市 开户行所在省必须同时上送 非必填
     */
    private String city;

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getParentBankName() {
        return parentBankName;
    }

    public void setParentBankName(String parentBankName) {
        this.parentBankName = parentBankName;
    }

    public String getBankCityNo() {
        return bankCityNo;
    }

    public void setBankCityNo(String bankCityNo) {
        this.bankCityNo = bankCityNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getUnionBank() {
        return unionBank;
    }

    public void setUnionBank(String unionBank) {
        this.unionBank = unionBank;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Override
    public String toString() {
        return "BindCompanyAccount{" +
                ", accountNo='" + accountNo + '\'' +
                ", parentBankName='" + parentBankName + '\'' +
                ", bankCityNo='" + bankCityNo + '\'' +
                ", bankName='" + bankName + '\'' +
                ", unionBank='" + unionBank + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                '}';
    }
}
