package com.yami.shop.common.wukongim.constant;

import com.yami.shop.common.config.Constant;
import com.yami.shop.common.enums.SysTypeEnum;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public class WuKongConstant {

    public static final String UNDERLINE = "_";

    public static final String DEFAULT = "*";

    /**
     * 机器人(系统)账号，用来做转接提醒
     */
    public static final String SYSTEM_UID = "-1_SYSTEM_UID_0";

    /**
     * 用户uid前缀 用户id(客服通用)
     */
    public static final String USER = SysTypeEnum.ORDINARY.value() + UNDERLINE;

    /**
     * 商家uid前缀 管理员id + _ + 店铺id + _ + 权限默认标识 *
     */
    public static final String SHOP = SysTypeEnum.MULTISHOP.value() + UNDERLINE;

    /**
     * 平台uid前缀 管理员id + _ + 店铺id + _ + 权限默认标识 *
     */
    public static final String SYS = SysTypeEnum.PLATFORM.value() + UNDERLINE;

    /**
     * 用户与商品/平台的频道前缀 频道格式为 CHANNEL_ + userId + _ + shopId
     * 用户与商家/平台通信
     */
    public static final String CHANNEL_PREFIX = "CHANNEL_";

    /**
     * 商家/平台内部的频道前缀 频道格式为 CHANNEL_INSIDE_ + sysType + _ + shopId
     * 商家或者平台内部的通信，目前仅用于窗口中客服下线通知
     */
    public static final String CHANNEL_INSIDE_PREFIX = "CHANNEL_INSIDE_";

    /**
     * channel_type 群聊类型 2
     */
    public static final Integer GROUP_CHAT = 2;

    /**
     * 用户状态更新回调
     */
    public static final String USER_STATUS_WEBHOOK= "user.onlinestatus";

    /**
     * 红点消除
     */
    public static final String UNREAD_CLEAR = "unreadClear";

    /**
     * 群成员信息有更新
     */
    public static final String MEMBER_UPDATE = "memberUpdate";

    /**
     * 商家上下线消息
     */
    public static final String ONLINE_UPDATE = "onlineUpdate";

    /**
     * 商家、平台退出登录消息
     */
    public static final String LOG_OUT = "logOut";

    /**
     * 商家状态消息
     */
    public static final String SHOP_STATUS = "shopStatus";

    /**
     * 用户注销消息
     */
    public static final String USER_DESTROY = "userDestroy";

    // -----直播-------

    /**
     * 直播频道前缀 频道格式为 LIVE_CHANNEL_ + roomId
     */
    public static final String LIVE_CHANNEL_PREFIX = "LIVE_CHANNEL_";

    /**
     * 直播主播uid前缀 频道格式为 LIVE_ANCHOR_ + roomId + _ + userId
     */
    public static final String LIVE_ANCHOR = "LIVE_ANCHOR_";

    /**
     * 直播用户uid前缀 频道格式为 LIVE_USER_ + roomId + _ + userId
     */
    public static final String LIVE_USER = "LIVE_USER_";

    /**
     * 商品状态异常
     */
    public static final String PROD_ERROR = "prodError";

    /**
     * 讲解状态变更
     */
    public static final String EXPLAIN_STATUS = "explainStatus";

    /**
     * 直播状态变更
     */
    public static final String LIVE_STATUS = "liveStatus";

    /**
     * 直播间用户数变更
     */
    public static final String LIVE_USER_COUNT = "liveUserCount";


    // -----接口信息-----

    /**
     * 将用户信息注册到悟空 IM，如果存在则更新
     */
    public static final String REGISTER_OR_LOGIN = "/user/token";

    /**
     * 查询一批用户的在线状态。
     */
    public static final String ONLINE_STATUS = "/user/onlinestatus";

    /**
     * 创建一个频道，如果系统中存在则更新
     */
    public static final String CHANNEL_CREATE = "/channel";

    /**
     * 删除一个频道
     */
    public static final String CHANNEL_DELETE = "/channel/delete";

    /**
     * 向一个已存在的频道内添加订阅者
     */
    public static final String SUBSCRIBER_ADD = "/channel/subscriber_add";

    /**
     * 向一个已存在的频道内移除订阅者
     */
    public static final String SUBSCRIBER_REMOVE = "/channel/subscriber_remove";

    /**
     * 服务端调用发送消息接口可以主要用来发送系统类的消息
     */
    public static final String MESSAGE_SEND = "/message/send";

    /**
     * 获取某个频道的消息列表
     */
    public static final String MESSAGE_SYNC = "/channel/messagesync";

    /**
     * 客户端离线后每次进来需要同步一次最近会话（包含离线的最新的消息）
     */
    public static final String CONVERSATION_SYNC = "/conversation/sync";

    /**
     * 最近会话的红点数清空
     */
    public static final String SET_UNREAD = "/conversations/setUnread";

    /**
     * 将用户的设备踢出登录,用户被禁用的时候使用
     */
    public static final String DEVICE_QUIT = "/user/device_quit";

    public static final Long getSysUserId(String uid) {
        return Long.parseLong(uid.substring(WuKongConstant.SYS.length()).split(WuKongConstant.UNDERLINE)[0]);
    }

    public static final Long getEmployeeId(String uid) {
        return Long.parseLong(uid.substring(WuKongConstant.SHOP.length()).split(WuKongConstant.UNDERLINE)[0]);
    }

    public static final Long getShopIdByUid(String uid) {
        return Long.parseLong(uid.substring(WuKongConstant.SHOP.length()).split(WuKongConstant.UNDERLINE)[1]);
    }

    public static final String getShopUid(Long employeeId, Long shopId) {
        return WuKongConstant.SHOP + employeeId + WuKongConstant.UNDERLINE + shopId + WuKongConstant.UNDERLINE + WuKongConstant.DEFAULT;
    }

    public static final String getSysUid(Long sysUserId) {
        return WuKongConstant.SYS + sysUserId + WuKongConstant.UNDERLINE + Constant.PLATFORM_SHOP_ID + WuKongConstant.UNDERLINE + WuKongConstant.DEFAULT;
    }

    /**
     * 获取用户与商品/平台的频道前缀 频道格式为 CHANNEL_ + userId + _ + shopId
     * 用户与商家/平台通信
     */
    public static final String getChannelId(String userId, Long shopId) {
        return WuKongConstant.CHANNEL_PREFIX + userId + WuKongConstant.UNDERLINE + shopId;
    }

    /**
     * 获取商家/平台内部的频道前缀 频道格式为 CHANNEL_INSIDE_ + sysType + _ + shopId
     * 商家或者平台内部的通信，目前仅用于窗口中客服下线通知
     */
    public static final String getAdminChannelId(Long shopId) {
        String sysType = Objects.equals(shopId, Constant.PLATFORM_SHOP_ID) ? WuKongConstant.SYS : WuKongConstant.SHOP;
        return CHANNEL_INSIDE_PREFIX + sysType + shopId;
    }
    public static final Long getShopId(String channelId) {
        String[] split = channelId.substring(CHANNEL_PREFIX.length()).split(WuKongConstant.UNDERLINE);
        return Long.parseLong(split[1]);
    }

    public static final String getUserId(String channelId) {
        String[] split = channelId.substring(CHANNEL_PREFIX.length()).split(WuKongConstant.UNDERLINE);
        return split[0];
    }

    public static final String getUid(String channelId) {
        return WuKongConstant.USER + WuKongConstant.getUserId(channelId);
    }

    public static final String getLiveAnchorUid(String userId, Long roomId) {
        return WuKongConstant.LIVE_ANCHOR + roomId + WuKongConstant.UNDERLINE + userId;
    }

    public static final String getLiveUserUid(String userId, Long roomId) {
        return WuKongConstant.LIVE_USER + roomId + WuKongConstant.UNDERLINE + userId;
    }
}
