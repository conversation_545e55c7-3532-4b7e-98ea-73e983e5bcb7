package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 购物车中选中的满减活动项信息
 * <AUTHOR>
 */
@Data
public class ChooseDiscountItemDto implements Serializable {

    @Schema(description = "满减满折优惠id" )
    private Long discountId;

    @Schema(description = "优惠规则(0:满钱减钱 1:满件减钱 2:满钱打折 3:满件打折)" )
    private Integer discountRule;

    @Schema(description = "优惠项id" )
    private Long discountItemId;

    @Schema(description = "所需需要金额" )
    private Double needAmount;

    @Schema(description = "减免类型 0按满足最高层级减一次 1每满一次减一次" )
    private Integer discountType;

    @Schema(description = "参与满减满折优惠的商品数量" )
    private int prodCount = 0;

    @Schema(description = "参与满减满折优惠的商品金额" )
    private double prodsPrice = 0.00;

    @Schema(description = "优惠（元/折）" )
    private Double discount;

    @Schema(description = "参与满减满折优惠的金额" )
    private Double reduceAmount = 0.0;

}
