package com.yami.shop.distribution.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yami.shop.distribution.common.model.DistributionUserBan;
import com.yami.shop.distribution.common.param.DistributionParam;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface DistributionUserBanMapper extends BaseMapper<DistributionUserBan> {
    /**
     * 分销员封禁列表
     * @param page 分页参数
     * @param shopId 店铺id
     * @param distributionParam 分销查询参数
     * @param distributionUserBan 查询参数
     * @return 封禁列表
     */
    IPage<DistributionUserBan> distributionUserBanPage(Page page, @Param("shopId") Long shopId,
                                                       @Param("distributionParam")DistributionParam distributionParam,
                                                       @Param("distributionUserBan") DistributionUserBan distributionUserBan);
}
