package com.yami.shop.common.allinpay.member.resp;

import cn.hutool.core.util.StrUtil;
import com.yami.shop.common.allinpay.constant.AllinpayCardType;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class BindCard implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 绑定银行卡号
     */
    private String bankCardNo;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 绑定时间，yyyy-MM-ddHH:mm:ss
     */
    private String bindTime;

    /**
     * 银行卡类型
     */
    private Integer cardType;

    /**
     * 绑定状态
     * 1 已绑定 2已解除
     */
    private Integer bindState;

    /**
     * 银行预留手机号码（仅四要素绑定的银行卡返回）
     */
    private String phone;

    /**
     * 银行卡/账户属性 0个人银行卡 1企业对公账户
     */
    private Integer bankCardPro;

    /**
     * 若为企业对公户返回0
     */
    private Integer bindMethod;

    private String cardTypeString;

    /**
     * 支付行号
     */
    private String unionBank;

    /**
     * 开户支行名称
     */
    private String branchBankName;

    /**
     * 企业对公账户
     */
    private String accountNo;

    /**
     * 返回解密字符串，也就是真正银行卡号码
     *
     * @return 真正银行卡号码
     */
    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBindTime() {
        return bindTime;
    }

    public void setBindTime(String bindTime) {
        this.bindTime = bindTime;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public Integer getBindState() {
        return bindState;
    }

    public void setBindState(Integer bindState) {
        this.bindState = bindState;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getBankCardPro() {
        return bankCardPro;
    }

    public void setBankCardPro(Integer bankCardPro) {
        this.bankCardPro = bankCardPro;
    }

    public Integer getBindMethod() {
        return bindMethod;
    }

    public void setBindMethod(Integer bindMethod) {
        this.bindMethod = bindMethod;
    }

    public String getUnionBank() {
        return unionBank;
    }

    public void setUnionBank(String unionBank) {
        this.unionBank = unionBank;
    }

    public String getCardTypeString() {
        if (StrUtil.isNotBlank(this.cardTypeString)) {
            return this.cardTypeString;
        }
        return AllinpayCardType.getDescByCode(cardType);
    }

    public void setCardTypeString(String cardTypeString) {
        this.cardTypeString = cardTypeString;

    }

    public String getBranchBankName() {
        return branchBankName;
    }

    public void setBranchBankName(String branchBankName) {
        this.branchBankName = branchBankName;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }
}
