package com.yami.shop.distribution.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  分销提现状态
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistributionWithdrawCashStateEnum {
    /**
     * 提现中
     */
    APPLY(0, "提现中"),
    /**
     * 提现成功
     */
    CASH_SUCCESS(1, "提现成功"),
    /**
     * 提现失败
     */
    CASH_REJECT(2,"提现失败"),
    /**
     * 提现失败
     */
    CASH_FAIL(-1, "提现失败");

    private final int value;
    private final String desc;
}
