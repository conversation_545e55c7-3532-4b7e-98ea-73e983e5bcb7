server:
  port: 8114
spring:
  datasource:
    url: jdbc:mysql://${MYSQL_HOST:mall4j-mysql}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:yami_bbc}?allowMultiQueries=true&rewriteBatchedStatements=true&useSSL=false&useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8&nullCatalogMeansCurrent=true
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:root}
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 0
      maximum-pool-size: 20
      idle-timeout: 25000
      auto-commit: true
      connection-test-query: select 1
  data:
    redis:
      host: ${REDIS_HOST:}
      port: ${REDIS_PORT:}
      database: ${REDIS_DATABASE:0}
      password: ${REDIS_PASSWORD:}
  # es默认关闭，开启es的时候，请把spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration 这两行删除
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration
  elasticsearch:
    enable: ${ELASTIC_ENABLE:false}
    uris: ${ELASTIC_ADDRESS:http://mall4j-elasticsearch:9200}
    username: ${ELASTIC_USERNAME:}
    password: ${ELASTIC_PASSWORD:}
  mail:
    # 企微邮箱为：smtp.exmail.qq.com，qq邮箱为：smtp.qq.com，163邮箱为：smtp.163.com，其他的请自行查找相关资料
    host: ${MAIL_HOST:smtp.exmail.qq.com}
    # 进行发送通知的邮箱账户名
    username: ${MAIL_USERNAME:}
    # 进行发送通知的邮箱密码，这个不是密码，而是自己邮箱-账户-开启POP3/SMTP时的客户端授权码
    password: ${MAIL_PASSWORD:}

# 这个redis和上面的redis不一样，是用来存库存的
redis:
  aof:
    database: ${REDIS_AOF_DATABASE:0}
    redis-addr: ${REDIS_AOF_ADDR:***************:6389}
    password: ${REDIS_AOF_PASSWORD:}

logging:
  config: classpath:logback/logback-docker.xml
  # 接受异常通知的邮箱
  log-error-email: ${LOG_ERROR_EMAIL:}
  # 当前环境，比如测试环境，生产环境
  log-env: ${LOG_ENV:}

xxl-job:
  accessToken: ${XXL_JOB_ACCESS_TOKEN:mall4j_token}
  logPath: ${XXL_JOB_LOG_PATH:/data/applogs/xxl-job/jobhandler}
  local:
    ip: ${XXL_JOB_LOCAL_IP:}
  admin:
    addresses: ${XXL_JOB_ADDRESS:http://mall4j-job:8080/xxl-job-admin}
wukongim:
  address: ${WUKONGIM_ADDRESS:http://mall4j-wukongim:5001}
  token: ${WUKONGIM_TOKEN:}

# 短信配置
sms:
  # 标注从yml读取配置
  config-type: yaml
  restricted: true
  account-max: 10
  minute-max: 1
  blends:
    local:
      supplier: ${SMS_SUPPLIER:alibaba} #默认阿里云,参考详情见 SmsTypeEnum
      access-key-id: ${SMS_ACCESS_KEY_ID:必填短信服务accessKeyId}
      access-key-secret: ${SMS_ACCESS_KEY_SECRET:必填短信accessKeySecret}
      signature: ${SMS_SIGNATURE:必填短信签名signName}
      request-url: ${SMS_REQUEST_URL:} #APP接入地址 (亿美软通)
      sender: ${SMS_SENDER:} #国内短信签名通道号 (华为云)
      status-call-back: ${SMS_STATUS_CALL_BACK:} #短信状态报告接收地 (华为云)
      url: ${SMS_URL:} #APP接入地址 (华为云)
      callback-url: ${SMS_CALLBACK_URL:} #短信发送后将向这个地址推送(运营商返回的)发送报告 (云片短信)
      mch-id: ${SMS_MCH_ID:} #企业ID （联麓短信）
      app-key: ${SMS_APP_KEY:} #appKey （联麓短信）
      app-id: ${SMS_APP_ID:} #appId （联麓短信）
      signature-id: ${SMS_SIGNATURE_ID:} #签名ID （七牛云短信）

management:
  endpoint:
    health:
      probes:
        enabled: true
  endpoints:
    web:
      exposure:
        include: health,startup
