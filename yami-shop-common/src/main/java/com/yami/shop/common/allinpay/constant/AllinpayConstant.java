package com.yami.shop.common.allinpay.constant;

import java.time.LocalTime;

/**
 * <AUTHOR>
 */
public class AllinpayConstant {
    public static final String SHOP = "Mall4jBBCShop";
    public static final String USER = "Mall4jBBCUser";

    public static final Integer SHOP_LENGTH = SHOP.length();

    /**
     * 暂时定死行业代码为 1910:其他
     */
    public static final String INDUSTRY_CODE = "1910";
    public static final String INDUSTRY_NAME = "其他";

    /**
     * 回调成功响应
     */
    public static final String NOTICE_SUCCESS = "success";

    public static final String SET_COMPANY_INFO_RESULT = "当前企业用户已审核通过，无法设置企业信息。";

    /**
     * 通联企业信息审核成功
     */
    public static final String ALLINPAY_AUDIT_SUCCESS = "审核成功";

    public static final String ID_CARD_UPDATE_SUCCESS = "影印件更新成功";

    /**
     * 通联无感注册默认绑定手机号
     */
    public static final String DEFAULT_BIND_PHONE_NUMBER = "88888888888";

    /**
     * 允许提现起始时间
     */
    public static final LocalTime ALLOW_WITHDRAW_START_TIME = LocalTime.of(7, 0);
    /**
     * 允许提现结束时间
     */
    public static final LocalTime ALLOW_WITHDRAW_END_TIME = LocalTime.of(20, 0);

    /**
     * 支付宝app支付url模板
     */
    public static final String ALI_APP_PAY_URL_TEMPLATE = "alipays://platformapi/startapp?appId=%s&page=%s&thirdPartSchema=%s&query=%s";
    public static final String ALI_DEFAULT_APP_ID = "2021001104615521";
    public static final String ALI_DEFAULT_PAGE = "pages/orderDetail/orderDetail";
}
