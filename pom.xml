<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.yami.shop</groupId>
    <artifactId>yami-shop</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>yami-shop-sys</module>
        <module>yami-shop-common</module>
        <module>yami-shop-api</module>
        <module>yami-shop-bean</module>
        <module>yami-shop-service</module>
        <module>yami-shop-security</module>
        <module>yami-shop-discount</module>
        <module>yami-shop-coupon</module>
        <module>yami-shop-mp</module>
        <module>yami-shop-distribution</module>
        <module>yami-shop-groupbuy</module>
        <module>yami-shop-seckill</module>
        <module>yami-shop-user</module>
        <module>yami-shop-multishop</module>
        <module>yami-shop-platform</module>
        <module>yami-shop-delivery</module>
        <module>yami-shop-live</module>
        <module>yami-shop-search</module>
        <module>yami-shop-combo</module>
<!--        <module>system-test</module>-->
    </modules>

    <properties>
        <yami.shop.version>0.0.1-SNAPSHOT</yami.shop.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven-compiler-plugin.version>3.12.0</maven-compiler-plugin.version>
        <maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
        <spring-boot.version>3.2.5</spring-boot.version>
        <java.version>17</java.version>
        <guava.version>33.0.0-jre</guava.version>
        <hutool.version>5.8.27</hutool.version>
        <poi.version>5.2.5</poi.version>
        <!--poi的这个依赖有漏洞，固定下这个依赖版本，poi升级了的话，可以适当移除-->
        <commons-compress.version>1.26.1</commons-compress.version>
        <weixin.version>4.6.0</weixin.version>
        <alipay.version>4.39.27.ALL</alipay.version>
        <fastjson.version>1.2.83</fastjson.version>
        <mybatis-plus.version>3.5.6</mybatis-plus.version>
        <redisson.version>3.25.2</redisson.version>
        <paypal.version>1.14.0</paypal.version>
        <paypal.v2.version>1.0.5</paypal.v2.version>
        <elasticsearch.version>7.17.21</elasticsearch.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <xxl-job.version>2.4.1</xxl-job.version>
        <knife4j.version>4.4.0</knife4j.version>
        <ip2region.version>2.6.6</ip2region.version>
        <captcha.version>1.3.0</captcha.version>
        <satoken.version>1.37.0</satoken.version>
        <aws-s3.version>2.21.5</aws-s3.version>
        <ik-analyzer.version>5.0.1</ik-analyzer.version>
        <sms4j.version>3.2.1</sms4j.version>
        <bcprov-jdk18on.version>1.78</bcprov-jdk18on.version>
        <sensitive-word.version>0.16.1</sensitive-word.version>
        <!--        <jdcloud.version>1.3.3</jdcloud.version>-->
    </properties>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-dependencies</artifactId>
                <version>${knife4j.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-security</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${weixin.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>javax.servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk15on</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-miniapp</artifactId>
                <version>${weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-mp</artifactId>
                <version>${weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-sdk-java</artifactId>
                <version>${alipay.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.bouncycastle</groupId>
                        <artifactId>bcprov-jdk15on</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>dom4j</groupId>
                        <artifactId>dom4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bcprov-jdk18on.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.paypal.sdk</groupId>
                <artifactId>rest-api-sdk</artifactId>
                <version>${paypal.version}</version>
            </dependency>
            <dependency>
                <groupId>com.paypal.sdk</groupId>
                <artifactId>checkout-sdk</artifactId>
                <version>${paypal.v2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-crypto</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-http</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-extra</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-json</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-poi</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!-- 使用redisson集成分布式锁等 -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>
            <dependency>
                <groupId>com.anji-plus</groupId>
                <artifactId>captcha</artifactId>
                <version>${captcha.version}</version>
            </dependency>
            <!-- Sa-Token 权限认证，在线文档：https://sa-token.cc -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>
            <!-- Sa-Token 整合 Redis （使用 jackson 序列化方式） -->
            <!-- https://mvnrepository.com/artifact/cn.dev33/sa-token-redis-jackson -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/software.amazon.awssdk/s3 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${aws-s3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.truenewx</groupId>
                <artifactId>ik-analyzer-core</artifactId>
                <version>${ik-analyzer.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-spring-boot-starter</artifactId>
                <version>${sms4j.version}</version>
            </dependency>
            <!-- 短信配置的京东云依赖，因为京东配置经常变更，所以不建议使用 -->
            <!--            <dependency>-->
            <!--                <groupId>com.jdcloud.sdk</groupId>-->
            <!--                <artifactId>sms</artifactId>-->
            <!--                <version>${jdcloud.version}</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.github.houbb</groupId>
                <artifactId>sensitive-word</artifactId>
                <version>${sensitive-word.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <parameters>true</parameters>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resources-plugin.version}</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xdb</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <layers>
                        <enabled>true</enabled>
                    </layers>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>aliyun</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
