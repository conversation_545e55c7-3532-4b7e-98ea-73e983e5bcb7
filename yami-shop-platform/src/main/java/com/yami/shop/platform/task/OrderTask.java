package com.yami.shop.platform.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yami.shop.bean.app.param.ProdCommParam;
import com.yami.shop.bean.bo.OrderChangeShopWalletAmountBO;
import com.yami.shop.bean.bo.PayInfoResultBO;
import com.yami.shop.bean.dto.OrderRefundDto;
import com.yami.shop.bean.enums.*;
import com.yami.shop.bean.event.RemoveSeckillOrderCacheEvent;
import com.yami.shop.bean.model.Order;
import com.yami.shop.bean.model.OrderItem;
import com.yami.shop.bean.model.PayInfo;
import com.yami.shop.bean.vo.OrderAndPayInfoVO;
import com.yami.shop.common.allinpay.constant.PaySysType;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.enums.PayType;
import com.yami.shop.manager.impl.PayManager;
import com.yami.shop.service.*;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class OrderTask {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private final OrderService orderService;
    private final OrderRefundService orderRefundService;
    private final OrderItemService orderItemService;
    private final NotifyTemplateService notifyTemplateService;
    private final ProductService productService;
    private final ProdCommService prodCommService;
    private final SkuService skuService;
    private final PayInfoService payInfoService;
    private final PayManager payManager;
    private final NotifyLogService notifyLogService;
    private final ApplicationContext applicationContext;

    @XxlJob("cancelOrder")
    public void cancelOrder() {

        logger.info("解锁创建失败订单的库存。。。");
        orderService.unLockFailOrderStock();

        Date now = new Date();
        logger.info("取消超时未支付订单。。。");
        // 获取30分钟之前未支付的订单
        List<Order> orders = orderService.listUnRefundOrderAndOrderItems(OrderStatus.UNPAY.value(), DateUtil.offsetMinute(now, -30));
        if (CollectionUtil.isEmpty(orders)) {
            return;
        }
        List<Order> cancelOrderList = this.checkOrders(orders);
        orderService.cancelOrders(cancelOrderList);
        // 移除缓存
        this.removeCache(cancelOrderList);

    }

    /**
     * 确认收货15天后执行订单结算
     */
    @XxlJob("orderCommissionSettlement")
    public void orderCommissionSettlement() {
        logger.info("开始执行订单结算任务》》》》》》》》》》》》》》》》》》》》》");
        Date now = new Date();
        // 确认收货15天的订单，进行结算(更新时间为15天前的订单)
        List<Order> orders = orderService.listPendingSettlementOrders(OrderStatus.SUCCESS.value(), DateUtil.beginOfDay(DateUtil.offsetDay(now, -Constant.DISTRIBUTION_SETTLEMENT_TIME)));
        if (CollectionUtil.isEmpty(orders)) {
            return;
        }
        List<com.yami.shop.bean.model.Order> normalOrderList = orders.stream().filter(order -> Objects.equals(order.getPaySysType(), PaySysType.DEFAULT.value())).toList();
        List<com.yami.shop.bean.model.Order> allinpayOrderList = orders.stream().filter(order -> Objects.equals(order.getPaySysType(), PaySysType.ALLINPAY.value())).toList();
        if(CollectionUtil.isNotEmpty(normalOrderList)) {
            orderService.orderCommissionSettlement(normalOrderList);
            // 移除缓存
            this.removeCache(normalOrderList);
        }
        if (CollUtil.isNotEmpty(allinpayOrderList)) {
            for (com.yami.shop.bean.model.Order order : allinpayOrderList) {
                OrderChangeShopWalletAmountBO orderChangeShopWalletAmountBO = new OrderChangeShopWalletAmountBO();
                orderChangeShopWalletAmountBO.setShopId(order.getShopId());
                orderChangeShopWalletAmountBO.setOrderNumber(order.getOrderNumber());
                try {
                    orderService.doAllinPaySettlement(orderChangeShopWalletAmountBO);
                }catch (Exception e) {
                    logger.info("通联订单结算失败，订单号:{}，异常信息:{}", order.getOrderNumber(), e.getMessage());
                }

            }
        }
        logger.info("结束执行订单结算任务》》》》》》》》》》》》》》》》》》》》》");
    }

    /**
     * 订单催付提醒,每1分钟执行发送一次订单未支付的提醒
     */
    @XxlJob("pressPayOrder")
    public void pressPayOrder() {
        Date now = new Date();
        logger.info("执行订单催付提醒");
        // 获取15分钟之前未支付的订单
        List<Order> orders = orderService.listUnRefundOrderAndOrderItems(OrderStatus.UNPAY.value(), DateUtil.offsetMinute(now, -15));
        if (CollectionUtil.isEmpty(orders)) {
            return;
        }
        // 消息推送-订单催付提醒
        // 对相同用户id进行去重
        orders = orders.stream().filter(distinctByKey(Order::getUserId)).collect(Collectors.toList());
        for (Order order : orders) {
            Integer num = notifyLogService.countMsgNum(order.getOrderNumber());
            if (num < 1) {
                //没发过催付消息才会发送
                notifyTemplateService.sendNotifyOfDelivery(order, null, SendType.PRESS_PAY);
            }
        }
    }

    /**
     * 待发货订单发货超时退款
     * 普通待发货订单发货超时退款
     *  ① 7天内订单都还未发货，则订单发货超时自动取消；
     *  ② 到店自提和7天内部分发货了的订单不用限制，订单状态正常跳转不改变；
     * 预售待发货订单发货超时退款
     *  ① 要在规定发货时间的7天内进行发货，否则也会超时退款
     */
    @XxlJob("handleDeliveryTimeOutOrder")
    public void handleDeliveryTimeOutOrder() {
        Date now = new Date();
        logger.info("执行待发货订单发货超时退款");
        // 获取15分钟之前未支付的订单
        List<OrderAndPayInfoVO> orders =  orderService.listDeliveryOutTimeOrder(DateUtil.offsetDay(now, -7),DateUtil.offsetDay(now, -365));
        for (OrderAndPayInfoVO order : orders) {
            try {
                List<OrderRefundDto> orderRefundDtos = orderRefundService.doRefundByTimeOut(order, BuyerReasonType.DELIVERY_TIME_OUT.getCn());
                if(CollectionUtil.isEmpty(orderRefundDtos)){
                    continue;
                }
                for (OrderRefundDto orderRefundDto : orderRefundDtos) {
                    orderRefundService.handleRefundStock(orderRefundDto.getStockKeys());
                }
            } catch (Exception e) {
                logger.error(e.getMessage(),e);
                logger.error("待发货订单发货超时退款失败,订单号{}", order.getOrderNumber());
            }

        }
    }

    /**
     * 虚拟商品到期未使用自动退款(确认收货7天内)
     * 单次核销未使用,有效期到期自动退；
     * 多次核销一次都未使用,到期自动退
     */
    @XxlJob("handleExpireUnusedVirtualOrder")
    public void handleExpireUnusedVirtualOrder() {
        logger.info("有效期到期未使用的虚拟商品订单,自动退款(确认收货7天内)");
        Date now = new Date();
        List<OrderAndPayInfoVO> orders = orderService.listUnusedVirtualOrderByTimeOut(DateUtil.offsetDay(now, -7));
        for (OrderAndPayInfoVO order : orders) {
            if(CollectionUtil.isNotEmpty(order.getOrderVirtualVerifyLogList())){
                continue;
            }
            try {
                List<OrderRefundDto> orderRefundDtos = orderRefundService.doRefundByTimeOut(order, BuyerReasonType.VIRTUAL_TIME_OUT.getCn());
                if(CollectionUtil.isEmpty(orderRefundDtos)){
                    continue;
                }
                for (OrderRefundDto orderRefundDto : orderRefundDtos) {
                    orderRefundService.handleRefundStock(orderRefundDto.getStockKeys());
                }
            } catch (Exception e) {
                logger.info("虚拟商品订单:{}过期自动退款失败，原因为:{}", order.getOrderNumber(), e.getMessage());
            }
        }
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    /**
     * 确认收货
     */
    @XxlJob("confirmOrder")
    public void confirmOrder() {
        Date now = new Date();
        logger.info("系统自动确认收货订单。。。");
        // 获取15天之前等待确认收货的订单
        List<Order> orders = orderService.listUnRefundOrderAndOrderItems(OrderStatus.CONSIGNMENT.value(), DateUtil.offsetDay(now, -15));
        // 获取核销时间超出当前时间的，已支付但未完成的虚拟订单
        List<Order> overdueOrders = orderService.list(new LambdaQueryWrapper<Order>()
                .eq(Order::getOrderMold, 1)
                .eq(Order::getIsPayed, 1)
                .notIn(Order::getStatus, OrderStatus.SUCCESS.value(), OrderStatus.CLOSE.value())
                .lt(Order::getWriteOffEnd, new Date()));
        orders.addAll(overdueOrders);
        if (CollectionUtil.isEmpty(orders)) {
            return;
        }
        orderService.receiptOrder(orders);
        // 移除缓存
        this.removeCache(orders);
    }

    /**
     * 自动商品好评（确认收货30天，未进行评论，自动默认5星好评）
     */
    @XxlJob("autoProdGoodComm")
    public void autoProdGoodComm() {
        logger.info("确认收货30天，自动好评...");
        Date now = new Date();
        DateTime endTime = DateUtil.offsetDay(now, -30);
        List<OrderItem> orderItems = orderItemService.listUnCommOrderItem(endTime);
        if (CollectionUtil.isEmpty(orderItems)) {
            return;
        }
        List<ProdCommParam> prodCommParams = new ArrayList<>(orderItems.size());
        for (int i=0; i<orderItems.size(); i++) {
            ProdCommParam prodCommParam = new ProdCommParam();
            prodCommParam.setScore(5);
            prodCommParam.setIsAnonymous(1);
            prodCommParam.setContent("系统默认好评");
            prodCommParam.setRecTime(now);
            prodCommParams.add(prodCommParam);
        }
        prodCommService.saveBatchComm(orderItems, prodCommParams);
    }

    /**
     * 查询订单，去除已支付的订单
     *
     * @param orders
     */
    private List<Order> checkOrders(List<Order> orders) {
        // 收集未支付的订单
        List<Order> orderList = new ArrayList<>();
        for (Order order : orders) {
            // 获取支付过的信息
            List<PayInfo> payInfoList = payInfoService.list(new LambdaQueryWrapper<PayInfo>().like(PayInfo::getOrderNumbers, order.getOrderNumber()));
            if(CollectionUtil.isEmpty(payInfoList)){
                orderList.add(order);
                continue;
            }
            boolean unPay = true;
            for (PayInfo payInfo : payInfoList) {
                String bizPayNo = null;
                if (Objects.nonNull(payInfo.getPayNo())) {
                    bizPayNo = payInfo.getBizPayNo();
                }
                PayInfoResultBO payInfoResultBO = payManager.getPayInfo(PayType.instance(payInfo.getPayType()), payInfo.getPayNo(), payInfo.getPaySysType(),bizPayNo);
                // 如果之前是已经支付成功过的，直接更新订单
                if (payInfoResultBO.getIsPaySuccess()) {
                    // 根据内部订单号更新order settlement
                    if (Objects.equals(payInfo.getPayStatus(), PayStatus.UNPAY.value())) {
                        payInfoService.noticeOrder(payInfoResultBO, payInfo, payInfoResultBO.getPaySysType());
                        if (Objects.equals(payInfo.getOrderType(), OrderType.SECKILL.value())) {
                            applicationContext.publishEvent(new RemoveSeckillOrderCacheEvent(payInfo.getOrderNumbers(), payInfo.getUserId()));
                        }
                    }
                    unPay = false;
                    break;
                }
            }
            // 没有支付过就直接取消订单
            if(unPay){
                orderList.add(order);
            }
        }
        return orderList;
    }

    /**
     * 移除缓存
     */
    private void removeCache(List<Order> orders) {
        for (Order order : orders) {
            List<OrderItem> orderItems = order.getOrderItems();
            for (OrderItem orderItem : orderItems) {
                productService.removeProdCacheByProdId(orderItem.getProdId());
                skuService.removeSkuCacheBySkuId(orderItem.getSkuId(), orderItem.getProdId());
            }
        }
    }
}
