package com.yami.shop.common.allinpay.order.req;

/**
 * 查询账户收支明细
 *
 * <AUTHOR>
 */
public class QueryInExpDetail extends AbstractOrderReq {

    /**
     * 账户集编号
     */
    private String accountSetNo;
    /**
     * 开始日期
     */
    private String dateStart;
    /**
     * 结束日期
     */
    private String dateEnd;
    /**
     * 起始位置 例如:11-100  startPosition=11
     */
    private Integer startPosition;
    /**
     * 查询条数 例如:11-100  queryNum=90
     */
    private Integer queryNum;
    /**
     * 金额类型: 1:余额 2：未结算余额 3：物业费 4：停车费 5:商城收益
     */
    private Integer amountType;


    /**
     * 方法名称
     */
    public static final String METHOD_NAME = "queryInExpDetail";

    @Override
    public String getMethod() {
        return METHOD_NAME;
    }


    public QueryInExpDetail() {
    }

    public QueryInExpDetail(String bizUserId, String accountSetNo,
                            Integer startPosition, Integer queryNum) {
        this.bizUserId = bizUserId;
        this.accountSetNo = accountSetNo;
        this.startPosition = startPosition;
        this.queryNum = queryNum;
    }

    public String getAccountSetNo() {
        return accountSetNo;
    }

    public void setAccountSetNo(String accountSetNo) {
        this.accountSetNo = accountSetNo;
    }

    public String getDateStart() {
        return dateStart;
    }

    public void setDateStart(String dateStart) {
        this.dateStart = dateStart;
    }

    public String getDateEnd() {
        return dateEnd;
    }

    public void setDateEnd(String dateEnd) {
        this.dateEnd = dateEnd;
    }

    public Integer getStartPosition() {
        return startPosition;
    }

    public void setStartPosition(Integer startPosition) {
        this.startPosition = startPosition;
    }

    public Integer getQueryNum() {
        return queryNum;
    }

    public void setQueryNum(Integer queryNum) {
        this.queryNum = queryNum;
    }

    public Integer getAmountType() {
        return amountType;
    }

    public void setAmountType(Integer amountType) {
        this.amountType = amountType;
    }

}
