package com.yami.shop.bean.vo.search;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yami.shop.bean.vo.SameCityVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店信息
 * @since 2024/6/21 11:34
 */
@Data
public class StationSearchVO {

    @Schema(description = "门店id")
    private Long stationId;

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "门店名称")
    private String stationName;

    @Schema(description = "门店状态")
    private Integer status;

    @Schema(description = "时间数据")
    private String timeDate;

    @Schema(description = "库存模式 1共享总部库存 2独立销售库存")
    private Integer stockMode;

    @Schema(description = "自提门店用途是否支持自提（0：不支持，1：支持）")
    private Integer selfPickup;

    @Schema(description = "自提门店用途是否支持同城配送（0：不支持，1：支持）")
    private Integer sameCityDelivery;

    @TableField(exist = false)
    private SameCityVO sameCityVO;
}
