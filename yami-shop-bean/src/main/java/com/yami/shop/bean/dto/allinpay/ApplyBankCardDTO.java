package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class ApplyBankCardDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "银行卡号")
    private String cardNo;

    @Schema(description = "银行预留手机号")
    private String phone;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "绑卡方式(具体看:https://cloud.allinpay.com/ts-cloud-dev-web/#/apiCenter/index?params=y&key=300), 默认7")
    private Integer cardCheck;

    @Schema(description = "身份证号")
    private String identityNo;

    @Schema(description = "有效期 月年 例如0321")
    private String validate;

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCardCheck() {
        return cardCheck;
    }

    public void setCardCheck(Integer cardCheck) {
        this.cardCheck = cardCheck;
    }

    public String getIdentityNo() {
        return identityNo;
    }

    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo;
    }

    public String getValidate() {
        return validate;
    }

    public void setValidate(String validate) {
        this.validate = validate;
    }

    @Override
    public String toString() {
        return "ApplyBankCardDTO{" +
                "cardNo='" + cardNo + '\'' +
                ", phone='" + phone + '\'' +
                ", name='" + name + '\'' +
                ", cardCheck=" + cardCheck +
                ", identityNo='" + identityNo + '\'' +
                ", validate='" + validate + '\'' +
                '}';
    }
}
