
package com.yami.shop.common.allinpay.bean;

import com.yami.shop.common.allinpay.util.OpenUtils;

import java.io.Serializable;
/**
 * <AUTHOR>
 */
public class OpenConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    private String url;
    private String appId;
    private String secretKey;
    private String certPath;
    private String certPwd;
    private String tlCertPath;
    private String format;
    private String charset;
    private String signType;
    private String version;
    private String notifyUrl;

    public OpenConfig(String url, String appId, String secretKey, String certPath, String certPwd, String tlCertPath) {
        this(url, appId, secretKey, certPath, certPwd, tlCertPath, "SHA256WithRSA");
    }

    public OpenConfig(String url, String appId, String secretKey, String certPath, String certPwd, String tlCertPath, String signType) {
        this.format = "JSON";
        this.charset = "utf-8";
        this.version = "1.0";
        this.url = url;
        this.appId = appId;
        this.secretKey = secretKey;
        this.certPath = certPath;
        this.certPwd = certPwd;
        this.tlCertPath = tlCertPath;
        this.signType = signType;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getAppId() {
        return this.appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSecretKey() {
        return this.secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getCertPath() {
        return this.certPath;
    }

    public void setCertPath(String certPath) {
        this.certPath = certPath;
    }

    public String getCertPwd() {
        return this.certPwd;
    }

    public void setCertPwd(String certPwd) {
        this.certPwd = certPwd;
    }

    public String getTlCertPath() {
        return this.tlCertPath;
    }

    public void setTlCertPath(String tlCertPath) {
        this.tlCertPath = tlCertPath;
    }

    public String getFormat() {
        return this.format;
    }

    public String getCharset() {
        return this.charset;
    }

    public String getSignType() {
        return this.signType;
    }

    public String getVersion() {
        return this.version;
    }

    public String getNotifyUrl() {
        return this.notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public OpenConfig() {
        this.format = "JSON";
        this.charset = "utf-8";
        this.signType = "SHA256WithRSA";
        this.version = "1.0";
    }

    public void validate() {
        OpenUtils.assertNotNull(this.url, "property url must be configured");
        OpenUtils.assertNotNull(this.appId, "property appId must be configured");
        OpenUtils.assertNotNull(this.secretKey, "property secretKey must be configured");
        OpenUtils.assertNotNull(this.certPath, "property certPath must be configured");
        OpenUtils.assertNotNull(this.certPwd, "property certPwd must be configured");
        OpenUtils.assertNotNull(this.tlCertPath, "property tlCertPath must be configured");
    }
}
