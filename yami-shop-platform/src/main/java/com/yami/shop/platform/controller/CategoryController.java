package com.yami.shop.platform.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.app.dto.CategoryDto;
import com.yami.shop.bean.dto.CategoryShopDTO;
import com.yami.shop.bean.enums.EsOperationType;
import com.yami.shop.bean.enums.SigningStatus;
import com.yami.shop.bean.event.EsProductUpdateEvent;
import com.yami.shop.bean.model.Category;
import com.yami.shop.bean.model.CategoryShop;
import com.yami.shop.bean.model.Product;
import com.yami.shop.bean.vo.CategoryShopVO;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.platform.config.PlatformConstant;
import com.yami.shop.service.CategoryExcelService;
import com.yami.shop.service.CategoryService;
import com.yami.shop.service.CategoryShopService;
import com.yami.shop.service.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 分类管理
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/prod/category")
@Tag(name = "分类相关接口")
@AllArgsConstructor
public class CategoryController {

    private final CategoryService categoryService;
    private final ProductService productService;
    private final CategoryShopService categoryShopService;

    private final ApplicationEventPublisher eventPublisher;

    private final CategoryExcelService categoryExcelService;

    @GetMapping("/info/{categoryId}")
    @Operation(summary = "获取分类信息" , description = "获取分类信息")
    @Parameter(name = "categoryId", description = "分类id" )
    public ServerResponseEntity<Category> info(@PathVariable("categoryId") Long categoryId){
        Category category = categoryService.getCategoryByCategoryId(categoryId);

        return ServerResponseEntity.success(category);
    }

    @SysLog("保存分类")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('prod:category:save')")
    @Operation(summary = "保存分类" , description = "保存分类")
    public ServerResponseEntity<Long> save(@RequestBody Category category){
        category.setShopId(Constant.PLATFORM_SHOP_ID);
        category.setRecTime(new Date());
//        int count = categoryService.count(new LambdaQueryWrapper<Category>().eq(Category::getCategoryName, category.getCategoryName())
//                .eq(Category::getParentId, category.getParentId()).eq(Category::getShopId,Constant.PLATFORM_SHOP_ID))
        Integer count = categoryService.getCategoryName(category);
        if(count > 0){
            // 类目名称已存在
            throw new YamiShopBindException(I18nMessage.getMessage("yami.category.name.exist"));
        }
        category.setGrade(getGradeByParentId(category.getParentId()));
        category.setSuperiorId(-1L);
        // 获取上级的上级
        if(category.getGrade() == PlatformConstant.MAX_CATEGORY_GRADE) {
            Long superiorId = categoryService.getParentCategoryByParentId(category.getParentId());
            category.setSuperiorId(superiorId);
        }
        categoryService.saveCategroy(category);
        // 清除缓存
        removeCategoryCacheByParentId(category);
        return ServerResponseEntity.success(category.getCategoryId());
    }

    @SysLog("更新分类")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('prod:category:update')")
    @Operation(summary = "更新分类" , description = "更新分类")
    public ServerResponseEntity<String> update(@RequestBody Category category){
        category.setShopId(Constant.PLATFORM_SHOP_ID);
        Category categoryDb = categoryService.getCategoryByCategoryId(category.getCategoryId());
        if (!Objects.equals(category.getGrade(), categoryDb.getGrade())) {
            // 不能改变分类层级
            throw new YamiShopBindException("yami.category.exception.levelCannotChange");
        }
        Integer count = categoryService.getCategoryName(category);
        if(count > 0){
            // 类目名称已存在
            throw new YamiShopBindException(I18nMessage.getMessage("yami.category.name.exist"));
        }
        // 如果从下线改成正常，则需要判断上级的状态
        if (Objects.equals(categoryDb.getStatus(),0) && Objects.equals(category.getStatus(),1) && !Objects.equals(category.getParentId(),0L)){
            Category parentCategory = categoryService.getOne(new LambdaQueryWrapper<Category>().eq(Category::getCategoryId, category.getParentId()));
            if(Objects.isNull(parentCategory) || Objects.equals(parentCategory.getStatus(),0)){
                // 修改失败，上级分类不存在或者不为正常状态
                throw new YamiShopBindException("yami.category.status.check");
            }
        }
        category.setGrade(getGradeByParentId(category.getParentId()));
        category.setOldCategoryName(categoryDb.getCategoryName());
        category.setSuperiorId(-1L);
        // 获取上级的上级
        if(category.getGrade() == PlatformConstant.MAX_CATEGORY_GRADE) {
            Long superiorId = categoryService.getParentCategoryByParentId(category.getParentId());
            category.setSuperiorId(superiorId);
        }
        categoryService.updateCategroy(category);
        // 清除缓存
        removeCategoryCacheByParentId(category);
        // 更新es商品数据
        eventPublisher.publishEvent(new EsProductUpdateEvent(category.getCategoryId(), null, EsOperationType.UPDATE_BY_CATEGORY_ID));
        return ServerResponseEntity.success();
    }

    @SysLog("删除分类")
    @DeleteMapping("/{categoryId}")
    @PreAuthorize("@pms.hasPermission('prod:category:delete')")
    @Operation(summary = "删除分类" , description = "删除分类")
    @Parameter(name = "categoryId", description = "分类id" )
    public ServerResponseEntity<String> delete(@PathVariable("categoryId") Long categoryId){
        if (categoryService.count(new LambdaQueryWrapper<Category>().eq(Category::getParentId,categoryId)) >0) {
            // 请删除子分类，再删除该分类
            return ServerResponseEntity.showFailMsg(I18nMessage.getMessage("yami.category.delete.child"));
        }
        long categoryProdCount = productService.count(new LambdaQueryWrapper<Product>().eq(Product::getCategoryId, categoryId).ne(Product::getStatus, -1));
        if (categoryProdCount>0){
            // 该分类下还有商品，请先删除该分类下的商品
            return ServerResponseEntity.showFailMsg(I18nMessage.getMessage("yami.category.delete.check"));
        }
        long shopCategroycount = categoryShopService.count(new LambdaQueryWrapper<CategoryShop>().eq(CategoryShop::getCategoryId, categoryId).eq(CategoryShop::getStatus, SigningStatus.PENDING_REVIEW.value()));
        if (shopCategroycount>0){
            // 请勿删除商家正在申请签约的分类
            return ServerResponseEntity.showFailMsg(I18nMessage.getMessage("yami.category.signing.check"));
        }
        Category category = categoryService.getById(categoryId);
        category.setSuperiorId(-1L);
        // 获取上级的上级
        if(category.getGrade() == PlatformConstant.MAX_CATEGORY_GRADE) {
            Long superiorId = categoryService.getParentCategoryByParentId(category.getParentId());
            category.setSuperiorId(superiorId);
        }
        categoryService.deleteCategroy(category);
        // 清除缓存
        removeCategoryCacheByParentId(category);
        return ServerResponseEntity.success();
    }

    @GetMapping("/listCategory")
    @Operation(summary = "获取全部分类" , description = "获取全部分类")
    @Parameters(value = {
            @Parameter(name = "maxGrade", description = "0：一级分类，1：二级分类，2：三级分类(后面的包含前面等级的分类)" ),
            @Parameter(name = "status", description = "默认是1，表示正常状态,0为下线状态" )
    })
    public ServerResponseEntity<List<Category>> listCategory(@RequestParam(value = "maxGrade", required = false, defaultValue = "2") Integer maxGrade,
                                                       @RequestParam(value = "status", required = false) Integer status) {

        List<Category> categories =  categoryService.listByLang(I18nMessage.getLang(),maxGrade,null,status,Constant.PLATFORM_SHOP_ID);
        return ServerResponseEntity.success(categories);
    }

    @GetMapping("/listCategoryByGrade")
    @Operation(summary = "根据等级与状态获取平台分类列表" , description = "根据等级与状态获取平台分类列表")
    @Parameters(value = {
            @Parameter(name = "grade", description = "0：一级分类，1：二级分类，2：三级分类" ),
            @Parameter(name = "status", description = "默认是1，表示正常状态,0为下线状态" )
    })
    public ServerResponseEntity<List<Category>> listCategoryByGrade(@RequestParam(value = "grade", defaultValue = "2") Integer grade, @RequestParam(value = "status", required = false) Integer status) {
        List<Category> categories = categoryService.listByGrade(grade, status, Constant.PLATFORM_SHOP_ID);
        return ServerResponseEntity.success(categories);
    }

    @GetMapping("/upAndCurrCategoryList/{categoryId}")
    @Operation(summary = "获取上架分类和当前选中分类的父类" , description = "获取上架分类和当前选中分类的父类")
    @Parameters(value = {
            @Parameter(name = "maxGrade", description = "0：一级分类，1：二级分类，2：三级分类" ),
            @Parameter(name = "categoryId", description = "分类id" )
    })
    public ServerResponseEntity<List<Category>> upAndCurrCategoryList(
            @RequestParam(value = "maxGrade", required = false, defaultValue = "2") Integer maxGrade,
            @PathVariable("categoryId") Long categoryId){
        Category category = new Category();
        category.setLang(I18nMessage.getDbLang());
        category.setStatus(1);
        category.setShopId(Constant.PLATFORM_SHOP_ID);
        category.setGrade(maxGrade);
        //获取上架的分类
        List<Category> upList = categoryService.categoryList(category);

        //如果是新增的，直接返回上架的分类即可
        if (categoryId==0){
            return ServerResponseEntity.success(upList);
        }
        Category currCategory = categoryService.getCategoryByCategoryId(categoryId);
        if (currCategory == null) {
            return ServerResponseEntity.success(upList);
        }
        while (currCategory.getParentId() != 0) {
            currCategory=categoryService.getCategoryByCategoryId(currCategory.getParentId());
            if (!Objects.equals(currCategory.getStatus(), 1)) {
                upList.add(currCategory);
            }
        }
        return ServerResponseEntity.success(upList);
    }

    private int getGradeByParentId(Long parentId) {
        // 如果上级为id为0，则设置分类等级为0
        if (Objects.equals(parentId,0L)) {
            return 0;
        }
        Category parentCategory = categoryService.getById(parentId);
        return parentCategory.getGrade() + 1;
    }

    @GetMapping("/platformCategory")
    @Operation(summary = "平台可用分类-必须是启用分类且分类下包含启动的三级分类" , description = "平台可用分类-必须是启用分类且分类下包含启动的三级分类")
    public ServerResponseEntity<List<Category>> platformCategory(){
        List<Category> list =  categoryService.platformCategory();
        return ServerResponseEntity.success(list);
    }

    @GetMapping("/signingInfoByShopId")
    @Operation(summary = "获取签约的分类列表（状态参数为空则返回所有）" , description = "获取签约的分类列表（状态参数为空则返回所有）")
    @Parameters(value = {
            @Parameter(name = "shopId", description = "店铺id" ),
            @Parameter(name = "status", description = "签约状态：1：已通过 0待审核 -1未通过" )
    })
    public ServerResponseEntity<List<CategoryShopVO>> listSigningByShopId(@RequestParam(value = "shopId") Long shopId, @RequestParam(value = "status", required = false) Integer status) {
        List<CategoryShopVO> categoryShopList = categoryShopService.listSigningCategoryByShopId(shopId);
        if (Objects.nonNull(status)) {
            categoryShopList = categoryShopList.stream().filter(item -> Objects.equals(item.getStatus(), status)).collect(Collectors.toList());
        }
        return ServerResponseEntity.success(categoryShopList);
    }

    @GetMapping("/pageSigningInfo")
    @Operation(summary = "分页获取签约的分类列表" , description = "分页获取签约的分类列表")
    public ServerResponseEntity<IPage<CategoryShopVO>> pageSigningInfo(PageParam<CategoryShopVO> page, CategoryShopDTO categoryShop) {
        IPage<CategoryShopVO> categoryShopPage = categoryShopService.pageSigningInfo(page, categoryShop);
        return ServerResponseEntity.success(categoryShopPage);
    }

    @PutMapping("/signing")
    @Operation(summary = "更新店铺签约分类" , description = "更新店铺签约分类")
    @Parameter(name = "shopId", description = "店铺id" )
    public ServerResponseEntity<Void> signing(@Valid @RequestBody List<CategoryShop> categoryShopDTOList, @RequestParam(value = "shopId") Long shopId) {
        categoryShopService.signingCategory(categoryShopDTOList, shopId, true);
        return ServerResponseEntity.success();
    }

    private void removeCategoryCacheByParentId(Category category) {
        categoryService.removeListRateCache();
        categoryService.removeCacheByParentIdAndLang(category.getParentId(), category.getShopId());
        categoryService.removeCacheByParentIdAndLang(category.getSuperiorId(), category.getShopId());
        // 如果是2/3级分类，第一分类也需要清空缓存数据
        if(category.getGrade() == 1 || category.getGrade() == PlatformConstant.MAX_CATEGORY_GRADE) {
            categoryService.removeCacheByParentIdAndLang(Constant.CATEGORY_ID, Constant.PLATFORM_SHOP_ID);
        }
    }

    @GetMapping("/categoryInfo")
    @Operation(summary = "分类信息列表" , description = "获取所有的产品分类信息，顶级分类的parentId为0,默认为顶级分类")
    @Parameters({
            @Parameter(name = "parentId", description = "分类ID" ),
            @Parameter(name = "shopId", description = "店铺id" )
    })
    public ServerResponseEntity<List<CategoryDto>> categoryInfo(@RequestParam(value = "parentId", defaultValue = "0") Long parentId,
                                                          @RequestParam(value = "shopId", defaultValue = "0") Long shopId) {
        List<Category> categories = categoryService.listByParentIdAndShopId(parentId, shopId);
        List<CategoryDto> categoryDtos = BeanUtil.mapAsList(categories, CategoryDto.class);
        return ServerResponseEntity.success(categoryDtos);
    }

    @GetMapping("/getCategoryAndParent")
    @Operation(summary = "获取平台分类及所有上级分类" , description = "获取平台分类及所有上级分类")
    public ServerResponseEntity<List<Category>> getCategoryAndParent(@RequestParam(value = "categoryId") Long categoryId){
        List<Category> categories = categoryService.getCategoryAndParent(categoryId);
        return ServerResponseEntity.success(categories);
    }

    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('prod:prod:exportProd')")
    @Operation(summary = "导出分类" , description = "导出分类")
    public void export(HttpServletResponse response) {
        categoryExcelService.export(response, Constant.PLATFORM_SHOP_ID);
    }
}
