package com.yami.shop.bean.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/10 10:53
 */
@Data
public class ComboVO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "套餐id" )
    private Long comboId;

    @Schema(description = "套餐名称" )
    private String name;

    @Schema(description = "状态， -1：已删除 0：已失效 1：开启" )
    private Integer status;

    @Schema(description = "商品数量" )
    private Integer prodCount;

    @Schema(description = "主商品" )
    private ComboProdVO mainProd;

    @Schema(description = "开始时间" )
    private Date startTime;

    @Schema(description = "结束时间" )
    private Date endTime;

    @Schema(description = "套餐金额" )
    private Double comboAmount;

    @Schema(description = "搭配商品列表" )
    private List<ComboProdVO> matchingProds;

    @Schema(description = "搭配商品id列表" )
    private List<Long> matchingProdIds;
}
