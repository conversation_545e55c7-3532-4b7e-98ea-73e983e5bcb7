package com.yami.shop.distribution.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 优惠券状态
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistributionProdStateEnum {

    /**
     * 商家下架
     */
    PUT_OFF(0, "商家下架"),

    /**
     * 商家上架
     */
    PUT_ON(1, "商家上架"),

    /**
     * 违规下架
     */
    OFFLINE(2, "违规下架"),

    /**
     * 等待审核
     */
    WAIT_AUDIT(3, "等待审核"),
    ;
    private final int value;
    private final String desc;
}
