package com.yami.shop.common.allinpay.order.resp;

/**
 * 订单 返回
 *
 * <AUTHOR>
 */
public class ApplicationCollectionResp {
    /**
     * 支付状态
     * 仅交易验证方式为“0”时返回
     * 成功：success
     * 进行中：pending
     * 失败：fail
     * 订单成功时会发订单结果通知商户。
     */
    private Integer payStatus;
    private String payFailMessage;
    private String bizUserId;
    private String orderNo;
    private String bizOrderNo;
    private String reqPayInterfaceNo;
    private String payInterfaceOutTradeNo;
    private String payInterfacetrxcode;
    private String acct;
    /**
     * 扩展参数
     */
    private String extendInfo;
    /**
     * 微信APP支付信息
     */
    private String weChatAPPInfo;
    /**
     * 扫码支付信息/ JS支付串信息（微信、支付宝、QQ钱包）/微信小程序/微信原生H5支付串信息/支付宝原生APP支付串信息
     * 1、扫码支付(正扫)必传;
     * 微信、支付宝的支付串，供转化为二维码
     * 2. JS支付必传;
     * 微信公众号JS支付：返回json字符串。
     * 支付宝JS支付：返回json字符串。
     * 支付宝JS支付：返回支付宝交易单号，将此参数上送至“支付宝服务窗文档的创建订单+JSAPI唤起收银台支付”方法中 tradeNO参数
     * QQ钱包的JS支付：返回支付的链接,消费者只需跳转到此链接即可完成支付。
     * 3、收银宝微信小程序支付参数/微信原生小程序支付参数必传
     * 注：有效时间60分钟
     * 4、微信原生H5支付参数
     * 微信返回mweb_url支付跳转链接,消费者只需跳转此链接完成支付，有效期5分钟
     * 5、支付宝原生APP支付参数必传，商户获取支付串调用支付宝APP端SDK直接呼起支付宝APP支付即可
     */
    private String payInfo;
    /**
     * 交易验证方式
     */
    private String validateType;
    /**
     * 收银台支付宝小程序支付信息(JSON)
     */
    private String miniprogramPayInfo_VSP;

    public String getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }

    public String getMiniprogramPayInfo_VSP() {
        return miniprogramPayInfo_VSP;
    }

    public void setMiniprogramPayInfo_VSP(String miniprogramPayInfo_VSP) {
        this.miniprogramPayInfo_VSP = miniprogramPayInfo_VSP;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    public String getPayFailMessage() {
        return payFailMessage;
    }

    public void setPayFailMessage(String payFailMessage) {
        this.payFailMessage = payFailMessage;
    }

    public String getBizUserId() {
        return bizUserId;
    }

    public void setBizUserId(String bizUserId) {
        this.bizUserId = bizUserId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getReqPayInterfaceNo() {
        return reqPayInterfaceNo;
    }

    public void setReqPayInterfaceNo(String reqPayInterfaceNo) {
        this.reqPayInterfaceNo = reqPayInterfaceNo;
    }

    public String getPayInterfaceOutTradeNo() {
        return payInterfaceOutTradeNo;
    }

    public void setPayInterfaceOutTradeNo(String payInterfaceOutTradeNo) {
        this.payInterfaceOutTradeNo = payInterfaceOutTradeNo;
    }

    public String getPayInterfacetrxcode() {
        return payInterfacetrxcode;
    }

    public void setPayInterfacetrxcode(String payInterfacetrxcode) {
        this.payInterfacetrxcode = payInterfacetrxcode;
    }

    public String getAcct() {
        return acct;
    }

    public void setAcct(String acct) {
        this.acct = acct;
    }

    public String getWeChatAPPInfo() {
        return weChatAPPInfo;
    }

    public void setWeChatAPPInfo(String weChatAPPInfo) {
        this.weChatAPPInfo = weChatAPPInfo;
    }

    public String getPayInfo() {
        return payInfo;
    }

    public void setPayInfo(String payInfo) {
        this.payInfo = payInfo;
    }

    public String getValidateType() {
        return validateType;
    }

    public void setValidateType(String validateType) {
        this.validateType = validateType;
    }

    @Override
    public String toString() {
        return "ApplicationCollectionResp{" +
                "payStatus=" + payStatus +
                ", payFailMessage='" + payFailMessage + '\'' +
                ", bizUserId='" + bizUserId + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                ", reqPayInterfaceNo='" + reqPayInterfaceNo + '\'' +
                ", payInterfaceOutTradeNo='" + payInterfaceOutTradeNo + '\'' +
                ", payInterfacetrxcode='" + payInterfacetrxcode + '\'' +
                ", acct='" + acct + '\'' +
                ", extendInfo='" + extendInfo + '\'' +
                ", weChatAPPInfo='" + weChatAPPInfo + '\'' +
                ", payInfo='" + payInfo + '\'' +
                ", validateType='" + validateType + '\'' +
                ", miniprogramPayInfo_VSP='" + miniprogramPayInfo_VSP + '\'' +
                '}';
    }
}
