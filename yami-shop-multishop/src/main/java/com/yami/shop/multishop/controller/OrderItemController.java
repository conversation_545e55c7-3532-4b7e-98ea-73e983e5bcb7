package com.yami.shop.multishop.controller;

import com.yami.shop.bean.vo.OrderDetailVO;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.OrderItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/6/9 9:19
 */
@RestController("multihopOrderItemController")
@RequestMapping("/multishop/order_item")
@Tag(name = "订单项信息接口")
@AllArgsConstructor
public class OrderItemController {

    private final OrderItemService orderItemService;

    @GetMapping("/get_order_detail")
    @Operation(summary = "查询订单项、退款详情" , description = "查询订单项、退款详情")
    @PreAuthorize("@pms.hasPermission('multishop:orderItem:info')")
    public ServerResponseEntity<OrderDetailVO> getOrderItemDetail(String orderNumber, String refundSn, Integer reason){
        Long shopId = SecurityUtils.getShopUser().getShopId();
        OrderDetailVO orderDetailVO = orderItemService.listDetailByParam(orderNumber, refundSn, reason, shopId);
        return ServerResponseEntity.success(orderDetailVO);
    }
}
