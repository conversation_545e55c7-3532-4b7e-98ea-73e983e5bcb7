package com.yami.shop.platform.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yami.shop.bean.model.StockChangeReason;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.StockChangeReasonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 *
 * 出入库原因
 * <AUTHOR>
 * @date 2021-09-07 16:04:18
 */
@RestController
@AllArgsConstructor
@RequestMapping("/platform/stockChangeReason" )
@Tag(name = "platform")
public class StockChangeReasonController {

    private final StockChangeReasonService stockChangeReasonService;

    @GetMapping("/page" )
    @Operation(summary = "分页获取出入库原因" , description = "分页获取出入库原因")
    public ServerResponseEntity<IPage<StockChangeReason>> getStockChangeReasonPage(PageParam<StockChangeReason> page, StockChangeReason stockChangeReason) {
        stockChangeReason.setShopId(Constant.PLATFORM_SHOP_ID);
        return ServerResponseEntity.success(stockChangeReasonService.pageByParams(page, stockChangeReason));
    }

    @GetMapping("/list")
    @Schema(description = "获取出入库原因列表" )
    public ServerResponseEntity<List<StockChangeReason>> listResponse(StockChangeReason stockChangeReason) {
        stockChangeReason.setShopId(Constant.PLATFORM_SHOP_ID);
        return ServerResponseEntity.success(stockChangeReasonService.listByParams(stockChangeReason));
    }

    @GetMapping("/info/{stockChangeReasonId}" )
    @Operation(summary = "根据id查询出入库原因" , description = "根据id查询出入库原因")
    public ServerResponseEntity<StockChangeReason> getById(@PathVariable("stockChangeReasonId") Long stockChangeReasonId) {
        Long shopId = Constant.PLATFORM_SHOP_ID;
        StockChangeReason stockChangeReason = stockChangeReasonService.getOne(Wrappers.lambdaQuery(StockChangeReason.class)
                .eq(StockChangeReason::getStockChangeReasonId, stockChangeReasonId)
                .eq(StockChangeReason::getShopId, shopId)
        );
        stockChangeReason.setShopId(null);
        return ServerResponseEntity.success(stockChangeReason);
    }

    @PostMapping
    @Operation(summary = "新增出入库原因" , description = "新增出入库原因")
    public ServerResponseEntity<Void> save(@RequestBody @Valid StockChangeReason stockChangeReason) {
        stockChangeReason.setShopId(Constant.PLATFORM_SHOP_ID);
        stockChangeReasonService.saveInfo(stockChangeReason);
        return ServerResponseEntity.success();
    }

    @PutMapping
    @Operation(summary = "修改出入库原因" , description = "修改出入库原因")
    public ServerResponseEntity<Void> updateById(@RequestBody @Valid StockChangeReason stockChangeReason) {
        stockChangeReason.setShopId(Constant.PLATFORM_SHOP_ID);
        stockChangeReasonService.updateInfo(stockChangeReason);
        return ServerResponseEntity.success();
    }

    @PutMapping("/changeStatus" )
    @Schema(description = "修改出入库原因状态" )
    @Parameters(value = {
            @Parameter(name = "stockChangeReasonId", description = "出入库原因id" ),
            @Parameter(name = "status", description = "状态，1：启用 0：禁用" )
    })
    public ServerResponseEntity<Void> removeById(@RequestParam("stockChangeReasonId") Long stockChangeReasonId, @RequestParam("status") Integer status) {
        Long shopId = Constant.PLATFORM_SHOP_ID;
        stockChangeReasonService.update(Wrappers.lambdaUpdate(StockChangeReason.class)
                .set(StockChangeReason::getStatus, status)
                .eq(StockChangeReason::getStockChangeReasonId, stockChangeReasonId)
                .eq(StockChangeReason::getShopId, shopId)
        );
        return ServerResponseEntity.success();
    }

}
