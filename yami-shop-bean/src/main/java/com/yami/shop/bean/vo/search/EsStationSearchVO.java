package com.yami.shop.bean.vo.search;

import com.yami.shop.bean.app.dto.SeckillSkuDto;
import com.yami.shop.bean.app.dto.SkuDto;
import com.yami.shop.bean.app.vo.StationPeoductVO;
import com.yami.shop.bean.vo.SameCityVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店es数据
 * @since 2024/6/4 15:24
 */
@Data
public class EsStationSearchVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "自提门店id")
    private Long stationId;

    @Schema(description = "门店所属店铺id")
    private Long shopId;

    @Schema(description = "门店自提点名称")
    private String stationName;

    @Schema(description = "门店自提点图片")
    private String pic;

    @Schema(description = "电话区号")
    private String phonePrefix;

    @Schema(description = "手机/电话号码")
    private String phone;

    @Schema(description = "经度")
    private Double lng;

    @Schema(description = "纬度")
    private Double lat;

    @Schema(description = "省id")
    private Long provinceId;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市id")
    private Long cityId;

    @Schema(description = "市")
    private String city;

    @Schema(description = "区id")
    private Long areaId;

    @Schema(description = "区")
    private String area;

    @Schema(description = "邮编")
    private String postCode;

    @Schema(description = "地址")
    private String addr;

    @Schema(description = "时间数据")
    private String timeDate;

    @Schema(description = "分类ids")
    private List<Long> categoryIds;

    @Schema(description = "门店营业状态(0:停业中 1:营业中)")
    private Integer status;

    @Schema(description = "门店评分")
    private Double stationScore;

    @Schema(description = "距离")
    private Double distance;

    @Schema(description = "库存模式 1共享总部库存 2独立销售库存")
    private Integer stockMode;

    @Schema(description = "自提门店用途是否支持自提（0：不支持，1：支持）")
    private Integer selfPickup;

    @Schema(description = "自提门店用途是否支持同城配送（0：不支持，1：支持）")
    private Integer sameCityDelivery;

    @Schema(description = "商品信息")
    private List<StationPeoductVO> spuList;

    @Schema(description = "门店同城配送信息")
    private SameCityVO sameCityVO;

    @Schema(description = "库存是否充足 1.库存充足 0.库存不足")
    private Integer hasStock;

    @Schema(description = "营业时间")
    private String business;

    @Schema(description = "门店地址(省+市+区+详细地址拼接)")
    private String stationAddr;

    @Schema(description = "门店账号")
    private String account;

    @Schema(description = "门店的sku列表")
    private List<SkuDto> skuList;

    @Schema(description = "秒杀sku列表")
    List<SeckillSkuDto> seckillSkuList;

}
