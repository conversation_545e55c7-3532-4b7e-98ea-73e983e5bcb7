package com.yami.shop.platform.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.enums.*;
import com.yami.shop.bean.event.EsProductUpdateEvent;
import com.yami.shop.bean.model.OfflineHandleEvent;
import com.yami.shop.bean.model.Product;
import com.yami.shop.bean.model.Sku;
import com.yami.shop.bean.param.ProductParam;
import com.yami.shop.bean.param.ProductScoreParam;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.constants.SegmentIdKey;
import com.yami.shop.common.exception.YamiShopBindException;
import com.yami.shop.common.i18n.I18nMessage;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.BeanUtil;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.delivery.common.model.Transport;
import com.yami.shop.delivery.common.service.TransportService;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * 商品列表、商品发布controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/platform/scoreProduct")
@AllArgsConstructor
@Tag(name = "积分商品")
public class ScoreProductController {

    private final ProductService productService;
    private final SegmentService segmentService;
    private final SkuService skuService;
    private final ProdLangService prodLangService;
    private final BasketService basketService;
    private final ApplicationEventPublisher eventPublisher;
    private final OfflineHandleEventService offlineHandleEventService;

    private final TransportService transportService;


    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('score:prod:page')")
    @Operation(summary = "分页获取积分商品信息" , description = "分页获取积分商品信息")
    public ServerResponseEntity<IPage<Product>> page(ProductParam product, PageParam<Product> page) {
        product.setLang(I18nMessage.getDbLang());
        product.setShopId(Constant.PLATFORM_SHOP_ID);
        product.setProdType(ProdType.PROD_TYPE_SCORE.value());
        IPage<Product> products = productService.pageByLang(page,product);
        return ServerResponseEntity.success(products);
    }

    @GetMapping("/info/{prodId}")
    @PreAuthorize("@pms.hasPermission('score:prod:info')")
    @Operation(summary = "获取信息" , description = "获取信息")
    @Parameter(name = "prodId", description = "商品id" )
    public ServerResponseEntity<Product> info(@PathVariable("prodId") Long prodId) {
        Product prod = productService.getProductAndLang(prodId);
        List<Sku> skuList = skuService.listSkuAndSkuStockForAdmin(prod);
        prod.setSkuList(skuList);
        return ServerResponseEntity.success(prod);
    }

    @DeleteMapping("/{prodId}")
    @PreAuthorize("@pms.hasPermission('score:prod:delete')")
    @Operation(summary = "删除积分商品" , description = "删除积分商品")
    @Parameter(name = "prodId", description = "商品id" )
    public ServerResponseEntity<Void> delete(@PathVariable("prodId") Long prodId) {
        Product dbProduct = productService.getProductByProdId(prodId);
        if (Objects.equals(dbProduct.getStatus(), ProdStatusEnums.DELETE.getValue())) {
            // 该商品已经被删除
            throw new YamiShopBindException("yami.product.already.deleted");
        }
        List<Sku> dbSkus = skuService.listSkuByProdId(dbProduct.getProdId());
        // 删除商品
        productService.removeProductByProdId(prodId, null);

        // 清除缓存
        productService.removeProdCacheByProdId(prodId);

        for (Sku sku : dbSkus) {
            skuService.removeSkuCacheBySkuId(sku.getSkuId(), sku.getProdId());
        }

        List<String> userIds = basketService.listUserIdByProdId(prodId);
        //清除购物车缓存
        basketService.removeCacheByUserIds(userIds, null);
        eventPublisher.publishEvent(new EsProductUpdateEvent(prodId, null, EsOperationType.DELETE));
        return ServerResponseEntity.success();
    }

    @GetMapping("/getOfflineHandleEventByProdId/{prodId}")
    @Operation(summary = "获取最新下线商品的事件" , description = "获取最新下线商品的事件")
    @Parameter(name = "prodId", description = "商品id" )
    @PreAuthorize("@pms.hasPermission('score:prod:offlineInfo')")
    public ServerResponseEntity<OfflineHandleEvent> getOfflineHandleEventByProdId(@PathVariable Long prodId) {
        OfflineHandleEvent offlineHandleEvent = offlineHandleEventService.getProcessingEventByHandleTypeAndHandleId(OfflineHandleEventType.PROD.getValue(), prodId);
        return ServerResponseEntity.success(offlineHandleEvent);
    }

    @PutMapping("/prodStatus")
    @Operation(summary = "更新商品状态" , description = "更新商品状态")
    @PreAuthorize("@pms.hasPermission('score:prod:update')")
    public ServerResponseEntity<Void> shopStatus(@RequestBody ProductParam productParam) {
        Long prodId = productParam.getProdId();
        Integer prodStatus = productParam.getStatus();
        Product dbProduct = productService.getProductByProdId(prodId);
        if (!(Objects.equals(dbProduct.getStatus(), ProdStatusEnums.NORMAL.getValue())
                || Objects.equals(dbProduct.getStatus(), ProdStatusEnums.SHOP_OFFLINE.getValue()))) {
            // 商品不在正常状态，修改失败
            throw new YamiShopBindException("yami.product.on.normal");
        }
        Product product = new Product();
        product.setProdId(prodId);
        product.setStatus(prodStatus);
        if (prodStatus == 1) {
            product.setPutawayTime(new Date());
        }
        dbProduct.setStatus(prodStatus);
        productService.updateById(product);
        List<String> userIds = basketService.listUserIdByProdId(prodId);
        productService.removeProdCacheByProdId(prodId);
        //清除购物车缓存
        basketService.removeCacheByUserIds(userIds, null);
        eventPublisher.publishEvent(new EsProductUpdateEvent(prodId, null, EsOperationType.UPDATE));
        return ServerResponseEntity.success();
    }

    @PostMapping
    @PreAuthorize("@pms.hasPermission('score:prod:save')")
    @Operation(summary = "保存积分商品" , description = "保存积分商品")
    public ServerResponseEntity<Long> save(@Valid @RequestBody ProductScoreParam productScoreParam) {
        checkParam(productScoreParam);
        ProductParam productParam = BeanUtil.map(productScoreParam,ProductParam.class);
        productParam.setShopId(Constant.PLATFORM_SHOP_ID);
        //积分商品类型
        productParam.setProdType(ProdType.PROD_TYPE_SCORE.value());
        productParam.setMold(ProdMoldEnum.REAL.value());
        productService.saveProduct(productParam);
        eventPublisher.publishEvent(new EsProductUpdateEvent(productParam.getProdId(), null, EsOperationType.SAVE));
        return ServerResponseEntity.success(productParam.getProdId());
    }

    @PutMapping
    @PreAuthorize("@pms.hasPermission('score:prod:update')")
    @Operation(summary = "修改积分商品" , description = "修改积分商品")
    public ServerResponseEntity<String> update(@Valid @RequestBody ProductScoreParam productScoreParam) {
        checkParam(productScoreParam);
        Product dbProduct = productService.getProductInfo(productScoreParam.getProdId());
        if (Objects.isNull(dbProduct) || Objects.equals(dbProduct.getStatus(), ProdStatusEnums.DELETE.getValue())) {
            // 该商品已经被删除
            throw new YamiShopBindException("yami.product.already.deleted");
        }
        List<String> userIds = basketService.listUserIdByProdId(productScoreParam.getProdId());
        List<Sku> dbSkus = skuService.listSkuByProdId(dbProduct.getProdId());
        dbProduct.setSkuList(dbSkus);
        productScoreParam.setProdType(ProdType.PROD_TYPE_SCORE.value());
        ProductParam productParam = BeanUtil.map(productScoreParam,ProductParam.class);
        productParam.setShopId(Constant.PLATFORM_SHOP_ID);
        productParam.setEmployeeId(AuthUserContext.getSysUserId());
        productService.updateProduct(productParam, dbProduct);
        productService.removeProdCacheByProdId(productParam.getProdId());
        //清除缓存
        for (Sku sku : dbSkus) {
            skuService.removeSkuCacheBySkuId(sku.getSkuId(), sku.getProdId());
        }
        //清除购物车缓存
        basketService.removeCacheByUserIds(userIds, null);
        eventPublisher.publishEvent(new EsProductUpdateEvent(dbProduct.getProdId(), null, EsOperationType.UPDATE));
        return ServerResponseEntity.success();
    }


    private void checkParam(ProductScoreParam productScoreParam) {
        //运费模板
        Transport transport = transportService.getTransportAndAllItems(productScoreParam.getDeliveryTemplateId());
        if (Objects.isNull(transport) && !DeliveryTemplateType.isUnifiedTemplate(productScoreParam.getDeliveryTemplateId())) {
            // 产品运费模板不存在
            throw new YamiShopBindException("yami.prod.transport.not.exist");
        }
        boolean isAllUnUse = true;
        List<Sku> skuList = productScoreParam.getSkuList();
        for (Sku sku : skuList) {
            if (sku.getStatus() == 1) {
                isAllUnUse = false;
            }
        }
        if (isAllUnUse) {
            // 至少要启用一种商品规格
            throw new YamiShopBindException("yami.product.enable.sku");
        }
        // 校验sku
        // 商品编码
        List<Product> products = productService.list(new LambdaQueryWrapper<Product>().eq(Product::getShopId, Constant.PLATFORM_SHOP_ID)
                .ne(Product::getStatus, -1));
        List<Long> prodIds = products.stream().map(Product::getProdId).toList();
        List<String> partyCodes = skuService.listSkuByProdIds(prodIds, productScoreParam.getProdId());
        for (Sku sku : skuList) {
            // 雪花算法生成商品编码
            if (StrUtil.isBlank(sku.getPartyCode())) {
                String partyCode = StringUtils.join("RM", String.valueOf(segmentService.getSegmentId(SegmentIdKey.PRODUCT)));
                sku.setPartyCode(partyCode);
            }
            if (CollectionUtils.isNotEmpty(partyCodes) && partyCodes.contains(sku.getPartyCode())) {
                String message = I18nMessage.getMessage("yami.sku.party.code");
                String isExit = I18nMessage.getMessage("yami.is.exist");
                //商品编码已存在
                throw new YamiShopBindException(message + sku.getPartyCode() + isExit);
            }
            if (1 == sku.getStatus()) {
                isAllUnUse = false;
            }
            if (Objects.isNull(sku.getStockWarning())) {
                sku.setStockWarning(0);
            }
            if(sku.getStockWarning()>Constant.STOCK_WARNING_MAX){
                //超过库存预警最大值
                throw new YamiShopBindException("yami.sku.stockWarning.limit");
            }
        }
    }

}
