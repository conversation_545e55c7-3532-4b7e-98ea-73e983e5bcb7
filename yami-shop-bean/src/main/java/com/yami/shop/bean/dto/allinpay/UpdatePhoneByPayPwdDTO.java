package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-03-23
 */
public class UpdatePhoneByPayPwdDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "跳转页面类型(1:H5页面, 2:小程序页面)，默认H5")
    private Long jumpPageType;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "身份证号")
    private String identityNo;

    @Schema(description = "原手机号")
    private String oldPhone;

    @Schema(description = "成功后跳转地址(非必填)")
    private String jumpUrl;

    @Schema(description = "失败跳转地址(非必填)")
    private String errorJumpUrl;

    public Long getJumpPageType() {
        return jumpPageType;
    }

    public void setJumpPageType(Long jumpPageType) {
        this.jumpPageType = jumpPageType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdentityNo() {
        return identityNo;
    }

    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo;
    }

    public String getOldPhone() {
        return oldPhone;
    }

    public void setOldPhone(String oldPhone) {
        this.oldPhone = oldPhone;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getErrorJumpUrl() {
        return errorJumpUrl;
    }

    public void setErrorJumpUrl(String errorJumpUrl) {
        this.errorJumpUrl = errorJumpUrl;
    }

    @Override
    public String toString() {
        return "UpdatePhoneByPayPwdDTO{" +
                "jumpPageType=" + jumpPageType +
                ", name='" + name + '\'' +
                ", identityNo='" + identityNo + '\'' +
                ", oldPhone='" + oldPhone + '\'' +
                ", jumpUrl='" + jumpUrl + '\'' +
                ", errorJumpUrl='" + errorJumpUrl + '\'' +
                '}';
    }
}
