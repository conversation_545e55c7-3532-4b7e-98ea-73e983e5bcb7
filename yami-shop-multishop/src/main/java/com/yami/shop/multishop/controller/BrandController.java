package com.yami.shop.multishop.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.dto.BrandSigningDTO;
import com.yami.shop.bean.model.Brand;
import com.yami.shop.bean.vo.BrandSigningVO;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.enums.StatusEnum;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.service.BrandService;
import com.yami.shop.service.BrandShopService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 品牌管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/brand")
@Tag(name = "品牌接口")
@AllArgsConstructor
public class BrandController {

    private final BrandService brandService;
    private final BrandShopService brandShopService;

    @GetMapping("/page")
    @Operation(summary = "根据参数分页获取平台品牌列表" , description = "根据参数分页获取平台品牌列表")
    @PreAuthorize("@pms.hasPermission('admin:brand:page')")
    public ServerResponseEntity<IPage<Brand>> page(PageParam<Brand> page, Brand brand) {
        brand.setStatus(StatusEnum.ENABLE.value());
        brand.setShopId(Constant.PLATFORM_SHOP_ID);
        IPage<Brand> brandList = brandService.page(page, brand);
        return ServerResponseEntity.success(brandList);
    }

    @GetMapping("/list")
    @Operation(summary = "获取平台品牌列表（不分页）" , description = "获取平台品牌列表（不分页）")
    public ServerResponseEntity<List<Brand>> list(Brand brand) {
        brand.setShopId(Constant.PLATFORM_SHOP_ID);
        List<Brand> brandList = brandService.listByParams(brand);
        return ServerResponseEntity.success(brandList);
    }

    @PostMapping("/signing")
    @Operation(summary = "签约品牌" , description = "签约品牌")
    public ServerResponseEntity<Void> signingBrands(@Valid @RequestBody BrandSigningDTO brandSigningDTO) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        brandShopService.signingBrands(brandSigningDTO, shopId, false);
        return ServerResponseEntity.success();
    }

    @GetMapping("/listSigningBrand")
    @Operation(summary = "获取店铺下已签约的品牌列表(status为空则返回所有）" , description = "获取店铺下已签约的品牌列表(status为空则返回所有）")
    @Parameter(name = "status", description = "签约状态：1：已通过 0待审核 -1未通过")
    @PreAuthorize("@pms.hasPermission('admin:brand:list')")
    public ServerResponseEntity<BrandSigningVO> listSigning(@RequestParam(value = "status", required = false) Integer status) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        BrandSigningVO brandSigningVO = brandShopService.listSigningByShopId(shopId, status);
        return ServerResponseEntity.success(brandSigningVO);
    }

    @GetMapping("" +
            "/listAvailableByCategoryAndName")
    @Parameters(value = {
            @Parameter(name = "categoryId", description = "分类id" ),
            @Parameter(name = "brandName", description = "品牌名称" )
    })
    @Operation(summary = "根据分类id与品牌名称分页获取分类下的品牌与店铺签约的品牌" , description = "根据分类id与品牌名称分页获取分类下的品牌与店铺签约的品牌")
    @PreAuthorize("@pms.hasPermission('admin:brand:page')")
    public ServerResponseEntity<IPage<Brand>> listAvailableBrandByCategoryIdAndBrandName(PageParam<Brand> page, @RequestParam(required = false) Long categoryId, @RequestParam(defaultValue = "") String brandName) {
        return ServerResponseEntity.success(brandService.pageAvailableBrandByCategoryIdAndBrandNameAndShopId(page, categoryId, brandName, SecurityUtils.getShopUser().getShopId()));
    }
}
