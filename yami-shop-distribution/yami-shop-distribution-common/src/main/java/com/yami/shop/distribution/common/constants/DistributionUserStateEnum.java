package com.yami.shop.distribution.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 * @date 2021/6/7 16:45
 */
@Getter
@AllArgsConstructor
public enum DistributionUserStateEnum {
    /**
     * 永久封禁
     */
    PER_BAN(-1, "永久封禁"),
    /**
     * 暂时封禁
     */
    BAN(2, "暂时封禁"),
    /**
     * 待审核状态
     */
    WAIT_AUDIT(0,"待审核状态"),
    /**
     * 正常
     */
    NORMAL(1, "正常"),
    /**
     * 审核未通过
     */
    FAIL_AUDIT(3,"审核未通过");

    private final int value;
    private final String desc;
}
