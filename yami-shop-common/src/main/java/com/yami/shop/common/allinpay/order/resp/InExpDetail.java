package com.yami.shop.common.allinpay.order.resp;


/**
 * 查询账户收支明细 返回
 *
 * <AUTHOR>
 */
public class InExpDetail {

    /**
     * 订单号
     */
    private String bizOrderNo;
    /**
     * 现有冻结金额
     */
    private Integer curFreezenAmount;
    /**
     * 收支明细流水号
     */
    private String tradeNo;
    /**
     * 原始金额
     */
    private Integer oriAmount;
    /**
     * 现有金额
     */
    private Integer curAmount;
    /**
     * 变更金额
     */
    private Integer chgAmount;
    /**
     * 变更时间
     */
    private String changeTime;
    /**
     * 账户集名称
     */
    private String accountSetName;

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public Integer getCurFreezenAmount() {
        return curFreezenAmount;
    }

    public void setCurFreezenAmount(Integer curFreezenAmount) {
        this.curFreezenAmount = curFreezenAmount;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public Integer getOriAmount() {
        return oriAmount;
    }

    public void setOriAmount(Integer oriAmount) {
        this.oriAmount = oriAmount;
    }

    public Integer getCurAmount() {
        return curAmount;
    }

    public void setCurAmount(Integer curAmount) {
        this.curAmount = curAmount;
    }

    public Integer getChgAmount() {
        return chgAmount;
    }

    public void setChgAmount(Integer chgAmount) {
        this.chgAmount = chgAmount;
    }

    public String getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(String changeTime) {
        this.changeTime = changeTime;
    }

    public String getAccountSetName() {
        return accountSetName;
    }

    public void setAccountSetName(String accountSetName) {
        this.accountSetName = accountSetName;
    }

}
