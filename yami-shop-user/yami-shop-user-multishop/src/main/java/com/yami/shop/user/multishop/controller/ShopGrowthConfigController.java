package com.yami.shop.user.multishop.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yami.shop.bean.dto.ShopGrowthGainDTO;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.security.multishop.util.SecurityUtils;
import com.yami.shop.user.common.model.ShopGrowthConfig;
import com.yami.shop.user.common.service.ShopGrowthConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 店铺成长值配置
 *
 * <AUTHOR>
 * @date 2022-10-17 17:23:24
 */
@RestController
@RequestMapping("/brandGrowthConfig")
@Tag(name = "店铺成长值配置")
@AllArgsConstructor
public class ShopGrowthConfigController {

    private final ShopGrowthConfigService brandGrowthConfigService;

    /**
     * 类型(0:全部 1:成长值配置 2:成长值问题 3:会员权益页)
     */
    private final static Integer TYPE_ALL = 0;
    /**
     * 类型 1:成长值配置
     */
    private final static Integer TYPE_GROWTH_CONFIG = 1;
    /**
     * 类型 2:成长值问题
     */
    private final static Integer TYPE_GROWTH_QUESTION = 2;
    /**
     * 类型 3:会员权益页
     */
    private final static Integer TYPE_MEMBER_RIGHTS = 3;


    @PutMapping("/saveOrUpdate")
    @PreAuthorize("@pms.hasPermission('brand:brandGrowthConfig:update')")
    @Operation(summary = "保存or更新店铺成长值配置", description =  "保存or更新店铺成长值配置")
    public ServerResponseEntity<Void> saveOrUpdate(@RequestBody @Valid ShopGrowthConfig brandGrowthConfig) {
        Long shopId = SecurityUtils.getShopUser().getShopId();
        brandGrowthConfig.setShopId(shopId);
        // 实体类转json
        if (brandGrowthConfig.getGrowthGainDTO() != null) {
            brandGrowthConfig.setGrowthGain(JSONObject.toJSONString(brandGrowthConfig.getGrowthGainDTO()));
        }
        brandGrowthConfigService.saveOrUpdate(brandGrowthConfig, new LambdaUpdateWrapper<ShopGrowthConfig>()
                .eq(ShopGrowthConfig::getShopId, shopId));
        return ServerResponseEntity.success();
    }

    @GetMapping
    @Operation(summary = "获取当前用户的成长值配置", description =  "获取当前用户的成长值配置")
    @Parameter(name = "type", description = "类型(0:全部 1:成长值配置 2:成长值问题 3:会员权益页)")
    @PreAuthorize("@pms.hasPermission('brand:brandGrowthConfig:info')")
    public ServerResponseEntity<ShopGrowthConfig> getInfo(Integer type) {
        if(Objects.isNull(type)){
            type = 0;
        }
        Long shopId = SecurityUtils.getShopUser().getShopId();
        ShopGrowthConfig growthConfig = brandGrowthConfigService.getOne(new LambdaQueryWrapper<ShopGrowthConfig>()
                .eq(ShopGrowthConfig::getShopId, shopId));
        if(growthConfig == null) {
            growthConfig = new ShopGrowthConfig();
            growthConfig.setShopId(shopId);
        }
        // json转实体类
        ShopGrowthGainDTO growthGain;
        if (ObjectUtils.isEmpty(growthConfig.getGrowthGain())) {
            // 空数据填补默认数据
            growthGain = new ShopGrowthGainDTO();
            growthGain.setBuyOrder(0);
            growthGain.setBuyPrice(0);
            growthGain.setShopGrowthSwitch(false);
        } else {
            growthGain = JSONObject.parseObject(growthConfig.getGrowthGain(), ShopGrowthGainDTO.class);
        }
        growthConfig.setGrowthGainDTO(growthGain);
        growthConfig.setGrowthGain(null);
        // 根据类型，隐藏要返回的数据
        if (TYPE_GROWTH_CONFIG.equals(type)) {
            growthConfig.setGrowthQuestion(null);
            growthConfig.setContent(null);
        } else if (TYPE_GROWTH_QUESTION.equals(type)) {
            growthConfig.setGrowthGainDTO(null);
            growthConfig.setContent(null);
        } else if (TYPE_MEMBER_RIGHTS.equals(type)) {
            growthConfig.setGrowthGainDTO(null);
            growthConfig.setGrowthQuestion(null);
        }
        return ServerResponseEntity.success(growthConfig);
    }
}
