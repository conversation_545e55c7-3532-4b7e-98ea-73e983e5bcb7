package com.yami.shop.common.allinpay.order.resp;

import java.io.Serial;
import java.io.Serializable;

/**
 * 提现申请返回
 *
 * <AUTHOR>
 */
public class WithdrawApplyResp implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 云商通订单号
     */
    private String orderNo;

    /**
     * 商户订单号（支付订单）
     */
    private String bizOrderNo;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    @Override
    public String toString() {
        return "WithdrawApplyResp{" +
                ", orderNo='" + orderNo + '\'' +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                '}';
    }
}
