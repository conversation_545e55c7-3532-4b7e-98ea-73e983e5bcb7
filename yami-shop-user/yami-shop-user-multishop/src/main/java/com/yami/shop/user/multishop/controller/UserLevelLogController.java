package com.yami.shop.user.multishop.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.common.annotation.SysLog;
import com.yami.shop.common.bean.PaySettlementConfig;
import com.yami.shop.common.config.Constant;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.security.common.util.AuthUserContext;
import com.yami.shop.service.SysConfigService;
import com.yami.shop.user.common.model.UserLevelLog;
import com.yami.shop.user.common.service.UserLevelLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;



/**
 * 用户等级记录
 *
 * <AUTHOR>
 * @date 2020-02-26 16:03:14
 */
@Tag(name = "用户等级记录")
@RestController
@AllArgsConstructor
@RequestMapping("/user/userLevelLog" )
public class UserLevelLogController {

    private final SysConfigService sysConfigService;
    private final UserLevelLogService userLevelLogService;

    @GetMapping("/page" )
    @Operation(summary = "分页查询", description =  "分页查询")
    @PreAuthorize("@pms.hasPermission('user:userLevelLog:page')")
    public ServerResponseEntity<IPage<UserLevelLog>> getUserLevelLogPage(PageParam<UserLevelLog> page,
                                                                         UserLevelLog userLevelLog) {
        Long shopId = AuthUserContext.getShopId();
        userLevelLog.setShopId(shopId);
        PaySettlementConfig config = sysConfigService.getSysConfigObject(Constant.PAY_SETTLEMENT_CONFIG, PaySettlementConfig.class);
        return ServerResponseEntity.success(userLevelLogService.getPage(page, userLevelLog, config.getPaySettlementType()));
    }

    @GetMapping("/info/{levelLogId}" )
    @Operation(summary = "查询用户等级记录", description =  "查询用户等级记录")
    @Parameter(name = "levelLogId", description = "用户等级记录id")
    @PreAuthorize("@pms.hasPermission('user:userLevelLog:info')")
    public ServerResponseEntity<UserLevelLog> getById(@PathVariable("levelLogId") Long levelLogId) {
        return ServerResponseEntity.success(userLevelLogService.getById(levelLogId));
    }

    @SysLog("新增" )
    @PostMapping
    @Operation(summary = "新增", description =  "新增")
    @PreAuthorize("@pms.hasPermission('user:userLevelLog:save')" )
    public ServerResponseEntity<Boolean> save(@RequestBody @Valid UserLevelLog userLevelLog) {
        Long shopId = AuthUserContext.getShopId();
        userLevelLog.setShopId(shopId);
        return ServerResponseEntity.success(userLevelLogService.save(userLevelLog));
    }

    @SysLog("修改" )
    @PutMapping
    @Operation(summary = "修改", description =  "修改")
    @PreAuthorize("@pms.hasPermission('user:userLevelLog:update')" )
    public ServerResponseEntity<Boolean> updateById(@RequestBody @Valid UserLevelLog userLevelLog) {
        userLevelLog.setState(1);
        return ServerResponseEntity.success(userLevelLogService.updateById(userLevelLog));
    }

    @SysLog("删除" )
    @DeleteMapping("/{levelLogId}" )
    @Operation(summary = "删除", description =  "通过id删除")
    @Parameter(name = "levelLogId", description = "用户等级记录id")
    @PreAuthorize("@pms.hasPermission('user:userLevelLog:delete')" )
    public ServerResponseEntity<Boolean> removeById(@PathVariable Long levelLogId) {
        return ServerResponseEntity.success(userLevelLogService.removeById(levelLogId));
    }
}
