package com.yami.shop.common.allinpay.order.resp;

/**
 * <AUTHOR>
 * @date 2023-06-02
 */
public class QueryReserveFundBalanceResp {
    /**
     * 账户号
     */
    private String accountNo;
    /**
     * 账户名
     */
    private String accountName;
    /**
     * 余额
     */
    private Long balance;
    /**
     * 是否默认结算户
     */
    private Long defClr;

    public QueryReserveFundBalanceResp() {
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    public Long getDefClr() {
        return defClr;
    }

    public void setDefClr(Long defClr) {
        this.defClr = defClr;
    }

    @Override
    public String toString() {
        return "QueryReserveFundBalanceResp{" +
                "accountNo='" + accountNo + '\'' +
                ", accountName='" + accountName + '\'' +
                ", balance=" + balance +
                ", defClr=" + defClr +
                '}';
    }
}
