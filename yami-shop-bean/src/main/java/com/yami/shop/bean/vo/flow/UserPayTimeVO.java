package com.yami.shop.bean.vo.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024-03-11
 */
@Data
public class UserPayTimeVO {

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "最近支付时间")
    private Date lastPayTime;

    public UserPayTimeVO() {
    }

    public UserPayTimeVO(String userId, Date lastPayTime) {
        this.userId = userId;
        this.lastPayTime = lastPayTime;
    }
}
