package com.yami.shop.groupbuy.common.dao;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yami.shop.groupbuy.common.api.dto.ApiGroupSkuDto;
import com.yami.shop.groupbuy.common.model.GroupSku;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 拼团活动商品规格
 *
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
public interface GroupSkuMapper extends BaseMapper<GroupSku> {

    /**
     * 获取SKU列表数据
     *
     * @param groupActivityId 拼团活动ID
     * @return SKU列表
     */
    List<ApiGroupSkuDto> getApiByGroupActivityIdAndProdId(@Param("groupActivityId") Long groupActivityId);

    /**
     * 获取指定商品的活动sku列表
     *
     * @param prodIds
     * @return
     */
    List<GroupSku> listSkuByProdIds(@Param("prodIds") List<Long> prodIds);

    /**
     * 根据活动id和活动价格获取skuId
     * @param groupActivityId
     * @param actPrice
     * @return
     */
    Long getSkuIdByProdIdAndActPrice(@Param("groupActivityId")Long groupActivityId,@Param("actPrice") Double actPrice);
}
