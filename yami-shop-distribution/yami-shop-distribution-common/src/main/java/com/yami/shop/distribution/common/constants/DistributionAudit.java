package com.yami.shop.distribution.common.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DistributionAudit {

    /**
     * 提现方式
     */
    Withdrawals_ZERO(0,"无需审核直接发放"),
    Withdrawals_ONE(1,"审核后系统发放"),
    TWithdrawals_TWO(2,"审核后人工发放"),
    /**
     * 收入提示
     */
    INCOME_ZERO(0,"正常"),
    INCOME_ONE(1,"分销佣金大于或者等于订单项实付金额"),
    INCOME_TWO(2,"订单项售后成功"),
    INCOME_THREE(3, "订单无效：无效原因：分销佣金小于0.01"),
    /**
     * 用户分销员绑定状态
     */
    BIND_SYMBOL_ONE(-1,"失效"),
    BIND_ZERO(0, "预绑定"),
    BIND_ONE(1,"生效");

    private final Integer value;
    private final String info;


}
