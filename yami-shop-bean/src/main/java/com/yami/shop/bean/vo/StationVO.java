package com.yami.shop.bean.vo;

import com.yami.shop.bean.app.dto.StationSalesDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 自提点信息VO
 *
 * <AUTHOR>
 * @date 2020-12-07 15:10:01
 */
@Data
public class StationVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "自提点id")
    private Long stationId;

    @Schema(description = "关联店铺id")
    private Long shopId;

    @Schema(description = "关联店铺名称")
    private String shopName;

    @Schema(description = "自提点名称")
    private String stationName;

    @Schema(description = "自提点图片")
    private String pic;

    @Schema(description = "电话区号")
    private String phonePrefix;

    @Schema(description = "手机/电话号码")
    private String phone;

    @Schema(description = "省ID")
    private Long provinceId;

    @Schema(description = "省")
    private String province;

    @Schema(description = "城市ID")
    private Long cityId;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "区ID")
    private Long areaId;

    @Schema(description = "区")
    private String area;

    @Schema(description = "邮编")
    private String postCode;

    @Schema(description = "地址")
    private String addr;

    @Schema(description = "经度")
    private Double lng;

    @Schema(description = "纬度")
    private Double lat;

    @Schema(description = "时间数据")
    private String timeInfo;

    @Schema(description = "0:关闭 1:营业 2:强制关闭 3:审核中 4:审核失败")
    private Integer status;

    @Schema(description = "账号名")
    private String account;

    @Schema(description = "销售数据")
    private List<StationSalesDto> stationSalesDTOList;

    @Schema(description = "库存")
    private Integer stock;

    @Schema(description = "库存模式 参考StockModeEnum")
    private Integer stockMode;

    @Schema(description = "自提门店用途是否支持自提（0：不支持，1：支持）")
    private Integer selfPickup;

    @Schema(description = "自提门店用途是否支持同城配送（0：不支持，1：支持）")
    private Integer sameCityDelivery;

}
