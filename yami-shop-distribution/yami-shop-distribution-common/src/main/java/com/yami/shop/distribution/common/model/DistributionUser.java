package com.yami.shop.distribution.common.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yami.shop.distribution.common.dto.DistributionUserAchievementDataDto;
import com.yami.shop.distribution.common.vo.DistributionUserVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
/**
 * <AUTHOR>
 */
@Data
@TableName("tz_distribution_user")
public class DistributionUser implements Serializable {

    @Schema(description = "促销员表id" )
    @TableId(type = IdType.AUTO)
    private Long distributionUserId;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "卡号" )
    private String cardNo;

    @Schema(description = "用户id" )
    private String userId;

    @Schema(description = "上级id" )
    private Long parentId;

    @Schema(description = "上级促销员ids （如：1,2,3）最上级处于最前面的位置" )
    private String parentIds;

    @Schema(description = "层级（0顶级）" )
    private Integer grade;

    @Schema(description = "关联等级id" )
    private Integer level;

    @Schema(description = "分组id" )
    private Long groupId;

    @Schema(description = "状态(-1永久封禁 0 待审核 1 正常 2 暂时封禁 3 审核未通过，需要重新申请)" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bindTime;

    @Schema(description = "状态(-1永久封禁 0 待审核 1 正常 2 暂时封禁 3 审核未通过，需要重新申请)" )
    private Integer state;

    @Schema(description = "用户昵称" )
    private String nickName;

    @Schema(description = "手机号码" )
    private String userMobile;

    @Schema(description = "真实姓名" )
    private String realName;

    @Schema(description = "身份证号" )
    private String identityCardNumber;

    @Schema(description = "改变成永久封禁或者暂时封禁时的状态记录" )
    private Integer stateRecord;

    @Schema(description = "身份证正面" )
    private String identityCardPicFront;

    @Schema(description = "身份证背面" )
    private String identityCardPicBack;

    @Schema(description = "手持身份证照片" )
    private String identityCardPicHold;

    @Schema(description = "头像" )
    private String pic;

    @Schema(description = "上级分销员" )
    @TableField(exist = false)
    private DistributionUserVO parentDistributionUser;

    @Schema(description = "分销员收入记录" )
    @TableField(exist = false)
    private DistributionUserIncome distributionUserIncome;

    @Schema(description = "分销员各项数据" )
    @TableField(exist = false)
    private DistributionUserAchievementDataDto distributionUserAchievementDataDto;

    @Schema(description ="排序字段" +
            "分销管理-分销员管理： 0无 1加入时间 2累计客户 3累计邀请 4累计收益" +
            "分销管理-业绩统计： 0无 1一代佣金 2二代佣金 3邀请奖励 4待结算金额 5可提现金额 6已失效金额")
    @TableField(exist = false)
    private Integer sortParam = 0;

    @Schema(description = "排序类型 0无 1 正序 2倒序" )
    @TableField(exist = false)
    private Integer sortType = 0;
}
