package com.yami.shop.groupbuy.common.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 拼团活动商品信息
 *
 * <AUTHOR>
 * @date 2019-08-27 17:55:57
 */
@Data
public class ApiGroupProdDto {

    @Schema(description = "商品名称" )
    private String prodName;

    @Schema(description = "商品图片" )
    private String prodPic;

    @Schema(description = "成团人数" )
    private Integer groupNumber;

    @Schema(description = "原售价格" )
    private Double price;

    @Schema(description = "活动价格" )
    private Double actPrice;

    @Schema(description = "活动开始时间" )
    private Date startTime;

    @Schema(description = "活动结束时间" )
    private Date endTime;

    @Schema(description = "创建时间" )
    private Date createTime;

    @Schema(description = "拼团活动ID" )
    private Long groupActivityId;

    @Schema(description = "商品ID" )
    private Long prodId;

    @Schema(description = "虚拟商品的留言备注" )
    private String virtualRemark;

    @Schema(description = "核销次数类型 -1.多次核销 0.无需核销 1.单次核销" )
    private Integer writeOffNum;

    @Schema(description = "多次核销次数 -1.无限次" )
    private Integer writeOffMultipleCount;

    @Schema(description = "核销有效期 -1.长期有效 0.自定义  x.x天内有效" )
    private Integer writeOffTime;

    @Schema(description = "核销开始时间" )
    private Date writeOffStart;

    @Schema(description = "核销结束时间" )
    private Date writeOffEnd;


    @Schema(description = "商品类别 0.实物商品 1. 虚拟商品 2.组合商品" )
    private Integer mold;

}
