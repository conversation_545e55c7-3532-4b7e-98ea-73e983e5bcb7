package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class SignAcctProtocolDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "签约户名(个人会员：名称)(企业会员：[法人提现]法人姓名、[对公户提现]企业名称)")
    private String signAcctName;

    @Schema(description = "签约类型 1.个人 2.法人 3.企业")
    private Integer signAcctType;

    @Schema(description = "跳转页面类型(1:H5页面, 2:小程序页面)(不传默认H5)")
    private Long jumpPageType;

    @Schema(description = "成功后跳转地址")
    private String jumpUrl;

    @Schema(description = "失败/取消后跳转地址")
    private String noContractUrl;

    public String getSignAcctName() {
        return signAcctName;
    }

    public void setSignAcctName(String signAcctName) {
        this.signAcctName = signAcctName;
    }

    public Integer getSignAcctType() {
        return signAcctType;
    }

    public void setSignAcctType(Integer signAcctType) {
        this.signAcctType = signAcctType;
    }

    public Long getJumpPageType() {
        return jumpPageType;
    }

    public void setJumpPageType(Long jumpPageType) {
        this.jumpPageType = jumpPageType;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getNoContractUrl() {
        return noContractUrl;
    }

    public void setNoContractUrl(String noContractUrl) {
        this.noContractUrl = noContractUrl;
    }

    @Override
    public String toString() {
        return "SignAcctProtocolDTO{" +
                "signAcctName='" + signAcctName + '\'' +
                ", signAcctType=" + signAcctType +
                ", jumpPageType=" + jumpPageType +
                ", jumpUrl='" + jumpUrl + '\'' +
                ", noContractUrl='" + noContractUrl + '\'' +
                '}';
    }
}
