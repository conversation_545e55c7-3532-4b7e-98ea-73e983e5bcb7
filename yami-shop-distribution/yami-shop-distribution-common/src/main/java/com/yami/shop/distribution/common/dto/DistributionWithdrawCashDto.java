package com.yami.shop.distribution.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
/**
 * <AUTHOR>
 */
@Data
@Schema(description = "提现数据")
public class DistributionWithdrawCashDto {

    /**
     * 提现记录id
     */
    @Schema(description = "提现记录id" )
    private Long withdrawCashId;

    /**
     * 金额
     */
    @Schema(description = "提现金额" )
    private Double amount;


    /**
     * 手续费
     */
    @Schema(description = "手续费" )
    private Double fee;

    /**
     * 类型(0 手动提现 1自动提现)
     */
    @Schema(description = "类型(0线下打款 1线上打款)" )
    private Integer type;

    /**
     * 资金流向(0 微信红包 1 商家转账款到微信零钱)
     */
    @Schema(description = "0 微信红包 1商家转账款到微信零钱" )
    private Integer moneyFlow;

    /**
     * 流水号
     */
    @Schema(description = "流水号" )
    private String merchantOrderId;

    /**
     * 提现状态(0:申请中 1:提现成功 2:拒绝提现 -1:提现失败)
     */
    @Schema(description = "提现状态(0:申请中 1:提现成功 2:拒绝提现 -1:提现失败)" )
    private Integer state;


    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间" )
    private Date createTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间" )
    private Date updateTime;


}
