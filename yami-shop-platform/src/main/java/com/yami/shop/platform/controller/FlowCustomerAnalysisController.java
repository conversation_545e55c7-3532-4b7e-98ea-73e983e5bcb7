package com.yami.shop.platform.controller;

import com.yami.shop.bean.dto.flow.CustomerRetainedDTO;
import com.yami.shop.bean.dto.flow.MemberReqDTO;
import com.yami.shop.bean.vo.flow.*;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.service.CustomerAnalysisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 顾客分析接口
 * <AUTHOR>
@Tag(name = "顾客分析接口")
@RestController("flowCustomerAnalysisController")
@RequestMapping("/platform/flowCustomerAnalysis")
@AllArgsConstructor
public class FlowCustomerAnalysisController {

    private final CustomerAnalysisService customerAnalysisService;


    /**
     * 会员分析，会员概况
     */
    @Operation(summary = "会员分析，会员概况" , description = "会员分析，会员概况")
    @GetMapping("/getMemberSurvey")
    @PreAuthorize("@pms.hasPermission('platform:flowCustomerAnalysis:info')")
    public ServerResponseEntity<MemberSurveyRespVO> getMemberSurvey(MemberReqDTO param) {
        MemberSurveyRespVO memberSurveyRespVO = customerAnalysisService.getMemberSurvey(param);
        return ServerResponseEntity.success(memberSurveyRespVO);
    }

    /**
     * // bbc平台/b2c商家 接口
     * 会员分析，会员人数趋势/ 会员占比趋势
     */
    @Operation(summary = "会员分析，会员人数趋势/ 会员占比趋势" , description = "会员分析，会员人数趋势/ 会员占比趋势")
    @GetMapping("/getMemberTrend")
    @PreAuthorize("@pms.hasPermission('platform:flowCustomerAnalysis:info')")
    public ServerResponseEntity<List<MemberTrendRespVO>> getMemberTrend(MemberReqDTO param) {
        List<MemberTrendRespVO> resList = customerAnalysisService.getMemberTrend(param);
        return ServerResponseEntity.success(resList);
    }

    /**
     * // bbc平台/b2c商家 接口
     * 导出会员分析，会员人数趋势
     */
    @Operation(summary = "导出导出会员分析，会员人数趋势/ 会员占比趋势" , description = "导出会员分析，会员人数趋势/ 会员占比趋势")
    @GetMapping("/memberTrendExport")
    @PreAuthorize("@pms.hasPermission('member:analysis:export')")
    public void memberTrendExport(MemberReqDTO param, HttpServletResponse response) {
        customerAnalysisService.memberTrendExport(param, response);
    }

    /**
     * // bbc平台/b2c商家 接口
     * 会员分析，会员贡献价值分析
     */
    @Operation(summary = "会员分析，会员贡献价值分析" , description = "会员分析，会员贡献价值分析")
    @GetMapping("/getMemberVontributeValue")
    @PreAuthorize("@pms.hasPermission('platform:flowCustomerAnalysis:info')")
    public ServerResponseEntity<MemberContributeRespVO> getMemberContributeValue(MemberReqDTO param) {
        MemberContributeRespVO contributeRespVO = customerAnalysisService.getMemberContributeValue(param);
        return ServerResponseEntity.success(contributeRespVO);
    }

    /**
     * // bbc平台/b2c商家 接口
     * 会员分析，新老会员成交分析
     */
    @GetMapping("/getMemberDeal")
    @Operation(summary = "会员分析，新老会员成交分析" , description = "会员分析，新老会员成交分析")
    @PreAuthorize("@pms.hasPermission('platform:flowCustomerAnalysis:info')")
    public ServerResponseEntity<MemberDealRespVO> getMemberDeal(MemberReqDTO param) {
        MemberDealRespVO respParam = customerAnalysisService.getMemberDeal(param);
        return ServerResponseEntity.success(respParam);
    }

    /**
     * 客户分析，客户留存分析
     */
    @Operation(summary = "客户分析-客户留存分析" , description = "客户分析，客户留存分析,不做周留存数据")
    @GetMapping("/getCustomerRetained")
    @PreAuthorize("@pms.hasPermission('platform:flowCustomerAnalysis:info')")
    public ServerResponseEntity<List<CustomerRetainVO>> getCustomerRetained(CustomerRetainedDTO customerRetainedDTO) {
        Integer dateType = customerRetainedDTO.getDateType();
        Integer dateRetainType = customerRetainedDTO.getDateRetainType();
        List<CustomerRetainVO> respList = new ArrayList<>();
        if (Objects.equals(1,dateType) && !Objects.equals(1,dateRetainType)) {
            // 最近一月，月留存。此时不显示数据
            return ServerResponseEntity.success(respList);
        }
        respList = customerAnalysisService.getTradeRetained(customerRetainedDTO);
        return ServerResponseEntity.success(respList);
    }
}
