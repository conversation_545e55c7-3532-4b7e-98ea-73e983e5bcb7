package com.yami.shop.bean.vo;

import com.yami.shop.common.util.BmapPoint;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @since 2024/6/4 16:14
 */
@Data
public class SameCityVO {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "同城配送")
    private Long samecityId;

    @Schema(description = "门店id")
    private Long stationId;

    @Schema(description = "配送区域经纬度json")
    private String positionInfo;

    @Schema(description = "收费类型 1按区域收取固定配送费 2按距离收取配送费")
    private Integer chargeType;

    @Schema(description = "起送价 是优惠券/码和满减优惠抵扣前的商品金额，运费不计入起送价。")
    private Double startingFee;

    @Schema(description = "配送费")
    private Double deliveryFee;

    @Schema(description = "默认距离(km)")
    private Double headDistance;

    @Schema(description = "每超出距离(km)")
    private Double overDistance;

    @Schema(description = "每超出距离费用")
    private Double overDistanceFee;

    @Schema(description = "免费重量")
    private Double freeWeight;

    @Schema(description = "续重重量")
    private Double overWeight;

    @Schema(description = "续重费用")
    private Double overWeightFee;

    @Schema(description = "启用状态 1启用 0未启用")
    private Integer status;

    @Schema(description = "经度")
    private Double lng;

    @Schema(description = "纬度")
    private Double lat;

    @Schema(description = "后台坐标点数组")
    private List<BmapPoint> polygonPath;

    @Schema(description = "店铺所在经度")
    private String shopLng;

    @Schema(description = "店铺所在纬度")
    private String shopLat;
}
