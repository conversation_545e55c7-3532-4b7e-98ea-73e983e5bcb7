package com.yami.shop.distribution.common.dto;

import com.yami.shop.distribution.common.vo.DistributionUserIncomeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 分销员数据
 * <AUTHOR>
 */
@Data
public class DistributionUserAchievementDataDto {

    /**
     * 积累绑定客户数
     */
    @Schema(description = "积累绑定客户数" )
    private Integer boundCustomers;

    /**
     * 积累邀请分销员数
     */
    private Integer invitedVeeker;

    /**
     * 积累支付单数
     */
    private Integer payNumber;

    /**
     * 积累成功成交单数
     */
    private Integer successOrderNumber;

    /**
     * 积累成功成交金额
     */
    private Integer successTradingVolume;

    /**
     * 积累推广订单数
     */
    private Integer orderNumber;

    /**
     * 用户消费笔数
     */
    private Double expenseNumber;

    /**
     * 用户消费金额
     */
    private Double sumOfConsumption;

//    /**
//     * 积累充值金额
//     */
//    private Double rechargeAmount ;

    /**
     * 分销员收益明细
     */
    @Schema(description = "收益明细" )
    private DistributionUserIncomeVO distributionUserIncome;

    @Schema(description = "分销员钱包" )
    private DistributionUserWalletDto distributionUserWallet;
}
