package com.yami.shop.groupbuy.common.api.dto;

import com.yami.shop.bean.app.dto.UserAddrDto;
import com.yami.shop.bean.vo.VirtualRemarkVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 拼团用户订单
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "拼团用户订单对象")
public class ApiGroupUserOrderDto {

    @Schema(description = "商品图片" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String prodPic;

    @Schema(description = "规格图片" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String skuPic;

    @Schema(description = "商品名称" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String prodName;

    @Schema(description = "规格名称" , requiredMode = Schema.RequiredMode.REQUIRED)
    private String skuName;

    @Schema(description = "商品原价(单价)" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Double price;

    @Schema(description = "商品拼团价" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Double actPrice;

    @Schema(description = "商品总数" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer prodTotalCount;

    @Schema(description = "商品总值" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Double prodTotalPrice;

    @Schema(description = "拼团商品实付金额" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Double groupProdActualTotal;

    @Schema(description = "运费" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Double transfee;

    @Schema(description = "订单总额" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Double orderTotalPrice;

    @Schema(description = "优惠金额" , requiredMode = Schema.RequiredMode.REQUIRED)
    private Double discountPrice;

    @Schema(description = "店铺id" )
    private Long shopId;

    @Schema(description = "店铺名称" )
    private String shopName;

    @Schema(description = "规格id" )
    private Long skuId;

    @Schema(description = "商品id" )
    private Long prodId;

    @Schema(description = "用户地址信息" , requiredMode = Schema.RequiredMode.REQUIRED)
    private UserAddrDto userAddrDto;

    @Schema(description = "拼团活动id" )
    private Long groupActivityId;

    @Schema(description = "拼团团队id" )
    private Long groupTempId;

    @Schema(description = "拼团商品id" )
    private Long groupProdId;

    @Schema(description = "拼团商品规格id" )
    private Long groupSkuId;

    @Schema(description = "商品类别 0.实物商品 1. 虚拟商品 2.组合商品" )
    private Integer mold = 0;

    @Schema(description = "虚拟商品留言备注" )
    private List<VirtualRemarkVO> virtualRemarkList;
}
