package com.yami.shop.common.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @since 2023/5/22 13:36
 */
@Configuration
public class UserThreadConfig {
    private static final int CORE_SIZE = 0;
    private static final int MAX_SIZE = 200;
    private static final int KEEP_ALIVE_TIME = 60;
    /**
     * 因为每台机器不同，所以线程数量也可以通过配置文件进行配置的，暂时这里直接用写死的
     * 主要用来多线程导出用户信息
     * @return 用户信息线程池
     */
    @Bean
    public ThreadPoolExecutor userThreadPoolExecutor() {
        return new ThreadPoolExecutor(
                CORE_SIZE,
                MAX_SIZE,
                KEEP_ALIVE_TIME,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(100000),
                new ThreadFactoryBuilder()
                        .setNameFormat("User-Thread-Pool-%d").build()
        );
    }
}
