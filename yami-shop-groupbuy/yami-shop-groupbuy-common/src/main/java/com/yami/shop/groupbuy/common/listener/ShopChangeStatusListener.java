package com.yami.shop.groupbuy.common.listener;

import com.yami.shop.bean.enums.ShopStatus;
import com.yami.shop.bean.event.ShopChangeStatusEvent;
import com.yami.shop.groupbuy.common.service.GroupActivityService;
import lombok.AllArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 店铺改变状态监听
 *
 * <AUTHOR>
 * @Date 2021/11/23 10:45
 */
@Component("groupBuyShopChangeStatusListener")
@AllArgsConstructor
public class ShopChangeStatusListener {

    private final GroupActivityService groupActivityService;

    @EventListener(ShopChangeStatusEvent.class)
    public void groupBuyShopChangeStatusListener(ShopChangeStatusEvent event) {
        Long shopId = event.getShopId();
        ShopStatus shopStatus = event.getShopStatus();
        if (Objects.equals(shopStatus, ShopStatus.OFFLINE)) {
            // 店铺下线时，失效所有状态为正在进行中的团购活动
            groupActivityService.invalidGroupByShopId(shopId);
        }
    }
}
