package com.yami.shop.bean.dto.allinpay;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class SetRealNameDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @Schema(description = "姓名")
    private String name;

    @Schema(description = "身份证号码")
    private String identityNo;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdentityNo() {
        return identityNo;
    }

    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo;
    }

    @Override
    public String toString() {
        return "SetRealNameDTO{" +
                "name='" + name + '\'' +
                ", identityNo='" + identityNo + '\'' +
                '}';
    }
}
