package com.yami.shop.common.allinpay.member.resp;


import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class CompanyInfo extends CompanyBasicInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "审核状态 1.待审核 2.审核成功 3.审核失败")
    private Integer status;

    @Schema(description = "审核时间")
    private String checkTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "审核失败原因")
    private String failReason;

    @Schema(description = "OCR识别与企业工商认证信息是否一致 0否1是 没有则不返回")
    private Integer ocrRegnumComparisonResult;

    @Schema(description = "OCR识别与企业法人实名信息是否一致 0否1是 没有则不返回")
    private Integer ocrIdcardComparisonResult;

    @Schema(description = "签约提现协议号")
    private String acctProtocolNo;

    @Schema(description = "法人签约提现协议号")
    private String legalAcctProtocolNo;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(String checkTime) {
        this.checkTime = checkTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public Integer getOcrRegnumComparisonResult() {
        return ocrRegnumComparisonResult;
    }

    public void setOcrRegnumComparisonResult(Integer ocrRegnumComparisonResult) {
        this.ocrRegnumComparisonResult = ocrRegnumComparisonResult;
    }

    public Integer getOcrIdcardComparisonResult() {
        return ocrIdcardComparisonResult;
    }

    public void setOcrIdcardComparisonResult(Integer ocrIdcardComparisonResult) {
        this.ocrIdcardComparisonResult = ocrIdcardComparisonResult;
    }

    public String getAcctProtocolNo() {
        return acctProtocolNo;
    }

    public void setAcctProtocolNo(String acctProtocolNo) {
        this.acctProtocolNo = acctProtocolNo;
    }

    public String getLegalAcctProtocolNo() {
        return legalAcctProtocolNo;
    }

    public void setLegalAcctProtocolNo(String legalAcctProtocolNo) {
        this.legalAcctProtocolNo = legalAcctProtocolNo;
    }

}
