package com.yami.shop.distribution.common.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yami.shop.distribution.common.dto.AchievementDataDto;
import com.yami.shop.distribution.common.dto.DistributionUserAchievementDataDto;
import com.yami.shop.distribution.common.dto.DistributionUserSimpleDto;
import com.yami.shop.distribution.common.model.DistributionUser;
import com.yami.shop.distribution.common.param.RangeTimeParam;
import com.yami.shop.distribution.common.param.UpdateDistributionUserParam;
import com.yami.shop.distribution.common.vo.DistributionUserInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR> on 2019/04/01.
 */
public interface DistributionUserService extends IService<DistributionUser> {

    /**
     * 获取分销员用户信息
     * @param userId 用户id
     * @param shopId 店铺id
     * @return 分销员用户信息
     */
    DistributionUser getByUserIdAndShopId(String userId,Long shopId);

    /**
     * 移除分销员用户信息缓存
     * @param userId 用户id
     * @param shopId 店铺id
     */
    void removeCacheByUserIdAndShopId(String userId,Long shopId);

    /**
     * 分销员业绩数据
     * @param id 分销员id
     * @return 分销员业绩数据
     */
    AchievementDataDto getAchievementDataDtoById(Long id);

    /**
     * 获取分销员与等级条件匹配的数据
     * @param distributionUserId 分销员id
     * @return 分销员与等级条件匹配的数据
     */
    DistributionUserAchievementDataDto getDistributionUserLevelAchievementDataByDistributionUserId(@Param("distributionUserId") Long distributionUserId);

    /**
     * 查询分销员列表
     * @param page 分页参数
     * @param distributionUser 查询参数
     * @param rangeTimeParam 分销员上级绑定时间
     * @param mobile 手机号
     * @param parentMobile 上级手机号
     * @param sortChange 排序规则
     * @param state 状态
     * @return 分销员列表
     */
    IPage<DistributionUser> distributionUserPage(Page page, DistributionUser distributionUser, RangeTimeParam rangeTimeParam
            , String mobile, String parentMobile, Integer sortChange, Integer state);


    /**
     * 封禁分销员
     * @param param 更新分销员状态的状态参数
     * @param modifier 操作人
     */
    void updateSelectiveAndInsertDistributionUserBan(UpdateDistributionUserParam param, Long modifier);

    /**
     * 查询分销员列表
     * @param page 分页参数
     * @param distributionUser 查询参数
     * @param userMobile 手机号
     * @return 分销员列表
     */
    IPage<DistributionUser> getDistributionUserAchievementPage(Page page,DistributionUser distributionUser,String userMobile);

    /**
     * 申请注册成为分销员
     * @param distributionUser 分销员信息
     */
    void registerDistributionUser(DistributionUser distributionUser);

    /**
     * 分页获取精简版分销员数据
     * @param page 分页参数
     * @param parentDistributionUserId 上级分销员id
     * @return 精简版分销员分页数据
     */
    IPage<DistributionUserSimpleDto> getDistributionUserSimpleDtoByParentUserIdPage(Page page, Long parentDistributionUserId);


    /**
     * 根据分销员卡号获取分销员信息
     * @param cardNo 卡号
     * @return 分销员信息
     */
    DistributionUser getByCardNo(String cardNo);

    /**
     * 移除根据分销员卡号获取分销员信息的缓存
     * @param cardNo 卡号
     */
    void removeCacheByCardNo(String cardNo);

    /**
     * 获取分销员列表
     * @param identityCardNumber 分销员身份证信息
     * @param userMobile 手机号
     * @return 分销员列表
     */
    List<DistributionUser> getDistributionUserByIdCardNumberAndUserMobile(String identityCardNumber, String userMobile);


    /**
     * 获取好友列表
     * @param user 分销员信息
     * @param userType
     * @return
     */
    List<DistributionUserInfoVO> getDistributionMyFriend(DistributionUser user, Integer userType);
}
