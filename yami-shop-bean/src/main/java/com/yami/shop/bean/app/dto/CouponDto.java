package com.yami.shop.bean.app.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "优惠券对象")
public class CouponDto implements Serializable {

    @Schema(description = "优惠券id" )
    private Long couponId;

    @Schema(description = "店铺ID" )
    private Long shopId;

    @Schema(description = "店铺名称" )
    private String shopName;

    @Schema(description = "店铺logo" )
    private String shopLogo;

    @Schema(description = "优惠券名称" )
    private String couponName;

    @Schema(description = "副标题" )
    private String subTitle;

    @Schema(description = "优惠类型 1:代金券 2:折扣券 3:兑换券" )
    private Integer couponType;

    @Schema(description = "使用条件" )
    private Double cashCondition;

    @Schema(description = "减免金额" )
    private Double reduceAmount;

    @Schema(description = "折扣额度" )
    private Double couponDiscount;

    @Schema(description = "生效类型 1:固定时间 2:领取后生效" )
    private Integer validTimeType;

    @Schema(description = "开始时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @Schema(description = "结束时间" )
    @DateTimeFormat(pattern = "yyyy-MM-dd HH")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @Schema(description = "领券后X天起生效" )
    private Integer afterReceiveDays;

    @Schema(description = "有效天数" )
    private Integer validDays;

    @Schema(description = "库存" )
    private Integer stocks;

    @Schema(description = "初始库存" )
    private Integer sourceStock;

    @Schema(description = "适用商品类型 0全部商品参与 1指定商品参与 2指定商品不参与" )
    private Integer suitableProdType;

    @Schema(description = "指定商品图片" )
    private String pic;

    @Schema(description = "指定商品id" )
    private Long prodId;

    @Schema(description = "指定商品价格" )
    private Double price;

    @Schema(description = "每个用户领券上限" )
    private Integer limitNum;

    @Schema(description = "优惠券状态 0:失效 1:有效 2:使用过" )
    private Integer status;

    @Schema(description = "优惠券过期状态 0:过期 1:未过期" )
    private Integer overdueStatus;

    @Schema(description = "优惠券投放状态 0:未投放 1:投放" )
    private Integer putonStatus;

    @Schema(description = "用户优惠券id" )
    private Long couponUserId;

    @Schema(description = "领券时间" )
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date receiveTime;

    @Schema(description = "参与优惠券的商品集合" )
    private List<ProductDto> prods;

    @Schema(description = "当前登陆用户领取的优惠券数量" )
    private Integer curUserReceiveCount;

    @Schema(description = "当前登陆用户可用的优惠券数量" )
    private Integer userHasCount;
}
