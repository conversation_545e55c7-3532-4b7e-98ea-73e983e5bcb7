package com.yami.shop.common.allinpay.order.resp;

/**
 * 单笔托管代付列表请求返回
 *
 * <AUTHOR>
 */
public class AgentPayResp {
    /**
     * 托管代付状态
     * 1、成功：success
     * 2、进行中：pending
     * 3、失败：fail
     */
    private String payStatus;

    /**
     * 支付失败信息，只有payStatus为fail时有效
     */
    private String payFailMessage;

    /**
     * 云商通订单号
     */
    private String orderNo;

    /**
     * 商户订单号（支付订单）
     */
    private String bizOrderNo;

    /**
     * 代付去向，1.到账户余额
     */
    private String payWhereabouts;

    /**
     * 扩展参数
     */
    private String extendInfo;

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public String getPayFailMessage() {
        return payFailMessage;
    }

    public void setPayFailMessage(String payFailMessage) {
        this.payFailMessage = payFailMessage;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public String getPayWhereabouts() {
        return payWhereabouts;
    }

    public void setPayWhereabouts(String payWhereabouts) {
        this.payWhereabouts = payWhereabouts;
    }

    public String getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo;
    }

    @Override
    public String toString() {
        return "AgentPayResp{" +
                "payStatus='" + payStatus + '\'' +
                ", payFailMessage='" + payFailMessage + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", bizOrderNo='" + bizOrderNo + '\'' +
                ", payWhereabouts='" + payWhereabouts + '\'' +
                ", extendInfo='" + extendInfo + '\'' +
                '}';
    }
}
