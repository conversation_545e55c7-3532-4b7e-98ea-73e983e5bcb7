package com.yami.shop.platform.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yami.shop.bean.dto.FinanceDetailsDto;
import com.yami.shop.bean.dto.RevenueOverviewDto;
import com.yami.shop.bean.param.FinanceDetailsParam;
import com.yami.shop.bean.param.RevenueOverviewParam;
import com.yami.shop.common.response.ServerResponseEntity;
import com.yami.shop.common.util.PageParam;
import com.yami.shop.service.RevenueOverviewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;

/**
 * 财务管理—营收概况
 *
 * <AUTHOR>
 * @date 2020-08-17
 */

@RestController
@RequestMapping("/platform/financialManagement")
@Tag(name = "营收概况")
@AllArgsConstructor
public class RevenueOverviewController {

    private final RevenueOverviewService revenueOverviewService;

    @GetMapping("/getIncomeProfile")
    @Operation(summary = "获取商家和日期的收入金额和退款金额" , description = "获取商家和日期的收入金额和退款金额")
    @PreAuthorize("@pms.hasPermission('platform:financialManagement:info')")
    public ServerResponseEntity<RevenueOverviewDto> getIncomeProfile(RevenueOverviewParam param) throws ParseException {
        RevenueOverviewDto result = revenueOverviewService.getData(param);
        return ServerResponseEntity.success(result);
    }

    @GetMapping("/getFinanceDetail")
    @Operation(summary = "分页获取财务明细" , description = "分页获取财务明细")
    @PreAuthorize("@pms.hasPermission('platform:financialManagement:page')")
    public ServerResponseEntity<IPage<FinanceDetailsDto>> getFinanceDetails(PageParam<FinanceDetailsDto> page, FinanceDetailsParam param) {
        IPage<FinanceDetailsDto> result = revenueOverviewService.getPageDetail(page, param);
        return ServerResponseEntity.success(result);
    }

    @GetMapping("/getFinanceDetailForm")
    @Operation(summary = "导出报表" , description = "导出报表")
    @PreAuthorize("@pms.hasPermission('finance:detail:excel')")
    public void getFinanceDetailForm(FinanceDetailsParam param, HttpServletResponse response) {
        revenueOverviewService.excelFianceDetail(param, response);
    }
}
