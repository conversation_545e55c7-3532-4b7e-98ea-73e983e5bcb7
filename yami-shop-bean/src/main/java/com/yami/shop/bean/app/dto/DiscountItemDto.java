package com.yami.shop.bean.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class DiscountItemDto implements Serializable {

    @Schema(description = "优惠项id" )
    private Long discountItemId;

    @Schema(description = "优惠活动id" )
    private Long discountId;

    @Schema(description = "所需需要金额" )
    private Double needAmount;

    @Schema(description = "优惠（元/折）" )
    private Double discount;
}
