package com.yami.shop.bean.dto.wukong;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 注册或登录
 * <AUTHOR>
 */
public class RegisterOrLoginDTO {

    @Schema(description = "通信的用户唯一ID，可以随机uuid（建议自己服务端的用户唯一uid）")
    private String uid;

    @Schema(description = "校验的token，随机uuid")
    private String token;

    @Schema(description = "设备标识  0.app 1.web 相同用户相同设备标记的主设备登录会互相踢 从设备将共存")
    private Integer device_flag;

    @Schema(description = "设备等级 0.为从设备 1.为主设备")
    private Integer device_level;

    public RegisterOrLoginDTO() {
    }

    public RegisterOrLoginDTO(String uid, Integer device_flag) {
        this.uid = uid;
        this.device_flag = device_flag;
    }

    public RegisterOrLoginDTO(String uid, String token, Integer device_flag, Integer device_level) {
        this.uid = uid;
        this.token = token;
        this.device_flag = device_flag;
        this.device_level = device_level;
    }

    public RegisterOrLoginDTO(String uid, String token, Integer device_flag) {
        this.uid = uid;
        this.token = token;
        this.device_flag = device_flag;
        this.device_level = 1;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getDevice_flag() {
        return device_flag;
    }

    public void setDevice_flag(Integer device_flag) {
        this.device_flag = device_flag;
    }

    public Integer getDevice_level() {
        return device_level;
    }

    public void setDevice_level(Integer device_level) {
        this.device_level = device_level;
    }

    @Override
    public String toString() {
        return "RegisterOrLogin{" +
                "uid='" + uid + '\'' +
                ", token='" + token + '\'' +
                ", device_flag=" + device_flag +
                ", device_level=" + device_level +
                '}';
    }
}
